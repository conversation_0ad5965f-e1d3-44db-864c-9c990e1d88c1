from data_sdk.domain import DisplayPortal
from data_sdk.domain.platform import DisplayPlatform
from data_sdk.domain.ppr import get_portal_platform_region_id
from data_sdk.domain.regions import Region


def test_get_portal_platform_region_id_return_proper_values():
    assert (
        get_portal_platform_region_id(
            f"{DisplayPortal.NINTENDO.value}:{DisplayPlatform.SWITCH.value}:{Region.NINTENDO_AMERICAS.value}"
        )
        == 151511
    )

    assert (
        get_portal_platform_region_id(
            f"{DisplayPortal.NINTENDO.value}:{DisplayPlatform.SWITCH.value}:{Region.NINTENDO_EUROPE_AUSTRALIA.value}"
        )
        == 151513
    )

    assert (
        get_portal_platform_region_id(
            f"{DisplayPortal.PLAYSTATION.value}:{DisplayPlatform.PS_5.value}:{Region.PLAYSTATION_AMERICA.value}"
        )
        == 162315
    )


def test_get_portal_platform_region_id_return_default_value_for_unknown_portal():
    assert (
        get_portal_platform_region_id(
            f"UnknownPortal:{DisplayPlatform.SWITCH.value}:{Region.NINTENDO_AMERICAS.value}"
        )
        == 1511
    )


def test_get_portal_platform_region_id_return_default_value_for_unknown_platform():
    assert (
        get_portal_platform_region_id(
            f"{DisplayPortal.NINTENDO.value}:UnknownPlatform:{Region.NINTENDO_EUROPE_AUSTRALIA.value}"
        )
        == 150013
    )


def test_get_portal_platform_region_id_return_default_value_for_unknown_region():
    assert (
        get_portal_platform_region_id(
            f"{DisplayPortal.NINTENDO.value}:{DisplayPlatform.SWITCH.value}:UnknownRegion"
        )
        == 151500
    )
