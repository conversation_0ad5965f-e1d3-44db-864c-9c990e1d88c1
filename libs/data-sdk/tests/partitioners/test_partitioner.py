from data_sdk.custom_partition.partitioner import get_table_partition
from data_sdk.domain import Portal
from data_sdk.domain.domain_types import StudioId
from data_sdk.domain.observations import ObservationType
from data_sdk.domain.tables import (
    ExternalSKUsTable,
    ObservationSalesTable,
    SilverReportsTable,
)


def test_partitioning_by_studio():
    result = get_table_partition(ExternalSKUsTable.empty(), studio_id=StudioId(1))

    assert result == "external_skus/studio_id=1/version=v1"


def test_partitioning_by_studio_portal():
    result = get_table_partition(
        ObservationSalesTable.empty(), studio_id=StudioId(1), portal=Portal.STEAM
    )

    assert result == "observation_sales/studio_id=1/portal=steam/version=v1"


def test_partitioning_by_studio_portal_observation_type():
    result = get_table_partition(
        SilverReportsTable.empty(),
        studio_id=StudioId(1),
        portal=Portal.STEAM,
        observation_type=ObservationType.SALES,
    )

    assert (
        result
        == "silver_reports/studio_id=1/portal=steam/observation_type=sales/version=v1"
    )
