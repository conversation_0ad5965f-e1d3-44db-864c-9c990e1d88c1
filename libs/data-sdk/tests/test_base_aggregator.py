from datetime import datetime

import pandera.polars as pa
import polars as pl
import pytest
from pandera.engines.polars_engine import Date

from data_sdk.aggregator import BaseAggregator, StrictBaseSchema
from data_sdk.domain import TableName
from data_sdk.domain.tables import TablePartitionedByPortal


class ExampleModel(pa.DataFrameModel):
    field: int = pa.Field(ge=0)


class SilverAcquisitionPropertiesTable(TablePartitionedByPortal):
    table_name = TableName.SILVER_ACQUISITION_PROPERTIES
    model = ExampleModel


def test_aggregate_strict_validates_output_schema_for_completely_non_compliant_aggregator():
    class NonSchemaCompliantAggregator(BaseAggregator):
        table_cls = SilverAcquisitionPropertiesTable

        def _aggregate(self) -> pl.DataFrame:
            return pl.DataFrame({"non_schema_defined": [1, 2, 3]})

    with pytest.raises(pa.errors.SchemaError):
        aggregator = NonSchemaCompliantAggregator()
        aggregator.aggregate()


def test_aggregate_strict_validates_output_schema():
    class AggregatorWithNonStrictAggregateReturn(BaseAggregator):
        table_cls = SilverAcquisitionPropertiesTable

        def _aggregate(self) -> pl.DataFrame:
            return pl.DataFrame(
                {"non_schema_defined": [1, 2, 3], "field": ["one", "two", "three"]}
            )

    with pytest.raises(pa.errors.SchemaError):
        aggregator = AggregatorWithNonStrictAggregateReturn()
        aggregator.aggregate()


class ComplexStrictModel(StrictBaseSchema):
    date: Date = pa.Field()
    date_str: str = pa.Field()
    ordinal: int = pa.Field()
    ordinal_str: str = pa.Field()


class ComplexSilverAcquisitionPropertiesTable(TablePartitionedByPortal):
    table_name = TableName.SILVER_ACQUISITION_PROPERTIES
    model = ComplexStrictModel


class AggregatorWithComplexStrictSchema(BaseAggregator):
    table_cls = ComplexSilverAcquisitionPropertiesTable

    def _aggregate(self) -> pl.DataFrame:
        date_str_list = ["2021-01-01", "2021-01-02", "2021-01-03"]
        int_list = list(range(1, 4))
        # Fields purposefully have invalid types
        return pl.DataFrame(
            {
                "date": ["2021-01-01", "2021-01-02", "2021-01-03"],
                "date_str": [
                    datetime.strptime(date_str, "%Y-%m-%d").date()
                    for date_str in date_str_list
                ],
                "ordinal": [str(i) for i in int_list],
                "ordinal_str": int_list,
            },
        )


def test_aggregate_strict_coerces_types():
    aggregator = AggregatorWithComplexStrictSchema()
    result = aggregator.aggregate()
    # Fields have been coerced to the types defined in schema
    assert result.df.dtypes == [pl.Date, pl.String, pl.Int64, pl.String]
