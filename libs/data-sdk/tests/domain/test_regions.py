import pytest

from data_sdk.domain.regions import Region, get_nintendo_region_by_reported_region


def test_get_nintendo_region_by_reported_region_valid_regions():
    test_cases = [
        ("Americas", Region.NINTENDO_AMERICAS.value),
        ("Australia", Region.NINTENDO_EUROPE_AUSTRALIA.value),
        ("Europe", Region.NINTENDO_EUROPE_AUSTRALIA.value),
        ("Japan", Region.NINTENDO_JAPAN.value),
        ("China", Region.NINTENDO_CHINA.value),
        ("Korea", Region.NINTENDO_KOREA.value),
        ("Taiwan/Hong Kong", Region.NINTENDO_TAIWAN_HONG_KONG.value),
        ("Hong Kong", Region.NINTENDO_TAIWAN_HONG_KONG.value),
        ("Korea/Taiwan/Hong Kong", Region.NINTENDO_TAIWAN_HONG_KONG.value),
    ]

    for input_region, expected_region in test_cases:
        assert get_nintendo_region_by_reported_region(input_region) == expected_region


def test_get_nintendo_region_by_reported_region_nonexistent_raises_exception():
    with pytest.raises(
        KeyError,
        match="Region 'InvalidRegion' is not recognized as a valid nintendo region.",
    ):
        get_nintendo_region_by_reported_region("InvalidRegion")
