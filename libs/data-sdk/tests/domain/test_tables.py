import polars as pl
import pytest
from pandera.errors import SchemaError

from data_sdk.domain.tables import ExternalSKUsTable


@pytest.fixture
def expected_external_sku_schema():
    return {
        "store_id": pl.String,
        "unique_sku_id": pl.String,
        "studio_id": pl.Int64,
        "product_name": pl.String,
        "custom_group": pl.String,
        "ratio": pl.Int64,
        "base_sku_id": pl.String,
        "human_name": pl.String,
        "package_name": pl.String,
        "product_type": pl.String,
        "portal": pl.String,
        "sku_type": pl.String,
        "human_name_indicator": pl.String,
        "is_discountable": pl.<PERSON><PERSON>,
    }


def test_table_with_invalid_schema_raises_error_on_creation():
    with pytest.raises(SchemaError):
        ExternalSKUsTable(df=pl.DataFrame({"not_a_valid_column": 123}))


def test_returns_empty_df_with_valid_columns(expected_external_sku_schema):
    empty_table = ExternalSKUsTable.empty()

    assert dict(empty_table.df.schema) == expected_external_sku_schema
