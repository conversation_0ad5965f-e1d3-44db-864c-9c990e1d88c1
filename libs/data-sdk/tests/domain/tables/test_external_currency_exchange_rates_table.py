import polars as pl

from data_sdk.domain.tables import ExternalCurrencyExchangeRatesTable


def test_table_should_be_sorted_by_date_and_currency_code_after_initialization():
    unsorted_data = [
        {"date": "2020-01-02", "currency_code": "AUD", "rate_to_usd": 1.00},
        {"date": "2020-01-02", "currency_code": "CNY", "rate_to_usd": 4.02},
        {"date": "2020-01-01", "currency_code": "CNY", "rate_to_usd": 4.03},
        {"date": "2020-01-01", "currency_code": "AUD", "rate_to_usd": 3.01},
    ]
    table = ExternalCurrencyExchangeRatesTable(df=pl.DataFrame(unsorted_data))
    assert table.df.equals(
        pl.DataFrame(
            [
                {"date": "2020-01-01", "currency_code": "AUD", "rate_to_usd": 3.01},
                {"date": "2020-01-01", "currency_code": "CNY", "rate_to_usd": 4.03},
                {"date": "2020-01-02", "currency_code": "AUD", "rate_to_usd": 1.00},
                {"date": "2020-01-02", "currency_code": "CNY", "rate_to_usd": 4.02},
            ]
        )
    )


def test_table_should_allow_empty_df_on_initialization():
    table = ExternalCurrencyExchangeRatesTable(df=pl.DataFrame())
    assert table.df.is_empty()
