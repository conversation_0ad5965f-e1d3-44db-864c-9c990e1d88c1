from enum import Enum
from typing import Annotated

import pandera.polars as pap
import polars as pl
import pytest
from pandera.typing import Series

from data_sdk.config import LocalConfig
from data_sdk.custom_partition.reader import LocalCustomPartitionReader
from data_sdk.domain.domain_types import StudioId
from data_sdk.domain.tables import TablePartitionedByPortal
from data_sdk.utils.pandera import Enum as PanderaEnum
from data_sdk.validators.schemas import NoCheckSchema


class StoreV1(str, Enum):
    EPIC = "Epic"


class StoreV2(str, Enum):
    EPIC = "Epic"
    GOG = "GOG"


StoreV1Column = Series[Annotated[PanderaEnum, [x.value for x in StoreV1]]]
StoreV2Column = Series[Annotated[PanderaEnum, [x.value for x in StoreV2]]]


class StoreV1Model(pap.DataFrameModel):
    store: StoreV1Column

    class Config:
        coerce = True
        strict = True


class StoreV2Model(pap.DataFrameModel):
    store: StoreV2Column

    class Config:
        coerce = True
        strict = True


class DummyTable(TablePartitionedByPortal):
    table_name = type("TableName", (), {"value": "dummy_table"})
    model = StoreV2Model


class DummyTableWithNoCheckSchema(TablePartitionedByPortal):
    table_name = type("TableName", (), {"value": "dummy_table"})
    model = NoCheckSchema


@pytest.fixture
def partitions_with_different_enum_versions(tmp_path):
    file1 = (
        tmp_path
        / "dummy_table/studio_id=1/portal=steam/version=v1/20250605T162623Z.parquet"
    )
    file2 = (
        tmp_path
        / "dummy_table/studio_id=1/portal=epic/version=v1/20250605T162623Z.parquet"
    )

    file1.parent.mkdir(parents=True, exist_ok=True)
    file2.parent.mkdir(parents=True, exist_ok=True)

    df_old = StoreV1Model.validate(pl.DataFrame({"store": ["Epic"]}))
    df_new = StoreV2Model.validate(pl.DataFrame({"store": ["GOG"]}))

    df_old.write_parquet(file1)
    df_new.write_parquet(file2)


def test_read_partitions_with_different_enum_values(
    partitions_with_different_enum_versions, tmp_path
):
    reader = LocalCustomPartitionReader(LocalConfig(local_dir=tmp_path))

    result = reader.read_table(DummyTable, studio_id=StudioId(1))

    assert sorted(result.df["store"].to_list()) == ["Epic", "GOG"]


def test_read_ignores_extra_columns(tmp_path):
    table_file = (
        tmp_path
        / "dummy_table/studio_id=1/portal=steam/version=v1/20250605T162623Z.parquet"
    )
    table_file.parent.mkdir(parents=True, exist_ok=True)

    pl.DataFrame({"store": ["Epic"], "column_to_ignore": [1]}).write_parquet(table_file)

    reader = LocalCustomPartitionReader(LocalConfig(local_dir=tmp_path))

    result = reader.read_table(DummyTable, studio_id=StudioId(1))

    assert result.df.columns == ["store"]


def test_read_ignores_index_with_no_check_schema(tmp_path):
    table_file = (
        tmp_path
        / "dummy_table/studio_id=1/portal=steam/version=v1/20250605T162623Z.parquet"
    )
    table_file.parent.mkdir(parents=True, exist_ok=True)

    pl.DataFrame({"store": ["Epic"], "__index_level_0__": [1]}).write_parquet(
        table_file
    )

    reader = LocalCustomPartitionReader(LocalConfig(local_dir=tmp_path))

    result = reader.read_table(DummyTableWithNoCheckSchema, studio_id=StudioId(1))

    assert result.df.columns == ["store"]
