from typing import cast

from data_sdk.dependency_injection import generate_init_kwargs
from data_sdk.domain.domain_types import StudioId
from data_sdk.domain.tables import (
    ExternalSKUsTable,
    ObservationSalesTable,
    TableDefinition,
)
from tests.utils import Mock<PERSON>eader, compare_dataframes


def test_get_init_kwargs__with_tables():
    class TestClass:
        def __init__(
            self, skus: ExternalSKUsTable, sales: ObservationSalesTable
        ) -> None:
            pass

    result = generate_init_kwargs(
        TestClass,
        MockReader(),
        studio_id=StudioId(1),
    )

    expected_tables = {
        "skus": ExternalSKUsTable.empty(),
        "sales": ObservationSalesTable.empty(),
    }

    assert result.keys() == expected_tables.keys()

    for table in expected_tables:
        df = cast(TableDefinition, result[table]).df
        compare_dataframes(
            actual=df.to_pandas(),
            expected=expected_tables[table].df.to_pandas(),
        )


def test_get_init_kwargs__with_tables_and_dependencies():
    class Dep1: ...

    class Dep2: ...

    class TestClass:
        def __init__(
            self,
            skus: ExternalSKUsTable,
            sales: ObservationSalesTable,
            dep1: Dep1,
            name_not_matching: Dep2,
        ) -> None:
            pass

    dep_1 = Dep1()
    dep_2 = Dep2()
    dependencies = {Dep1: dep_1, Dep2: dep_2}

    result = generate_init_kwargs(
        TestClass,
        MockReader(),
        studio_id=StudioId(1),
        dependencies=dependencies,
    )

    expected_tables = {
        "skus": ExternalSKUsTable.empty(),
        "sales": ObservationSalesTable.empty(),
    }
    assert result.keys() == {"skus", "sales", "dep1", "name_not_matching"}

    assert result["dep1"] == dep_1
    assert result["name_not_matching"] == dep_2

    for table in expected_tables:
        df = cast(TableDefinition, result[table]).df
        compare_dataframes(
            actual=df.to_pandas(),
            expected=expected_tables[table].df.to_pandas(),
        )
