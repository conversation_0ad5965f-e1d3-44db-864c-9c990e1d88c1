from datetime import datetime
from pathlib import Path, PosixPath

# from unittest.mock import MagicMock
from unittest.mock import patch

import polars as pl
import pytest

from data_sdk.config import DLSConfig, Extension, LocalConfig
from data_sdk.custom_partition.reader import (
    partition_template_builder,
)
from data_sdk.custom_partition.writer import (
    CustomPartitionsWriter,
    DLSCustomPartitionWriter,
    LocalCustomPartitionsWriter,
)
from data_sdk.domain.tables import ObservationSalesTable
from data_sdk.segmentator import SingleSegmentator


@pytest.fixture
def default_partition_filter():
    return {
        "table": "observation_sales",
        "studio_id": 1,
        "portal": "steam",
        "version": "v1",
    }


@pytest.fixture
def default_observation_sales_table_definition():
    return ObservationSalesTable(df=pl.DataFrame())


def test_local_writer_is_returned_with_correct_config():
    local_config = LocalConfig(local_dir=Path("test"))
    writer = CustomPartitionsWriter.get_writer(local_config)
    assert isinstance(writer, LocalCustomPartitionsWriter)


def test_dls_writer_is_returned_with_correct_config():
    local_config = DLSConfig(account_name="test", container_name="test")
    writer = CustomPartitionsWriter.get_writer(local_config)
    assert isinstance(writer, DLSCustomPartitionWriter)


def test_local_writer_saves_table_in_correct_path(
    default_partition_filter, default_observation_sales_table_definition
):
    directory = "test_dir"
    with patch(
        "data_sdk.custom_partition.writer.pl.DataFrame.write_parquet"
    ) as mock_write_parquet:
        local_config = LocalConfig(local_dir=Path(directory))
        writer = CustomPartitionsWriter.get_writer(local_config)
        patrition_template = partition_template_builder(
            default_observation_sales_table_definition.partitions
        )
        partition_filter = default_partition_filter
        partition = patrition_template.format(**partition_filter)
        segments = SingleSegmentator().create_segments(
            default_observation_sales_table_definition, datetime(2012, 1, 1)
        )
        writer.save_table(
            default_observation_sales_table_definition, partition, segments
        )

        mock_write_parquet.assert_called_once()
        assert mock_write_parquet.call_args[0][0] == PosixPath(
            "test_dir/observation_sales/studio_id=1/portal=steam/version=v1/20120101T000000Z.parquet"
        )


def test_local_writer_saves_csv_table_in_correct_path(
    default_partition_filter, default_observation_sales_table_definition
):
    directory = "test_dir"
    with patch(
        "data_sdk.custom_partition.writer.pl.DataFrame.write_csv"
    ) as mock_write_csv:
        local_config = LocalConfig(
            local_dir=Path(directory), file_extension=Extension.CSV
        )
        writer = CustomPartitionsWriter.get_writer(local_config)
        patrition_template = partition_template_builder(
            default_observation_sales_table_definition.partitions
        )
        partition_filter = default_partition_filter
        partition = patrition_template.format(**partition_filter)
        segments = SingleSegmentator().create_segments(
            default_observation_sales_table_definition, datetime(2012, 1, 1)
        )
        writer.save_table(
            default_observation_sales_table_definition, partition, segments
        )

        mock_write_csv.assert_called_once()
        assert mock_write_csv.call_args[0][0] == PosixPath(
            "test_dir/observation_sales/studio_id=1/portal=steam/version=v1/20120101T000000Z.csv"
        )


def test_dls_writer_saves_table_in_correct_path(
    default_partition_filter, default_observation_sales_table_definition
):
    directory = "test_dir"
    with (
        patch(
            "data_sdk.custom_partition.writer.FileSystemClient.get_file_client"
        ) as mock_get_file_system_client,
        patch(
            "data_sdk.custom_partition.writer.pl.DataFrame.write_parquet"
        ) as mock_write_parquet,
    ):
        local_config = DLSConfig(
            account_name="test",
            container_name="test",
            base_dir=Path(directory),
        )
        writer = CustomPartitionsWriter.get_writer(local_config)
        patrition_template = partition_template_builder(
            default_observation_sales_table_definition.partitions
        )
        partition_filter = default_partition_filter
        partition = patrition_template.format(**partition_filter)
        segments = SingleSegmentator().create_segments(
            default_observation_sales_table_definition, datetime(2012, 1, 1)
        )
        writer.save_table(
            default_observation_sales_table_definition, partition, segments
        )

        mock_write_parquet.assert_called_once()
        mock_get_file_system_client.assert_called_once()

        assert (
            mock_get_file_system_client.call_args[0][0]
            == "test_dir/observation_sales/studio_id=1/portal=steam/version=v1/20120101T000000Z.parquet"
        )


def test_dls_writer_saves_csv_table_in_correct_path(
    default_partition_filter, default_observation_sales_table_definition
):
    directory = "test_dir"
    with (
        patch(
            "data_sdk.custom_partition.writer.FileSystemClient.get_file_client"
        ) as mock_get_file_system_client,
        patch(
            "data_sdk.custom_partition.writer.pl.DataFrame.write_csv"
        ) as mock_write_csv,
    ):
        local_config = DLSConfig(
            account_name="test",
            container_name="test",
            base_dir=Path(directory),
            file_extension=Extension.CSV,
        )
        writer = CustomPartitionsWriter.get_writer(local_config)
        patrition_template = partition_template_builder(
            default_observation_sales_table_definition.partitions
        )
        partition_filter = default_partition_filter
        partition = patrition_template.format(**partition_filter)
        segments = SingleSegmentator().create_segments(
            default_observation_sales_table_definition, datetime(2012, 1, 1)
        )
        writer.save_table(
            default_observation_sales_table_definition, partition, segments
        )

        mock_write_csv.assert_called_once()
        mock_get_file_system_client.assert_called_once()

        assert (
            mock_get_file_system_client.call_args[0][0]
            == "test_dir/observation_sales/studio_id=1/portal=steam/version=v1/20120101T000000Z.csv"
        )
