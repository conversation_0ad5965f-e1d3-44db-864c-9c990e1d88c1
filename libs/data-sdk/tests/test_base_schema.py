import pandera.polars as pandera_polars
import polars as pl
import pytest
from pandera.typing.polars import DataFrame as DataframePolarsType

from data_sdk.aggregator import StrictBaseSchema


class SchemaWithStrictTrue(StrictBaseSchema):
    field: int = pandera_polars.Field(ge=0)

    class Config:
        coerce = True
        strict = True


class SchemaWithStrictFilter(StrictBaseSchema):
    field: int = pandera_polars.Field(ge=0)


# This is an example on what will happen when strict is set to True
def test_check_type_should_raise_exception_with_strict_true_and_extended_dataframe():
    frame = pl.DataFrame({"non_schema_defined": [1, 2, 3], "field": [1, 2, 3]})

    @pandera_polars.check_types
    def _internal(
        input_frame: pl.DataFrame,
    ) -> DataframePolarsType[SchemaWithStrictTrue]:
        return input_frame

    with pytest.raises(pandera_polars.errors.SchemaError):
        _internal(frame)


def test_check_type_should_pass_with_strict_filter_and_extended_dataframe_but_discard_the_non_defined_part():
    frame = pl.DataFrame({"non_schema_defined": [1, 2, 3], "field": [1, 2, 3]})

    @pandera_polars.check_types
    def _internal(
        input_frame: pl.DataFrame,
    ) -> DataframePolarsType[SchemaWithStrictFilter]:
        return input_frame

    assert _internal(frame).columns == ["field"]
