import logging
from abc import ABC, abstractmethod

from data_sdk.custom_partition.reader import Custom<PERSON>art<PERSON><PERSON>eader
from data_sdk.dependency_injection import generate_init_kwargs
from data_sdk.domain import Portal
from data_sdk.domain.domain_types import StudioId
from data_sdk.domain.observations import ObservationType

log = logging.getLogger(__name__)


class BaseReporter(ABC):
    @abstractmethod
    def _process(self) -> None:
        pass

    def process(self) -> None:
        return self._process()


def process_reporters(
    reporters: list[type[BaseReporter]],
    reader: CustomPartitionReader,
    dependencies: dict,
    studio_id: StudioId,
    portal: Portal,
    observation_type: ObservationType,
):
    for reporter_cls in reporters:
        log.info("Processing reporter %s", reporter_cls)
        _process_reporter(
            reporter_cls,
            reader,
            dependencies,
            studio_id=studio_id,
            portal=portal,
            observation_type=observation_type,
        )
        log.info("Finished reporter %s", reporter_cls)


def _process_reporter(
    reporter_cls: type[BaseReporter],
    reader: CustomPartitionReader,
    dependencies: dict,
    studio_id: StudioId,
    portal: Portal,
    observation_type: ObservationType,
):
    log.info("Reading tables for studio %s", studio_id)

    init_kwargs = generate_init_kwargs(
        cls=reporter_cls,
        reader=reader,
        studio_id=studio_id,
        portal=portal,
        observation_type=observation_type,
        dependencies={
            **dependencies,
            StudioId: studio_id,
            Portal: portal,
            ObservationType: observation_type,
        },
    )
    log.info("Processing reporter")
    reporter_cls(**init_kwargs).process()
