import logging
import sys


def _override_logger(logger, handler):
    logger.handlers.clear()
    logger.propagate = False
    logger.addHandler(handler)


def force_human_readable_handler_for_tty():
    if sys.stdout.isatty():
        from rich.logging import RichHandler

        rich_handler = RichHandler(rich_tracebacks=True)

        root_logger = logging.getLogger()
        _override_logger(logger=root_logger, handler=rich_handler)

        for logger_name in logging.root.manager.loggerDict:
            _override_logger(
                logger=logging.getLogger(logger_name),
                handler=rich_handler,
            )
