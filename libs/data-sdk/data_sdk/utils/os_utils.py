import os


def create_nested_directories(path):
    # Split the path into directories
    directories = path.split("/")

    # Start from the root directory
    current_path = ""

    # Iterate over each directory in the path
    for directory in directories:
        # Add the new directory to the current path
        current_path = os.path.join(current_path, directory)

        # Check if the directory exists
        if not os.path.exists(current_path):
            # If the directory does not exist, create it
            os.mkdir(current_path)
