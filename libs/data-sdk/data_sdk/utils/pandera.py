import pandera as pa
import pandera.polars as pap
import polars as pl
from pandera import dtypes
from pandera.engines import polars_engine


@polars_engine.Engine.register_dtype
@dtypes.immutable(init=True)
class Enum(polars_engine.DataType):
    type = pl.Enum

    def __init__(self, categories: list) -> None:
        object.__setattr__(self, "categories", categories)
        object.__setattr__(self, "type", pl.Enum(categories=categories))

    @classmethod
    def from_parametrized_dtype(cls, polars_dtype: pl.Enum):
        return cls(categories=polars_dtype.categories)


def empty_polars_df_from_model(model: type[pap.DataFrameModel]) -> pl.DataFrame:
    schema_fields = model.to_schema().columns
    return pl.DataFrame(
        {
            field: pl.Series(
                name=field,
                dtype=get_polars_dtype(field_schema.dtype),
            )
            for field, field_schema in schema_fields.items()
        }
    )


def get_polars_dtype(field_dtype: type[pa.DataType]) -> type[pl.DataType] | pl.DataType:
    # Map Pandera types to Polars types
    if isinstance(field_dtype, dtypes.String):
        return pl.Utf8
    elif isinstance(field_dtype, dtypes.Int):
        return pl.Int64
    elif isinstance(field_dtype, dtypes.Float):
        return pl.Float64
    elif isinstance(field_dtype, dtypes.Bool):
        return pl.Boolean
    elif isinstance(field_dtype, dtypes.Timestamp):
        return pl.Datetime
    elif isinstance(field_dtype, dtypes.Date):
        return pl.Date
    elif isinstance(field_dtype, dtypes.Category):
        return pl.Categorical(ordering="physical")
    elif isinstance(field_dtype.type, pl.Categorical):
        return pl.Categorical(ordering="physical")
    elif isinstance(field_dtype, Enum):
        return pl.Enum
    else:
        raise ValueError(f"Unsupported Pandera dtype: {field_dtype}")
