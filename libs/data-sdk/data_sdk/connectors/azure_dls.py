import logging

from azure.storage.filedatalake import FileSystemClient

from data_sdk.connectors.azure_identity import credential

log = logging.getLogger(__name__)


def get_filesystem_client(
    *, dls_account_name: str, file_system_name: str
) -> FileSystemClient:
    dls_account_url = f"https://{dls_account_name}.dfs.core.windows.net"
    return FileSystemClient(dls_account_url, file_system_name, credential)


def get_filesystem_token():
    return credential.get_token("https://storage.azure.com/.default").token
