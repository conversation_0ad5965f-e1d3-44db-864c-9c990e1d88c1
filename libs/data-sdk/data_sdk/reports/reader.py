from __future__ import annotations

import abc
from functools import singledispatchmethod
from io import BytesIO
from pathlib import Path
from typing import Any

import polars as pl
from azure.core.exceptions import ResourceNotFoundError
from azure.storage.filedatalake import FileSystemClient

from data_sdk.config import DLSConfig, LocalConfig
from data_sdk.connectors.azure_dls import get_filesystem_client


class RawReportsReader(abc.ABC):
    @singledispatchmethod
    @staticmethod
    def get_reader(config: Any):
        raise ValueError("Invalid source type")

    @get_reader.register
    @staticmethod
    def _(config: LocalConfig):
        return LocalBytesReader(
            config,
        )

    @get_reader.register
    @staticmethod
    def _(config: DLSConfig):
        return DLSBytesReader(config)

    @abc.abstractmethod
    def read(self, file_path) -> bytes:
        pass


class LocalBytesReader(RawReportsReader):
    def __init__(self, source_cfg: LocalConfig):
        self._local_path = source_cfg.local_dir

    def read(self, file_path) -> bytes:
        exact_path = self._local_path / file_path
        with open(exact_path, "rb") as f:
            raw_file_bytes = f.read()
            return raw_file_bytes


class DLSBytesReader(RawReportsReader):
    def __init__(self, source_cfg: DLSConfig):
        self._base_dir = source_cfg.base_dir
        self._client = get_filesystem_client(
            dls_account_name=source_cfg.account_name,
            file_system_name=source_cfg.container_name,
        )

    def read(self, file_path: str) -> bytes:
        exact_path = f"{self._base_dir}/{file_path}"
        file_client = self._client.get_file_client(exact_path)
        return file_client.download_file().readall()


class ConvertedReportsReader(abc.ABC):
    @singledispatchmethod
    @staticmethod
    def get_reader(config: Any) -> "ConvertedReportsReader":
        raise ValueError("Invalid source type")

    @get_reader.register
    @staticmethod
    def _(config: LocalConfig):
        return LocalConvertedReportsReader(config)

    @get_reader.register
    @staticmethod
    def _(config: DLSConfig):
        return DLSConvertedReportsReader(config)

    @abc.abstractmethod
    def read(self, file_path: str) -> pl.DataFrame:
        pass

    @abc.abstractmethod
    def get_file_list(self, file_path: str = "") -> list[str]:
        pass


class LocalConvertedReportsReader(ConvertedReportsReader):
    def __init__(self, source_cfg: LocalConfig):
        self._local_path = source_cfg.local_dir
        self._local_path.mkdir(parents=True, exist_ok=True)

    def read(self, file_path: str) -> pl.DataFrame:
        exact_path: Path = self._local_path / file_path
        return pl.read_parquet(exact_path, hive_partitioning=False)

    def get_file_list(self, file_path: str = "") -> list[str]:
        exact_path: Path = self._local_path / file_path
        return (
            [str(item.relative_to(exact_path)) for item in exact_path.iterdir()]
            if exact_path.exists()
            else []
        )


def check_if_path_exists(filesystem_client: FileSystemClient, path: str):
    try:
        return any(filesystem_client.get_paths(path=path, recursive=True))
    except ResourceNotFoundError:
        return False


class DLSConvertedReportsReader(ConvertedReportsReader):
    def __init__(self, source_cfg: DLSConfig):
        self._base_dir: Path = source_cfg.base_dir
        self._client = get_filesystem_client(
            dls_account_name=source_cfg.account_name,
            file_system_name=source_cfg.container_name,
        )

    def read(self, file_path: str) -> pl.DataFrame:
        exact_path = f"{self._base_dir}/{file_path}"
        if not check_if_path_exists(
            self._client,
            path=exact_path,
        ):
            return pl.DataFrame()
        file_client = self._client.get_file_client(exact_path)
        file = file_client.download_file().readall()
        return pl.read_parquet(BytesIO(file))

    def get_file_list(self, file_path: str = "") -> list[str]:
        exact_path = f"{self._base_dir}/{file_path}/"
        if not check_if_path_exists(
            self._client,
            path=exact_path,
        ):
            return []
        all_paths = [
            path_property.name
            for path_property in self._client.get_paths(path=exact_path)
        ]
        return [path.replace(exact_path, "") for path in all_paths]
