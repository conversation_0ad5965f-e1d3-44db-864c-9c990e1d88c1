from __future__ import annotations

import abc
from functools import singledispatchmethod
from io import BytesIO
from pathlib import Path
from typing import Any

import polars as pl

from data_sdk.config import DLSConfig, LocalConfig
from data_sdk.connectors.azure_dls import get_filesystem_client


class ConvertedReportsWriter(abc.ABC):
    @singledispatchmethod
    @staticmethod
    def get_writer(config: Any) -> "ConvertedReportsWriter":
        raise ValueError("Invalid source type")

    @get_writer.register
    @staticmethod
    def _(config: LocalConfig):
        return LocalConvertedReportsWriter(config)

    @get_writer.register
    @staticmethod
    def _(config: DLSConfig):
        return DLSConvertedReportsWriter(config)

    @abc.abstractmethod
    def save_with_file_path(self, data: pl.DataFrame, file_name: str):
        pass


class LocalConvertedReportsWriter(ConvertedReportsWriter):
    def __init__(self, source_cfg: LocalConfig):
        self._local_path: Path = source_cfg.local_dir
        self._local_path.mkdir(parents=True, exist_ok=True)

    def save_with_file_path(self, data: pl.DataFrame, file_path: str):
        output_path = self._local_path / file_path
        output_path.parent.mkdir(parents=True, exist_ok=True)
        data.write_parquet(output_path)


class DLSConvertedReportsWriter(ConvertedReportsWriter):
    def __init__(self, source_cfg: DLSConfig):
        self._client = get_filesystem_client(
            dls_account_name=source_cfg.account_name,
            file_system_name=source_cfg.container_name,
        )
        self._base_dir: Path = source_cfg.base_dir

    def save_with_file_path(self, data: pl.DataFrame, file_path: str):
        full_path = f"{self._base_dir}/{file_path}"
        bytes = BytesIO()
        data.write_parquet(bytes)
        self._client.get_file_client(full_path).upload_data(
            bytes.getvalue(),
            overwrite=True,
        )
