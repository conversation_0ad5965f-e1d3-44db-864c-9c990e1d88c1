from enum import Enum
from pathlib import Path
from typing import Literal

from pydantic_settings import BaseSettings


class Extension(str, Enum):
    PARQUET = "parquet"
    CSV = "csv"


class ConfigType(str, Enum):
    DUMMY = "dummy"
    DLS = "dls"
    LOCAL = "local"


class DummyConfig(BaseSettings):
    type: Literal[ConfigType.DUMMY] = ConfigType.DUMMY


class LocalConfig(BaseSettings):
    type: Literal[ConfigType.LOCAL] = ConfigType.LOCAL
    local_dir: Path = Path("playground")
    file_extension: Extension = Extension.PARQUET


class DLSConfig(BaseSettings):
    type: Literal[ConfigType.DLS] = ConfigType.DLS
    account_name: str
    container_name: str
    base_dir: Path = Path()
    file_extension: Extension = Extension.PARQUET
