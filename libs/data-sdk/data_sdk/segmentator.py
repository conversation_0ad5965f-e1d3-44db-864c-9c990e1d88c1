from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime

import polars as pl

from data_sdk.domain.tables import TableDefinition
from data_sdk.utils.date_utils import datetime_to_string


@dataclass
class SegmentDefinition:
    path: str
    mask_expression: pl.Expr


class BaseSegmentator(ABC):
    @abstractmethod
    def create_segments(
        self, table: TableDefinition, creation_datetime: datetime
    ) -> list[SegmentDefinition]:
        pass


class SingleSegmentator(BaseSegmentator):
    def create_segments(
        self, table: TableDefinition, creation_datetime: datetime
    ) -> list[SegmentDefinition]:
        chunks_folder_name = datetime_to_string(creation_datetime)

        return [
            SegmentDefinition(
                path=f"{chunks_folder_name}",
                mask_expression=(True),
            )
        ]
