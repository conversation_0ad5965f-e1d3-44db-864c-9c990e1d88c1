import logging

from data_sdk.domain.regions import Region

log = logging.getLogger(__name__)

# ids (values) are immutable, keys can be changed in case of naming issues
# or new ones can be added if there is new portal / platform / region
# we have Synapse check for that so if something will show up we will change these dicts accordingly
# final portal_platform_region_id looks like 171010

# TODO verify if I can just randomly change the ID here
PORTAL_ID = {
    "Epic": 10,
    "GOG": 11,
    "Humble": 12,
    "Meta": 13,
    "Microsoft": 14,
    "Nintendo": 15,
    "PlayStation": 16,
    "Steam": 17,
    "Apple": 18,
    "Google": 19,
}
PLATFORM_ID = {
    "PC": 10,
    "Key": 11,
    "Quest": 12,
    "Rift": 13,
    "Microsoft": 14,
    "Switch": 15,
    "Wii U": 16,
    "Home": 17,
    "PS Vita": 18,
    "PS VR": 19,
    "PS2": 20,
    "PS3": 21,
    "PS4": 22,
    "PS5": 23,
    "PSP": 24,
    "PS VR2": 25,
    "iPhone": 26,
    "iPad": 27,
    "Desktop": 28,
    "iPod touch": 29,
    "Google": 30,
    "PSone": 31,
    "miniS": 32,
    "Switch 2": 33,
    "Unknown": 99,
}
REGION_ID = {
    Region.GLOBAL.value: 10,
    Region.NINTENDO_AMERICAS.value: 11,
    # Taken by legacy "Nintendo Asia": 12
    Region.NINTENDO_EUROPE_AUSTRALIA.value: 13,
    Region.NINTENDO_JAPAN.value: 14,
    Region.PLAYSTATION_AMERICA.value: 15,
    Region.PLAYSTATION_EUROPE.value: 16,
    Region.PLAYSTATION_ASIA.value: 17,
    Region.PLAYSTATION_JAPAN.value: 18,
    Region.PLAYSTATION_UNKNOWN.value: 19,
    Region.NINTENDO_CHINA.value: 20,
    Region.NINTENDO_KOREA.value: 21,
    Region.NINTENDO_TAIWAN_HONG_KONG.value: 22,
}


def get_portal_platform_region_id(portal_platform_region: str) -> int:
    invalid_id = 0

    portal, platform, region = portal_platform_region.split(":")
    portal_id, platform_id, region_id = (
        PORTAL_ID.get(portal, invalid_id),
        PLATFORM_ID.get(platform, invalid_id),
        REGION_ID.get(region, invalid_id),
    )

    if not all([portal_id, platform_id, region_id]):
        log.error("Unmapped portal, platform or region %s", portal_platform_region)

    return portal_id * 10000 + platform_id * 100 + region_id
