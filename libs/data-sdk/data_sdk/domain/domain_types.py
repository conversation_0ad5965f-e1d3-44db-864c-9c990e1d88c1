from datetime import date
from enum import Enum

import polars as pl
from pydantic import AwareDatetime, BaseModel, ConfigDict, Field, RootModel

from data_sdk.domain import BaseIntField, BaseStrField, Portal
from data_sdk.domain.source import (
    Source,
    source_to_observation_type,
    source_to_portal,
)


class StudioId(BaseIntField):  # old studio id is int but new one is str
    pass


class OrganizationId(BaseStrField):
    pass


class UserId(BaseStrField):
    pass


class ProductName(BaseStrField):
    pass


class ReportState(str, Enum):
    PENDING = "PENDING"
    FAILED = "FAILED"
    CONVERTED = "CONVERTED"
    DELETED = "DELETED"


class ReportMetadata(BaseModel):
    model_config = ConfigDict(populate_by_name=True)
    source: Source
    studio_id: StudioId
    report_id: int = Field(..., alias="id")
    upload_date: AwareDatetime
    blob_name: str = Field(..., alias="file_path_raw")
    original_name: str
    state: ReportState
    no_data: bool
    date_from: date
    date_to: date

    @property
    def converted_filename(self) -> str:
        return f"{self.report_id}_{self.date_from}_{self.date_to}.parquet"

    def mark_failed(self) -> None:
        self.state = ReportState.FAILED

    def mark_converted(self) -> None:
        self.state = ReportState.CONVERTED


class ReportsMetadata(RootModel):
    root: list[ReportMetadata]

    @property
    def with_data(self) -> "ReportsMetadata":
        return ReportsMetadata(root=[file for file in self.root if not file.no_data])

    def to_lazy_frame(self) -> pl.LazyFrame:
        return pl.LazyFrame(
            self.root
        ).with_columns(
            [
                pl.col("source").map_dict(source_to_portal).alias("portal"),
                pl.col("source")
                .map_dict(source_to_observation_type)
                .alias("observation_type"),
                pl.col("upload_date")
                .dt.replace_time_zone("UTC")
                .alias(
                    "upload_date"
                ),  # This looks like a bug that should be resolved but is not https://github.com/pola-rs/polars/issues/4174
            ]
        )


class SKUType(str, Enum):
    SALES = "SALES"
    STORE = "STORE"


# it's RP - CS contract, I can't see why should it be here.
class ExternalSKU(BaseModel):
    store_id: str
    sku_studio: str
    studio_id: StudioId
    product_name: str | None
    custom_group: str | None
    ratio: int
    base_sku_id: str
    human_name: str
    package_name: str | None
    product_type: str | None
    portal: Portal
    sku_type: SKUType
    human_name_indicator: str
    is_discountable: bool


class ReportMetadataWithRawFile(BaseModel):
    raw_file: bytes
    metadata: ReportMetadata
