from enum import Enum


class Store(str, Enum):
    EPIC = "Epic"
    GOG = "GOG"
    HUMBLE = "Humble"
    MICROSOFT = "Microsoft"
    NINTENDO_SWITCH_AMERICAS = "Nintendo Switch Americas"
    NINTENDO_SWITCH_EUROPE_AUSTRALIA = "Nintendo Switch Europe/Australia"
    NINTENDO_SWITCH_JAPAN = "Nintendo Switch Japan"
    NINTENDO_SWITCH_KOREA = "Nintendo Switch Korea"
    NINTENDO_SWITCH_TAIWAN_HONG_KONG = "Nintendo Switch Taiwan/Hong Kong"
    NINTENDO_SWITCH_CHINA = "Nintendo Switch China"
    NINTENDO_SWITCH_UNKNOWN = "Nintendo Switch Unknown"
    NINTENDO_SWITCH_2_AMERICAS = "Nintendo Switch 2 Americas"
    NINTENDO_SWITCH_2_EUROPE_AUSTRALIA = "Nintendo Switch 2 Europe/Australia"
    NINTENDO_SWITCH_2_JAPAN = "Nintendo Switch 2 Japan"
    NINTENDO_SWITCH_2_KOREA = "Nintendo Switch 2 Korea"
    NINTENDO_SWITCH_2_TAIWAN_HONG_KONG = "Nintendo Switch 2 Taiwan/Hong Kong"
    NINTENDO_SWITCH_2_CHINA = "Nintendo Switch 2 China"
    NINTENDO_SWITCH_2_UNKNOWN = "Nintendo Switch 2 Unknown"
    NINTENDO_OTHER_AMERICAS = "Nintendo Other Americas"
    NINTENDO_OTHER_EUROPE_AUSTRALIA = "Nintendo Other Europe/Australia"
    NINTENDO_OTHER_JAPAN = "Nintendo Other Japan"
    NINTENDO_OTHER_KOREA = "Nintendo Other Korea"
    NINTENDO_OTHER_TAIWAN_HONG_KONG = "Nintendo Other Taiwan/Hong Kong"
    NINTENDO_OTHER_CHINA = "Nintendo Other China"
    NINTENDO_OTHER_UNKNOWN = "Nintendo Other Unknown"
    META_RIFT = "Meta Rift"
    META_QUEST = "Meta Quest"
    PLAYSTATION_AMERICA = "PlayStation America"
    PLAYSTATION_ASIA = "PlayStation Asia"
    PLAYSTATION_EUROPE = "PlayStation Europe"
    PLAYSTATION_JAPAN = "PlayStation Japan"
    PLAYSTATION_UNKNOWN = "PlayStation Unknown"
    STEAM = "Steam"
    APPLE = "Apple"
    GOOGLE = "Google"

    def abbreviate(self):
        abbreviations_map = {
            self.EPIC: self.value,
            self.GOG: self.value,
            self.MICROSOFT: self.value,
            self.STEAM: self.value,
            self.HUMBLE: self.value,
            self.NINTENDO_SWITCH_AMERICAS: "Switch US",
            self.NINTENDO_SWITCH_EUROPE_AUSTRALIA: "Switch EU/AU",
            self.NINTENDO_SWITCH_JAPAN: "Switch JP",
            self.NINTENDO_SWITCH_KOREA: "Switch KR",
            self.NINTENDO_SWITCH_TAIWAN_HONG_KONG: "Switch TW/HK",
            self.NINTENDO_SWITCH_CHINA: "Switch CN",
            self.NINTENDO_SWITCH_UNKNOWN: "Switch Unknown",
            self.NINTENDO_SWITCH_2_AMERICAS: "Switch 2 US",
            self.NINTENDO_SWITCH_2_EUROPE_AUSTRALIA: "Switch 2 EU/AU",
            self.NINTENDO_SWITCH_2_JAPAN: "Switch 2 JP",
            self.NINTENDO_SWITCH_2_KOREA: "Switch 2 KR",
            self.NINTENDO_SWITCH_2_TAIWAN_HONG_KONG: "Switch 2 TW/HK",
            self.NINTENDO_SWITCH_2_CHINA: "Switch 2 CN",
            self.NINTENDO_SWITCH_2_UNKNOWN: "Switch 2 Unknown",
            self.NINTENDO_OTHER_AMERICAS: "Nintendo Other US",
            self.NINTENDO_OTHER_EUROPE_AUSTRALIA: "Nintendo Other EU/AU",
            self.NINTENDO_OTHER_JAPAN: "Nintendo Other JP",
            self.NINTENDO_OTHER_KOREA: "Nintendo Other KR",
            self.NINTENDO_OTHER_TAIWAN_HONG_KONG: "Nintendo Other TW/HK",
            self.NINTENDO_OTHER_CHINA: "Nintendo Other CN",
            self.NINTENDO_OTHER_UNKNOWN: "Nintendo Other Unknown",
            self.META_QUEST: "Quest",
            self.META_RIFT: "Rift",
            self.PLAYSTATION_AMERICA: "PS US",
            self.PLAYSTATION_ASIA: "PS AS",
            self.PLAYSTATION_EUROPE: "PS EU",
            self.PLAYSTATION_JAPAN: "PS JP",
            self.PLAYSTATION_UNKNOWN: "PS Unknown",
            self.APPLE: "Apple",
            self.GOOGLE: "Google",
        }
        return abbreviations_map[self]
