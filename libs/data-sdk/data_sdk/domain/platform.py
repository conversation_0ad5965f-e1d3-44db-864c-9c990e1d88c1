from enum import Enum

from data_sdk.domain.constants import DEFAULT_SORTING_ORDER, UNKNOWN


class DisplayPlatform(str, Enum):
    PC = "PC"
    KEY = "Key"
    # META
    QUEST = "Quest"
    RIFT = "Rift"
    # NINTENDO
    SWITCH = "Switch"
    WII_U = "Wii U"
    SWITCH_2 = "Switch 2"
    # PLAYSTATION
    PS_HOME = "Home"
    PS_VITA = "PS Vita"
    PS_1 = "PSone"
    PS_2 = "PS2"
    PS_3 = "PS3"
    PS_4 = "PS4"
    PS_5 = "PS5"
    PSP = "PSP"
    PS_VR = "PS VR"
    PS_VR2 = "PS VR2"
    PS_MINIS = "miniS"
    # MICROSOFT
    MICROSOFT = "Microsoft"
    # APPLE
    DESKTOP = "Desktop"
    IPAD = "iPad"
    IPHONE = "iPhone"
    IPOD_TOUCH = "iPod touch"
    # GOOGLE PLAY STORE
    GOOGLE = "Google"
    # OTHER
    UNKNOWN = UNKNOWN


class Platform(str, Enum):
    PC = "pc"
    KEY = "key"
    # META
    QUEST = "quest"
    RIFT = "rift"
    # NINTENDO
    SWITCH = "switch"
    WII_U = "wii_u"
    SWITCH_2 = "switch_2"
    # PLAYSTATION
    PS_HOME = "home"
    PS_VITA = "psvita"
    PS_1 = "ps1"
    PS_2 = "ps2"
    PS_3 = "ps3"
    PS_4 = "ps4"
    PS_5 = "ps5"
    PSP = "psp"
    PS_VR = "ps_vr"
    PS_VR2 = "ps_vr2"
    PS_MINIS = "miniS"
    # MICROSOFT
    MICROSOFT = "microsoft"
    # APPLE
    DESKTOP = "desktop"
    IPAD = "ipad"
    IPHONE = "iphone"
    IPOD_TOUCH = "ipod_touch"
    # GOOGLE PLAY STORE
    GOOGLE = "google"
    # OTHER
    UNKNOWN = UNKNOWN.lower()


_platform_sorting_order_map = {
    # global
    DisplayPlatform.PC.value: 0,
    DisplayPlatform.KEY.value: 0,
    # meta
    DisplayPlatform.RIFT.value: 1,
    DisplayPlatform.QUEST.value: 2,
    # nintendo
    DisplayPlatform.SWITCH.value: 1,
    DisplayPlatform.WII_U.value: 2,
    DisplayPlatform.SWITCH_2.value: 3,
    # playstation
    DisplayPlatform.PS_1.value: 0,
    DisplayPlatform.PS_VR.value: 1,
    DisplayPlatform.PS_5.value: 2,
    DisplayPlatform.PS_4.value: 3,
    DisplayPlatform.PS_3.value: 4,
    DisplayPlatform.PS_2.value: 5,
    DisplayPlatform.PSP.value: 6,
    DisplayPlatform.PS_VITA.value: 7,
    DisplayPlatform.PS_HOME.value: 8,
    DisplayPlatform.PS_VR2.value: 9,
    # TODO: for now it is not possible to add more sorting values and we will use default one
    # DisplayPlatform.PS_MINIS.value: 10,
    # microsoft
    DisplayPlatform.MICROSOFT.value: 0,
    # app_store
    DisplayPlatform.IPHONE.value: 1,
    DisplayPlatform.IPAD.value: 2,
    DisplayPlatform.DESKTOP.value: 3,
    DisplayPlatform.IPOD_TOUCH.value: 4,
    DisplayPlatform.UNKNOWN.value: 9,
    # google play store,
    DisplayPlatform.GOOGLE.value: 1,
}


def get_sorting_order_for_platform(display_platform) -> int:
    """Return sorting priority for a given platform (1-highest)

    Args:
        platform: name of the platform

    Returns:
        Number representing platform sorting order. Defaults to 9 if platform unrecognized.

    >>> get_sorting_order_for_platform('PC')
    0
    >>> get_sorting_order_for_platform('nonexistent')
    9
    """
    try:
        return _platform_sorting_order_map[display_platform]
    except KeyError:
        return DEFAULT_SORTING_ORDER


_platform_to_display_platform_map = {
    Platform.PC.value: DisplayPlatform.PC.value,
    Platform.KEY.value: DisplayPlatform.KEY.value,
    # META
    Platform.QUEST.value: DisplayPlatform.QUEST.value,
    Platform.RIFT.value: DisplayPlatform.RIFT.value,
    # NINTENDO
    Platform.SWITCH.value: DisplayPlatform.SWITCH.value,
    Platform.SWITCH_2.value: DisplayPlatform.SWITCH_2.value,
    Platform.WII_U.value: DisplayPlatform.WII_U.value,
    # PLAYSTATION
    Platform.PS_HOME.value: DisplayPlatform.PS_HOME.value,
    Platform.PS_VITA.value: DisplayPlatform.PS_VITA.value,
    Platform.PS_1.value: DisplayPlatform.PS_1.value,
    Platform.PS_2.value: DisplayPlatform.PS_2.value,
    Platform.PS_3.value: DisplayPlatform.PS_3.value,
    Platform.PS_4.value: DisplayPlatform.PS_4.value,
    Platform.PS_5.value: DisplayPlatform.PS_5.value,
    Platform.PSP.value: DisplayPlatform.PSP.value,
    Platform.PS_VR.value: DisplayPlatform.PS_VR.value,
    Platform.PS_VR2.value: DisplayPlatform.PS_VR2.value,
    Platform.PS_MINIS.value: DisplayPlatform.PS_MINIS.value,
    # MICROSOFT
    Platform.MICROSOFT.value: DisplayPlatform.MICROSOFT.value,
    # APPLE
    Platform.IPHONE.value: DisplayPlatform.IPHONE.value,
    Platform.IPAD.value: DisplayPlatform.IPAD.value,
    Platform.DESKTOP.value: DisplayPlatform.DESKTOP.value,
    Platform.IPOD_TOUCH.value: DisplayPlatform.IPOD_TOUCH.value,
    Platform.UNKNOWN.value: DisplayPlatform.UNKNOWN.value,
    # GOOGLE PLAY STORE
    Platform.GOOGLE.value: DisplayPlatform.GOOGLE.value,
}


def get_display_platform_from_platform(platform: str) -> str:
    """Return platform display name for aggregated tables. It is the name exclusively for use in aggregated tables
    (as these will be seen by users in Synapse)

    Args:
        platform: platform name

    Returns:
        platform: display name

    >>> get_display_platform_from_platform('rift')
    'Rift'

    >>> get_display_platform_from_platform('Ps9')
    'Ps9'

    """
    try:
        return _platform_to_display_platform_map[platform.lower()]
    except KeyError:
        return platform
