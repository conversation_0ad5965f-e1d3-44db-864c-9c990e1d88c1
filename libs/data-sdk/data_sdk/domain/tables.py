from __future__ import annotations

from enum import Enum
from typing import <PERSON><PERSON><PERSON>, ClassVar, Literal, Type

import pandera as pa
import pandera.polars as pap
import polars as pl
from pandera.engines.polars_engine import Date, DateTime

from data_sdk.domain import Portal, TableName, TableNameType, TableVersion
from data_sdk.domain.domain_types import ReportState, SKUType, StudioId
from data_sdk.domain.flags import Flag
from data_sdk.domain.models import ValidateableModel
from data_sdk.domain.observations import (
    CumulativeWishlistSalesModel,
    DailyActiveUsersModel,
    DiscountsModel,
    MonthlyActiveUsersModel,
    ObservationType,
    SalesModel,
    VisibilityModel,
    WishlistActionsModel,
    WishlistBalanceModel,
    WishlistCohortsModel,
)
from data_sdk.domain.silver import (
    AcquisitionPropertiesModel,
    PortalsModel,
    SilverSkuModel,
    SilverTrafficSourceModel,
)
from data_sdk.domain.source import Source
from data_sdk.validators.fields import (
    BasicField,
    <PERSON>geStringField,
    MediumStringField,
    NonNegativeNumberField,
    SmallStringField,
    TinyStringField,
)
from data_sdk.validators.schemas import NoCheckSchema, StrictBaseSchema

PartitionTypes = (
    type[TableNameType]
    | type[StudioId]
    | type[Portal]
    | type[ObservationType]
    | type[TableVersion]
)


class TableDefinition(ValidateableModel):
    table_name: ClassVar[Enum]
    partitions: ClassVar[list[PartitionTypes]]
    version: ClassVar[TableVersion] = TableVersion("v1")


class TablePartitionedByStudio(TableDefinition):
    partitions = [TableNameType, StudioId, TableVersion]


class TablePartitionedByPortal(TableDefinition):
    partitions = [TableNameType, StudioId, Portal, TableVersion]


class TablePartitionedByObservationType(TableDefinition):
    partitions = [TableNameType, StudioId, Portal, ObservationType, TableVersion]


class TableWithGoldPartitions(TableDefinition):
    partitions = [StudioId, TableNameType]


class ExternalTable(TablePartitionedByStudio):
    pass


class ExternalSKUModel(pap.DataFrameModel):
    store_id: str = pa.Field()
    unique_sku_id: str = pa.Field()
    studio_id: int = pa.Field()
    product_name: str | None = pa.Field(nullable=True)
    custom_group: str | None = pa.Field(nullable=True)
    ratio: int = pa.Field(ge=0)
    base_sku_id: str = pa.Field()
    human_name: str = pa.Field()
    package_name: str | None = pa.Field(nullable=True)
    product_type: str | None = pa.Field(nullable=True)
    portal: str = pa.Field(isin=list(Portal))
    sku_type: str = pa.Field(isin=list(SKUType), description="Source of SKU")
    human_name_indicator: str = pa.Field()
    is_discountable: bool = pa.Field()

    class Config:
        coerce = True
        strict = True


class ExternalSKUsTable(ExternalTable):
    """
    SKUs fetched from report_service_v2 saved without any modifications.
    """

    table_name: ClassVar[Literal[TableName.EXTERNAL_SKUS]] = TableName.EXTERNAL_SKUS
    model: ClassVar[Type[pap.DataFrameModel]] = ExternalSKUModel


class ExternalStudiosModel(pap.DataFrameModel):
    studio_id: int = pa.Field(ge=0)
    organization_id: str = pa.Field()
    email: str = pa.Field()
    company_name: str = pa.Field()
    is_test_account: bool = pa.Field()
    is_verified: bool = pa.Field()
    agreement_date: Annotated[DateTime, True, "UTC", None]
    studio_parent_id: int | None = pa.Field(nullable=True)

    class Config:
        coerce = True
        strict = True


class ExternalStudiosTable(ExternalTable):
    """
    Studio data fetched from report_service_v2 saved without any modifications.
    """

    table_name: ClassVar[Literal[TableName.EXTERNAL_STUDIOS]] = (
        TableName.EXTERNAL_STUDIOS
    )
    model: ClassVar[Type[pap.DataFrameModel]] = ExternalStudiosModel


class ExternalSharedModel(pap.DataFrameModel):
    id: int = pa.Field(ge=0)
    shared_with_id: int = pa.Field(ge=0)
    shared_by_id: int = pa.Field(ge=0)
    product_name: str | None = pa.Field(nullable=True)
    date_from: DateTime = pa.Field()
    date_to: DateTime = pa.Field()
    portal_id: None = pa.Field(nullable=True)
    game_id: None = pa.Field(nullable=True)

    class Config:
        coerce = True
        strict = True


class ExternalSharedTable(ExternalTable):
    """
    Shared table generated based on role assignments fetched from report_service_v2.
    """

    table_name: ClassVar[Literal[TableName.EXTERNAL_SHARED]] = TableName.EXTERNAL_SHARED
    model: ClassVar[Type[pap.DataFrameModel]] = ExternalSharedModel


class ExternalReportsModel(pap.DataFrameModel):
    report_id: int = pa.Field(ge=0)
    studio_id: int = pa.Field(ge=0)
    source: str = pa.Field(isin=list(Source))
    portal: str = pa.Field(isin=list(Portal))
    observation_type: str = pa.Field(isin=list(ObservationType))
    date_from: Date = pa.Field()
    date_to: Date = pa.Field()
    upload_date: Annotated[DateTime, True, "UTC", None]
    blob_name: str = pa.Field()
    original_name: str = pa.Field()
    state: str = pa.Field(isin=list(ReportState))
    no_data: bool = pa.Field()

    class Config:
        coerce = True
        strict = True


class ExternalReportsTable(ExternalTable):
    """
    Reports table fetched from report_service_v2. State before running any coversions.
    Used internally to detect if any report state was changed during core silver run.
    You probably want to use [SilverReportsTable](#silver-reports-table) instead.
    """

    table_name: ClassVar[Literal[TableName.EXTERNAL_REPORTS]] = (
        TableName.EXTERNAL_REPORTS
    )
    model: ClassVar[Type[pap.DataFrameModel]] = ExternalReportsModel


class ExternalCountryCodesModel(pap.DataFrameModel):
    country: str = pa.Field()
    alpha_2_code: str | None = pa.Field(
        str_length={"min_value": 2, "max_value": 2}, nullable=True
    )
    country_code: str = pa.Field()
    numeric: int = pa.Field()
    country_currency: str = pa.Field()
    region: str = pa.Field(nullable=True)

    class Config:
        coerce = True
        strict = True


class ExternalCountryCodesTable(ExternalTable):
    """
    Static table generated using `data-jobs/jobs/core_silver/core_silver/external_sources/static/country_codes.csv` file.
    """

    table_name: ClassVar[Literal[TableName.EXTERNAL_COUNTRY_CODES]] = (
        TableName.EXTERNAL_COUNTRY_CODES
    )
    model: ClassVar[Type[pap.DataFrameModel]] = ExternalCountryCodesModel


class ExternalSteamEvents(pap.DataFrameModel):
    start_year: int = pa.Field()
    start_date: Date = pa.Field()
    end_date: Date = pa.Field()
    major: bool = pa.Field()
    name: str = pa.Field()
    start_day: int = pa.Field()
    start_month: int = pa.Field()
    end_day: int = pa.Field()
    end_month: int = pa.Field()
    end_year: int = pa.Field()

    class Config:
        coerce = True
        strict = True


class ExternalSteamEventsTable(ExternalTable):
    table_name: ClassVar[Literal[TableName.EXTERNAL_STEAM_EVENTS]] = (
        TableName.EXTERNAL_STEAM_EVENTS
    )
    model: ClassVar[Type[pap.DataFrameModel]] = ExternalSteamEvents


class ExternalCurrencyExchangeRates(pap.DataFrameModel):
    date: Date = pa.Field()
    currency_code: str = pa.Field()
    rate_to_usd: float = NonNegativeNumberField()

    class Config:
        coerce = True
        strict = True


class ExternalCurrencyExchangeRatesTable(ExternalTable):
    table_name: ClassVar[Literal[TableName.EXTERNAL_CURRENCY_EXCHANGE_RATES]] = (
        TableName.EXTERNAL_CURRENCY_EXCHANGE_RATES
    )
    model: ClassVar[Type[pap.DataFrameModel]] = ExternalCurrencyExchangeRates
    partitions = [TableNameType, TableVersion]

    def __init__(self, df: pl.DataFrame, *args, **kwargs):
        if not df.is_empty():
            df = df.sort(["date", "currency_code"])

        super().__init__(df=df, *args, **kwargs)


class ExternalFeatureFlags(pap.DataFrameModel):
    namespace: str = pa.Field()
    name: str = pa.Field()

    class Config:
        coerce = True
        strict = True


class ExternalFeatureFlagsTable(ExternalTable):
    table_name: ClassVar[Literal[TableName.EXTERNAL_FEATURE_FLAGS]] = (
        TableName.EXTERNAL_FEATURE_FLAGS
    )
    model: ClassVar[Type[pap.DataFrameModel]] = ExternalFeatureFlags
    partitions = [TableNameType, TableVersion]

    def __init__(self, df: pl.DataFrame, *args, **kwargs):
        if not df.is_empty():
            df = df.sort(["name"])

        super().__init__(df=df, *args, **kwargs)

    def has_flag(self, name: Flag) -> bool:  # TODO handle namespace
        return not self.df.filter(pl.col("name") == name.value).is_empty()


class ObservationSalesTable(TablePartitionedByPortal):
    table_name: ClassVar[Literal[TableName.OBSERVATION_SALES]] = (
        TableName.OBSERVATION_SALES
    )
    model: ClassVar[Type[pap.DataFrameModel]] = SalesModel


class ObservationDiscountsTable(TablePartitionedByPortal):
    table_name: ClassVar[Literal[TableName.OBSERVATION_DISCOUNTS]] = (
        TableName.OBSERVATION_DISCOUNTS
    )
    model: ClassVar[Type[pap.DataFrameModel]] = DiscountsModel


class ObservationVisibilityTable(TablePartitionedByPortal):
    table_name: ClassVar[Literal[TableName.OBSERVATION_VISIBILITY]] = (
        TableName.OBSERVATION_VISIBILITY
    )
    model: ClassVar[Type[pap.DataFrameModel]] = VisibilityModel


class ObservationWishlistActionsTable(TablePartitionedByPortal):
    table_name: ClassVar[Literal[TableName.OBSERVATION_WISHLIST_ACTIONS]] = (
        TableName.OBSERVATION_WISHLIST_ACTIONS
    )
    model: ClassVar[Type[pap.DataFrameModel]] = WishlistActionsModel


class ObservationWishlistCohortsTable(TablePartitionedByPortal):
    table_name: ClassVar[Literal[TableName.OBSERVATION_WISHLIST_COHORTS]] = (
        TableName.OBSERVATION_WISHLIST_COHORTS
    )
    model: ClassVar[Type[pap.DataFrameModel]] = WishlistCohortsModel


class ObservationCumulativeWishlistSalesTable(TablePartitionedByPortal):
    table_name: ClassVar[Literal[TableName.OBSERVATION_CUMULATIVE_WISHLIST_SALES]] = (
        TableName.OBSERVATION_CUMULATIVE_WISHLIST_SALES
    )
    model: ClassVar[Type[pap.DataFrameModel]] = CumulativeWishlistSalesModel


class ObservationWishlistBalanceTable(TablePartitionedByPortal):
    table_name: ClassVar[Literal[TableName.OBSERVATION_WISHLIST_BALANCE]] = (
        TableName.OBSERVATION_WISHLIST_BALANCE
    )
    model: ClassVar[Type[pap.DataFrameModel]] = WishlistBalanceModel


class ObservationDailyActiveUsersTable(TablePartitionedByPortal):
    table_name: ClassVar[Literal[TableName.OBSERVATION_DAILY_ACTIVE_USERS]] = (
        TableName.OBSERVATION_DAILY_ACTIVE_USERS
    )
    model: ClassVar[Type[pap.DataFrameModel]] = DailyActiveUsersModel


class ObservationMonthlyActiveUsersTable(TablePartitionedByPortal):
    table_name: ClassVar[Literal[TableName.OBSERVATION_MONTHLY_ACTIVE_USERS]] = (
        TableName.OBSERVATION_MONTHLY_ACTIVE_USERS
    )
    model: ClassVar[Type[pap.DataFrameModel]] = MonthlyActiveUsersModel


class SilverReportsModel(ExternalReportsModel):
    pass


class SilverReportsTable(TablePartitionedByObservationType):
    table_name: ClassVar[Literal[TableName.SILVER_REPORTS]] = TableName.SILVER_REPORTS
    model = SilverReportsModel


class SilverAcquisitionPropertiesTable(TablePartitionedByPortal):
    table_name: ClassVar[Literal[TableName.SILVER_ACQUISITION_PROPERTIES]] = (
        TableName.SILVER_ACQUISITION_PROPERTIES
    )
    model: ClassVar[Type[pap.DataFrameModel]] = AcquisitionPropertiesModel


class SilverPortalsTable(TablePartitionedByPortal):
    table_name: ClassVar[Literal[TableName.SILVER_PORTALS]] = TableName.SILVER_PORTALS
    model: ClassVar[Type[pap.DataFrameModel]] = PortalsModel


class SilverSKUsTable(TablePartitionedByPortal):
    table_name: ClassVar[Literal[TableName.SILVER_SKUS]] = TableName.SILVER_SKUS
    model: ClassVar[Type[pap.DataFrameModel]] = SilverSkuModel


class SilverTrafficSourceTable(TablePartitionedByPortal):
    table_name: ClassVar[Literal[TableName.SILVER_TRAFFIC_SOURCE]] = (
        TableName.SILVER_TRAFFIC_SOURCE
    )
    model: ClassVar[Type[pap.DataFrameModel]] = SilverTrafficSourceModel


class LegacyDimSourceFileTable(TablePartitionedByPortal):
    table_name: ClassVar[Literal[TableName.DIM_SOURCE_FILE]] = TableName.DIM_SOURCE_FILE
    model = NoCheckSchema


class LegacyDimSKUModel(StrictBaseSchema):
    base_sku_id: str = MediumStringField()
    human_name: str = MediumStringField()
    store_id: str = MediumStringField()
    studio_id: int = NonNegativeNumberField()
    portal_platform_region: str = MediumStringField()
    human_name_indicator: str = MediumStringField()
    sku_type: str = MediumStringField()
    product_name: str = MediumStringField(nullable=True)
    product_type: str = MediumStringField(nullable=True)
    sku_studio: str = MediumStringField()
    portal_platform_region_id: int = NonNegativeNumberField()
    product_id: str = MediumStringField()
    package_name: str | None = MediumStringField(nullable=True)
    custom_group: str | None = MediumStringField(nullable=True)
    ratio: float = NonNegativeNumberField()
    gso: int = BasicField()
    is_baseline_precalculated: bool = BasicField()


class LegacyDimSKUsTable(TablePartitionedByPortal):
    table_name: ClassVar[Literal[TableName.DIM_SKU]] = TableName.DIM_SKU
    model = LegacyDimSKUModel


class LegacyDimPortalsModel(StrictBaseSchema):
    portal: str = TinyStringField()
    platform: str = TinyStringField()
    region: str = TinyStringField()
    store: str = TinyStringField()
    abbreviated_name: str = TinyStringField()
    portal_platform_region: str = MediumStringField()
    portal_platform_region_id: int = NonNegativeNumberField()
    so_portal: int = NonNegativeNumberField()
    so_platform: int = NonNegativeNumberField()
    so_region: int = NonNegativeNumberField()
    pso: int = NonNegativeNumberField()


class LegacyDimPortalsTable(TablePartitionedByPortal):
    table_name: ClassVar[Literal[TableName.DIM_PORTALS]] = TableName.DIM_PORTALS
    model = LegacyDimPortalsModel


class LegacyFactSalesModel(StrictBaseSchema):
    country_code: str = TinyStringField()
    currency_code: str = TinyStringField()
    studio_id: int = NonNegativeNumberField()
    sku_studio: str = MediumStringField()
    bundle_name: str = MediumStringField()
    portal_platform_region: str = MediumStringField()
    portal_platform_region_id: int = NonNegativeNumberField()
    product_id: str = MediumStringField()
    hash_acquisition_properties: str = MediumStringField()
    date: Date = BasicField()
    date_sku_studio: str = MediumStringField()
    source_file_id: int = NonNegativeNumberField()
    retailer_tag: str = MediumStringField()
    base_price_local: float = NonNegativeNumberField(nullable=True)
    calculated_base_price_usd: float = NonNegativeNumberField(nullable=True)
    net_sales: float = pa.Field()
    gross_returned: float = pa.Field()
    gross_sales: float = pa.Field()
    units_returned: int = pa.Field()
    units_sold: int = pa.Field()
    free_units: int = BasicField()  # TODO: check if this is correct
    price_local: float = BasicField()  # TODO: check if this is correct
    price_usd: float = BasicField()  # TODO: check if this is correct
    net_sales_approx: float = pa.Field()
    category: str = MediumStringField()
    calculated_base_price_local_v2: float = BasicField(
        nullable=True
    )  # TODO: check if this is correct
    calculated_base_price_usd_v2: float = BasicField(
        nullable=True
    )  # TODO: check if this is correct


class LegacyFactSalesTable(TablePartitionedByPortal):
    table_name: ClassVar[Literal[TableName.FACT_SALES]] = TableName.FACT_SALES
    model = LegacyFactSalesModel


class LegacyDimStudioModel(StrictBaseSchema):
    studio_id: int = NonNegativeNumberField()
    organization_id: str = MediumStringField()
    email: str = MediumStringField()
    company_name: str = MediumStringField()
    is_test_account: bool = BasicField()
    is_verified: bool = BasicField()
    agreement_date: str = MediumStringField()
    studio_parent_id: int = NonNegativeNumberField(nullable=True)


class LegacyDimStudioTable(TablePartitionedByStudio):
    table_name: ClassVar[Literal[TableName.DIM_STUDIO]] = TableName.DIM_STUDIO
    model = LegacyDimStudioModel


class LegacyFactBaselineModel(StrictBaseSchema):
    country_code: str = TinyStringField()
    studio_id: int = NonNegativeNumberField()
    sku_studio: str = MediumStringField()
    unique_sku_id: str = MediumStringField()
    date: Date = BasicField()
    date_sku_studio: str = MediumStringField()
    portal_platform_region: str = MediumStringField()
    portal_platform_region_id: int = NonNegativeNumberField()
    product_id: str = MediumStringField()
    portal: str = MediumStringField()
    baseline_units_sold_directly: int = BasicField()
    baseline_units_sold_non_billable: int = BasicField()
    baseline_units_sold_retail: int = BasicField()
    baseline_units_returned: int = BasicField()
    baseline_gross_sales: float = BasicField()
    baseline_gross_returned: float = BasicField()
    baseline_free_units: int = BasicField()
    baseline_net_sales_approx: float = BasicField()
    uplift_units_sold_directly: int = BasicField()
    uplift_units_sold_non_billable: int = BasicField()
    uplift_units_sold_retail: int = BasicField()
    uplift_units_returned: int = BasicField()
    uplift_gross_sales: float = BasicField()
    uplift_gross_returned: float = BasicField()
    uplift_free_units: int = BasicField()
    uplift_net_sales_approx: float = BasicField()


class LegacyFactBaselineTable(TablePartitionedByPortal):
    table_name: ClassVar[Literal[TableName.FACT_BASELINE]] = TableName.FACT_BASELINE
    model = LegacyFactBaselineModel


class EventDayModel(StrictBaseSchema):
    discount: float = NonNegativeNumberField()
    date: Date = BasicField()
    unique_sku_id: str = MediumStringField()
    portal_platform_region_id: int = BasicField(ge=101000, le=999999)
    studio_id: int = NonNegativeNumberField()
    date_from: Date = BasicField()
    date_to: Date = BasicField()
    event_day_number: float = BasicField(ge=0, le=1)
    event_status: str = SmallStringField()
    event_id: str = MediumStringField()
    type: str = SmallStringField()
    event_name: str = MediumStringField()
    event_description: str = HugeStringField(nullable=True)
    dates_short: str = SmallStringField()
    promo_length: int = NonNegativeNumberField()
    days_since_previous_discount: int = NonNegativeNumberField()


class LegacyFactEventDayTable(TablePartitionedByPortal):
    table_name: ClassVar[Literal[TableName.FACT_EVENT_DAY]] = TableName.FACT_EVENT_DAY
    model = EventDayModel


class LegacyFactDetectedEventsModel(StrictBaseSchema):
    unique_sku_id: str = MediumStringField()
    studio_id: int = NonNegativeNumberField()
    release_date: str = TinyStringField()
    last_sales_date: str = TinyStringField()
    discount_depth: float | None = NonNegativeNumberField(nullable=True)
    discount_type: str | None = MediumStringField(nullable=True)
    date_from: str = TinyStringField()  # TODO: verify if this should be DateTime
    date_to: str = TinyStringField()  # TODO: verify if this should be DateTime
    major: int = NonNegativeNumberField()
    event_name: str = MediumStringField()
    portal_platform_region_id: int = NonNegativeNumberField()
    base_sku_id: str = MediumStringField()
    human_name: str = MediumStringField()
    product_name: str | None = MediumStringField(nullable=True)
    gso: int = BasicField()
    event_id: str = MediumStringField()
    event_type: str = MediumStringField()
    promo_length: float | None = NonNegativeNumberField(nullable=True)


class LegacyFactDetectedEventsTable(TablePartitionedByPortal):
    table_name: ClassVar[Literal[TableName.FACT_DETECTED_EVENTS]] = (
        TableName.FACT_DETECTED_EVENTS
    )
    model = LegacyFactDetectedEventsModel


class FactDicoscuntsModel(StrictBaseSchema):
    report_id: int = NonNegativeNumberField()
    create_time: str = SmallStringField()
    update_time: str = SmallStringField()
    base_sku_id: str = MediumStringField()
    source_specific_discount_sku_id: str = MediumStringField()
    unique_sku_id: str = MediumStringField()
    studio_id: int = NonNegativeNumberField()
    discount_depth: int = NonNegativeNumberField()
    discount_type: str = SmallStringField()
    datetime_from: str = SmallStringField()
    datetime_to: str = SmallStringField()
    is_event_joined: bool = BasicField()
    triggers_cooldown: bool = BasicField()
    major: bool = BasicField()
    event_name: str = MediumStringField()
    base_event_id: str = BasicField()
    unique_event_id: str = MediumStringField()
    promo_length: int = NonNegativeNumberField()
    max_discount_percentage: int = NonNegativeNumberField()
    price_increase_time: str = SmallStringField()
    portal_platform_region_id: int = BasicField(ge=101000, le=999999)


class LegacyFactDiscountsTable(TablePartitionedByPortal):
    table_name: ClassVar[Literal[TableName.FACT_DISCOUNTS]] = TableName.FACT_DISCOUNTS
    model = FactDicoscuntsModel


class LegacyFactVisibilityModel(StrictBaseSchema):
    portal_platform_region: str = MediumStringField()
    portal_platform_region_id: int = NonNegativeNumberField()
    product_id: str = MediumStringField()
    sku_studio: str = MediumStringField()
    date: Date = BasicField()
    studio_id: int = NonNegativeNumberField()
    date_sku_studio: str = MediumStringField()
    hash_traffic_source: str = MediumStringField()
    date_product_studio: str = MediumStringField()
    visits: int = NonNegativeNumberField()
    owner_visits: int = NonNegativeNumberField()
    impressions: int = NonNegativeNumberField()
    owner_impressions: int = NonNegativeNumberField()
    navigation: str = MediumStringField()


class LegacyFactVisibilityTable(TablePartitionedByPortal):
    table_name: ClassVar[Literal[TableName.FACT_VISIBILITY]] = TableName.FACT_VISIBILITY
    model = LegacyFactVisibilityModel


class LegacyFactWishlistActionsModel(StrictBaseSchema):
    portal_platform_region: str = MediumStringField()
    portal_platform_region_id: int = NonNegativeNumberField()
    product_id: str = MediumStringField()
    sku_studio: str = MediumStringField()
    date: Date = BasicField()
    studio_id: int = NonNegativeNumberField()
    date_sku_studio: str = MediumStringField()
    date_product_studio: str = MediumStringField()
    adds: int = NonNegativeNumberField()
    deletes: int = NonNegativeNumberField()
    purchases_and_activations: int = NonNegativeNumberField()
    gifts: int = NonNegativeNumberField()
    country_code: str = TinyStringField()


class LegacyFactWishlistActionsTable(TablePartitionedByPortal):
    table_name: ClassVar[Literal[TableName.FACT_WISHLIST_ACTIONS]] = (
        TableName.FACT_WISHLIST_ACTIONS
    )
    model = LegacyFactWishlistActionsModel


class LegacyFactWishlistCohortsModel(StrictBaseSchema):
    portal_platform_region: str = MediumStringField()
    portal_platform_region_id: int = NonNegativeNumberField()
    product_id: str = MediumStringField()
    sku_studio: str = MediumStringField()
    date: Date = BasicField()
    studio_id: int = NonNegativeNumberField()
    date_sku_studio: str = MediumStringField()
    date_product_studio: str = MediumStringField()
    month_cohort: str = MediumStringField()
    total_conversions: int = NonNegativeNumberField()
    purchases_and_activations: int = NonNegativeNumberField()
    gifts: int = NonNegativeNumberField()


class LegacyFactWishlistCohortsTable(TablePartitionedByPortal):
    table_name: ClassVar[Literal[TableName.FACT_WISHLIST_COHORTS]] = (
        TableName.FACT_WISHLIST_COHORTS
    )
    model = LegacyFactWishlistCohortsModel
