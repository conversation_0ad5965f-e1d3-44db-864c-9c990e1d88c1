import re
from typing import Literal

import numpy as np

# https://en.wikipedia.org/wiki/ISO_3166-1_alpha-3

CountryCodeAlpha3 = Literal[
    # Officially assigned code elements
    "ABW",  # Aruba
    "AFG",  # Afghanistan
    "AGO",  # Angola
    "AIA",  # Anguilla
    "ALA",  # Åland Islands
    "ALB",  # Albania
    "AND",  # Andorra
    "ARE",  # United Arab Emirates
    "ARG",  # Argentina
    "ARM",  # Armenia
    "ASM",  # American Samoa
    "ATA",  # Antarctica
    "ATF",  # French Southern Territories
    "ATG",  # Antigua and Barbuda
    "AUS",  # Australia
    "AUT",  # Austria
    "AZE",  # Azerbaijan
    "BDI",  # Burundi
    "BEL",  # Belgium
    "BEN",  # Benin
    "BES",  # Bonaire, Sint Eustatius and Saba
    "BFA",  # Burkina Faso
    "BGD",  # Bangladesh
    "BGR",  # Bulgaria
    "BHR",  # Bahrain
    "BHS",  # Bahamas
    "BIH",  # Bosnia and Herzegovina
    "BLM",  # Saint Barthélemy
    "BLR",  # Belarus
    "BLZ",  # Belize
    "BMU",  # Bermuda
    "BOL",  # Bolivia (Plurinational State of)
    "BRA",  # Brazil
    "BRB",  # Barbados
    "BRN",  # Brunei Darussalam
    "BTN",  # Bhutan
    "BVT",  # Bouvet Island
    "BWA",  # Botswana
    "CAF",  # Central African Republic
    "CAN",  # Canada
    "CCK",  # Cocos (Keeling) Islands
    "CHE",  # Switzerland
    "CHL",  # Chile
    "CHN",  # China
    "CIV",  # Côte d'Ivoire
    "CMR",  # Cameroon
    "COD",  # Congo, Democratic Republic of the
    "COG",  # Congo
    "COK",  # Cook Islands
    "COL",  # Colombia
    "COM",  # Comoros
    "CPV",  # Cabo Verde
    "CRI",  # Costa Rica
    "CUB",  # Cuba
    "CUW",  # Curaçao
    "CXR",  # Christmas Island
    "CYM",  # Cayman Islands
    "CYP",  # Cyprus
    "CZE",  # Czechia
    "DEU",  # Germany
    "DJI",  # Djibouti
    "DMA",  # Dominica
    "DNK",  # Denmark
    "DOM",  # Dominican Republic
    "DZA",  # Algeria
    "ECU",  # Ecuador
    "EGY",  # Egypt
    "ERI",  # Eritrea
    "ESH",  # Western Sahara
    "ESP",  # Spain
    "EST",  # Estonia
    "ETH",  # Ethiopia
    "FIN",  # Finland
    "FJI",  # Fiji
    "FLK",  # Falkland Islands (Malvinas)
    "FRA",  # France
    "FRO",  # Faroe Islands
    "FSM",  # Micronesia (Federated States of)
    "GAB",  # Gabon
    "GBR",  # United Kingdom of Great Britain and Northern Ireland
    "GEO",  # Georgia
    "GGY",  # Guernsey
    "GHA",  # Ghana
    "GIB",  # Gibraltar
    "GIN",  # Guinea
    "GLP",  # Guadeloupe
    "GMB",  # Gambia
    "GNB",  # Guinea-Bissau
    "GNQ",  # Equatorial Guinea
    "GRC",  # Greece
    "GRD",  # Grenada
    "GRL",  # Greenland
    "GTM",  # Guatemala
    "GUF",  # French Guiana
    "GUM",  # Guam
    "GUY",  # Guyana
    "HKG",  # Hong Kong
    "HMD",  # Heard Island and McDonald Islands
    "HND",  # Honduras
    "HRV",  # Croatia
    "HTI",  # Haiti
    "HUN",  # Hungary
    "IDN",  # Indonesia
    "IMN",  # Isle of Man
    "IND",  # India
    "IOT",  # British Indian Ocean Territory
    "IRL",  # Ireland
    "IRN",  # Iran (Islamic Republic of)
    "IRQ",  # Iraq
    "ISL",  # Iceland
    "ISR",  # Israel
    "ITA",  # Italy
    "JAM",  # Jamaica
    "JEY",  # Jersey
    "JOR",  # Jordan
    "JPN",  # Japan
    "KAZ",  # Kazakhstan
    "KEN",  # Kenya
    "KGZ",  # Kyrgyzstan
    "KHM",  # Cambodia
    "KIR",  # Kiribati
    "KNA",  # Saint Kitts and Nevis
    "KOR",  # Korea, Republic of
    "KWT",  # Kuwait
    "LAO",  # Lao People's Democratic Republic
    "LBN",  # Lebanon
    "LBR",  # Liberia
    "LBY",  # Libya
    "LCA",  # Saint Lucia
    "LIE",  # Liechtenstein
    "LKA",  # Sri Lanka
    "LSO",  # Lesotho
    "LTU",  # Lithuania
    "LUX",  # Luxembourg
    "LVA",  # Latvia
    "MAC",  # Macao
    "MAF",  # Saint Martin (French part)
    "MAR",  # Morocco
    "MCO",  # Monaco
    "MDA",  # Moldova, Republic of
    "MDG",  # Madagascar
    "MDV",  # Maldives
    "MEX",  # Mexico
    "MHL",  # Marshall Islands
    "MKD",  # North Macedonia
    "MLI",  # Mali
    "MLT",  # Malta
    "MMR",  # Myanmar
    "MNE",  # Montenegro
    "MNG",  # Mongolia
    "MNP",  # Northern Mariana Islands
    "MOZ",  # Mozambique
    "MRT",  # Mauritania
    "MSR",  # Montserrat
    "MTQ",  # Martinique
    "MUS",  # Mauritius
    "MWI",  # Malawi
    "MYS",  # Malaysia
    "MYT",  # Mayotte
    "NAM",  # Namibia
    "NCL",  # New Caledonia
    "NER",  # Niger
    "NFK",  # Norfolk Island
    "NGA",  # Nigeria
    "NIC",  # Nicaragua
    "NIU",  # Niue
    "NLD",  # Netherlands
    "NOR",  # Norway
    "NPL",  # Nepal
    "NRU",  # Nauru
    "NZL",  # New Zealand
    "OMN",  # Oman
    "PAK",  # Pakistan
    "PAN",  # Panama
    "PCN",  # Pitcairn
    "PER",  # Peru
    "PHL",  # Philippines
    "PLW",  # Palau
    "PNG",  # Papua New Guinea
    "POL",  # Poland
    "PRI",  # Puerto Rico
    "PRK",  # Korea (Democratic People's Republic of)
    "PRT",  # Portugal
    "PRY",  # Paraguay
    "PSE",  # Palestine, State of
    "PYF",  # French Polynesia
    "QAT",  # Qatar
    "REU",  # Réunion
    "ROU",  # Romania
    "RUS",  # Russian Federation
    "RWA",  # Rwanda
    "SAU",  # Saudi Arabia
    "SDN",  # Sudan
    "SEN",  # Senegal
    "SGP",  # Singapore
    "SGS",  # South Georgia and the South Sandwich Islands
    "SHN",  # Saint Helena, Ascension and Tristan da Cunha
    "SJM",  # Svalbard and Jan Mayen
    "SLB",  # Solomon Islands
    "SLE",  # Sierra Leone
    "SLV",  # El Salvador
    "SMR",  # San Marino
    "SOM",  # Somalia
    "SPM",  # Saint Pierre and Miquelon
    "SRB",  # Serbia
    "SSD",  # South Sudan
    "STP",  # Sao Tome and Principe
    "SUR",  # Suriname
    "SVK",  # Slovakia
    "SVN",  # Slovenia
    "SWE",  # Sweden
    "SWZ",  # Eswatini
    "SXM",  # Sint Maarten (Dutch part)
    "SYC",  # Seychelles
    "SYR",  # Syrian Arab Republic
    "TCA",  # Turks and Caicos Islands
    "TCD",  # Chad
    "TGO",  # Togo
    "THA",  # Thailand
    "TJK",  # Tajikistan
    "TKL",  # Tokelau
    "TKM",  # Turkmenistan
    "TLS",  # Timor-Leste
    "TON",  # Tonga
    "TTO",  # Trinidad and Tobago
    "TUN",  # Tunisia
    "TUR",  # Turkey
    "TUV",  # Tuvalu
    "TWN",  # Taiwan, Province of China
    "TZA",  # Tanzania, United Republic of
    "UGA",  # Uganda
    "UKR",  # Ukraine
    "UMI",  # United States Minor Outlying Islands
    "UNK",  # Kosovo
    "URY",  # Uruguay
    "USA",  # United States of America
    "UZB",  # Uzbekistan
    "VAT",  # Holy See
    "VCT",  # Saint Vincent and the Grenadines
    "VEN",  # Venezuela (Bolivarian Republic of)
    "VGB",  # Virgin Islands (British)
    "VIR",  # Virgin Islands (U.S.)
    "VNM",  # Viet Nam
    "VUT",  # Vanuatu
    "WLF",  # Wallis and Futuna
    "WSM",  # Samoa
    "YEM",  # Yemen
    "ZAF",  # South Africa
    "ZMB",  # Zambia
    "ZWE",  # Zimbabwe
    # Transitional reservations
    "ANT",  # Netherlands Antilles
    "BUR",  # Burma
    "BYS",  # Byelorussian SSR
    "CSK",  # Czechoslovakia
    "NTZ",  # Neutral Zone
    "ROM",  # Romania
    "SCG",  # Serbia and Montenegro
    "TMP",  # East Timor
    "YUG",  # Yugoslavia
    "ZAR",  # Zaire
    # User-assigned code elements
    # * AAA to AAZ
    # * QMA to QZZ
    # * XAA to XZZ
    # * ZZA to ZZZ
    "ZZZ",  # Unknown
    # Exceptionally reserved code elements
    "YYY",  # International
]


# new country name? Simply add it to this dictionary
# YYY - international
# ZZZ - unknown
countries_dictionary: dict[str, CountryCodeAlpha3] = {
    # International, other, global
    "international": "YYY",
    "other": "YYY",
    "": "YYY",
    "global": "YYY",
    "a1": "YYY",
    "a2": "YYY",
    "ap": "YYY",
    "eu": "YYY",
    "yy": "YYY",
    "yyy": "YYY",
    # Undefined
    "undefined": "ZZZ",
    "unknown": "ZZZ",
    "notconfigured": "ZZZ",
    "nodata": "ZZZ",
    "unrecognized": "ZZZ",
    "zz": "ZZZ",
    "zzz": "ZZZ",
    "en": "ZZZ",
    "neutral": "ZZZ",
    # Afghanistan
    "afghanistan": "AFG",
    "af": "AFG",
    "afg": "AFG",
    # Åland Islands
    "landislands": "ALA",
    "alandislands": "ALA",
    "ax": "ALA",
    "ala": "ALA",
    # Netherlands Antilles
    "thenetherlandsantilles": "ANT",
    "netherlandsantilles": "ANT",
    "an": "ANT",
    "nederlandseantillen": "ANT",
    # Kosovo
    "kosovo": "UNK",
    "xk": "UNK",
    # Albania
    "albania": "ALB",
    "al": "ALB",
    "alb": "ALB",
    # Algeria
    "algeria": "DZA",
    "dz": "DZA",
    "dza": "DZA",
    # American Samoa
    "americansamoa": "ASM",
    "as": "ASM",
    "asm": "ASM",
    # Andorra
    "andorra": "AND",
    "ad": "AND",
    "and": "AND",
    # Angola
    "angola": "AGO",
    "ao": "AGO",
    "ago": "AGO",
    # Anguilla
    "anguilla": "AIA",
    "ai": "AIA",
    "aia": "AIA",
    # Antarctica
    "antarctica": "ATA",
    "aq": "ATA",
    "ata": "ATA",
    # Antigua and Barbuda
    "antiguaandbarbuda": "ATG",
    "ag": "ATG",
    "atg": "ATG",
    # Argentina
    "argentina": "ARG",
    "ar": "ARG",
    "arg": "ARG",
    # Armenia
    "armenia": "ARM",
    "am": "ARM",
    "arm": "ARM",
    # Aruba
    "aruba": "ABW",
    "aw": "ABW",
    "abw": "ABW",
    # Australia
    "australia": "AUS",
    "au": "AUS",
    "aus": "AUS",
    # Austria
    "austria": "AUT",
    "at": "AUT",
    "aut": "AUT",
    # Azerbaijan
    "azerbaijan": "AZE",
    "az": "AZE",
    "aze": "AZE",
    # Bahamas
    "bahamas": "BHS",
    "bs": "BHS",
    "bhs": "BHS",
    "commonwealthofbahamas": "BHS",
    # Bahrain
    "bahrain": "BHR",
    "bh": "BHR",
    "bhr": "BHR",
    # Bangladesh
    "bangladesh": "BGD",
    "bd": "BGD",
    "bgd": "BGD",
    # Barbados
    "barbados": "BRB",
    "bb": "BRB",
    "brb": "BRB",
    # Belarus
    "belarus": "BLR",
    "by": "BLR",
    "blr": "BLR",
    # Belgium
    "belgium": "BEL",
    "kingdomofbelgium": "BEL",
    "be": "BEL",
    "bel": "BEL",
    # Belize
    "belize": "BLZ",
    "bz": "BLZ",
    "blz": "BLZ",
    # Benin
    "benin": "BEN",
    "bj": "BEN",
    "ben": "BEN",
    # Bermuda
    "bermuda": "BMU",
    "bm": "BMU",
    "bmu": "BMU",
    # Bhutan
    "bhutan": "BTN",
    "bt": "BTN",
    "btn": "BTN",
    # Bolivia (Plurinational State of)
    "boliviaplurinationalstateof": "BOL",
    "bo": "BOL",
    "bol": "BOL",
    "bolivia": "BOL",
    "plurinationalstateofbolivia": "BOL",
    "republicofbolivia": "BOL",
    # Bonaire, Sint Eustatius and Saba
    "bonairesinteustatiusandsaba": "BES",
    "bq": "BES",
    "bes": "BES",
    # Bosnia and Herzegovina
    "bosniaandherzegovina": "BIH",
    "ba": "BIH",
    "bih": "BIH",
    "bosniaandherzegowina": "BIH",
    # Botswana
    "botswana": "BWA",
    "bw": "BWA",
    "bwa": "BWA",
    # Bouvet Island
    "bouvetisland": "BVT",
    "bv": "BVT",
    "bvt": "BVT",
    # Brazil
    "brazil": "BRA",
    "br": "BRA",
    "bra": "BRA",
    # British Indian Ocean Territory
    "britishindianoceanterritory": "IOT",
    "io": "IOT",
    "iot": "IOT",
    # Brunei Darussalam
    "bruneidarussalam": "BRN",
    "bn": "BRN",
    "brn": "BRN",
    "brunei": "BRN",
    "nationofbruneitheabodeofpeace": "BRN",
    # Bulgaria
    "bulgaria": "BGR",
    "bg": "BGR",
    "bgr": "BGR",
    "republicofbulgaria": "BGR",
    # Burkina Faso
    "burkinafaso": "BFA",
    "bf": "BFA",
    "bfa": "BFA",
    # Burundi
    "burundi": "BDI",
    "bi": "BDI",
    "bdi": "BDI",
    # Cabo Verde
    "caboverde": "CPV",
    "cv": "CPV",
    "cpv": "CPV",
    "capeverde": "CPV",
    # Cambodia
    "cambodia": "KHM",
    "kh": "KHM",
    "khm": "KHM",
    # Cameroon
    "cameroon": "CMR",
    "cm": "CMR",
    "cmr": "CMR",
    # Canada
    "canada": "CAN",
    "ca": "CAN",
    "can": "CAN",
    # Cayman Islands
    "caymanislands": "CYM",
    "ky": "CYM",
    "cym": "CYM",
    # Central African Republic
    "centralafricanrepublic": "CAF",
    "cf": "CAF",
    "caf": "CAF",
    # Chad
    "chad": "TCD",
    "td": "TCD",
    "tcd": "TCD",
    # Chile
    "chile": "CHL",
    "cl": "CHL",
    "chl": "CHL",
    # China
    "china": "CHN",
    "steamchina": "CHN",
    "cn": "CHN",
    "chn": "CHN",
    "peoplesrepublicofchina": "CHN",
    "prc": "CHN",
    # Christmas Island
    "christmasisland": "CXR",
    "cx": "CXR",
    "cxr": "CXR",
    # Cocos (Keeling) Islands
    "cocoskeelingislands": "CCK",
    "cc": "CCK",
    "cck": "CCK",
    "cocosislands": "CCK",
    # Colombia
    "colombia": "COL",
    "co": "COL",
    "col": "COL",
    # Comoros
    "comoros": "COM",
    "km": "COM",
    "com": "COM",
    # Congo
    "congo": "COG",
    "cg": "COG",
    "cog": "COG",
    # Congo, Democratic Republic of the
    "congodemocraticrepublicofthe": "COD",
    "cd": "COD",
    "cod": "COD",
    "congothedrc": "COD",
    # Cook Islands
    "cookislands": "COK",
    "ck": "COK",
    "cok": "COK",
    # Costa Rica
    "costarica": "CRI",
    "cr": "CRI",
    "cri": "CRI",
    # Côte d'Ivoire
    "ctedivoire": "CIV",
    "ci": "CIV",
    "civ": "CIV",
    "cotedivoire": "CIV",
    # Croatia
    "croatia": "HRV",
    "hr": "HRV",
    "hrv": "HRV",
    "republicofcroatia": "HRV",
    # Cuba
    "cuba": "CUB",
    "cu": "CUB",
    "cub": "CUB",
    # Curaçao
    "curaao": "CUW",
    "cw": "CUW",
    "cuw": "CUW",
    "curacao": "CUW",
    # Cyprus
    "cyprus": "CYP",
    "cy": "CYP",
    "cyp": "CYP",
    "republicofcyprus": "CYP",
    # Czechia
    "czechia": "CZE",
    "cz": "CZE",
    "cze": "CZE",
    "czechrepublic": "CZE",
    # Denmark
    "denmark": "DNK",
    "dk": "DNK",
    "dnk": "DNK",
    # Djibouti
    "djibouti": "DJI",
    "dj": "DJI",
    "dji": "DJI",
    # Dominica
    "dominica": "DMA",
    "dm": "DMA",
    "dma": "DMA",
    "commonwealthofdominica": "DMA",
    # Dominican Republic
    "dominicanrepublic": "DOM",
    "do": "DOM",
    "dom": "DOM",
    # Ecuador
    "ecuador": "ECU",
    "ec": "ECU",
    "ecu": "ECU",
    # Egypt
    "egypt": "EGY",
    "eg": "EGY",
    "egy": "EGY",
    # El Salvador
    "elsalvador": "SLV",
    "sv": "SLV",
    "slv": "SLV",
    "republicofelsalvador": "SLV",
    # Equatorial Guinea
    "equatorialguinea": "GNQ",
    "gq": "GNQ",
    "gnq": "GNQ",
    # Eritrea
    "eritrea": "ERI",
    "er": "ERI",
    "eri": "ERI",
    # Estonia
    "estonia": "EST",
    "ee": "EST",
    "est": "EST",
    "republicofestonia": "EST",
    # Ethiopia
    "ethiopia": "ETH",
    "et": "ETH",
    "eth": "ETH",
    # Falkland Islands (Malvinas)
    "falklandislandsmalvinas": "FLK",
    "fk": "FLK",
    "flk": "FLK",
    # Faroe Islands
    "faroeislands": "FRO",
    "fo": "FRO",
    "fro": "FRO",
    # Fiji
    "fiji": "FJI",
    "fj": "FJI",
    "fji": "FJI",
    # Finland
    "finland": "FIN",
    "fi": "FIN",
    "fin": "FIN",
    # France
    "france": "FRA",
    "fr": "FRA",
    "fra": "FRA",
    # French Guiana
    "frenchguiana": "GUF",
    "frenchguinea": "GUF",
    "gf": "GUF",
    "guf": "GUF",
    "guiana": "GUF",
    # French Polynesia
    "frenchpolynesia": "PYF",
    "pf": "PYF",
    "pyf": "PYF",
    # French Southern Territories
    "frenchsouthernterritories": "ATF",
    "tf": "ATF",
    "atf": "ATF",
    # Gabon
    "gabon": "GAB",
    "ga": "GAB",
    "gab": "GAB",
    # Gambia
    "gambia": "GMB",
    "gm": "GMB",
    "gmb": "GMB",
    # Georgia
    "georgia": "GEO",
    "ge": "GEO",
    "geo": "GEO",
    # Germany
    "germany": "DEU",
    "de": "DEU",
    "deu": "DEU",
    # Ghana
    "ghana": "GHA",
    "gh": "GHA",
    "gha": "GHA",
    # Gibraltar
    "gibraltar": "GIB",
    "gi": "GIB",
    "gib": "GIB",
    # Greece
    "greece": "GRC",
    "gr": "GRC",
    "grc": "GRC",
    # Greenland
    "greenland": "GRL",
    "gl": "GRL",
    "grl": "GRL",
    # Grenada
    "grenada": "GRD",
    "gd": "GRD",
    "grd": "GRD",
    # Guadeloupe
    "guadeloupe": "GLP",
    "gp": "GLP",
    "glp": "GLP",
    # Guam
    "guam": "GUM",
    "gu": "GUM",
    "gum": "GUM",
    # Guatemala
    "guatemala": "GTM",
    "gt": "GTM",
    "gtm": "GTM",
    # Guernsey
    "guernsey": "GGY",
    "gg": "GGY",
    "ggy": "GGY",
    # Guinea
    "guinea": "GIN",
    "gn": "GIN",
    "gin": "GIN",
    # Guinea-Bissau
    "guineabissau": "GNB",
    "gw": "GNB",
    "gnb": "GNB",
    # Guyana
    "guyana": "GUY",
    "gy": "GUY",
    "guy": "GUY",
    "cooperativerepublicofguyana": "GUY",
    # Haiti
    "haiti": "HTI",
    "ht": "HTI",
    "hti": "HTI",
    "republicofhaiti": "HTI",
    # Heard Island and McDonald Islands
    "heardislandandmcdonaldislands": "HMD",
    "heardandmcdonaldislands": "HMD",
    "hm": "HMD",
    "hmd": "HMD",
    # Holy See
    "holysee": "VAT",
    "va": "VAT",
    "vat": "VAT",
    "holyseevaticancitystate": "VAT",
    # Honduras
    "honduras": "HND",
    "hn": "HND",
    "hnd": "HND",
    # Hong Kong
    "hongkong": "HKG",
    "hk": "HKG",
    "hkg": "HKG",
    # Hungary
    "republicofhungary": "HUN",
    "hungary": "HUN",
    "hu": "HUN",
    "hun": "HUN",
    # Iceland
    "iceland": "ISL",
    "is": "ISL",
    "isl": "ISL",
    # India
    "india": "IND",
    "in": "IND",
    "ind": "IND",
    # Indonesia
    "indonesia": "IDN",
    "id": "IDN",
    "idn": "IDN",
    # Iran (Islamic Republic of)
    "iran": "IRN",
    "iranislamicrepublicof": "IRN",
    "ir": "IRN",
    "irn": "IRN",
    # Iraq
    "iraq": "IRQ",
    "iq": "IRQ",
    "irq": "IRQ",
    # Ireland
    "ireland": "IRL",
    "ie": "IRL",
    "irl": "IRL",
    # Isle of Man
    "isleofman": "IMN",
    "im": "IMN",
    "imn": "IMN",
    # Israel
    "israel": "ISR",
    "il": "ISR",
    "isr": "ISR",
    "stateofisrael": "ISR",
    # Italy
    "italy": "ITA",
    "it": "ITA",
    "ita": "ITA",
    # Jamaica
    "jamaica": "JAM",
    "jm": "JAM",
    "jam": "JAM",
    # Japan
    "japan": "JPN",
    "jp": "JPN",
    "jpn": "JPN",
    # Jersey
    "jersey": "JEY",
    "je": "JEY",
    "jey": "JEY",
    # Jordan
    "jordan": "JOR",
    "jo": "JOR",
    "jor": "JOR",
    # Kazakhstan
    "kazakhstan": "KAZ",
    "kz": "KAZ",
    "kaz": "KAZ",
    # Kenya
    "kenya": "KEN",
    "ke": "KEN",
    "ken": "KEN",
    # Kiribati
    "kiribati": "KIR",
    "ki": "KIR",
    "kir": "KIR",
    # Korea (Democratic People's Republic of)
    "koreademocraticpeoplesrepublicof": "PRK",
    "kp": "PRK",
    "prk": "PRK",
    "koreadpro": "PRK",
    "koreanorth": "PRK",
    # Korea, Republic of
    "korea": "KOR",
    "kr": "KOR",
    "kor": "KOR",
    "republicofkorea": "KOR",
    "republicofsouthkorea": "KOR",
    "southkorea": "KOR",
    "koreasouthkorea": "KOR",
    "korearepublicof": "KOR",
    "koreasouth": "KOR",
    # Kuwait
    "kuwait": "KWT",
    "kw": "KWT",
    "kwt": "KWT",
    # Kyrgyzstan
    "kyrgyzstan": "KGZ",
    "kg": "KGZ",
    "kgz": "KGZ",
    # Lao People's Democratic Republic
    "laopeoplesdemocraticrepublic": "LAO",
    "la": "LAO",
    "lao": "LAO",
    "laos": "LAO",
    # Latvia
    "republicoflatvia": "LVA",
    "latvia": "LVA",
    "lv": "LVA",
    "lva": "LVA",
    # Lebanon
    "lebanon": "LBN",
    "lb": "LBN",
    "lbn": "LBN",
    # Lesotho
    "lesotho": "LSO",
    "ls": "LSO",
    "lso": "LSO",
    # Liberia
    "liberia": "LBR",
    "lr": "LBR",
    "lbr": "LBR",
    # Libya
    "libya": "LBY",
    "ly": "LBY",
    "lby": "LBY",
    # Liechtenstein
    "liechtenstein": "LIE",
    "li": "LIE",
    "lie": "LIE",
    # Lithuania
    "lithuania": "LTU",
    "republicoflithuania": "LTU",
    "lt": "LTU",
    "ltu": "LTU",
    # Luxembourg
    "luxembourg": "LUX",
    "lu": "LUX",
    "lux": "LUX",
    # Macao
    "macao": "MAC",
    "macau": "MAC",
    "mo": "MAC",
    "mac": "MAC",
    # North Macedonia
    "macedoniatheformeryugoslavrepublicof": "MKD",
    "mk": "MKD",
    "mkd": "MKD",
    "macedonia": "MKD",
    "northmacedonia": "MKD",
    # Madagascar
    "madagascar": "MDG",
    "mg": "MDG",
    "mdg": "MDG",
    # Malawi
    "malawi": "MWI",
    "mw": "MWI",
    "mwi": "MWI",
    # Malaysia
    "malaysia": "MYS",
    "my": "MYS",
    "mys": "MYS",
    "malysia": "MYS",
    # Maldives
    "maldives": "MDV",
    "mv": "MDV",
    "mdv": "MDV",
    # Mali
    "mali": "MLI",
    "ml": "MLI",
    "mli": "MLI",
    # Malta
    "republicofmalta": "MLT",
    "malta": "MLT",
    "mt": "MLT",
    "mlt": "MLT",
    # Marshall Islands
    "marshallislands": "MHL",
    "mh": "MHL",
    "mhl": "MHL",
    # Martinique
    "martinique": "MTQ",
    "mq": "MTQ",
    "mtq": "MTQ",
    # Mauritania
    "mauritania": "MRT",
    "mr": "MRT",
    "mrt": "MRT",
    # Mauritius
    "mauritius": "MUS",
    "mu": "MUS",
    "mus": "MUS",
    # Mayotte
    "mayotte": "MYT",
    "yt": "MYT",
    "myt": "MYT",
    # Mexico
    "mexico": "MEX",
    "mx": "MEX",
    "mex": "MEX",
    # Micronesia (Federated States of)
    "micronesia": "FSM",
    "micronesiafederatedstatesof": "FSM",
    "fm": "FSM",
    "fsm": "FSM",
    # Moldova, Republic of
    "moldovarepublicof": "MDA",
    "md": "MDA",
    "mda": "MDA",
    # Monaco
    "monaco": "MCO",
    "mc": "MCO",
    "mco": "MCO",
    # Mongolia
    "mongolia": "MNG",
    "mn": "MNG",
    "mng": "MNG",
    # Montenegro
    "montenegro": "MNE",
    "me": "MNE",
    "mne": "MNE",
    # Montserrat
    "montserrat": "MSR",
    "ms": "MSR",
    "msr": "MSR",
    # Morocco
    "morocco": "MAR",
    "ma": "MAR",
    "mar": "MAR",
    # Mozambique
    "mozambique": "MOZ",
    "mz": "MOZ",
    "moz": "MOZ",
    # Myanmar
    "myanmar": "MMR",
    "mm": "MMR",
    "mmr": "MMR",
    "myanmarburma": "MMR",
    "burma": "MMR",
    "republicoftheunionofmyanmar": "MMR",
    # Namibia
    "namibia": "NAM",
    "na": "NAM",
    "nam": "NAM",
    # Nauru
    "nauru": "NRU",
    "nr": "NRU",
    "nru": "NRU",
    # Nepal
    "nepal": "NPL",
    "np": "NPL",
    "npl": "NPL",
    # Netherlands
    "netherlands": "NLD",
    "nl": "NLD",
    "nld": "NLD",
    # New Caledonia
    "newcaledonia": "NCL",
    "nc": "NCL",
    "ncl": "NCL",
    # New Zealand
    "newzealand": "NZL",
    "nz": "NZL",
    "nzl": "NZL",
    # Nicaragua
    "nicaragua": "NIC",
    "ni": "NIC",
    "nic": "NIC",
    # Niger
    "niger": "NER",
    "ne": "NER",
    "ner": "NER",
    # Nigeria
    "nigeria": "NGA",
    "ng": "NGA",
    "nga": "NGA",
    # Niue
    "niue": "NIU",
    "nu": "NIU",
    "niu": "NIU",
    # Norfolk Island
    "norfolkisland": "NFK",
    "nf": "NFK",
    "nfk": "NFK",
    # Northern Mariana Islands
    "northernmarianaislands": "MNP",
    "mp": "MNP",
    "mnp": "MNP",
    # Norway
    "norway": "NOR",
    "no": "NOR",
    "nor": "NOR",
    # Oman
    "oman": "OMN",
    "om": "OMN",
    "omn": "OMN",
    # Pakistan
    "pakistan": "PAK",
    "pk": "PAK",
    "pak": "PAK",
    # Palau
    "palau": "PLW",
    "pw": "PLW",
    "plw": "PLW",
    # Palestine, State of
    "palestine": "PSE",
    "palestinestateof": "PSE",
    "ps": "PSE",
    "pse": "PSE",
    "palestinianterritory": "PSE",
    # Panama
    "panama": "PAN",
    "pa": "PAN",
    "pan": "PAN",
    # Papua New Guinea
    "papuanewguinea": "PNG",
    "pg": "PNG",
    "png": "PNG",
    # Paraguay
    "paraguay": "PRY",
    "py": "PRY",
    "pry": "PRY",
    # Peru
    "peru": "PER",
    "pe": "PER",
    "per": "PER",
    # Philippines
    "philippines": "PHL",
    "ph": "PHL",
    "phl": "PHL",
    # Pitcairn
    "pitcairn": "PCN",
    "pn": "PCN",
    "pcn": "PCN",
    # Poland
    "poland": "POL",
    "pl": "POL",
    "pol": "POL",
    # Portugal
    "portugal": "PRT",
    "pt": "PRT",
    "prt": "PRT",
    # Puerto Rico
    "puertorico": "PRI",
    "pr": "PRI",
    "pri": "PRI",
    # Qatar
    "qatar": "QAT",
    "qa": "QAT",
    "qat": "QAT",
    # Réunion
    "runion": "REU",
    "re": "REU",
    "reu": "REU",
    "reunion": "REU",
    # Romania
    "romania": "ROU",
    "ro": "ROU",
    "rou": "ROU",
    # Russian Federation
    "russianfederation": "RUS",
    "ru": "RUS",
    "rus": "RUS",
    "russia": "RUS",
    # Rwanda
    "rwanda": "RWA",
    "rw": "RWA",
    "rwa": "RWA",
    # Saint Barthélemy
    "saintbarthlemy": "BLM",
    "bl": "BLM",
    "blm": "BLM",
    "saintbarthelemy": "BLM",
    # Saint Helena, Ascension and Tristan da Cunha
    "sainthelenaascensionandtristandacunha": "SHN",
    "sthelena": "SHN",
    "sh": "SHN",
    "shn": "SHN",
    # Saint Kitts and Nevis
    "saintkittsandnevis": "KNA",
    "kn": "KNA",
    "kna": "KNA",
    "saintchristopherandnevis": "KNA",
    # Saint Lucia
    "saintlucia": "LCA",
    "lc": "LCA",
    "lca": "LCA",
    # Moldova, Republic of
    "moldova": "MDA",
    # Saint Martin (French part)
    "saintmartinfrenchpart": "MAF",
    "mf": "MAF",
    "maf": "MAF",
    # Saint Pierre and Miquelon
    "saintpierreandmiquelon": "SPM",
    "pm": "SPM",
    "spm": "SPM",
    "stpierreandmiquelon": "SPM",
    # Saint Vincent and the Grenadines
    "saintvincentandthegrenadines": "VCT",
    "vc": "VCT",
    "vct": "VCT",
    # Samoa
    "samoa": "WSM",
    "ws": "WSM",
    "wsm": "WSM",
    # San Marino
    "sanmarino": "SMR",
    "sm": "SMR",
    "smr": "SMR",
    # Sao Tome and Principe
    "saotomeandprincipe": "STP",
    "st": "STP",
    "stp": "STP",
    # Saudi Arabia
    "saudiarabia": "SAU",
    "sa": "SAU",
    "sau": "SAU",
    "kingdomofsaudiarabia": "SAU",
    # Serbia and Montenegro
    "cs": "SCG",
    # Senegal
    "senegal": "SEN",
    "sn": "SEN",
    "sen": "SEN",
    # Serbia
    "serbia": "SRB",
    "rs": "SRB",
    "srb": "SRB",
    # Seychelles
    "seychelles": "SYC",
    "sc": "SYC",
    "syc": "SYC",
    # Sierra Leone
    "sierraleone": "SLE",
    "sl": "SLE",
    "sle": "SLE",
    # Singapore
    "singapore": "SGP",
    "sg": "SGP",
    "sgp": "SGP",
    "republicofsingapore": "SGP",
    # Sint Maarten (Dutch part)
    "sintmaartendutchpart": "SXM",
    "sx": "SXM",
    "sxm": "SXM",
    # Slovakia
    "slovakia": "SVK",
    "sk": "SVK",
    "svk": "SVK",
    "slovakiaslovakrepublic": "SVK",
    "slovakrepublic": "SVK",
    # Slovenia
    "slovenia": "SVN",
    "si": "SVN",
    "svn": "SVN",
    "republicofslovenia": "SVN",
    # Solomon Islands
    "solomonislands": "SLB",
    "sb": "SLB",
    "slb": "SLB",
    # Somalia
    "somalia": "SOM",
    "so": "SOM",
    "som": "SOM",
    # South Africa
    "southafrica": "ZAF",
    "za": "ZAF",
    "zaf": "ZAF",
    # South Georgia and the South Sandwich Islands
    "southgeorgiaandthesouthsandwichislands": "SGS",
    "southgeorgiaandsouthss": "SGS",
    "gs": "SGS",
    "sgs": "SGS",
    # South Sudan
    "southsudan": "SSD",
    "ss": "SSD",
    "ssd": "SSD",
    # Spain
    "spain": "ESP",
    "es": "ESP",
    "esp": "ESP",
    # Sri Lanka
    "srilanka": "LKA",
    "lk": "LKA",
    "lka": "LKA",
    # Sudan
    "sudan": "SDN",
    "sd": "SDN",
    "sdn": "SDN",
    # Suriname
    "suriname": "SUR",
    "sr": "SUR",
    "sur": "SUR",
    "republicofsuriname": "SUR",
    # Svalbard and Jan Mayen
    "svalbardandjanmayen": "SJM",
    "svalbardandjanmayenislands": "SJM",
    "sj": "SJM",
    "sjm": "SJM",
    # Eswatini
    "swaziland": "SWZ",
    "sz": "SWZ",
    "swz": "SWZ",
    "eswatini": "SWZ",
    # Sweden
    "sweden": "SWE",
    "se": "SWE",
    "swe": "SWE",
    # Switzerland
    "switzerland": "CHE",
    "ch": "CHE",
    "che": "CHE",
    # Syrian Arab Republic
    "syrianarabrepublic": "SYR",
    "sy": "SYR",
    "syr": "SYR",
    "syria": "SYR",
    # Taiwan, Province of China
    "taiwanprovinceofchina": "TWN",
    "tw": "TWN",
    "twn": "TWN",
    "taiwan": "TWN",
    # Tajikistan
    "tajikistan": "TJK",
    "tj": "TJK",
    "tjk": "TJK",
    # Tanzania, United Republic of
    "tanzaniaunitedrepublicof": "TZA",
    "tz": "TZA",
    "tza": "TZA",
    "tanzania": "TZA",
    # Timor-Leste
    "tls": "TLS",
    "easttimor": "TLS",
    "timorleste": "TLS",
    "tl": "TLS",
    # Togo
    "togo": "TGO",
    "tg": "TGO",
    "tgo": "TGO",
    # Tokelau
    "tokelau": "TKL",
    "tk": "TKL",
    "tkl": "TKL",
    # Tonga
    "tonga": "TON",
    "to": "TON",
    "ton": "TON",
    # Trinidad and Tobago
    "trinidadandtobago": "TTO",
    "tt": "TTO",
    "tto": "TTO",
    "republicoftrinidadandtobago": "TTO",
    # Tunisia
    "tunisia": "TUN",
    "tn": "TUN",
    "tun": "TUN",
    # Turkey
    "turkey": "TUR",
    "tr": "TUR",
    "tur": "TUR",
    # Turkmenistan
    "turkmenistan": "TKM",
    "tm": "TKM",
    "tkm": "TKM",
    # Thailand
    "thailand": "THA",
    "th": "THA",
    "tha": "THA",
    # Turks and Caicos Islands
    "turksandcaicosislands": "TCA",
    "tc": "TCA",
    "tca": "TCA",
    # Tuvalu
    "tuvalu": "TUV",
    "tv": "TUV",
    "tuv": "TUV",
    # Uganda
    "uganda": "UGA",
    "ug": "UGA",
    "uga": "UGA",
    # Ukraine
    "ukraine": "UKR",
    "ua": "UKR",
    "ukr": "UKR",
    # United Arab Emirates
    "unitedarabemirates": "ARE",
    "ae": "ARE",
    "are": "ARE",
    # United Kingdom of Great Britain and Northern Ireland
    "unitedkingdomofgreatbritainandnorthernireland": "GBR",
    "gb": "GBR",
    "gbr": "GBR",
    "unitedkingdom": "GBR",
    "greatbritain": "GBR",
    # United States of America
    "unitedstatesofamerica": "USA",
    "us": "USA",
    "usa": "USA",
    "unitedstates": "USA",
    # United States Minor Outlying Islands
    "unitedstatesminoroutlyingislands": "UMI",
    "um": "UMI",
    "umi": "UMI",
    "usminorislands": "UMI",
    # Uruguay
    "uruguay": "URY",
    "uy": "URY",
    "ury": "URY",
    # Uzbekistan
    "uzbekistan": "UZB",
    "uz": "UZB",
    "uzb": "UZB",
    # Vanuatu
    "vanuatu": "VUT",
    "vu": "VUT",
    "vut": "VUT",
    # Venezuela (Bolivarian Republic of)
    "venezuelabolivarianrepublicof": "VEN",
    "ve": "VEN",
    "ven": "VEN",
    "venezuela": "VEN",
    # Vietnam
    "vietnam": "VNM",
    "vn": "VNM",
    "vnm": "VNM",
    # Virgin Islands (British)
    "virginislandsbritish": "VGB",
    "vg": "VGB",
    "vgb": "VGB",
    "britishvirginislands": "VGB",
    # Virgin Islands (U.S.)
    "virginislandsus": "VIR",
    "vi": "VIR",
    "vir": "VIR",
    "virginislandsoftheunitedstates": "VIR",
    # Wallis and Futuna
    "wallisandfutuna": "WLF",
    "wf": "WLF",
    "wlf": "WLF",
    "wallisandfutunaislands": "WLF",
    # Western Sahara
    "westernsahara": "ESH",
    "eh": "ESH",
    "esh": "ESH",
    # Yemen
    "yemen": "YEM",
    "ye": "YEM",
    "yem": "YEM",
    # Zambia
    "zambia": "ZMB",
    "zm": "ZMB",
    "zmb": "ZMB",
    # Zimbabwe
    "zimbabwe": "ZWE",
    "zw": "ZWE",
    "zwe": "ZWE",
}


@np.vectorize
def get_country_alpha_3(country_name: str) -> CountryCodeAlpha3:
    """
    Returns id if country exists and throw DOES_NOT_EXIST otherwise
    >>> get_country_alpha_3('westernsahara')
    'ESH'
    >>> get_country_alpha_3('WesternSahara')
    'ESH'
    >>> get_country_alpha_3('svalbardandjanmayenislands')
    'SJM'
    >>> get_country_alpha_3('eswatini')
    'SWZ'
    >>> get_country_alpha_3('EU')
    'YYY'
    >>> get_country_alpha_3('DOES_NOT_EXIST') # doctest: +IGNORE_EXCEPTION_DETAIL
    Traceback (most recent call last):
    KeyError: 'DOES_NOT_EXIST'
    """
    return countries_dictionary[_normalize_country_name(country_name)]


def _normalize_country_name(country_name: str) -> str:
    """
    The function normalizes the country name so it does not contain unexpected
    characters and is in lower case. That way our dictionary of countries is shorter and easier to maintain

    >>> _normalize_country_name('bir-Ma88')
    'birma88'
    >>> _normalize_country_name('')
    ''
    >>> _normalize_country_name(8) # doctest: +IGNORE_EXCEPTION_DETAIL
    Traceback (most recent call last):
    AttributeError: 'int' object has no attribute 'lower'
    """
    if not country_name:
        return ""
    return re.sub("[^\\dA-Za-z一-龠]", "", country_name.lower())
