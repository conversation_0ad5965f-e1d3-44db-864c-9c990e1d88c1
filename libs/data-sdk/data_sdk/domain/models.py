from __future__ import annotations

import abc
from typing import ClassVar, Type

import pandera.polars as pap
import polars as pl
from pydantic import BaseModel, ConfigDict, model_validator

from data_sdk.utils.pandera import empty_polars_df_from_model
from data_sdk.validators.schemas import NoCheckSchema


class ValidateableModel(BaseModel, abc.ABC):
    model_config = ConfigDict(arbitrary_types_allowed=True)
    model: ClassVar[Type[pap.DataFrameModel]]
    df: pl.DataFrame

    def __add__(self, other):
        assert self.model == other.model
        if self.model is NoCheckSchema:
            if self.df.is_empty():
                return other
            elif other.df.is_empty():
                return self
            columns = set(self.df.columns) | set(other.df.columns)
            return self.__class__(
                df=pl.concat([self.df.select(columns), other.df.select(columns)])
            )
        return self.__class__(df=pl.concat([self.df, other.df]))

    @model_validator(mode="after")
    def validate_schema(self):
        # When NoCheckSchema is on, we use the columns from the DataFrame not the schema
        # Since there is no schema :D
        if self.df.is_empty() and self.model is not NoCheckSchema:
            self.df = empty_polars_df_from_model(self.model)
        # For NoCheckSchema convert date columns to Date type since we sometimes use strings
        # in legacy tables
        if self.model is NoCheckSchema:
            for column in ["date", "date_ir"]:
                if column in self.df.columns:
                    self.df = self.df.with_columns(
                        pl.col(column).cast(pl.Date).alias(column)
                    )
        self.df = self.model.validate(self.df)
        return self

    @classmethod
    def empty(cls) -> ValidateableModel:
        # schema will be set on validation
        return cls(df=pl.DataFrame())
