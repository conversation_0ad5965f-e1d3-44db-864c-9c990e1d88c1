from enum import Enum

from pydantic import AwareDatetime, BaseModel

from data_sdk.domain.domain_types import OrganizationId, ProductName, StudioId, UserId


class Studio(BaseModel):
    studio_id: StudioId
    organization_id: OrganizationId
    email: str
    company_name: str
    is_test_account: bool
    is_verified: bool
    agreement_date: AwareDatetime
    studio_parent_id: int | None


class BaseUser(BaseModel):
    legacy_id: StudioId
    email: str


class User(BaseUser):
    first_name: str
    last_name: str
    company_name: str
    test_account: bool
    id: UserId
    verified: bool
    email_verification_token: str | None
    email_verification_deadline: AwareDatetime | None
    reset_password_token: str | None
    reset_password_deadline: AwareDatetime | None
    agreement_date: AwareDatetime
    unsuccessful_verification_attempts: int
    force_password_change: bool


class BaseOrganization(BaseModel):
    name: str
    legacy_id: StudioId


class Organization(BaseOrganization):
    id: OrganizationId


class PermissionName(str, Enum):
    # OWNER - all below:
    USER_SERVICE_V2_ASSIGN_PERMISSIONS = "USER_SERVICE_V2_ASSIGN_PERMISSIONS"
    # MAINTAINER - all below:
    REPORT_SERVICE_MODIFY_REPORTS = "REPORT_SERVICE_MODIFY_REPORTS"
    REPORT_SERVICE_MODIFY_SKUS = "REPORT_SERVICE_MODIFY_SKUS"
    REPORT_SERVICE_UPLOAD_REPORTS = "REPORT_SERVICE_UPLOAD_REPORTS"
    # READER exclusive - all below:
    REPORT_SERVICE_READ_REPORTS = "REPORT_SERVICE_READ_REPORTS"
    REPORT_SERVICE_READ_SKUS = "REPORT_SERVICE_READ_SKUS"
    # EXTERNAL_READER - all below:
    DATASET_MANAGER_VIEW_DASHBOARDS = "DATASET_MANAGER_VIEW_DASHBOARDS"


class Permission(BaseModel):
    organization_id: OrganizationId
    name: PermissionName
    filters: list[str]


class Role(str, Enum):
    EXTERNAL_READER = "EXTERNAL_READER"
    READER = "READER"
    MAINTAINER = "MAINTAINER"
    OWNER = "OWNER"


class RoleAssignment(BaseModel):
    user_id: UserId
    organization_id: OrganizationId
    role: Role
    filter: dict[str, list[ProductName]] | None
    created_by_user_id: str | None
    id: int
    created_at: AwareDatetime
    updated_at: AwareDatetime
    user: BaseUser
    organization: BaseOrganization


class Shared(BaseModel):
    id: int
    shared_with_id: StudioId
    shared_by_id: StudioId
    product_name: ProductName | None
    date_from: AwareDatetime
    date_to: AwareDatetime
    portal_id: None
    game_id: None
