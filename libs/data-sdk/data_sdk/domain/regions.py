from enum import Enum

from data_sdk.domain.constants import DEFAULT_SORTING_ORDER


class Region(str, Enum):
    GLOBAL = "Global"
    NINTENDO_AMERICAS = "Nintendo Americas"
    NINTENDO_EUROPE_AUSTRALIA = "Nintendo Europe/Australia"
    NINTENDO_JAPAN = "Nintendo Japan"
    NINTENDO_CHINA = "Nintendo China"
    NINTENDO_KOREA = "Nintendo Korea"
    NINTENDO_TAIWAN_HONG_KONG = "Nintendo Taiwan/Hong Kong"
    PLAYSTATION_AMERICA = "PS America"
    PLAYSTATION_ASIA = "PS Asia"
    PLAYSTATION_EUROPE = "PS Europe"
    PLAYSTATION_JAPAN = "PS Japan"
    PLAYSTATION_UNKNOWN = "PS Unknown"


_ps_region_dictionary = {
    "SIEA": Region.PLAYSTATION_AMERICA.value,
    "SIEE": Region.PLAYSTATION_EUROPE.value,
    "SIEAsia": Region.PLAYSTATION_ASIA.value,
    "SIEJ": Region.PLAYSTATION_JAPAN.value,
}


def get_ps_region_by_country_alpha_3(alpha_3):
    """
    Returns region if country id exists and throw an error otherwise
    >>> get_ps_region_by_country_alpha_3("SIEA")
    'PS America'
    >>> get_ps_region_by_country_alpha_3("ABC")
    'ABC'
    """
    return _ps_region_dictionary.get(alpha_3, Region.PLAYSTATION_UNKNOWN.value)


_nintendo_region_dictionary = {
    "AIA": Region.NINTENDO_AMERICAS.value,  # Anguilla
    "AI": Region.NINTENDO_AMERICAS.value,  # Anguilla
    "ATG": Region.NINTENDO_AMERICAS.value,  # Antigua and Barbuda
    "AG": Region.NINTENDO_AMERICAS.value,  # Antigua and Barbuda
    "ARG": Region.NINTENDO_AMERICAS.value,  # Argentina
    "AR": Region.NINTENDO_AMERICAS.value,  # Argentina
    "ABW": Region.NINTENDO_AMERICAS.value,  # Aruba
    "AW": Region.NINTENDO_AMERICAS.value,  # Aruba
    "AUS": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Australia
    "AU": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Australia
    "AUT": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Austria
    "AT": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Austria
    "BHS": Region.NINTENDO_AMERICAS.value,  # Bahamas
    "BS": Region.NINTENDO_AMERICAS.value,  # Bahamas
    "BRB": Region.NINTENDO_AMERICAS.value,  # Barbados
    "BB": Region.NINTENDO_AMERICAS.value,  # Barbados
    "BEL": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Belgium
    "BE": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Belgium
    "BLZ": Region.NINTENDO_AMERICAS.value,  # Belize
    "BZ": Region.NINTENDO_AMERICAS.value,  # Belize
    "BMU": Region.NINTENDO_AMERICAS.value,  # Bermuda
    "BM": Region.NINTENDO_AMERICAS.value,  # Bermuda
    "BOL": Region.NINTENDO_AMERICAS.value,  # Bolivia
    "BO": Region.NINTENDO_AMERICAS.value,  # Bolivia
    "BRA": Region.NINTENDO_AMERICAS.value,  # Brazil
    "BR": Region.NINTENDO_AMERICAS.value,  # Brazil
    "BGR": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Bulgaria
    "BG": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Bulgaria
    "CAN": Region.NINTENDO_AMERICAS.value,  # Canada
    "CA": Region.NINTENDO_AMERICAS.value,  # Canada
    "CYM": Region.NINTENDO_AMERICAS.value,  # Cayman Islands
    "KY": Region.NINTENDO_AMERICAS.value,  # Cayman Islands
    "CHL": Region.NINTENDO_AMERICAS.value,  # Chile
    "CL": Region.NINTENDO_AMERICAS.value,  # Chile
    "CHN": Region.NINTENDO_CHINA.value,  # China
    "CN": Region.NINTENDO_CHINA.value,  # China
    "COL": Region.NINTENDO_AMERICAS.value,  # Colombia
    "CO": Region.NINTENDO_AMERICAS.value,  # Colombia
    "CRI": Region.NINTENDO_AMERICAS.value,  # Costa Rica
    "CR": Region.NINTENDO_AMERICAS.value,  # Costa Rica
    "HRV": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Croatia
    "HR": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Croatia
    "CYP": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Cyprus
    "CY": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Cyprus
    "CZE": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Czech Republic
    "CZ": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Czech Republic
    "DNK": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Denmark
    "DK": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Denmark
    "DMA": Region.NINTENDO_AMERICAS.value,  # Dominica
    "DM": Region.NINTENDO_AMERICAS.value,  # Dominica
    "DOM": Region.NINTENDO_AMERICAS.value,  # Dominican Republic
    "DO": Region.NINTENDO_AMERICAS.value,  # Dominican Republic
    "ECU": Region.NINTENDO_AMERICAS.value,  # Ecuador
    "EC": Region.NINTENDO_AMERICAS.value,  # Ecuador
    "SLV": Region.NINTENDO_AMERICAS.value,  # El Salvador
    "SV": Region.NINTENDO_AMERICAS.value,  # El Salvador
    "EST": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Estonia
    "EE": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Estonia
    "FIN": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Finland
    "FI": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Finland
    "FRA": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # France
    "FR": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # France
    "GUF": Region.NINTENDO_AMERICAS.value,  # French Guiana
    "GF": Region.NINTENDO_AMERICAS.value,  # French Guiana
    "DEU": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Germany
    "DE": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Germany
    "GRC": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Greece
    "GR": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Greece
    "GRD": Region.NINTENDO_AMERICAS.value,  # Grenada
    "GD": Region.NINTENDO_AMERICAS.value,  # Grenada
    "GLP": Region.NINTENDO_AMERICAS.value,  # Guadeloupe
    "GP": Region.NINTENDO_AMERICAS.value,  # Guadeloupe
    "GTM": Region.NINTENDO_AMERICAS.value,  # Guatemala
    "GT": Region.NINTENDO_AMERICAS.value,  # Guatemala
    "GUY": Region.NINTENDO_AMERICAS.value,  # Guyana
    "GY": Region.NINTENDO_AMERICAS.value,  # Guyana
    "HTI": Region.NINTENDO_AMERICAS.value,  # Haiti
    "HT": Region.NINTENDO_AMERICAS.value,  # Haiti
    "HND": Region.NINTENDO_AMERICAS.value,  # Honduras
    "HN": Region.NINTENDO_AMERICAS.value,  # Honduras
    "HKG": Region.NINTENDO_TAIWAN_HONG_KONG.value,  # Hong Kong
    "HK": Region.NINTENDO_TAIWAN_HONG_KONG.value,  # Hong Kong
    "HUN": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Hungary
    "HU": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Hungary
    "IRL": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Ireland
    "IE": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Ireland
    "ISR": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Israel
    "IL": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Israel
    "ITA": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Italy
    "IT": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Italy
    "JAM": Region.NINTENDO_AMERICAS.value,  # Jamaica
    "JM": Region.NINTENDO_AMERICAS.value,  # Jamaica
    "JPN": Region.NINTENDO_JAPAN.value,  # Japan
    "JP": Region.NINTENDO_JAPAN.value,  # Japan
    "LVA": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Latvia
    "LV": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Latvia
    "LTU": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Lithuania
    "LT": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Lithuania
    "LUX": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Luxembourg
    "LU": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Luxembourg
    "MYS": Region.NINTENDO_AMERICAS.value,  # Malaysia
    "MY": Region.NINTENDO_AMERICAS.value,  # Malaysia
    "MLT": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Malta
    "MT": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Malta
    "MTQ": Region.NINTENDO_AMERICAS.value,  # Martinique
    "MQ": Region.NINTENDO_AMERICAS.value,  # Martinique
    "MEX": Region.NINTENDO_AMERICAS.value,  # Mexico
    "MX": Region.NINTENDO_AMERICAS.value,  # Mexico
    "MSR": Region.NINTENDO_AMERICAS.value,  # Montserrat
    "MS": Region.NINTENDO_AMERICAS.value,  # Montserrat
    "NLD": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Netherlands
    "NL": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Netherlands
    "ANT": Region.NINTENDO_AMERICAS.value,  # Netherlands Antilles
    "AN": Region.NINTENDO_AMERICAS.value,  # Netherlands Antilles
    "NZL": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # New Zealand
    "NZ": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # New Zealand
    "NIC": Region.NINTENDO_AMERICAS.value,  # Nicaragua
    "NI": Region.NINTENDO_AMERICAS.value,  # Nicaragua
    "NOR": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Norway
    "NO": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Norway
    "PAN": Region.NINTENDO_AMERICAS.value,  # Panama
    "PA": Region.NINTENDO_AMERICAS.value,  # Panama
    "PRY": Region.NINTENDO_AMERICAS.value,  # Paraguay
    "PY": Region.NINTENDO_AMERICAS.value,  # Paraguay
    "PER": Region.NINTENDO_AMERICAS.value,  # Peru
    "PE": Region.NINTENDO_AMERICAS.value,  # Peru
    "POL": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Poland
    "PL": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Poland
    "PRT": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Portugal
    "PT": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Portugal
    "ROU": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Romania
    "RO": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Romania
    "RUS": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Russia
    "RU": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Russia
    "KNA": Region.NINTENDO_AMERICAS.value,  # Saint Kitts and Nevis
    "KN": Region.NINTENDO_AMERICAS.value,  # Saint Kitts and Nevis
    "LCA": Region.NINTENDO_AMERICAS.value,  # Saint Lucia
    "LC": Region.NINTENDO_AMERICAS.value,  # Saint Lucia
    "VCT": Region.NINTENDO_AMERICAS.value,  # Saint Vincent and the Grenadines
    "VC": Region.NINTENDO_AMERICAS.value,  # Saint Vincent and the Grenadines
    "SAU": Region.NINTENDO_AMERICAS.value,  # Saudi Arabia
    "SA": Region.NINTENDO_AMERICAS.value,  # Saudi Arabia
    "SGP": Region.NINTENDO_AMERICAS.value,  # Singapore
    "SG": Region.NINTENDO_AMERICAS.value,  # Singapore
    "SVK": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Slovakia
    "SK": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Slovakia
    "SVN": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Slovenia
    "SI": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Slovenia
    "ZAF": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # South Africa
    "ZA": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # South Africa
    "ESP": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Spain
    "ES": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Spain
    "SUR": Region.NINTENDO_AMERICAS.value,  # Suriname
    "SR": Region.NINTENDO_AMERICAS.value,  # Suriname
    "SWE": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Sweden
    "SE": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Sweden
    "CHE": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Switzerland
    "CH": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # Switzerland
    "TWN": Region.NINTENDO_TAIWAN_HONG_KONG.value,  # Taiwan
    "TW": Region.NINTENDO_TAIWAN_HONG_KONG.value,  # Taiwan
    "TTO": Region.NINTENDO_AMERICAS.value,  # Trinidad and Tobago
    "TT": Region.NINTENDO_AMERICAS.value,  # Trinidad and Tobago
    "TCA": Region.NINTENDO_AMERICAS.value,  # Turks and Caicos Islands
    "TC": Region.NINTENDO_AMERICAS.value,  # Turks and Caicos Islands
    "ARE": Region.NINTENDO_AMERICAS.value,  # United Arab Emirates
    "AE": Region.NINTENDO_AMERICAS.value,  # United Arab Emirates
    "GBR": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # United Kingdom
    "GB": Region.NINTENDO_EUROPE_AUSTRALIA.value,  # United Kingdom
    "USA": Region.NINTENDO_AMERICAS.value,  # United States
    "US": Region.NINTENDO_AMERICAS.value,  # United States
    "URY": Region.NINTENDO_AMERICAS.value,  # Uruguay
    "UY": Region.NINTENDO_AMERICAS.value,  # Uruguay
    "VEN": Region.NINTENDO_AMERICAS.value,  # Venezuela
    "VE": Region.NINTENDO_AMERICAS.value,  # Venezuela
    "VGB": Region.NINTENDO_AMERICAS.value,  # Virgin Islands, British
    "VG": Region.NINTENDO_AMERICAS.value,  # Virgin Islands, British
    "VIR": Region.NINTENDO_AMERICAS.value,  # Virgin Islands, U.S.
    "VI": Region.NINTENDO_AMERICAS.value,  # Virgin Islands, U.S.
    "KOR": Region.NINTENDO_KOREA.value,  # South Korea
    "KR": Region.NINTENDO_KOREA.value,  # South Korea
}


def get_nintendo_region_by_country_code(alpha):
    """
    Returns region if country id exists and throw an error otherwise
    >>> get_nintendo_region_by_country_code("USA")
    'Nintendo America'
    >>> get_nintendo_region_by_country_code("ABC")
    Traceback (most recent call last):
    KeyError: 'ABC'
    """
    return _nintendo_region_dictionary[alpha]


_nintendo_wishlist_actions_region_dictionary = {
    "americas": Region.NINTENDO_AMERICAS.value,
    "australia/europe": Region.NINTENDO_EUROPE_AUSTRALIA.value,
    "australia": Region.NINTENDO_EUROPE_AUSTRALIA.value,
    "europe": Region.NINTENDO_EUROPE_AUSTRALIA.value,
    "china": Region.NINTENDO_CHINA.value,
    "japan": Region.NINTENDO_JAPAN.value,
    "korea": Region.NINTENDO_KOREA.value,
    "taiwan/hong kong": Region.NINTENDO_TAIWAN_HONG_KONG.value,
    "taiwan": Region.NINTENDO_TAIWAN_HONG_KONG.value,
    "hong kong": Region.NINTENDO_TAIWAN_HONG_KONG.value,
    "korea/taiwan/hong kong": Region.NINTENDO_TAIWAN_HONG_KONG.value,  # see ADR https://indiebi.atlassian.net/wiki/x/NgA_Rg
}


def get_nintendo_region_by_reported_region(region):
    """
    Returns region if nintendo wishlist actions region exists and throw an error otherwise
    >>> get_nintendo_region_by_reported_region("Americas")
    'Nintendo America'
    >>> get_nintendo_region_by_reported_region("ABC")
    Traceback (most recent call last):
    KeyError: 'ABC'
    """
    try:
        return _nintendo_wishlist_actions_region_dictionary[region.lower()]
    except KeyError:
        raise KeyError(
            f"Region '{region}' is not recognized as a valid nintendo region."
        )


_region_sorting_order_map = {
    # global
    Region.GLOBAL.value: 0,
    # nintendo
    Region.NINTENDO_AMERICAS.value: 1,
    Region.NINTENDO_EUROPE_AUSTRALIA.value: 2,
    Region.NINTENDO_JAPAN.value: 3,
    Region.NINTENDO_CHINA.value: 4,
    Region.NINTENDO_KOREA: 5,
    Region.NINTENDO_TAIWAN_HONG_KONG.value: 6,
    # playstation
    Region.PLAYSTATION_AMERICA.value: 1,
    Region.PLAYSTATION_EUROPE.value: 2,
    Region.PLAYSTATION_JAPAN.value: 3,
    Region.PLAYSTATION_ASIA.value: 4,
    Region.PLAYSTATION_UNKNOWN.value: 9,
}


def get_sorting_order_for_region(region) -> int:
    """Return sorting priority for a given region (1-highest)

    Args:
        region: name of the region

    Returns:
        Number representing region sorting order. Defaults to 9 if region unrecognized.

    >>> get_sorting_order_for_region('Nintendo Japan')
    4
    >>> get_sorting_order_for_region('nonexistent')
    9
    """
    try:
        return _region_sorting_order_map[region]
    except KeyError:
        return DEFAULT_SORTING_ORDER
