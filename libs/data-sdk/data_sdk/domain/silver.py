from pandera.engines.polars_engine import Date

from data_sdk.validators.fields import (
    BasicField,
    HashStringField,
    MediumStringField,
    SmallStringField,
    TinyStringField,
)
from data_sdk.validators.schemas import StrictBaseSchema


class AcquisitionPropertiesModel(StrictBaseSchema):
    transaction_type: str = SmallStringField()
    tax_type: str = SmallStringField()
    sale_modificator: str = TinyStringField()
    acquisition_platform: str = SmallStringField()
    acquisition_origin: str = SmallStringField()
    iap_flag: str = TinyStringField()
    hash_acquisition_properties: str = HashStringField()


class PortalsModel(StrictBaseSchema):
    portal: str = SmallStringField()
    platform: str = SmallStringField()
    region: str = SmallStringField()
    abbreviated_name: str = SmallStringField()
    portal_platform_region: str = MediumStringField()
    store_name: str = SmallStringField()


class SilverSkuModel(StrictBaseSchema):
    unique_sku_id: str = MediumStringField()
    base_sku_id: str = SmallStringField()
    portal_platform_region: str = MediumStringField()
    human_name: str = MediumStringField()
    store_id: str = SmallStringField()
    studio_id: int = BasicField()
    human_name_indicator: str = SmallStringField()
    sku_type: str = SmallStringField()
    product_name: str = MediumStringField(nullable=True)
    product_type: str = MediumStringField(nullable=True)
    release_date: Date = BasicField(nullable=True)


class SilverTrafficSourceModel(StrictBaseSchema):
    page_category: str = SmallStringField()
    page_category_group: str = SmallStringField()
    page_feature: str = SmallStringField()
    hash_traffic_source: str = HashStringField()
