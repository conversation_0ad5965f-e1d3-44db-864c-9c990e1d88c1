from functools import singledispatch

from data_sdk.domain import Portal, TableNameType, TableVersion
from data_sdk.domain.domain_types import StudioId
from data_sdk.domain.observations import ObservationType
from data_sdk.domain.tables import (
    PartitionTypes,
    TableDefinition,
    TablePartitionedByObservationType,
    TablePartitionedByPortal,
    TablePartitionedByStudio,
)


@singledispatch
def get_table_partition(table: TableDefinition, **kwargs) -> str:
    raise ValueError("Unsupported table definition passed to partitioner")


@get_table_partition.register
def _(
    table: TablePartitionedByStudio,
    studio_id: StudioId,
) -> str:
    partition_parts: dict[PartitionTypes, str] = {
        TableNameType: table.table_name.value,
        StudioId: f"studio_id={studio_id}",
        TableVersion: f"version={table.version}",
    }
    return "/".join([partition_parts[partition] for partition in table.partitions])


@get_table_partition.register
def _(
    table: TablePartitionedByPortal,
    studio_id: StudioId,
    portal: Portal,
) -> str:
    partition_parts: dict[PartitionTypes, str] = {
        TableNameType: table.table_name.value,
        StudioId: f"studio_id={studio_id}",
        Portal: f"portal={portal.value}",
        TableVersion: f"version={table.version}",
    }
    return "/".join([partition_parts[partition] for partition in table.partitions])


@get_table_partition.register
def _(
    table: TablePartitionedByObservationType,
    studio_id: StudioId,
    portal: Portal,
    observation_type: ObservationType,
) -> str:
    partition_parts: dict[PartitionTypes, str] = {
        TableNameType: table.table_name.value,
        StudioId: f"studio_id={studio_id}",
        Portal: f"portal={portal.value}",
        ObservationType: f"observation_type={observation_type.value}",
        TableVersion: f"version={table.version}",
    }
    return "/".join([partition_parts[partition] for partition in table.partitions])
