from __future__ import annotations

import abc
import logging
import os
from functools import singledispatchmethod
from io import Bytes<PERSON>
from itertools import product
from pathlib import Path
from time import sleep
from typing import Any, Type

import polars as pl
from azure.core.exceptions import ResourceNotFoundError
from azure.storage.filedatalake import PathProperties

from data_sdk.config import DLSConfig, LocalConfig
from data_sdk.connectors.azure_dls import get_filesystem_client
from data_sdk.custom_partition.exeptions import CustomPartitionTableNotFound
from data_sdk.domain import ALL_PORTALS, Portal, TableNameType, TableVersion
from data_sdk.domain.domain_types import StudioId
from data_sdk.domain.observations import ALL_OBSERVATION_TYPES, ObservationType
from data_sdk.domain.tables import PartitionTypes, TableDefinition
from data_sdk.validators.schemas import NoCheckSchema

log = logging.getLogger(__name__)

ALL_PORTALS_STR = [portal.value for portal in ALL_PORTALS]
ALL_OBSERVATION_TYPES_STR = [
    observation_type.value for observation_type in ALL_OBSERVATION_TYPES
]
short_partition_list = [TableNameType]

partition_type_string_map = {
    TableNameType: "table",
    StudioId: "studio_id",
    Portal: "portal",
    ObservationType: "observation_type",
    TableVersion: "version",
}


def partition_template_builder(partitions: list[PartitionTypes]):
    partitions_str = [partition_type_string_map[partition] for partition in partitions]  # type: ignore
    short_partition_list_str = [
        partition_type_string_map[partition] for partition in short_partition_list
    ]  # type: ignore
    template = "/".join(
        [
            f"{{{partition}}}"
            if partition in short_partition_list_str
            else f"{partition}={{{partition}}}"
            for partition in partitions_str
        ]
    )
    return template


def create_combinations_dict(filter_dict):
    keys, values = zip(*filter_dict.items())
    combinations = [dict(zip(keys, v)) for v in product(*values)]
    for combo in combinations:
        for key, value in combo.items():
            combo[key] = value
    return combinations


def dict_types_to_str(dict):
    return {partition_type_string_map[key]: value for key, value in dict.items()}


def partition_until_studio(partition_list):
    if StudioId not in partition_list:
        yield from partition_list
        return
    for partition in partition_list:
        if partition == StudioId:
            yield partition
            break
        yield partition


class CustomPartitionReader(abc.ABC):
    @singledispatchmethod
    @staticmethod
    def get_reader(config: Any) -> CustomPartitionReader:
        raise ValueError("Invalid source type")

    @get_reader.register
    @staticmethod
    def _(config: LocalConfig):
        return LocalCustomPartitionReader(config)

    @get_reader.register
    @staticmethod
    def _(config: DLSConfig):
        return DLSCustomPartitionReader(config)

    @abc.abstractmethod
    def read_table(
        self,
        table_cls: Type[TableDefinition],
        studio_id: StudioId,
        portal: Portal | None = None,
        observation_type: ObservationType | None = None,
    ) -> TableDefinition:
        pass

    def get_filter(
        self,
        table_cls: type[TableDefinition],
        studio_id: StudioId,
        portal: Portal | None = None,
        observation_type: ObservationType | None = None,
    ) -> dict[PartitionTypes, Any]:
        return {
            TableNameType: [table_cls.table_name.value],
            Portal: [portal.value] if portal else ALL_PORTALS_STR,
            ObservationType: [observation_type.value]
            if observation_type
            else ALL_OBSERVATION_TYPES_STR,
            TableVersion: [table_cls.version],
            StudioId: [studio_id],
        }

    def get_table_base_path(self, filter_dict, partition_list) -> str:
        partition_template = partition_template_builder(
            list(partition_until_studio(partition_list))
        )
        all_partitions = [
            dict_types_to_str(partition_dict)
            for partition_dict in create_combinations_dict(filter_dict)
        ]
        table_base_path = [
            partition_template.format(**partition) for partition in all_partitions
        ][0]
        return table_base_path

    def get_potential_partition_directory_paths(self, filter_dict, partition_list):
        partition_template = partition_template_builder(partition_list)
        all_partitions = [
            dict_types_to_str(partition_dict)
            for partition_dict in create_combinations_dict(filter_dict)
        ]
        all_paths = [
            partition_template.format(**partition) for partition in all_partitions
        ]
        all_paths_without_duplicates = list(set(all_paths))
        return all_paths_without_duplicates


fact_table_date_columns = ["date", "date_ir"]


class LocalCustomPartitionReader(CustomPartitionReader):
    def __init__(self, source_cfg: LocalConfig):
        self._local_dir: Path = source_cfg.local_dir

    def read_table(
        self,
        table_cls: type[TableDefinition],
        studio_id: StudioId,
        portal: Portal | None = None,
        observation_type: ObservationType | None = None,
    ) -> TableDefinition:
        filter_dict = self.get_filter(table_cls, studio_id, portal, observation_type)
        table_base_path = self.get_table_base_path(filter_dict, table_cls.partitions)
        available_table_partitions_paths = self._get_available_table_partition_paths(
            table_base_path
        )
        all_partition_paths = self._find_all_partitions(
            available_table_partitions_paths, table_cls, filter_dict
        )
        all_partition_files: list[TableDefinition] = [
            self._read_newest_file_in_directory(Path(directory_path), table_cls)
            for directory_path in all_partition_paths
        ]

        return sum(all_partition_files, table_cls(df=pl.DataFrame()))

    def _get_available_table_partition_paths(self, path: str) -> list[Path]:
        # read all paths starting with path_to_check from os
        path_to_check = Path(str(self._local_dir) + "/" + path)
        try:
            paths = list(
                path_to_check.rglob("**")
            )  # this directory and all subdirectories, recursively
        except FileNotFoundError:
            paths: list[Path] = [path_to_check]
        return paths

    def _find_all_partitions(
        self,
        available_partition_paths: list[Any],
        table_cls: type[TableDefinition],
        filter_dict: dict[PartitionTypes, Any],
    ) -> list[Any]:
        potential_partition_paths = [
            str(self._local_dir) + "/" + potential_path
            for potential_path in self.get_potential_partition_directory_paths(
                filter_dict, table_cls.partitions
            )
        ]
        available_partition_paths_str = [
            str(local_path) for local_path in available_partition_paths
        ]
        filtered_paths = [
            path
            for path in potential_partition_paths
            if path in available_partition_paths_str
        ]
        return filtered_paths

    def _read_newest_file_in_directory(
        self,
        directory_path: Path,
        table_cls: Type[TableDefinition],
    ) -> TableDefinition:
        newest_file_filepath_by_timestamp = max(
            directory_path.glob("*.parquet"), key=os.path.getctime, default=None
        )

        if newest_file_filepath_by_timestamp is not None:
            df = pl.read_parquet(
                newest_file_filepath_by_timestamp, hive_partitioning=False
            )
            allowed_columns = (
                table_cls.model.to_schema().columns.keys()
                if table_cls.model is not NoCheckSchema
                else [x for x in df.columns if x != "__index_level_0__"]
            )
            return table_cls(df=df.select(allowed_columns))
        else:
            return table_cls(df=pl.DataFrame())


class DLSCustomPartitionReader(CustomPartitionReader):
    def __init__(self, source_cfg: DLSConfig):
        self._base_dir: Path = source_cfg.base_dir
        self._client = get_filesystem_client(
            dls_account_name=source_cfg.account_name,
            file_system_name=source_cfg.container_name,
        )

    def read_table(
        self,
        table_cls: type[TableDefinition],
        studio_id: StudioId,
        portal: Portal | None = None,
        observation_type: ObservationType | None = None,
    ) -> TableDefinition:
        log.info(
            "Reading table %s for studio_id=%s portal=%s observation_type=%s",
            table_cls.table_name,
            studio_id,
            portal,
            observation_type,
        )
        filter_dict = self.get_filter(table_cls, studio_id, portal, observation_type)
        table_base_path = self.get_table_base_path(filter_dict, table_cls.partitions)
        available_table_partitions_paths = self._get_available_table_partition_paths(
            table_base_path
        )
        all_partition_paths = self._find_all_partitions(
            available_table_partitions_paths, table_cls, filter_dict
        )
        all_partition_files = [
            self._read_newest_file_in_directory(directory_path, table_cls)
            for directory_path in all_partition_paths
        ]

        return sum(all_partition_files, table_cls(df=pl.DataFrame()))

    def _find_all_partitions(
        self,
        available_partition_paths: list[Any],
        table_cls: type[TableDefinition],
        filter_dict: dict[PartitionTypes, Any],
    ) -> list[Any]:
        potential_partition_paths = [
            str(self._base_dir) + "/" + potential_path
            for potential_path in self.get_potential_partition_directory_paths(
                filter_dict, table_cls.partitions
            )
        ]
        all_paths_for_studio_str = [
            azure_path["name"] for azure_path in available_partition_paths
        ]
        filtered_paths = [
            path
            for path in potential_partition_paths
            if path in all_paths_for_studio_str
        ]
        return filtered_paths

    def _get_available_table_partition_paths(self, path: str) -> list[PathProperties]:
        try:
            path_to_check = str(self._base_dir) + "/" + path
            paths = list(self._client.get_paths(path_to_check))
            directories = [path for path in paths if path.is_directory]
            # Include path_to_check
            directories.append(PathProperties(name=path_to_check, is_directory=True))
        except ResourceNotFoundError:
            directories = [PathProperties(name=path_to_check, is_directory=True)]
        return directories

    def _read_newest_file_in_directory(
        self,
        directory_path: str,
        table_cls: Type[TableDefinition],
    ) -> TableDefinition:
        all_files = self._client.get_paths(path=directory_path)
        try:
            newest_file_filepath_by_timestamp = max(
                all_files, key=lambda x: x.last_modified
            )
        except ValueError as e:
            raise CustomPartitionTableNotFound(
                directory_path=directory_path,
                error=e,
                reason="No files found in directory",
            ) from e
        file_client = self._client.get_file_client(
            newest_file_filepath_by_timestamp.name
        )
        file = file_client.download_file().readall()
        if file == b"":
            # TODO: possible fix for NoDataError: empty CSV data from BytesIO
            # it happens when file is saved by PR and at the same time read in gold
            # this should not be a problem when we move to delta table
            sleep(30)
            file = file_client.download_file().readall()
        df = pl.read_parquet(BytesIO(file))

        allowed_columns = (
            table_cls.model.to_schema().columns.keys()
            if table_cls.model is not NoCheckSchema
            else [x for x in df.columns if x != "__index_level_0__"]
        )
        return table_cls(df=df.select(allowed_columns))
