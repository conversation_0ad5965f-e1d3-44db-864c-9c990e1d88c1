import logging
from abc import ABC, abstractmethod
from datetime import datetime

import polars as pl
from elasticapm import capture_span
from pandera import polars as pandera_polars

from data_sdk.custom_partition.reader import (
    CustomPartitionReader,
    partition_template_builder,
)
from data_sdk.custom_partition.writer import CustomPartitionsWriter
from data_sdk.dependency_injection import generate_init_kwargs
from data_sdk.domain import Portal, TableVersion
from data_sdk.domain.domain_error import DomainError
from data_sdk.domain.domain_types import StudioId
from data_sdk.domain.tables import TableDefinition
from data_sdk.segmentator import BaseSegmentator, SingleSegmentator

log = logging.getLogger(__name__)


# TODO: move both to schemas.py
class StrictBaseSchema(pandera_polars.DataFrameModel):
    class Config:
        coerce = True
        strict = "filter"


class NoCheckSchema(pandera_polars.DataFrameModel):
    pass


class SchemaNotDefined(DomainError):
    def __init__(self):
        super().__init__("Mandatory schema not defined! Check your implementation!")


class BaseAggregator(ABC):
    table_cls: type[TableDefinition]
    segmentator: BaseSegmentator = SingleSegmentator()

    @abstractmethod
    def _aggregate(self) -> pl.DataFrame:
        pass

    def aggregate(self) -> TableDefinition:
        return self.table_cls(df=self._aggregate())


def process_aggregators(
    aggregators: list[type[BaseAggregator]],
    reader: CustomPartitionReader,
    writer: CustomPartitionsWriter,
    creation_datetime: datetime,
    studio_id: StudioId,
    portal: Portal | None = None,
):
    aggregators = aggregators.copy()
    for aggregator_cls in aggregators:
        log.info("Processing aggregator %s", aggregator_cls)
        with capture_span(aggregator_cls.__name__):
            _process_single_aggregator(
                aggregator_cls=aggregator_cls,
                reader=reader,
                writer=writer,
                creation_datetime=creation_datetime,
                studio_id=studio_id,
                portal=portal,
            )

        log.info("Finished aggregator %s", aggregator_cls)

    log.info("Waiting for all writes to finish")
    writer.close()

    log.info("All writes finished")


def _process_single_aggregator(
    aggregator_cls: type[BaseAggregator],
    reader: CustomPartitionReader,
    writer: CustomPartitionsWriter,
    creation_datetime: datetime,
    studio_id: StudioId,
    portal: Portal | None = None,
):
    log.info(
        "Reading input tables for aggregator %s (output table %s) for studio %s",
        aggregator_cls,
        aggregator_cls.table_cls.table_name.value,
        studio_id,
    )
    init_kwargs = generate_init_kwargs(
        cls=aggregator_cls,
        reader=reader,
        studio_id=studio_id,
        portal=portal,
    )
    log.info("Aggregating")
    result_table: TableDefinition = aggregator_cls(**init_kwargs).aggregate()

    if result_table.df.is_empty():
        log.info("Empty aggregator result. Skipping partitioning and save")
        return

    log.info("Partitioning")
    # TODO: Temp, it should be in writer
    patrition_template = partition_template_builder(result_table.partitions)
    partition_filter = {
        "table": result_table.table_name.value,
        "studio_id": studio_id,
    }
    if Portal in result_table.partitions:
        assert portal is not None
        partition_filter["portal"] = portal.value

    if TableVersion in result_table.partitions:
        partition_filter["version"] = result_table.version

    partition = patrition_template.format(**partition_filter)

    log.info("Creating segments")
    segments = aggregator_cls.segmentator.create_segments(
        table=result_table, creation_datetime=creation_datetime
    )

    log.info("Saving table %s to %s", result_table.__class__.__name__, partition)
    writer.save_table(result_table, partition, segments)
