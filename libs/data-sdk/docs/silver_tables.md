# Silver Tables
# ObservationDiscountsTable
table name: *observation_discounts*

partitioned by: *TableNameType, StudioId, Portal, TableVersion*

version: *v1*

## Columns

| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |
| create_time | Datetime(time_unit='us', time_zone='UTC') | False | False | True |  |
| update_time | Datetime(time_unit='us', time_zone='UTC') | False | False | True |  |
| platform | Enum(categories=['PC', 'Key', 'Quest', 'Rift', 'Switch', 'Wii U', 'Home', 'PS Vita', 'PSone', 'PS2', 'PS3', 'PS4', 'PS5', 'PSP', 'PS VR', 'PS VR2', 'miniS', 'Microsoft', 'Desktop', 'iPad', 'iPhone', 'iPod touch', 'Google', 'Unknown']) | False | False | True |  |
| region | Enum(categories=['Global', 'Nintendo Asia', 'Nintendo America', 'Nintendo Europe', 'Nintendo Japan', 'Nintendo China', 'PS America', 'PS Asia', 'PS Europe', 'PS Japan', 'PS Unknown']) | False | False | True |  |
| portal | Enum(categories=['Apple', 'Epic', 'GOG', 'Google', 'Humble', 'Meta', 'Microsoft', 'Nintendo', 'PlayStation', 'Steam']) | False | False | True |  |
| event_name | String | False | False | True |  |
| datetime_from | Datetime(time_unit='us', time_zone='UTC') | False | False | True |  |
| datetime_to | Datetime(time_unit='us', time_zone='UTC') | False | False | True |  |
| is_event_joined | Boolean | False | False | True |  |
| discount_depth | Int64 | False | False | True |  |
| unique_event_id | String | False | False | True |  |
| base_event_id | String | False | False | True |  |
| group_id | String | False | False | True |  |
| base_sku_id | String | False | False | True |  |
| triggers_cooldown | Boolean | False | False | True |  |
| major | Boolean | False | False | True |  |
| discount_type | Categorical(ordering='physical') | False | False | True |  - isin([<DiscountType.CUSTOM: 'custom'>, <DiscountType.STORE: 'store'>]) |
| max_discount_percentage | Int64 | False | False | True |  |
| price_increase_time | Datetime(time_unit='us', time_zone='UTC') | False | False | True |  |
| promo_length | Int64 | False | False | True |  |
| studio_id | Int64 | False | False | True |  |
| unique_sku_id | Categorical(ordering='physical') | False | False | True |  |
| report_id | Int64 | False | False | True |  |
| portal_platform_region | Categorical(ordering='physical') | False | False | True |  |

# ObservationSalesTable
table name: *observation_sales*

partitioned by: *TableNameType, StudioId, Portal, TableVersion*

version: *v1*

## Columns

| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |
| sku_id | String | False | False | True |  |
| portal | Enum(categories=['Apple', 'Epic', 'GOG', 'Google', 'Humble', 'Meta', 'Microsoft', 'Nintendo', 'PlayStation', 'Steam']) | False | False | True |  |
| platform | Enum(categories=['PC', 'Key', 'Quest', 'Rift', 'Switch', 'Wii U', 'Home', 'PS Vita', 'PSone', 'PS2', 'PS3', 'PS4', 'PS5', 'PSP', 'PS VR', 'PS VR2', 'miniS', 'Microsoft', 'Desktop', 'iPad', 'iPhone', 'iPod touch', 'Google', 'Unknown']) | False | False | True |  |
| region | Enum(categories=['Global', 'Nintendo Asia', 'Nintendo America', 'Nintendo Europe', 'Nintendo Japan', 'Nintendo China', 'PS America', 'PS Asia', 'PS Europe', 'PS Japan', 'PS Unknown']) | False | False | True |  |
| transaction_type | Categorical(ordering='physical') | False | False | True |  |
| payment_instrument | Categorical(ordering='physical') | False | False | True |  |
| tax_type | Categorical(ordering='physical') | False | False | True |  |
| sale_modificator | Categorical(ordering='physical') | False | False | True |  |
| acquisition_platform | Categorical(ordering='physical') | False | False | True |  |
| acquisition_origin | Categorical(ordering='physical') | False | False | True |  |
| iap_flag | String | False | False | True |  |
| date | Date | False | False | True |  |
| retailer_tag | Categorical(ordering='physical') | False | False | True |  |
| human_name | Categorical(ordering='physical') | False | False | True |  |
| store_id | Categorical(ordering='physical') | False | False | True |  |
| base_price_local | Float64 | True | False | True |  |
| bundle_name | Categorical(ordering='physical') | False | False | True |  |
| net_sales | Float64 | False | False | True |  |
| gross_returned | Float64 | False | False | True |  |
| gross_sales | Float64 | False | False | True |  |
| units_returned | Int64 | False | False | True |  |
| units_sold | Int64 | False | False | True |  |
| free_units | Int64 | False | False | True |  |
| price_local | Float64 | False | False | True |  |
| price_usd | Float64 | False | False | True |  |
| store | Enum(categories=['Epic', 'GOG', 'Humble', 'Microsoft', 'Nintendo Switch Asia', 'Nintendo Switch America', 'Nintendo Switch Europe', 'Nintendo Switch Japan', 'Nintendo Switch Unknown', 'Nintendo Other Asia', 'Nintendo Other America', 'Nintendo Other Europe', 'Nintendo Other Japan', 'Nintendo Other Unknown', 'Meta Rift', 'Meta Quest', 'PlayStation America', 'PlayStation Asia', 'PlayStation Europe', 'PlayStation Japan', 'PlayStation Unknown', 'Steam', 'Apple', 'Google']) | False | False | True |  |
| abbreviated_name | Categorical(ordering='physical') | False | False | True |  |
| net_sales_approx | Float64 | False | False | True |  |
| category | Categorical(ordering='physical') | False | False | True |  |
| studio_id | Int64 | False | False | True |  |
| report_id | Int64 | False | False | True |  |
| unique_sku_id | Categorical(ordering='physical') | False | False | True |  |
| country_code | Enum(categories=['ABW', 'AFG', 'AGO', 'AIA', 'ALA', 'ALB', 'AND', 'ANT', 'ARE', 'ARG', 'ARM', 'ASM', 'ATA', 'ATF', 'ATG', 'AUS', 'AUT', 'AZE', 'BDI', 'BEL', 'BEN', 'BES', 'BFA', 'BGD', 'BGR', 'BHR', 'BHS', 'BIH', 'BLM', 'BLR', 'BLZ', 'BMU', 'BOL', 'BRA', 'BRB', 'BRN', 'BTN', 'BVT', 'BWA', 'CAF', 'CAN', 'CCK', 'CHE', 'CHL', 'CHN', 'CIV', 'CMR', 'COD', 'COG', 'COK', 'COL', 'COM', 'CPV', 'CRI', 'CUB', 'CUW', 'CXR', 'CYM', 'CYP', 'CZE', 'DEU', 'DJI', 'DMA', 'DNK', 'DOM', 'DZA', 'ECU', 'EGY', 'ERI', 'ESH', 'ESP', 'EST', 'ETH', 'FIN', 'FJI', 'FLK', 'FRA', 'FRO', 'FSM', 'GAB', 'GBR', 'GEO', 'GGY', 'GHA', 'GIB', 'GIN', 'GLP', 'GMB', 'GNB', 'GNQ', 'GRC', 'GRD', 'GRL', 'GTM', 'GUF', 'GUM', 'GUY', 'HKG', 'HMD', 'HND', 'HRV', 'HTI', 'HUN', 'IDN', 'IMN', 'IND', 'IOT', 'IRL', 'IRN', 'IRQ', 'ISL', 'ISR', 'ITA', 'JAM', 'JEY', 'JOR', 'JPN', 'KAZ', 'KEN', 'KGZ', 'KHM', 'KIR', 'KNA', 'KOR', 'KWT', 'LAO', 'LBN', 'LBR', 'LBY', 'LCA', 'LIE', 'LKA', 'LSO', 'LTU', 'LUX', 'LVA', 'MAC', 'MAF', 'MAR', 'MCO', 'MDA', 'MDG', 'MDV', 'MEX', 'MHL', 'MKD', 'MLI', 'MLT', 'MMR', 'MNE', 'MNG', 'MNP', 'MOZ', 'MRT', 'MSR', 'MTQ', 'MUS', 'MWI', 'MYS', 'MYT', 'NAM', 'NCL', 'NER', 'NFK', 'NGA', 'NIC', 'NIU', 'NLD', 'NOR', 'NPL', 'NRU', 'NZL', 'OMN', 'PAK', 'PAN', 'PCN', 'PER', 'PHL', 'PLW', 'PNG', 'POL', 'PRI', 'PRK', 'PRT', 'PRY', 'PSE', 'PYF', 'QAT', 'REU', 'ROU', 'RUS', 'RWA', 'SAU', 'SCG', 'SDN', 'SEN', 'SGP', 'SGS', 'SHN', 'SJM', 'SLB', 'SLE', 'SLV', 'SMR', 'SOM', 'SPM', 'SRB', 'SSD', 'STP', 'SUR', 'SVK', 'SVN', 'SWE', 'SWZ', 'SXM', 'SYC', 'SYR', 'TCA', 'TCD', 'TGO', 'THA', 'TJK', 'TKL', 'TKM', 'TLS', 'TON', 'TTO', 'TUN', 'TUR', 'TUV', 'TWN', 'TZA', 'UGA', 'UKR', 'UMI', 'UNK', 'URY', 'USA', 'UZB', 'VAT', 'VCT', 'VEN', 'VGB', 'VIR', 'VNM', 'VUT', 'WLF', 'WSM', 'YEM', 'YYY', 'ZAF', 'ZMB', 'ZWE', 'ZZZ']) | False | False | True |  |
| currency_code | Categorical(ordering='physical') | False | False | True |  |
| hash_acquisition_properties | Categorical(ordering='physical') | False | False | True |  |
| portal_platform_region | Categorical(ordering='physical') | False | False | True |  |

# ObservationVisibilityTable
table name: *observation_visibility*

partitioned by: *TableNameType, StudioId, Portal, TableVersion*

version: *v1*

## Columns

| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |
| page_category | Categorical(ordering='physical') | False | False | True |  |
| page_feature | Categorical(ordering='physical') | False | False | True |  |
| impressions | Int64 | False | False | True |  |
| visits | Int64 | False | False | True |  |
| owner_impressions | Int64 | False | False | True |  |
| owner_visits | Int64 | False | False | True |  |
| sku_id | String | False | False | True |  |
| date | Date | False | False | True |  |
| human_name | Categorical(ordering='physical') | False | False | True |  |
| platform | Enum(categories=['PC', 'Key', 'Quest', 'Rift', 'Switch', 'Wii U', 'Home', 'PS Vita', 'PSone', 'PS2', 'PS3', 'PS4', 'PS5', 'PSP', 'PS VR', 'PS VR2', 'miniS', 'Microsoft', 'Desktop', 'iPad', 'iPhone', 'iPod touch', 'Google', 'Unknown']) | False | False | True |  |
| portal | Enum(categories=['Apple', 'Epic', 'GOG', 'Google', 'Humble', 'Meta', 'Microsoft', 'Nintendo', 'PlayStation', 'Steam']) | False | False | True |  |
| store_id | Categorical(ordering='physical') | False | False | True |  |
| region | Enum(categories=['Global', 'Nintendo Asia', 'Nintendo America', 'Nintendo Europe', 'Nintendo Japan', 'Nintendo China', 'PS America', 'PS Asia', 'PS Europe', 'PS Japan', 'PS Unknown']) | False | False | True |  |
| store | Enum(categories=['Epic', 'GOG', 'Humble', 'Microsoft', 'Nintendo Switch Asia', 'Nintendo Switch America', 'Nintendo Switch Europe', 'Nintendo Switch Japan', 'Nintendo Switch Unknown', 'Nintendo Other Asia', 'Nintendo Other America', 'Nintendo Other Europe', 'Nintendo Other Japan', 'Nintendo Other Unknown', 'Meta Rift', 'Meta Quest', 'PlayStation America', 'PlayStation Asia', 'PlayStation Europe', 'PlayStation Japan', 'PlayStation Unknown', 'Steam', 'Apple', 'Google']) | False | False | True |  |
| abbreviated_name | Categorical(ordering='physical') | False | False | True |  |
| navigation | String | False | False | True |  |
| unique_sku_id | Categorical(ordering='physical') | False | False | True |  |
| report_id | Int64 | False | False | True |  |
| studio_id | Int64 | False | False | True |  |
| portal_platform_region | Categorical(ordering='physical') | False | False | True |  |
| hash_traffic_source | Categorical(ordering='physical') | False | False | True |  |

# ObservationWishlistActionsTable
table name: *observation_wishlist_actions*

partitioned by: *TableNameType, StudioId, Portal, TableVersion*

version: *v1*

## Columns

| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |
| platform | Enum(categories=['PC', 'Key', 'Quest', 'Rift', 'Switch', 'Wii U', 'Home', 'PS Vita', 'PSone', 'PS2', 'PS3', 'PS4', 'PS5', 'PSP', 'PS VR', 'PS VR2', 'miniS', 'Microsoft', 'Desktop', 'iPad', 'iPhone', 'iPod touch', 'Google', 'Unknown']) | False | False | True |  |
| region | Enum(categories=['Global', 'Nintendo Asia', 'Nintendo America', 'Nintendo Europe', 'Nintendo Japan', 'Nintendo China', 'PS America', 'PS Asia', 'PS Europe', 'PS Japan', 'PS Unknown']) | False | False | True |  |
| portal | Enum(categories=['Apple', 'Epic', 'GOG', 'Google', 'Humble', 'Meta', 'Microsoft', 'Nintendo', 'PlayStation', 'Steam']) | False | False | True |  |
| date | Date | False | False | True |  |
| human_name | Categorical(ordering='physical') | False | False | True |  |
| adds | Int64 | False | False | True |  |
| deletes | Int64 | False | False | True |  |
| purchases_and_activations | Int64 | False | False | True |  |
| gifts | Int64 | False | False | True |  |
| sku_id | String | False | False | True |  |
| store_id | Categorical(ordering='physical') | False | False | True |  |
| store | Enum(categories=['Epic', 'GOG', 'Humble', 'Microsoft', 'Nintendo Switch Asia', 'Nintendo Switch America', 'Nintendo Switch Europe', 'Nintendo Switch Japan', 'Nintendo Switch Unknown', 'Nintendo Other Asia', 'Nintendo Other America', 'Nintendo Other Europe', 'Nintendo Other Japan', 'Nintendo Other Unknown', 'Meta Rift', 'Meta Quest', 'PlayStation America', 'PlayStation Asia', 'PlayStation Europe', 'PlayStation Japan', 'PlayStation Unknown', 'Steam', 'Apple', 'Google']) | False | False | True |  |
| abbreviated_name | Categorical(ordering='physical') | False | False | True |  |
| report_id | Int64 | False | False | True |  |
| studio_id | Int64 | False | False | True |  |
| unique_sku_id | Categorical(ordering='physical') | False | False | True |  |
| country_code | Enum(categories=['ABW', 'AFG', 'AGO', 'AIA', 'ALA', 'ALB', 'AND', 'ANT', 'ARE', 'ARG', 'ARM', 'ASM', 'ATA', 'ATF', 'ATG', 'AUS', 'AUT', 'AZE', 'BDI', 'BEL', 'BEN', 'BES', 'BFA', 'BGD', 'BGR', 'BHR', 'BHS', 'BIH', 'BLM', 'BLR', 'BLZ', 'BMU', 'BOL', 'BRA', 'BRB', 'BRN', 'BTN', 'BVT', 'BWA', 'CAF', 'CAN', 'CCK', 'CHE', 'CHL', 'CHN', 'CIV', 'CMR', 'COD', 'COG', 'COK', 'COL', 'COM', 'CPV', 'CRI', 'CUB', 'CUW', 'CXR', 'CYM', 'CYP', 'CZE', 'DEU', 'DJI', 'DMA', 'DNK', 'DOM', 'DZA', 'ECU', 'EGY', 'ERI', 'ESH', 'ESP', 'EST', 'ETH', 'FIN', 'FJI', 'FLK', 'FRA', 'FRO', 'FSM', 'GAB', 'GBR', 'GEO', 'GGY', 'GHA', 'GIB', 'GIN', 'GLP', 'GMB', 'GNB', 'GNQ', 'GRC', 'GRD', 'GRL', 'GTM', 'GUF', 'GUM', 'GUY', 'HKG', 'HMD', 'HND', 'HRV', 'HTI', 'HUN', 'IDN', 'IMN', 'IND', 'IOT', 'IRL', 'IRN', 'IRQ', 'ISL', 'ISR', 'ITA', 'JAM', 'JEY', 'JOR', 'JPN', 'KAZ', 'KEN', 'KGZ', 'KHM', 'KIR', 'KNA', 'KOR', 'KWT', 'LAO', 'LBN', 'LBR', 'LBY', 'LCA', 'LIE', 'LKA', 'LSO', 'LTU', 'LUX', 'LVA', 'MAC', 'MAF', 'MAR', 'MCO', 'MDA', 'MDG', 'MDV', 'MEX', 'MHL', 'MKD', 'MLI', 'MLT', 'MMR', 'MNE', 'MNG', 'MNP', 'MOZ', 'MRT', 'MSR', 'MTQ', 'MUS', 'MWI', 'MYS', 'MYT', 'NAM', 'NCL', 'NER', 'NFK', 'NGA', 'NIC', 'NIU', 'NLD', 'NOR', 'NPL', 'NRU', 'NZL', 'OMN', 'PAK', 'PAN', 'PCN', 'PER', 'PHL', 'PLW', 'PNG', 'POL', 'PRI', 'PRK', 'PRT', 'PRY', 'PSE', 'PYF', 'QAT', 'REU', 'ROU', 'RUS', 'RWA', 'SAU', 'SCG', 'SDN', 'SEN', 'SGP', 'SGS', 'SHN', 'SJM', 'SLB', 'SLE', 'SLV', 'SMR', 'SOM', 'SPM', 'SRB', 'SSD', 'STP', 'SUR', 'SVK', 'SVN', 'SWE', 'SWZ', 'SXM', 'SYC', 'SYR', 'TCA', 'TCD', 'TGO', 'THA', 'TJK', 'TKL', 'TKM', 'TLS', 'TON', 'TTO', 'TUN', 'TUR', 'TUV', 'TWN', 'TZA', 'UGA', 'UKR', 'UMI', 'UNK', 'URY', 'USA', 'UZB', 'VAT', 'VCT', 'VEN', 'VGB', 'VIR', 'VNM', 'VUT', 'WLF', 'WSM', 'YEM', 'YYY', 'ZAF', 'ZMB', 'ZWE', 'ZZZ']) | False | False | True |  |
| portal_platform_region | Categorical(ordering='physical') | False | False | True |  |

# ObservationWishlistCohortsTable
table name: *observation_wishlist_cohorts*

partitioned by: *TableNameType, StudioId, Portal, TableVersion*

version: *v1*

## Columns

| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |
| platform | Enum(categories=['PC', 'Key', 'Quest', 'Rift', 'Switch', 'Wii U', 'Home', 'PS Vita', 'PSone', 'PS2', 'PS3', 'PS4', 'PS5', 'PSP', 'PS VR', 'PS VR2', 'miniS', 'Microsoft', 'Desktop', 'iPad', 'iPhone', 'iPod touch', 'Google', 'Unknown']) | False | False | True |  |
| region | Enum(categories=['Global', 'Nintendo Asia', 'Nintendo America', 'Nintendo Europe', 'Nintendo Japan', 'Nintendo China', 'PS America', 'PS Asia', 'PS Europe', 'PS Japan', 'PS Unknown']) | False | False | True |  |
| portal | Enum(categories=['Apple', 'Epic', 'GOG', 'Google', 'Humble', 'Meta', 'Microsoft', 'Nintendo', 'PlayStation', 'Steam']) | False | False | True |  |
| date | Date | False | False | True |  |
| human_name | Categorical(ordering='physical') | False | False | True |  |
| month_cohort | String | False | False | True |  |
| total_conversions | Int64 | False | False | True |  |
| purchases_and_activations | Int64 | False | False | True |  |
| gifts | Int64 | False | False | True |  |
| sku_id | String | False | False | True |  |
| store_id | Categorical(ordering='physical') | False | False | True |  |
| store | Enum(categories=['Epic', 'GOG', 'Humble', 'Microsoft', 'Nintendo Switch Asia', 'Nintendo Switch America', 'Nintendo Switch Europe', 'Nintendo Switch Japan', 'Nintendo Switch Unknown', 'Nintendo Other Asia', 'Nintendo Other America', 'Nintendo Other Europe', 'Nintendo Other Japan', 'Nintendo Other Unknown', 'Meta Rift', 'Meta Quest', 'PlayStation America', 'PlayStation Asia', 'PlayStation Europe', 'PlayStation Japan', 'PlayStation Unknown', 'Steam', 'Apple', 'Google']) | False | False | True |  |
| abbreviated_name | Categorical(ordering='physical') | False | False | True |  |
| report_id | Int64 | False | False | True |  |
| unique_sku_id | Categorical(ordering='physical') | False | False | True |  |
| studio_id | Int64 | False | False | True |  |
| portal_platform_region | Categorical(ordering='physical') | False | False | True |  |

# SilverAcquisitionPropertiesTable
table name: *silver_acquisition_properties*

partitioned by: *TableNameType, StudioId, Portal, TableVersion*

version: *v1*

## Columns

| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |
| transaction_type | String | False | False | True |  - str_length(1, 255) |
| tax_type | String | False | False | True |  - str_length(1, 255) |
| sale_modificator | String | False | False | True |  - str_length(1, 32) |
| acquisition_platform | String | False | False | True |  - str_length(1, 255) |
| acquisition_origin | String | False | False | True |  - str_length(1, 255) |
| iap_flag | String | False | False | True |  - str_length(1, 32) |
| hash_acquisition_properties | String | False | False | True |  - str_length(32, 32) |

# SilverPortalsTable
table name: *silver_portals*

partitioned by: *TableNameType, StudioId, Portal, TableVersion*

version: *v1*

## Columns

| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |
| portal | String | False | False | True |  - str_length(1, 255) |
| platform | String | False | False | True |  - str_length(1, 255) |
| region | String | False | False | True |  - str_length(1, 255) |
| abbreviated_name | String | False | False | True |  - str_length(1, 255) |
| portal_platform_region | String | False | False | True |  - str_length(1, 511) |
| store_name | String | False | False | True |  - str_length(1, 255) |

# SilverReportsTable
table name: *silver_reports*

partitioned by: *TableNameType, StudioId, Portal, ObservationType, TableVersion*

version: *v1*

## Columns

| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |
| report_id | Int64 | False | False | True |  - greater_than_or_equal_to(0) |
| studio_id | Int64 | False | False | True |  - greater_than_or_equal_to(0) |
| source | String | False | False | True |  - isin([<Source.APP_STORE_SALES: 'app_store_sales'>, <Source.EPIC_SALES: 'epic_sales'>, <Source.GOG_SALES: 'gog_sales'>, <Source.GOOGLE_SALES: 'google_sales'>, <Source.HUMBLE_SALES: 'humble_sales'>, <Source.META_QUEST_SALES: 'meta_quest_sales'>, <Source.META_RIFT_SALES: 'meta_rift_sales'>, <Source.MICROSOFT_SALES: 'microsoft_sales'>, <Source.NINTENDO_DISCOUNTS: 'nintendo_discounts'>, <Source.NINTENDO_SALES: 'nintendo_sales'>, <Source.PLAYSTATION_SALES: 'playstation_sales'>, <Source.PLAYSTATION_WISHLIST_ACTIONS: 'playstation_wishlist_actions'>, <Source.STEAM_DISCOUNTS: 'steam_discounts'>, <Source.STEAM_IMPRESSIONS: 'steam_impressions'>, <Source.STEAM_SALES: 'steam_sales'>, <Source.STEAM_WISHLIST_ACTIONS: 'steam_wishlist_actions'>, <Source.STEAM_WISHLIST_COHORTS: 'steam_wishlist_cohorts'>, <Source.STEAM_WISHLIST_COUNTRY: 'steam_wishlist_country'>]) |
| portal | String | False | False | True |  - isin([<Portal.APPLE: 'apple'>, <Portal.EPIC: 'epic'>, <Portal.GOG: 'gog'>, <Portal.GOOGLE: 'google'>, <Portal.HUMBLE: 'humble'>, <Portal.META: 'meta'>, <Portal.MICROSOFT: 'microsoft'>, <Portal.NINTENDO: 'nintendo'>, <Portal.PLAYSTATION: 'playstation'>, <Portal.STEAM: 'steam'>]) |
| observation_type | String | False | False | True |  - isin([<ObservationType.SALES: 'sales'>, <ObservationType.WISHLIST_ACTIONS: 'wishlist_actions'>, <ObservationType.WISHLIST_COHORTS: 'wishlist_cohorts'>, <ObservationType.VISIBILITY: 'visibility'>, <ObservationType.DISCOUNTS: 'discounts'>]) |
| date_from | Date | False | False | True |  |
| date_to | Date | False | False | True |  |
| upload_date | Datetime(time_unit='us', time_zone='UTC') | False | False | True |  |
| blob_name | String | False | False | True |  |
| original_name | String | False | False | True |  |
| state | String | False | False | True |  - isin([<ReportState.PENDING: 'PENDING'>, <ReportState.FAILED: 'FAILED'>, <ReportState.CONVERTED: 'CONVERTED'>, <ReportState.DELETED: 'DELETED'>]) |
| no_data | Boolean | False | False | True |  |

# SilverSKUsTable
table name: *silver_skus*

partitioned by: *TableNameType, StudioId, Portal, TableVersion*

version: *v1*

## Columns

| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |
| unique_sku_id | String | False | False | True |  - str_length(1, 511) |
| base_sku_id | String | False | False | True |  - str_length(1, 255) |
| portal_platform_region | String | False | False | True |  - str_length(1, 511) |
| human_name | String | False | False | True |  - str_length(1, 511) |
| store_id | String | False | False | True |  - str_length(1, 255) |
| studio_id | Int64 | False | False | True |  |
| human_name_indicator | String | False | False | True |  - str_length(1, 255) |
| sku_type | String | False | False | True |  - str_length(1, 255) |
| product_name | String | True | False | True |  - str_length(1, 511) |
| product_type | String | True | False | True |  - str_length(1, 511) |
| release_date | Date | True | False | True |  |

# SilverTrafficSourceTable
table name: *silver_traffic_source*

partitioned by: *TableNameType, StudioId, Portal, TableVersion*

version: *v1*

## Columns

| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |
| page_category | String | False | False | True |  - str_length(1, 255) |
| page_category_group | String | False | False | True |  - str_length(1, 255) |
| page_feature | String | False | False | True |  - str_length(1, 255) |
| hash_traffic_source | String | False | False | True |  - str_length(32, 32) |

# LegacyDimPortalsTable
table name: *dim_portals*

partitioned by: *TableNameType, StudioId, Portal, TableVersion*

version: *v1*

## Columns

| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |

# LegacyDimSKUsTable
table name: *dim_sku*

partitioned by: *TableNameType, StudioId, Portal, TableVersion*

version: *v1*

## Columns

| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |

# LegacyDimSourceFileTable
table name: *dim_source_file*

partitioned by: *TableNameType, StudioId, Portal, TableVersion*

version: *v1*

## Columns

| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |

# LegacyDimStudioTable
table name: *dim_studio*

partitioned by: *TableNameType, StudioId, TableVersion*

version: *v1*

## Columns

| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |

# LegacyFactBaselineTable
table name: *fact_baseline*

partitioned by: *TableNameType, StudioId, Portal, TableVersion*

version: *v1*

## Columns

| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |

# LegacyFactDetectedEventsTable
table name: *fact_detected_events*

partitioned by: *TableNameType, StudioId, Portal, TableVersion*

version: *v1*

## Columns

| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |

# LegacyFactDiscountsTable
table name: *fact_discounts*

partitioned by: *TableNameType, StudioId, Portal, TableVersion*

version: *v1*

## Columns

| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |

# LegacyFactEventDayTable
table name: *fact_event_day*

partitioned by: *TableNameType, StudioId, Portal, TableVersion*

version: *v1*

## Columns

| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |
| discount | Float64 | False | False | True |  - greater_than_or_equal_to(0) |
| date | Date | False | False | True |  |
| unique_sku_id | String | False | False | True |  - str_length(1, 511) |
| portal_platform_region_id | Int64 | False | False | True |  - greater_than_or_equal_to(101000)<br> - less_than_or_equal_to(999999) |
| studio_id | Int64 | False | False | True |  - greater_than_or_equal_to(0) |
| date_from | Date | False | False | True |  |
| date_to | Date | False | False | True |  |
| event_day_number | Float64 | False | False | True |  - greater_than_or_equal_to(0)<br> - less_than_or_equal_to(1) |
| event_status | String | False | False | True |  - str_length(1, 255) |
| event_id | String | False | False | True |  - str_length(1, 511) |
| type | String | False | False | True |  - str_length(1, 255) |
| event_name | String | False | False | True |  - str_length(1, 511) |
| event_description | String | True | False | True |  - str_length(1, 1023) |
| dates_short | String | False | False | True |  - str_length(1, 255) |
| promo_length | Int64 | False | False | True |  - greater_than_or_equal_to(0) |
| days_since_previous_discount | Int64 | False | False | True |  - greater_than_or_equal_to(0) |

# LegacyFactSalesTable
table name: *fact_sales*

partitioned by: *TableNameType, StudioId, Portal, TableVersion*

version: *v1*

## Columns

| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |

# LegacyFactVisibilityTable
table name: *fact_visibility*

partitioned by: *TableNameType, StudioId, Portal, TableVersion*

version: *v1*

## Columns

| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |

# LegacyFactWishlistActionsTable
table name: *fact_wishlist_actions*

partitioned by: *TableNameType, StudioId, Portal, TableVersion*

version: *v1*

## Columns

| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |

# LegacyFactWishlistCohortsTable
table name: *fact_wishlist_cohorts*

partitioned by: *TableNameType, StudioId, Portal, TableVersion*

version: *v1*

## Columns

| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |

# ExternalCountryCodesTable

Static table generated using `data-jobs/jobs/core_silver/core_silver/external_sources/static/country_codes.csv` file.


table name: *external_country_codes*

partitioned by: *TableNameType, StudioId, TableVersion*

version: *v1*

## Columns

| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |
| country | String | False | False | True |  |
| alpha_2_code | String | True | False | False |  - str_length(2, 2) |
| country_code | String | False | False | True |  |
| numeric | Int64 | False | False | True |  |
| country_currency | String | False | False | True |  |
| region | String | True | False | True |  |

# ExternalCurrencyExchangeRatesTable
table name: *external_currency_exchange_rates*

partitioned by: *TableNameType, TableVersion*

version: *v1*

## Columns

| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |
| date | Date | False | False | True |  |
| currency_code | String | False | False | True |  |
| rate_to_usd | Float64 | False | False | True |  - greater_than_or_equal_to(0) |

# ExternalReportsTable

Reports table fetched from report_service_v2. State before running any coversions.
Used internally to detect if any report state was changed during core silver run.
You probably want to use [SilverReportsTable](#silver-reports-table) instead.


table name: *external_reports*

partitioned by: *TableNameType, StudioId, TableVersion*

version: *v1*

## Columns

| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |
| report_id | Int64 | False | False | True |  - greater_than_or_equal_to(0) |
| studio_id | Int64 | False | False | True |  - greater_than_or_equal_to(0) |
| source | String | False | False | True |  - isin([<Source.APP_STORE_SALES: 'app_store_sales'>, <Source.EPIC_SALES: 'epic_sales'>, <Source.GOG_SALES: 'gog_sales'>, <Source.GOOGLE_SALES: 'google_sales'>, <Source.HUMBLE_SALES: 'humble_sales'>, <Source.META_QUEST_SALES: 'meta_quest_sales'>, <Source.META_RIFT_SALES: 'meta_rift_sales'>, <Source.MICROSOFT_SALES: 'microsoft_sales'>, <Source.NINTENDO_DISCOUNTS: 'nintendo_discounts'>, <Source.NINTENDO_SALES: 'nintendo_sales'>, <Source.PLAYSTATION_SALES: 'playstation_sales'>, <Source.PLAYSTATION_WISHLIST_ACTIONS: 'playstation_wishlist_actions'>, <Source.STEAM_DISCOUNTS: 'steam_discounts'>, <Source.STEAM_IMPRESSIONS: 'steam_impressions'>, <Source.STEAM_SALES: 'steam_sales'>, <Source.STEAM_WISHLIST_ACTIONS: 'steam_wishlist_actions'>, <Source.STEAM_WISHLIST_COHORTS: 'steam_wishlist_cohorts'>, <Source.STEAM_WISHLIST_COUNTRY: 'steam_wishlist_country'>]) |
| portal | String | False | False | True |  - isin([<Portal.APPLE: 'apple'>, <Portal.EPIC: 'epic'>, <Portal.GOG: 'gog'>, <Portal.GOOGLE: 'google'>, <Portal.HUMBLE: 'humble'>, <Portal.META: 'meta'>, <Portal.MICROSOFT: 'microsoft'>, <Portal.NINTENDO: 'nintendo'>, <Portal.PLAYSTATION: 'playstation'>, <Portal.STEAM: 'steam'>]) |
| observation_type | String | False | False | True |  - isin([<ObservationType.SALES: 'sales'>, <ObservationType.WISHLIST_ACTIONS: 'wishlist_actions'>, <ObservationType.WISHLIST_COHORTS: 'wishlist_cohorts'>, <ObservationType.VISIBILITY: 'visibility'>, <ObservationType.DISCOUNTS: 'discounts'>]) |
| date_from | Date | False | False | True |  |
| date_to | Date | False | False | True |  |
| upload_date | Datetime(time_unit='us', time_zone='UTC') | False | False | True |  |
| blob_name | String | False | False | True |  |
| original_name | String | False | False | True |  |
| state | String | False | False | True |  - isin([<ReportState.PENDING: 'PENDING'>, <ReportState.FAILED: 'FAILED'>, <ReportState.CONVERTED: 'CONVERTED'>, <ReportState.DELETED: 'DELETED'>]) |
| no_data | Boolean | False | False | True |  |

# ExternalSKUsTable

SKUs fetched from report_service_v2 saved without any modifications.


table name: *external_skus*

partitioned by: *TableNameType, StudioId, TableVersion*

version: *v1*

## Columns

| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |
| store_id | String | False | False | True |  |
| unique_sku_id | String | False | False | True |  |
| studio_id | Int64 | False | False | True |  |
| product_name | String | True | False | False |  |
| custom_group | String | True | False | False |  |
| ratio | Int64 | False | False | True |  - greater_than_or_equal_to(0) |
| base_sku_id | String | False | False | True |  |
| human_name | String | False | False | True |  |
| package_name | String | True | False | False |  |
| product_type | String | True | False | False |  |
| portal | String | False | False | True |  - isin([<Portal.APPLE: 'apple'>, <Portal.EPIC: 'epic'>, <Portal.GOG: 'gog'>, <Portal.GOOGLE: 'google'>, <Portal.HUMBLE: 'humble'>, <Portal.META: 'meta'>, <Portal.MICROSOFT: 'microsoft'>, <Portal.NINTENDO: 'nintendo'>, <Portal.PLAYSTATION: 'playstation'>, <Portal.STEAM: 'steam'>]) |
| sku_type | String | False | False | True |  - isin([<SKUType.SALES: 'SALES'>, <SKUType.STORE: 'STORE'>]) |
| description: | Source of SKU |||||
| human_name_indicator | String | False | False | True |  |

# ExternalSharedTable

Shared table generated based on role assignments fetched from report_service_v2.


table name: *external_shared*

partitioned by: *TableNameType, StudioId, TableVersion*

version: *v1*

## Columns

| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |
| id | Int64 | False | False | True |  - greater_than_or_equal_to(0) |
| shared_with_id | Int64 | False | False | True |  - greater_than_or_equal_to(0) |
| shared_by_id | Int64 | False | False | True |  - greater_than_or_equal_to(0) |
| product_name | String | True | False | False |  |
| date_from | Datetime(time_unit='us', time_zone=None) | False | False | True |  |
| date_to | Datetime(time_unit='us', time_zone=None) | False | False | True |  |
| portal_id | Null | True | False | False |  |
| game_id | Null | True | False | False |  |

# ExternalSteamEventsTable
table name: *external_steam_events*

partitioned by: *TableNameType, StudioId, TableVersion*

version: *v1*

## Columns

| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |
| start_year | Int64 | False | False | True |  |
| start_date | Date | False | False | True |  |
| end_date | Date | False | False | True |  |
| major | Boolean | False | False | True |  |
| name | String | False | False | True |  |
| start_day | Int64 | False | False | True |  |
| start_month | Int64 | False | False | True |  |
| end_day | Int64 | False | False | True |  |
| end_month | Int64 | False | False | True |  |
| end_year | Int64 | False | False | True |  |

# ExternalStudiosTable

Studio data fetched from report_service_v2 saved without any modifications.


table name: *external_studios*

partitioned by: *TableNameType, StudioId, TableVersion*

version: *v1*

## Columns

| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |
| studio_id | Int64 | False | False | True |  - greater_than_or_equal_to(0) |
| organization_id | String | False | False | True |  |
| email | String | False | False | True |  |
| company_name | String | False | False | True |  |
| is_test_account | Boolean | False | False | True |  |
| is_verified | Boolean | False | False | True |  |
| agreement_date | Datetime(time_unit='us', time_zone='UTC') | False | False | True |  |
| studio_parent_id | Int64 | True | False | False |  |
