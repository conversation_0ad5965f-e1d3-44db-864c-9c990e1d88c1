from pathlib import Path

from data_sdk.domain.tables import TableDefinition


def _get_all_subclasses(klass):
    all_subclasses = []
    for subclass in klass.__subclasses__():
        all_subclasses.append(subclass)
        all_subclasses.extend(_get_all_subclasses(subclass))
    return all_subclasses


def sort_classes_by_groups(classes, groups):
    return [
        klass
        for group in groups
        for klass in sorted(classes, key=lambda x: x.__name__)
        if klass.__name__.startswith(group)
    ]


def generate_markdown(table: type[TableDefinition]) -> str:
    model = table.model

    markdown_lines = []
    markdown_lines.append(f"# {table.__name__}")

    if table.__doc__:
        docs_without_spaces = "\n".join(
            line.lstrip() for line in table.__doc__.split("\n")
        )
        markdown_lines.append(docs_without_spaces)
        markdown_lines.append("")

    markdown_lines.append(f"table name: *{table.table_name.value}*\n")
    markdown_lines.append(
        f"partitioned by: *{', '.join([partition.__name__ for partition in table.partitions])}*\n"
    )
    markdown_lines.append(f"version: *{table.version}*\n")

    markdown_lines.append("## Columns")
    columns_table_header = """
| Column name     | dtype | nullable | unique | required | checks |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |"""
    markdown_lines.append(columns_table_header)

    for field_name, field in model.to_schema().columns.items():
        checks = ""
        if field.checks:
            checks = " - "
            checks += "<br> - ".join([x.error for x in field.checks])
        field_row = f"| {field_name} | {field.dtype} | {field.nullable} | {field.unique} | {field.required} | {checks} |"
        markdown_lines.append(field_row)
        if field.description:
            markdown_lines.append(f"| description: | {field.description} |||||")

    return "\n".join(markdown_lines) + "\n"


def run():
    docs_path = Path("docs/silver_tables.md")
    documentation = "# Silver Tables\n"
    all_tables = _get_all_subclasses(TableDefinition)
    tables = [x for x in all_tables if hasattr(x, "model")]
    groups = ["Observation", "Silver", "Legacy", "External"]
    sorted_tables = sort_classes_by_groups(tables, groups)

    documentation += "\n".join([generate_markdown(table) for table in sorted_tables])

    docs_path.parent.mkdir(exist_ok=True)

    docs_path.write_text(documentation)


if __name__ == "__main__":
    run()
