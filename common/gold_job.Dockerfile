ARG PYTHON_VERSION
ARG BASE_DISTRO

FROM python:$PYTHON_VERSION-$BASE_DISTRO as builder

# 🚀 Optimized for size and build speed using techniques from 
# https://indiebi.atlassian.net/wiki/spaces/DPT/pages/362512422/Optimizing+Dockerfiles+for+Python

ENV PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PIP_NO_CACHE_DIR=1

RUN pip install poetry && \
    poetry config virtualenvs.in-project true

ARG DATA_JOB_NAME
ENV DATA_JOB_NAME=$DATA_JOB_NAME

COPY jobs/${DATA_JOB_NAME}/poetry.lock jobs/${DATA_JOB_NAME}/pyproject.toml jobs/${DATA_JOB_NAME}/README.md /app/jobs/${DATA_JOB_NAME}/

COPY libs/data-sdk/poetry.lock libs/data-sdk/pyproject.toml libs/data-sdk/README.md /app/libs/data-sdk/
COPY libs/data-sdk/data_sdk/__init__.py /app/libs/data-sdk/data_sdk/

WORKDIR /app/jobs/${DATA_JOB_NAME}

RUN --mount=type=secret,id=poetry-auth,target=/kaniko/poetry-auth.toml \
    test -f /kaniko/poetry-auth.toml && ln -sf /kaniko/poetry-auth.toml /root/.config/pypoetry/auth.toml && \ 
    poetry install --no-interaction --no-root --no-cache --only main

FROM python:$PYTHON_VERSION-slim-$BASE_DISTRO

RUN apt-get update && apt-get install -y curl
RUN groupadd --gid 1000 service && useradd --uid 1000 --gid 1000 -r -g service service

ARG DATA_JOB_NAME
ENV DATA_JOB_NAME=$DATA_JOB_NAME

COPY --from=builder --chown=service:service /app/jobs/${DATA_JOB_NAME}/.venv /app/jobs/${DATA_JOB_NAME}/.venv
# Use .dockerignore in the directory of each data-job to control what gets copied into the image
COPY --chown=service:service . /app

# Copy definition.json to becasue it is required to be in /app folder
COPY --chown=service:service jobs/${DATA_JOB_NAME}/definition.json /app/definition.json

WORKDIR /app/jobs/${DATA_JOB_NAME}/

CMD python -m ${DATA_JOB_NAME}.main

USER service

ARG DOCKER_TAG
ARG DOCKER_BUILD_TIMESTAMP

ENV PATH=/app/jobs/${DATA_JOB_NAME}/.venv/bin:$PATH \
    PYTHONPATH=/app:$PYTHONPATH \
    DOCKER_TAG=$DOCKER_TAG \
    DOCKER_BUILD_TIMESTAMP=$DOCKER_BUILD_TIMESTAMP \
    SENTRY_RELEASE=$DOCKER_TAG
