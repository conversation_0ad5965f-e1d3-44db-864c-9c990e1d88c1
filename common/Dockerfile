ARG PYTHON_VERSION
ARG BASE_DISTRO

FROM python:$PYTHON_VERSION-$BASE_DISTRO as builder

# 🚀 Optimized for size and build speed using techniques from 
# https://indiebi.atlassian.net/wiki/spaces/DPT/pages/362512422/Optimizing+Dockerfiles+for+Python

ENV PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PIP_NO_CACHE_DIR=1

WORKDIR /app

RUN pip install poetry && \
    poetry config virtualenvs.in-project true

COPY poetry.lock pyproject.toml /app/

# if we're building with Docker, mount auth.toml as a secret
# if we're building with Kaniko and a poetry-auth.toml file is present, link it to the right spot
#   linking won't pollute the target image with auth secrets
RUN --mount=type=secret,id=poetry-auth,target=/kaniko/poetry-auth.toml \
    test -f /kaniko/poetry-auth.toml && ln -sf /kaniko/poetry-auth.toml /root/.config/pypoetry/auth.toml && \ 
    poetry install --no-interaction --no-root --no-cache

FROM python:$PYTHON_VERSION-slim-$BASE_DISTRO

RUN apt-get update && apt-get install -y curl
RUN groupadd --gid 1000 service && useradd --uid 1000 --gid 1000 -r -g service service

WORKDIR /app

COPY --from=builder --chown=service:service /app/.venv /app/.venv
# Use .dockerignore in the directory of each data-job to control what gets copied into the image
COPY --chown=service:service . /app/

ARG DATA_JOB_NAME
ENV DATA_JOB_NAME=$DATA_JOB_NAME

CMD python -m ${DATA_JOB_NAME}.main

USER service

ARG DOCKER_TAG
ARG DOCKER_BUILD_TIMESTAMP

ENV PATH=/app/.venv/bin:$PATH \ 
    PYTHONPATH=/app:$PYTHONPATH \
    DOCKER_TAG=$DOCKER_TAG \
    DOCKER_BUILD_TIMESTAMP=$DOCKER_BUILD_TIMESTAMP \
    SENTRY_RELEASE=$DOCKER_TAG
