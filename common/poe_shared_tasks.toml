[tool.poe.tasks]
test = ["_test", "_check_dead_fixtures"]
slow-test = "pytest -vvv --random-order --color=yes --runslow --log-cli-level=info"
_lint = "ruff check --fix ."
_format = "ruff format"
lint = ["_lint", "_format"]
check = ["_lint", "_format", "test"]
# 'hooks' task should be defined in each job's separatly, like this
# hooks = { "shell" = "../../.hooks/install.sh 'jobs/<job_name>'" }
_test = "pytest -vvv --random-order --color=yes -n auto"
_check_dead_fixtures = "pytest --dead-fixtures"

# tasks used in CI
ci-test = "pytest -vvv --color=yes -n 4 --junitxml pytest.xml"
ci-slow-test = ["slow-test"]
ci-lint = ["_ci-lint", "_ci-format", "_ci-fixtures"]
_ci-lint = "ruff check ."
_ci-format = "ruff format --check ."
_ci-fixtures = ["_check_dead_fixtures"]
