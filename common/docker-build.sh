#!/bin/bash

# Use this script to build data jobs from this repo locally.
# Usage: ./docker-build.sh <job-dir-name>

if [ -z "$1" ]; then
  echo "Please provide a job directory name."
  exit 1
fi

if [ ! -d "jobs/$1" ]; then
  echo "Job directory $1 does not exist."
  exit 1
fi

PYTHON_VERSION=${PYTHON_VERSION:-"3.10"}
BASE_DISTRO=${BASE_DISTRO:-"bullseye"}

docker build -t $1 -f common/Dockerfile jobs/$1 \
    --build-arg PYTHON_VERSION=$PYTHON_VERSION \
    --build-arg BASE_DISTRO=$BASE_DISTRO \
    --build-arg DATA_JOB_NAME=$1 \
    --build-arg DOCKER_TAG=local \
    --build-arg DOCKER_BUILD_TIMESTAMP=$(date +%s) \
    --secret id=poetry-auth,src=$HOME/.config/pypoetry/auth.toml

