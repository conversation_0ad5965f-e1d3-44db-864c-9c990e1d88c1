#!/bin/bash

# Default values
JOBS_DIR="jobs"
SDKS=()
ENSURE_PYTHON=false
UPDATE_SDKS=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --sdks)
            IFS=',' read -ra SDKS <<< "$2"
            UPDATE_SDKS=true
            shift 2
            ;;
        --ensure-python)
            ENSURE_PYTHON=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [--sdks SDK1,SDK2,...] [--ensure-python]"
            echo "  --sdks: Comma-separated list of SDKs to update (must be specified explicitly)"
            echo "  --ensure-python: Ensure Python environment version based on pyproject.toml version"
            echo "Examples:"
            echo "  $0 --sdks pipeline-sdk,data-sdk"
            echo "  $0 --ensure-python"
            echo "  $0 --sdks pipeline-sdk --ensure-python"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

get_python_version() {
    local project_dir="$1"
    local pyproject_file="$project_dir/pyproject.toml"

    # Look for python version in requires-python or python dependency
    local python_version=$(grep -E '^python\s*=|requires-python\s*=' "$pyproject_file" | head -1 | sed -E 's/.*["\^>=~<! ]*([0-9]+\.[0-9]+).*/\1/')

    if [[ -n "$python_version" && "$python_version" =~ ^[0-9]+\.[0-9]+$ ]]; then
        echo "$python_version"
    else
        echo ""
    fi
}

ensure_python_env() {
    local project_dir="$1"

    echo "Ensuring Python version for $project_dir..."

    local python_version=$(get_python_version "$project_dir")

    if [[ -z "$python_version" ]]; then
        echo "  ⚠ No Python version found in pyproject.toml, skipping"
        return
    fi

    echo "  Found Python version: $python_version"

    cd "$project_dir"

    echo "  Setting poetry environment to python$python_version..."
    if poetry env use "python$python_version"; then
        echo "  ✓ Successfully set Python environment to $python_version"

        echo "  Installing dependencies..."
        if poetry install; then
            echo "  ✓ Successfully installed dependencies"
        else
            echo "  ✗ Failed to install dependencies"
        fi
    else
        echo "  ✗ Failed to set Python environment to $python_version"
    fi

    cd - > /dev/null
}

# Function to check if SDK is installed via local path
is_local_path_sdk() {
    local project_dir="$1"
    local sdk="$2"
    local pyproject_file="$project_dir/pyproject.toml"

    # Check if the SDK line contains "path =" indicating local development install
    if grep -q "$sdk.*path.*=" "$pyproject_file"; then
        return 0  # true - it's a local path SDK
    else
        return 1  # false - it's a regular dependency
    fi
}

update_sdks() {
    local project_dir="$1"
    local updated_any=false

    echo "Updating SDKs for $project_dir..."

    # Check which SDKs are present in the project
    for sdk in "${SDKS[@]}"; do
        # Skip if SDK not found in project
        if ! grep -q "$sdk" "$project_dir/pyproject.toml"; then
            continue
        fi

        # Skip if it's a local path SDK
        if is_local_path_sdk "$project_dir" "$sdk"; then
            echo "  Found $sdk (local path) - skipping update"
            continue
        fi

        echo "  Found $sdk, determining latest version..."
        cd "$project_dir"

        # First, determine the latest version using --dry-run
        local latest_version=$(poetry add --source indiebi "$sdk@latest" --dry-run 2>/dev/null | grep "Using version" | sed -E 's/.*Using version \^?([0-9]+\.[0-9]+\.[0-9]+).*/\1/')

        # Skip if we couldn't determine the latest version
        if [[ -z "$latest_version" ]]; then
            echo "  ⚠ Could not determine latest version of $sdk, skipping update"
            cd - > /dev/null
            continue
        fi

        echo "  Latest version found: $latest_version"
        echo "  Updating $sdk to version $latest_version..."

        # Skip if the update fails
        if ! poetry add --source indiebi "$sdk==$latest_version"; then
            echo "  ✗ Failed to update $sdk to $latest_version"
            cd - > /dev/null
            continue
        fi

        echo "  ✓ Successfully updated $sdk to $latest_version"

        updated_any=true
        cd - > /dev/null
    done

    if [[ "$updated_any" == false ]]; then
        echo "  No target SDKs found for update in $project_dir"
    fi
}

# Check if at least one action is requested
if [[ "$ENSURE_PYTHON" == false && "$UPDATE_SDKS" == false ]]; then
    echo "Error: No action specified. Use --ensure-python and/or --sdks to specify what to update."
    echo "Use --help for usage information."
    exit 1
fi

# Iterate over each folder in the specified directory
for project in "$JOBS_DIR"/*; do
    if [[ -d "$project" && -f "$project/pyproject.toml" ]]; then
        echo "Processing $project..."

        if [[ "$ENSURE_PYTHON" == true ]]; then
            ensure_python_env "$project"
        fi

        if [[ "$UPDATE_SDKS" == true ]]; then
            update_sdks "$project"
        fi

        echo ""
    fi
done

echo "Update process completed."
