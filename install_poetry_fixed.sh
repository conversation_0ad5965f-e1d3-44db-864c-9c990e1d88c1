#!/bin/bash
set -e

VENV_PATH="$HOME/.venvs/poetry312"
POETRY_BIN="$VENV_PATH/bin/poetry"
SHELL_RC="$HOME/.zshrc"  # Z<PERSON>ń na ~/.bashrc jeśli używasz bash

echo "🧹 Usuwam starą wersję Poetry z pipx i ~/.local/bin..."
pipx uninstall poetry || true
rm -f ~/.local/bin/poetry

echo "🛠️ Tworzę virtualenv dla Poetry z Pythonem 3.12..."
/usr/bin/python3.12 -m venv "$VENV_PATH"

echo "📦 Instaluję Poetry wewnątrz virtualenv..."
"$VENV_PATH/bin/pip" install --upgrade pip
"$VENV_PATH/bin/pip" install poetry

echo "🔗 Ustawiam alias w $SHELL_RC..."
if ! grep -q 'alias poetry=' "$SHELL_RC"; then
    echo 'alias poetry="$HOME/.venvs/poetry312/bin/poetry"' >> "$SHELL_RC"
    echo "✅ Dodano alias do $SHELL_RC"
else
    echo "ℹ️  <PERSON><PERSON> już istnieje w $SHELL_RC – pomijam"
fi

echo "🔄 Ładuję alias do obecnej sesji..."
alias poetry="$HOME/.venvs/poetry312/bin/poetry"

echo "🧪 Sprawdzam wersję Poetry..."
poetry --version

echo "🎉 Gotowe! Teraz możesz używać Poetry bez błędów -I 🚀"
