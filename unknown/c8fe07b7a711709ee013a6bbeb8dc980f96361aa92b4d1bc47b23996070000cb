from pathlib import Path

import pytest

from core_silver.config import Config


@pytest.fixture(autouse=True)
def _set_env_variables(monkeypatch):
    monkeypatch.setenv("ENV", "production")
    monkeypatch.setenv("APP_VERSION", "2.0.0")
    monkeypatch.setenv("JOB_NAME", "core-silver-job")
    monkeypatch.setenv("DOCKER_TAG", "latest")
    monkeypatch.setenv("DOCKER_BUILD_TIMESTAMP", "2022-01-01")

    monkeypatch.setenv("INPUT_CFG__TYPE", "local")
    monkeypatch.setenv("INPUT_CFG__LOCAL_DIR", "./playground/raw")

    monkeypatch.setenv("OUTPUT_CFG__TYPE", "local")
    monkeypatch.setenv("OUTPUT_CFG__LOCAL_DIR", "./playground")

    monkeypatch.setenv("REPORT_SERVICE__TYPE", "static")

    monkeypatch.setenv("USER_SERVICE__TYPE", "static")


def test_config_from_env_vars_app_version(monkeypatch):
    monkeypatch.setenv("APP_VERSION", "1.2.3")

    config = Config()

    assert config.app_version == "1.2.3"


def test_config_from_env_vars_job_name(monkeypatch):
    monkeypatch.setenv("JOB_NAME", "my-job")

    config = Config()

    assert config.job_name == "my-job"


def test_config_from_env_vars_docker_tag(monkeypatch):
    monkeypatch.setenv("DOCKER_TAG", "dev")

    config = Config()

    assert config.docker_tag == "dev"


def test_config_from_env_vars_docker_build_timestamp(monkeypatch):
    monkeypatch.setenv("DOCKER_BUILD_TIMESTAMP", "2022-02-02")

    config = Config()

    assert config.docker_build_timestamp == "2022-02-02"


def test_config_from_env_vars_env(monkeypatch):
    monkeypatch.setenv("ENV", "test")

    config = Config()

    assert config.env == "test"


def test_config_from_env_vars_input_cfg_local(monkeypatch):
    monkeypatch.setenv("INPUT_CFG__TYPE", "local")
    monkeypatch.setenv("INPUT_CFG__LOCAL_DIR", "./playground/raw")

    config = Config()

    assert config.input_cfg.type == "local"
    assert config.input_cfg.local_dir == Path("./playground/raw")


def test_config_from_env_vars_output_cfg_local(monkeypatch):
    monkeypatch.setenv("OUTPUT_CFG__TYPE", "local")
    monkeypatch.setenv("OUTPUT_CFG__LOCAL_DIR", "./playground")

    config = Config()

    assert config.output_cfg.type == "local"
    assert config.output_cfg.local_dir == Path("./playground")


def test_config_from_env_vars_input_cfg_dls(monkeypatch):
    monkeypatch.delenv("INPUT_CFG__LOCAL_DIR")

    monkeypatch.setenv("INPUT_CFG__TYPE", "dls")
    monkeypatch.setenv("INPUT_CFG__ACCOUNT_NAME", "account_name")
    monkeypatch.setenv("INPUT_CFG__CONTAINER_NAME", "container_name")
    monkeypatch.setenv("INPUT_CFG__BASE_DIR", "")

    config = Config()

    assert config.input_cfg.type == "dls"
    assert config.input_cfg.account_name == "account_name"
    assert config.input_cfg.container_name == "container_name"
    assert config.input_cfg.base_dir == Path()


def test_config_from_env_vars_input_cfg_dls_with_default_base_dir(monkeypatch):
    monkeypatch.delenv("INPUT_CFG__LOCAL_DIR")

    monkeypatch.setenv("INPUT_CFG__TYPE", "dls")
    monkeypatch.setenv("INPUT_CFG__ACCOUNT_NAME", "account_name")
    monkeypatch.setenv("INPUT_CFG__CONTAINER_NAME", "container_name")

    config = Config()

    assert config.input_cfg.type == "dls"
    assert config.input_cfg.account_name == "account_name"
    assert config.input_cfg.container_name == "container_name"
    assert config.input_cfg.base_dir == Path()
