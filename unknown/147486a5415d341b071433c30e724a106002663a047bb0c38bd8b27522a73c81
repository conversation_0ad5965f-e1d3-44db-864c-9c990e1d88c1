import pandas as pd
import pytest

from core_silver.observation_converter.converters.playstation_utils import (
    assign_playstation_store_information,
    normalize_dataframe,
)
from data_sdk.domain.regions import Region
from data_sdk.domain.stores import Store


@pytest.fixture
def mock_extracted_df():
    data = {
        "columns": [["A", "B", "C"]],
        "rows": [[["1", "2", "3"], ["4", "5", "6"], ["7", "8", "9"]]],
    }
    return pd.DataFrame.from_dict(data, orient="index")


@pytest.fixture
def mock_converted_df():
    data = {
        "region": [
            Region.PLAYSTATION_AMERICA,
            Region.PLAYSTATION_ASIA,
            Region.PLAYSTATION_EUROPE,
            Region.PLAYSTATION_JAPAN,
            Store.PLAYSTATION_UNKNOWN,
        ]
    }
    return pd.DataFrame(data)


def test_normalize_dataframe(mock_extracted_df):
    expected_data = {
        "A": ["1", "4", "7"],
        "B": ["2", "5", "8"],
        "C": ["3", "6", "9"],
    }
    expected_df = pd.DataFrame(expected_data)

    result_df = normalize_dataframe(mock_extracted_df)

    pd.testing.assert_frame_equal(result_df, expected_df)


def test_empty_dataframe():
    extracted_df = pd.DataFrame()
    result_df = normalize_dataframe(extracted_df)

    assert result_df.empty


def test_stores_assigned_properly(mock_converted_df):
    result_df = assign_playstation_store_information(mock_converted_df)

    expected_stores = [
        Store.PLAYSTATION_AMERICA,
        Store.PLAYSTATION_ASIA,
        Store.PLAYSTATION_EUROPE,
        Store.PLAYSTATION_JAPAN,
        Store.PLAYSTATION_UNKNOWN,
    ]

    assert result_df["store"].tolist() == expected_stores


def test_missing_region_column():
    data = {
        "not_a_region_column": ["value1", "value2", "value3"],
    }
    converted_df = pd.DataFrame(data)

    with pytest.raises(KeyError):
        assign_playstation_store_information(converted_df)


def test_unexpected_region_value():
    data = {
        "region": [
            Store.PLAYSTATION_EUROPE.value,
            "unexpected_value",
            Store.PLAYSTATION_UNKNOWN.value,
        ],
    }

    converted_df = pd.DataFrame(data)

    result_df = assign_playstation_store_information(converted_df)

    assert all(result_df["store"] == Store.PLAYSTATION_UNKNOWN.value)
    assert all(result_df["abbreviated_name"] == Store.PLAYSTATION_UNKNOWN.abbreviate())
