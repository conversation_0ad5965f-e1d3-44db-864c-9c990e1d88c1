from datetime import date


def test_two_sales_can_be_added_together(legacy_steam_raw_sales_factory):
    steam_raw_sales = legacy_steam_raw_sales_factory(
        end_date=date(2024, 4, 2),
        rows__gross_units_sold=2,
        rows__bundle_name="SUPER COLD",
    )
    steam_raw_sales_2 = legacy_steam_raw_sales_factory(
        end_date=date(2024, 4, 2),
        rows__gross_units_sold=2,
        rows__bundle_name="MEDIUM COLD",
        rows__product_id="filter_this_id",
    )

    steam_raw_sales += steam_raw_sales_2

    assert steam_raw_sales.raw_rows == [
        "2024-04-01,1,SUPER COLD,1,SUPERHOT VR,Game,SUPERHOT VR,Steam,US,United States,North America,2,0,2,24.99,24.99,USD,49.98,0,0,49.98,VR",
        "2024-04-02,1,SUPER COLD,1,SUPERHOT VR,Game,SUPERHOT VR,Steam,US,United States,North America,2,0,2,24.99,24.99,USD,49.98,0,0,49.98,VR",
        "2024-04-01,1,MEDIUM COLD,filter_this_id,SUPERHOT VR,Game,SUPERHOT VR,Steam,US,United States,North America,2,0,2,24.99,24.99,USD,49.98,0,0,49.98,VR",
        "2024-04-02,1,MEDIUM COLD,filter_this_id,SUPERHOT VR,Game,SUPERHOT VR,Steam,US,United States,North America,2,0,2,24.99,24.99,USD,49.98,0,0,49.98,VR",
    ]
