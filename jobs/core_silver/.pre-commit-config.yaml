repos:
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.4.5
    hooks:
      - id: ruff
        args: [--fix]
        stages: [pre-commit]
        files: jobs/core_silver/.*

      - id: ruff-format
        stages: [pre-commit]
        files: jobs/core_silver/.*

  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
      - id: trailing-whitespace
        stages: [pre-commit]
        files: jobs/core_silver/.*

      - id: end-of-file-fixer
        stages: [pre-commit]
        files: jobs/core_silver/.*

  - repo: https://gitlab.com/bmares/check-json5
    rev: v1.0.0
    hooks:
      - id: check-json5
        name: vscode settings files
        stages: [pre-commit]
        files: jobs/core_silver/.vscode/.*\.json$

  - repo: local
    hooks:
      - id: tests
        name: unit tests
        entry: poe --directory jobs/core_silver test
        language: system
        pass_filenames: false
        stages: [pre-push]
        files: jobs/core_silver/.*
