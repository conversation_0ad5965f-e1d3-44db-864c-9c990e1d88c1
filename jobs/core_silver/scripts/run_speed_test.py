import argparse
import time
from datetime import datetime
from pathlib import Path

import polars as pl
from typer import echo, style
from typer.colors import B<PERSON><PERSON>, CYAN, GREEN, MAGENTA, YELLOW

from core_silver import get_project_root
from core_silver.observation_converter.converters.converters_factory import (
    get_converter_class_using_source,
)
from data_sdk.domain.domain_types import ReportMetadata, ReportMetadataWithRawFile
from data_sdk.domain.source import Source
from data_sdk.domain.tables import ExternalCurrencyExchangeRatesTable
from scripts.blue_pill import enlarge_zip
from tests.snapshot.metadata import STUDIO_ID

metadata = {
    Source.NINTENDO_SALES: ReportMetadata(
        source=Source.NINTENDO_SALES,
        studio_id=STUDIO_ID,
        report_id=23,  # type: ignore[]
        upload_date=datetime.fromisoformat("2021-12-21 00:00:01.00000Z"),
        blob_name="nintendo_valid.zip",  # type: ignore[]
        original_name="nintendo_valid.zip",
        date_from=datetime.fromisoformat("2021-11-16"),
        date_to=datetime.fromisoformat("2021-12-17"),
        state="PENDING",
        no_data=False,
    ),
}

SOURCE_UNDER_TEST = Source.NINTENDO_SALES


def create_currency_table() -> ExternalCurrencyExchangeRatesTable:
    start = time.time()

    df = pl.scan_csv(
        source=get_project_root()
        / "core_silver/external_sources/static/currency_exchange_rates.csv",
        separator=";",
    ).collect()
    table = ExternalCurrencyExchangeRatesTable(df=df)

    end = time.time()
    execution_time = end - start
    echo(
        style(
            f"CurrencyTable preparation Execution time: {execution_time:.4f} seconds",
            fg=BLUE,
        )
    )

    return table


def run_speed_test(input_zip, multiplier=None):
    with Path(input_zip).open("rb") as file:
        raw_file_bytes = file.read()

    enlarged_zip_path = None
    if multiplier:
        echo(style(f"Enlarging ZIP file {multiplier} times...", fg=YELLOW))
        raw_file_bytes, enlarged_zip_path = enlarge_zip(raw_file_bytes, multiplier)
        echo(style("ZIP file enlarged successfully.", fg=GREEN))

    file_with_metadata = ReportMetadataWithRawFile(
        raw_file=raw_file_bytes, metadata=metadata[SOURCE_UNDER_TEST]
    )

    converter = get_converter_class_using_source(SOURCE_UNDER_TEST)(
        raw_report=file_with_metadata,
        external_currency_exchange_rates_table=create_currency_table(),
    )

    echo(style("Starting conversion...", fg=MAGENTA))
    start = time.time()
    converter.convert()
    end = time.time()

    execution_time = end - start
    echo(
        style(
            f"Conversion execution time: {execution_time:.4f} seconds",
            fg=GREEN,
            bold=True,
        )
    )

    if enlarged_zip_path and enlarged_zip_path.exists():
        enlarged_zip_path.unlink()
        echo(style(f"Enlarged ZIP file removed: {enlarged_zip_path}", fg=YELLOW))

    return execution_time


def main():
    parser = argparse.ArgumentParser(
        description="Run speed test for Nintendo Sales Converter"
    )
    parser.add_argument("input_zip", help="Path to the input ZIP file")
    parser.add_argument(
        "multiplier",
        nargs="?",
        type=int,
        help="Number of times to multiply the content (optional)",
    )

    args = parser.parse_args()

    echo(style(f"Starting speed test with input file: {args.input_zip}", fg=CYAN))
    if args.multiplier:
        echo(style(f"Multiplier: {args.multiplier}", fg=CYAN))

    run_speed_test(args.input_zip, args.multiplier)


if __name__ == "__main__":
    main()
