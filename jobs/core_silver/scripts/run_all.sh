#!/bin/bash

# Check if parallel is installed
if ! command -v parallel &> /dev/null
then
    echo "parallel could not be found, please install it first."
    exit
fi

# Create a file with the commands
cat <<EOL > commands.txt
poetry run python scripts/run_locally.py --input-env=dev --output-env=local --local-output-dir=playground/compare --studio-id=1 --portal=nintendo --observation-type=discounts
poetry run python scripts/run_locally.py --input-env=dev --output-env=local --local-output-dir=playground/compare --studio-id=1 --portal=nintendo --observation-type=sales
poetry run python scripts/run_locally.py --input-env=dev --output-env=local --local-output-dir=playground/compare --studio-id=1 --portal=epic --observation-type=sales
poetry run python scripts/run_locally.py --input-env=dev --output-env=local --local-output-dir=playground/compare --studio-id=1 --portal=playstation --observation-type=sales
poetry run python scripts/run_locally.py --input-env=dev --output-env=local --local-output-dir=playground/compare --studio-id=1 --portal=gog --observation-type=sales
poetry run python scripts/run_locally.py --input-env=dev --output-env=local --local-output-dir=playground/compare --studio-id=1 --portal=microsoft --observation-type=sales
poetry run python scripts/run_locally.py --input-env=dev --output-env=local --local-output-dir=playground/compare --studio-id=1 --portal=meta --observation-type=sales
poetry run python scripts/run_locally.py --input-env=dev --output-env=local --local-output-dir=playground/compare --studio-id=1 --portal=google --observation-type=sales
poetry run python scripts/run_locally.py --input-env=dev --output-env=local --local-output-dir=playground/compare --studio-id=1 --portal=apple --observation-type=sales
poetry run python scripts/run_locally.py --input-env=dev --output-env=local --local-output-dir=playground/compare --studio-id=1 --portal=humble --observation-type=sales
poetry run python scripts/run_locally.py --input-env=dev --output-env=local --local-output-dir=playground/compare --studio-id=1 --portal=steam --observation-type=sales
poetry run python scripts/run_locally.py --input-env=dev --output-env=local --local-output-dir=playground/compare --studio-id=1 --portal=steam --observation-type=wishlist_actions
poetry run python scripts/run_locally.py --input-env=dev --output-env=local --local-output-dir=playground/compare --studio-id=1 --portal=steam --observation-type=wishlist_cohorts
poetry run python scripts/run_locally.py --input-env=dev --output-env=local --local-output-dir=playground/compare --studio-id=1 --portal=steam --observation-type=visibility
poetry run python scripts/run_locally.py --input-env=dev --output-env=local --local-output-dir=playground/compare --studio-id=1 --portal=steam --observation-type=discounts
EOL

# Run the commands in parallel
parallel -j 4 < commands.txt
