#!/bin/bash

# Function to display usage information
usage() {
    echo "Usage: $0 [--profile=<profile>] [--input-env=<input_env>] [--output-env=<output_env>] [--studio-id=<studio_id>] [--portal=<portal>] [--observation-type=<observation_type>]"
    echo
    echo "Options:"
    python scripts/run_locally.py --help | sed '1,/^options:/d'
    exit 1
}

# Initialize variables
profile="pyspy"
input_env=""
output_env=""
studio_id=""
portal=""
observation_type=""

# Parse arguments
for arg in "$@"; do
    case $arg in
        --profile=*)
        profile="${arg#*=}"
        shift
        ;;
        --input-env=*)
        input_env="${arg#*=}"
        shift
        ;;
        --output-env=*)
        output_env="${arg#*=}"
        shift
        ;;
        --studio-id=*)
        studio_id="${arg#*=}"
        shift
        ;;
        --portal=*)
        portal="${arg#*=}"
        shift
        ;;
        --observation-type=*)
        observation_type="${arg#*=}"
        shift
        ;;
        *)
        usage
        ;;
    esac
done

# Generate the filename components
filename_components=()
current_time=$(date +"%Y%m%d_%H%M%S")
filename_components+=("$current_time")

[ -n "$input_env" ] && filename_components+=("$input_env")
[ -n "$output_env" ] && filename_components+=("$output_env")
[ -n "$studio_id" ] && filename_components+=("$studio_id")
[ -n "$portal" ] && filename_components+=("$portal")
[ -n "$observation_type" ] && filename_components+=("$observation_type")

# Join components with underscores
filename=$(IFS=_; echo "${filename_components[*]}")

# Run the appropriate profiling command
if [ "$profile" = "pyspy" ]; then
    output_file="pyspy_${filename}.svg"
    py-spy record -o "$output_file" -- python scripts/run_locally.py \
      ${input_env:+--input-env="$input_env"} \
      ${output_env:+--output-env="$output_env"} \
      ${studio_id:+--studio-id="$studio_id"} \
      ${portal:+--portal="$portal"} \
      ${observation_type:+--observation-type="$observation_type"}

elif [ "$profile" = "mprof" ]; then
    mprof run "--python" scripts/run_locally.py \
      ${input_env:+--input-env="$input_env"} \
      ${output_env:+--output-env="$output_env"} \
      ${studio_id:+--studio-id="$studio_id"} \
      ${portal:+--portal="$portal"} \
      ${observation_type:+--observation-type="$observation_type"}
    mprof plot -o "mprofile_${filename}.png"
else
    usage
fi
