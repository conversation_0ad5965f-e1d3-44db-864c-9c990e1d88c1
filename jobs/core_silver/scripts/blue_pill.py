import argparse
import io
import tempfile
import zipfile
from pathlib import Path
from typing import <PERSON>ple, Union

from typer import echo, style
from typer.colors import BLUE, GREEN, RED, YELLOW

docs = """
This script makes ZIP reports and their CSV files much bigger... obviously.

With such big files you can easily measure performance of the system.

Example:

    python blue_pill.py input.zip 10

This will create a new ZIP file named 'input_enlarged.zip' with all CSV files enlarged 10 times.
"""


def usage():
    echo(style(docs, fg=BLUE))


def enlarge_zip(zip_file: Union[Path, bytes], multiplier: int) -> Tuple[bytes, Path]:
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        unzip_files(zip_file, temp_path)
        process_csv_files(temp_path, multiplier)
        enlarged_zip_bytes = create_new_zip(temp_path)

        if isinstance(zip_file, Path):
            output_zip = zip_file.with_name(
                f"{zip_file.stem}_enlarged{zip_file.suffix}"
            )
        else:
            output_zip = Path("enlarged.zip")

    return enlarged_zip_bytes, output_zip


def unzip_files(zip_file: Union[Path, bytes], temp_path: Path):
    if isinstance(zip_file, Path):
        with zipfile.ZipFile(zip_file, "r") as zip_ref:
            zip_ref.extractall(temp_path)
    else:
        with zipfile.ZipFile(io.BytesIO(zip_file), "r") as zip_ref:
            zip_ref.extractall(temp_path)


def process_csv_files(temp_path: Path, multiplier: int):
    for file_path in temp_path.glob("*.csv"):
        output_path = file_path.with_name(f"{file_path.stem}_enlarged.csv")
        enlarge_csv(file_path, output_path, multiplier)
        file_path.unlink()
        output_path.rename(file_path)


def create_new_zip(temp_path: Path) -> bytes:
    output_zip_bytes = io.BytesIO()
    with zipfile.ZipFile(
        output_zip_bytes, "w", zipfile.ZIP_DEFLATED, compresslevel=6
    ) as zipf:
        for file_path in temp_path.rglob("*"):
            if file_path.is_file():
                zipf.write(file_path, arcname=file_path.name)
    return output_zip_bytes.getvalue()


def enlarge_csv(input_path: Path, output_path: Path, multiplier: int):
    with input_path.open("r", encoding="utf-8") as f:
        lines = f.readlines()

    header = lines[0]
    content = lines[1:-1]
    # for Nintendo, last line is special, so we need to keep it and have it only once.
    last_line = lines[-1]

    with output_path.open("w", encoding="utf-8") as f:
        f.write(header)
        for _ in range(multiplier):
            f.writelines(content)
        f.write(last_line)


def save_enlarged_zip(enlarged_zip_bytes: bytes, output_path: Path):
    output_path.write_bytes(enlarged_zip_bytes)
    echo(style(f"New ZIP file created: {output_path}", fg=YELLOW))


def process_zip_file(zip_file_path: str, multiplier: int) -> None:
    input_zip = Path(zip_file_path)
    if not input_zip.exists():
        echo(style(f"Error: The file {input_zip} does not exist.", fg=RED))
        usage()
        return

    enlarged_zip_bytes, output_zip = enlarge_zip(input_zip.read_bytes(), multiplier)

    save_enlarged_zip(enlarged_zip_bytes, output_zip)

    echo(
        style(
            f"Report in ZIP file was enlarged {multiplier} times! Be careful!",
            fg=GREEN,
            bold=True,
        )
    )


def main():
    parser = argparse.ArgumentParser(
        description="Enlarge CSV files in a ZIP archive.", usage=docs
    )
    parser.add_argument("zip_file", help="Path to the input ZIP file")
    parser.add_argument(
        "multiplier", type=int, help="Number of times to multiply the content"
    )
    args = parser.parse_args()

    process_zip_file(args.zip_file, args.multiplier)


if __name__ == "__main__":
    main()
