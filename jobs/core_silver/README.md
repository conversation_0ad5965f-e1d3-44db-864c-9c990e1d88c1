# What types of tables does Core Silver produce?
## External Tables

Tables that are coming from external sources, like APIs or Static Files.
### Processed per: *studio_id*

## Observation Tables
Those are cleaned, mutualized and deduplicated reports in a form of a single table - like Observation_silver. Those are the starting point for any further tables.
Observation tables can use External Tables during creation
### Processed per: *studio_id*, *portal*, *observation_type*

## Core Silver Tables
Tables that are maintained by DPT and should contain all the building blocks for further tables in golds. It should not contain domain-specific tables or columns, but either give the blocks to rebuild the value or transform domain-specific values into shared abstractions that should be common across all the working teams.
Examples:
portal_platform_region - common key between tables, should be shared and that means it belongs to core silver tables
product_id - SaaS specific value, should be in SaaS Gold
portal_platform_region_id - specific to SaaS team needs in PowerBI, PPT uses it sometimes in their scripts, but they view on how this should be created differs, which potentially can lead to breaking change one day. - It belongs to one who'd like to use it
calculated_base_price - shared across organization, despite we have little knowledge how it's created, we need to maintain it
### Processed per: *studio_id*, *portal*

## Legacy Tables
Tables that should either be moved to Gold Jobs, or should be rewritten into Core Silver Tables - but we have no time for this atm! :D
### Processed per: *studio_id*, *portal*

# Slow tests

To exclude slow tests from the default `poe test` script and precommit hook, use @pytest.mark.slow decorator. The tests will still run in CI as a part of `ci-test-slow` step.

Then, to run them locally use:

```
poe slow-test
```

or

```
poetry run pytest --runslow
```

# Profiling
To start profiling Core Silver you need to install additional, optional requirements:
```
poetry install --with profiling
```

## CPU 🚀
To run CPU profiling, you can use the following command:

```
./scripts/run_with_profile.sh --input-env=dev --output-env=dev --studio-id=3 --portal=humble
```

This script is just a wrapper around `scripts/run_locally.py` with [py-spy](https://github.com/benfred/py-spy) and stores output automatically. Output is stored in format (all args are optional, and default values are not passed to the file name):

```
pyspy_{date}_{time}_{input_env}_{output_env}_{studio_id}_{portal}_{observation_type}.svg
example:
pyspy_20240624_173507_dev_dev_3_humble.svg
```

## RAM 🐏
For a starter, we can use [memory_profiler](https://pypi.org/project/memory-profiler/). It can be used as a wrapper of a whole process or as a decorator.

### Wrapper
The wrapper will produce a `.dat` file with raw ram usage data, and plot command will generate a png image of ram usage over time.

```shell
mprof run --python scripts/run_locally.py  --input-env=dev --output-env=dev --studio-id=3 --portal=playstation
mprof plot -o mprofile_20240624_173507_dev_dev_3_humble.png
```

You can also use our profiler script to generate the same oputpus as in py-spy
```shell
./scripts/run_with_profile.sh --profile=mprof --input-env=dev --output-env=local --studio-id=3 --portal=humble
```

### Decorator
The decorator will generate output while the code is running with details of RAM usage for each line with very useful diff
```python
import memory_profiler
@memory_profiler.profile
```

WARNING: using memory_profiler on DataFrame.apply will make it much slower.

---

# How to add a new converter?:

Start by adding a new converter

add a simple test with basic functionality ("inspire yourself" from existing tests and data types)

run test_smoke_test.py and fix your way from there :D

add snapshot tests for zero file (has entries but entries have 0 as data), empty file (contains headers but no actual entries (headers and footer only)) and valid file (contains headers,footer and entries have non 0 data entries)

add new observation type
add new source
