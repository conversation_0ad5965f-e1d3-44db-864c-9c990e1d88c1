from datetime import datetime

from zoneinfo import ZoneInfo

from core_silver.observation_converter.deduplicators.last_file_deduplicator import (
    LastFileDeduplicator,
)
from core_silver.utils.string_format import generate_expected_filename
from data_sdk.domain.domain_types import ReportMetadata, ReportState
from data_sdk.domain.source import Source


def test_deduplicate_returns_correct_dataframe():
    old_report = ReportMetadata(
        source=Source.STEAM_DISCOUNTS,
        date_from=datetime(2023, 9, 10, tzinfo=ZoneInfo("UTC")),
        date_to=datetime(2023, 9, 11, tzinfo=ZoneInfo("UTC")),
        studio_id=1,
        report_id=1,  # type: ignore[reportCallIssue]
        upload_date=datetime(2023, 9, 10, tzinfo=ZoneInfo("UTC")),
        blob_name="old.zip",  # type: ignore[reportCallIssue]
        original_name="old.zip",
        state=ReportState.PENDING,
        no_data=False,
    )
    new_report = ReportMetadata(
        source=Source.STEAM_DISCOUNTS,
        date_from=datetime(2023, 9, 10, tzinfo=ZoneInfo("UTC")),
        date_to=datetime(2023, 9, day=11, tzinfo=ZoneInfo("UTC")),
        studio_id=1,
        report_id=2,  # type: ignore[reportCallIssue]
        upload_date=datetime(2023, 10, 10, tzinfo=ZoneInfo("UTC")),
        blob_name="new.zip",  # type: ignore[reportCallIssue]
        original_name="new.zip",
        state=ReportState.PENDING,
        no_data=False,
    )

    report_metadata_list = [old_report, new_report]
    converted_filename_list = [
        generate_expected_filename(report_metadata)
        for report_metadata in report_metadata_list
    ]
    result_path = LastFileDeduplicator()._find_filepath(
        report_metadata_list, converted_filename_list
    )
    assert result_path == generate_expected_filename(new_report)


def test_deduplicate_ignores_the_last_report_when_failing():
    correct_report = ReportMetadata(
        source=Source.NINTENDO_DISCOUNTS,
        date_from=datetime(2023, 9, 10, tzinfo=ZoneInfo("UTC")),
        date_to=datetime(2023, 9, 11, tzinfo=ZoneInfo("UTC")),
        studio_id=1,
        report_id=1,  # type: ignore[reportCallIssue]
        upload_date=datetime(2023, 9, 10, tzinfo=ZoneInfo("UTC")),
        blob_name="old.zip",  # type: ignore[reportCallIssue]
        original_name="old.zip",
        state=ReportState.PENDING,
        no_data=False,
    )
    new_failed_report = ReportMetadata(
        source=Source.NINTENDO_DISCOUNTS,
        date_from=datetime(2023, 9, 10, tzinfo=ZoneInfo("UTC")),
        date_to=datetime(2023, 9, day=11, tzinfo=ZoneInfo("UTC")),
        studio_id=1,
        report_id=2,  # type: ignore[reportCallIssue]
        upload_date=datetime(2023, 10, 10, tzinfo=ZoneInfo("UTC")),
        blob_name="new.zip",  # type: ignore[reportCallIssue]
        original_name="new.zip",
        state=ReportState.FAILED,
        no_data=False,
    )

    report_metadata_list = [correct_report, new_failed_report]

    converted_filename_list = [
        generate_expected_filename(report_metadata)
        for report_metadata in report_metadata_list
    ]
    result_path = LastFileDeduplicator()._find_filepath(
        report_metadata_list, converted_filename_list
    )
    assert result_path == generate_expected_filename(correct_report)
