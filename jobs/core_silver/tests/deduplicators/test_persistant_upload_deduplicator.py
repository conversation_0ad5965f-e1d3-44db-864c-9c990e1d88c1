from datetime import date, datetime
from unittest.mock import MagicMock

import polars as pl
import pytest
from pydantic import AwareDatetime
from zoneinfo import ZoneInfo

from core_silver.observation_converter.deduplicators.persistant_upload_deduplicator import (
    PersistantUploadDeduplicator,
)
from core_silver.utils.string_format import generate_expected_filename
from data_sdk.domain.domain_types import ReportMetadata, ReportState, StudioId
from data_sdk.domain.source import Source
from data_sdk.reports.reader import ConvertedReportsReader


@pytest.fixture
def nintendo_cumulative_wishlist_sales_report_metadata_factory():
    def _factory(
        date_from: date, upload_date: AwareDatetime, report_id: int = 0
    ) -> ReportMetadata:
        return ReportMetadata(
            source=Source.NINTENDO_CUMULATIVE_WISHLIST_SALES,
            date_from=date_from,
            date_to=upload_date.date(),
            studio_id=StudioId(1),
            id=report_id,
            upload_date=upload_date,
            file_path_raw="fake_nintendo_cumulative_wishlist_sales.zip",
            original_name="fake_nintendo_cumulative_wishlist_sales.zip",
            state=ReportState.PENDING,
            no_data=False,
        )

    return _factory


@pytest.fixture
def converted_nintendo_cumulative_wishlist_sales_factory():
    def _factory(metadata: ReportMetadata) -> dict:
        return {
            "platform": "Switch",
            "region": "Nintendo Europe",
            "portal": "Nintendo",
            "date": metadata.date_to,
            "cumulative_wishlist_sales": 300,
            "source_based_conversion_rate": "0.01%",
            "source_based_total_downloads": 3000000,
            "human_name": "SUPERHOT",
            "sku_id": "HACPAURNA",
            "store_id": "70010000020724",
            "store": "Nintendo Switch Europe",
            "abbreviated_name": "Switch EU",
            "report_id": metadata.report_id,
            "studio_id": metadata.studio_id,
            "unique_sku_id": "HACPAURNA-europe-nintendo:1",
            "portal_platform_region": "Nintendo:Switch:Nintendo Europe",
        }

    return _factory


@pytest.fixture
def dummy_converter_reader_factory(monkeypatch):
    def _factory(sample_data: pl.DataFrame) -> ConvertedReportsReader:
        reader = MagicMock(spec=ConvertedReportsReader)
        monkeypatch.setattr(
            reader,
            "read",
            MagicMock(side_effect=sample_data),
        )
        return reader

    return _factory


@pytest.fixture
def two_consecutive_reports_metadata(
    nintendo_cumulative_wishlist_sales_report_metadata_factory,
) -> list[ReportMetadata]:
    return [
        nintendo_cumulative_wishlist_sales_report_metadata_factory(
            date_from=datetime(2024, 3, 1, tzinfo=ZoneInfo("UTC")),
            upload_date=datetime(2024, 3, 3, tzinfo=ZoneInfo("UTC")),
            report_id=11,
        ),
        nintendo_cumulative_wishlist_sales_report_metadata_factory(
            date_from=datetime(2024, 3, 2, tzinfo=ZoneInfo("UTC")),
            upload_date=datetime(2024, 3, 4, tzinfo=ZoneInfo("UTC")),
            report_id=12,
        ),
    ]


@pytest.fixture
def two_same_upload_date_reports_metadata(
    nintendo_cumulative_wishlist_sales_report_metadata_factory,
) -> list[ReportMetadata]:
    return [
        nintendo_cumulative_wishlist_sales_report_metadata_factory(
            date_from=datetime(2024, 3, 1, tzinfo=ZoneInfo("UTC")),
            upload_date=datetime(2024, 3, 3, 12, 20, tzinfo=ZoneInfo("UTC")),
            report_id=11,
        ),
        nintendo_cumulative_wishlist_sales_report_metadata_factory(
            date_from=datetime(2024, 3, 1, tzinfo=ZoneInfo("UTC")),
            upload_date=datetime(2024, 3, 3, 12, 25, tzinfo=ZoneInfo("UTC")),
            report_id=12,
        ),
    ]


def test_deduplicate_empty_metadata_list(dummy_converter_reader_factory):
    deduplicator = PersistantUploadDeduplicator()
    dummy_converted_reader = dummy_converter_reader_factory([
        pl.DataFrame(),
        pl.DataFrame(),
    ])

    result = deduplicator.deduplicate([], [], dummy_converted_reader)
    assert isinstance(result, pl.DataFrame)
    assert result.is_empty()


def test_deduplicate_of_empty_files_returns_an_empty_df(
    two_consecutive_reports_metadata,
    dummy_converter_reader_factory,
):
    deduplicator = PersistantUploadDeduplicator()

    dummy_converted_reader = dummy_converter_reader_factory([
        pl.DataFrame(),
        pl.DataFrame(),
    ])

    result = deduplicator.deduplicate(
        metadata_list=two_consecutive_reports_metadata,
        converted_filename_list=[
            generate_expected_filename(report)
            for report in two_consecutive_reports_metadata
        ],
        converted_reader=dummy_converted_reader,
    )
    assert result.is_empty()


def test_deduplicate_for_nintendo_cumulative_wishlist_sales(
    two_consecutive_reports_metadata,
    dummy_converter_reader_factory,
    converted_nintendo_cumulative_wishlist_sales_factory,
):
    first_file_content = pl.DataFrame([
        converted_nintendo_cumulative_wishlist_sales_factory(
            two_consecutive_reports_metadata[1]
        )
    ])

    second_file_content = pl.DataFrame([
        converted_nintendo_cumulative_wishlist_sales_factory(
            two_consecutive_reports_metadata[0]
        )
    ])

    deduplicator = PersistantUploadDeduplicator()
    dummy_converted_reader = dummy_converter_reader_factory([
        first_file_content,
        second_file_content,
    ])

    result = deduplicator.deduplicate(
        metadata_list=two_consecutive_reports_metadata,
        converted_filename_list=[
            generate_expected_filename(report)
            for report in two_consecutive_reports_metadata
        ],
        converted_reader=dummy_converted_reader,
    )

    concat = pl.concat([first_file_content, second_file_content])
    assert result.frame_equal(concat)


def test_deduplicate_for_nintendo_cumulative_wishlist_sales_returns_one_line(
    two_same_upload_date_reports_metadata,
    dummy_converter_reader_factory,
    converted_nintendo_cumulative_wishlist_sales_factory,
):
    first_file_content = pl.DataFrame([
        converted_nintendo_cumulative_wishlist_sales_factory(
            two_same_upload_date_reports_metadata[1]
        ),
    ])
    second_file_content = pl.DataFrame([
        converted_nintendo_cumulative_wishlist_sales_factory(
            two_same_upload_date_reports_metadata[0]
        ),
    ])

    deduplicator = PersistantUploadDeduplicator()
    dummy_converted_reader = dummy_converter_reader_factory([
        first_file_content,
        second_file_content,
    ])

    result = deduplicator.deduplicate(
        metadata_list=two_same_upload_date_reports_metadata,
        converted_filename_list=[
            generate_expected_filename(report)
            for report in two_same_upload_date_reports_metadata
        ],
        converted_reader=dummy_converted_reader,
    )
    assert result.shape == (1, 16)
    assert result.frame_equal(first_file_content)


def test_deduplicate_for_nintendo_cumulative_wishlist_sales_returns_separate_lines_for_different_skus(
    two_same_upload_date_reports_metadata,
    dummy_converter_reader_factory,
    converted_nintendo_cumulative_wishlist_sales_factory,
):
    def _make_rows(metadata):
        row1 = converted_nintendo_cumulative_wishlist_sales_factory(metadata)
        row2 = row1.copy()
        row2.update({
            "sku_id": "HACPAURNA-2",
            "unique_sku_id": "HACPAURNA-2-europe-nintendo:1",
            "store_id": "70010000020725",
        })
        return [row1, row2]

    first_file_content = pl.DataFrame(
        _make_rows(two_same_upload_date_reports_metadata[1])
    )

    deduplicator = PersistantUploadDeduplicator()
    dummy_converted_reader = dummy_converter_reader_factory([
        first_file_content,
    ])

    result = deduplicator.deduplicate(
        metadata_list=two_same_upload_date_reports_metadata,
        converted_filename_list=[
            generate_expected_filename(two_same_upload_date_reports_metadata[1])
        ],
        converted_reader=dummy_converted_reader,
    )

    concat = pl.concat([first_file_content])
    assert result.shape == (2, 16)
    assert result.frame_equal(concat)


def test_deduplicate_for_nintendo_cumulative_wishlist_sales_returns_one_line_per_sku_per_each_day(
    two_consecutive_reports_metadata,
    dummy_converter_reader_factory,
    converted_nintendo_cumulative_wishlist_sales_factory,
):
    def _make_rows(metadata):
        row1 = converted_nintendo_cumulative_wishlist_sales_factory(metadata)
        row2 = row1.copy()
        row2.update({
            "sku_id": "HACPAURNA-2",
            "unique_sku_id": "HACPAURNA-2-europe-nintendo:1",
            "store_id": "70010000020725",
        })
        return [row1, row2]

    first_file_content = pl.DataFrame(_make_rows(two_consecutive_reports_metadata[1]))
    second_file_content = pl.DataFrame(_make_rows(two_consecutive_reports_metadata[0]))

    deduplicator = PersistantUploadDeduplicator()
    dummy_converted_reader = dummy_converter_reader_factory([
        first_file_content,
        second_file_content,
    ])

    result = deduplicator.deduplicate(
        metadata_list=two_consecutive_reports_metadata,
        converted_filename_list=[
            generate_expected_filename(report)
            for report in two_consecutive_reports_metadata
        ],
        converted_reader=dummy_converted_reader,
    )

    concat = pl.concat([first_file_content, second_file_content])
    assert result.shape == (4, 16)
    assert result.frame_equal(concat)
