from datetime import datetime, timedelta, timezone

import factory
import pytest

from data_sdk.domain.domain_types import ReportMetadata


@pytest.fixture
def report_metadata_factory() -> type[factory.Factory]:
    class ReportMetadataFactory(factory.Factory):
        class Meta:
            model = ReportMetadata

        class Params:
            number_of_days = 5

        source = "steam_sales"
        studio_id = 1
        report_id = 2
        upload_date = datetime(2021, 1, 27, 11, 10, 0, 277000, tzinfo=timezone.utc)
        blob_name = "STEAM-2018-08-08_2019-07-25.zip"
        original_name = "STEAM-2018-08-08_2019-07-25.zip"
        state = "PENDING"
        date_from = datetime(2000, 1, 1, 0, 0)
        date_to = factory.LazyAttribute(
            lambda o: o.date_from + timedelta(days=o.number_of_days - 1)
        )
        no_data = False

    return ReportMetadataFactory
