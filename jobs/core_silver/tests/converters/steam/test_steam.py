import datetime

from core_silver.observation_converter.converters.steam_sales import SteamSalesConverter


def test_steam_sales_simpliest_report(
    legacy_report_steam_sales_metadata_with_raw_file_factory,
):
    report_metadata = legacy_report_steam_sales_metadata_with_raw_file_factory()

    converter = SteamSalesConverter(report_metadata)

    result = converter.convert()
    assert result.df.to_dicts() == [
        {
            "abbreviated_name": "Steam",
            "acquisition_origin": "RETAIL",
            "acquisition_platform": "Steam",
            "base_price_local": 24.99,
            "bundle_name": "SUPERHOT VR",
            "category": "Sale",
            "country_code": "USA",
            "currency_code": "USD",
            "date": datetime.date(2024, 4, 1),
            "free_units": 0,
            "gross_returned": 0.0,
            "gross_sales": 24.99,
            "hash_acquisition_properties": "f13e608a5489fb90cf2f809a8e05d610",
            "human_name": "SUPERHOT VR",
            "iap_flag": "False",
            "price_local": 24.99,
            "net_sales": 24.99,
            "net_sales_approx": 17.49,
            "payment_instrument": "Unknown",
            "platform": "PC",
            "portal": "Steam",
            "price_usd": 24.99,
            "region": "Global",
            "report_id": 2,
            "retailer_tag": "VR",
            "sale_modificator": "NOT_APPLICABLE",
            "sku_id": "1",
            "unique_sku_id": "1-steam:1",
            "store": "Steam",
            "store_id": "1",
            "studio_id": 1,
            "tax_type": "Unknown",
            "transaction_type": "Game",
            "units_returned": 0,
            "units_sold": 1,
            "portal_platform_region": "Steam:PC:Global",
        }
    ]


def test_steam_sales_recognize_free_units(
    legacy_report_steam_sales_metadata_with_raw_file_factory,
):
    report_metadata = legacy_report_steam_sales_metadata_with_raw_file_factory(
        input_raw_file__rows__base_price_local=0,
        input_raw_file__rows__gross_steam_sales=0,
        input_raw_file__rows__tag="",
    )

    converter = SteamSalesConverter(report_metadata)

    result = converter.convert()
    assert result.df.to_dicts() == [
        {
            "abbreviated_name": "Steam",
            "acquisition_origin": "MAIN_STORE",
            "acquisition_platform": "Steam",
            "base_price_local": 0.0,
            "bundle_name": "SUPERHOT VR",
            "category": "Free",
            "country_code": "USA",
            "currency_code": "USD",
            "date": datetime.date(2024, 4, 1),
            "free_units": 1,
            "gross_returned": 0.0,
            "gross_sales": 0.0,
            "hash_acquisition_properties": "d104e5b1cb17480f1da2c8ec87162aed",
            "human_name": "SUPERHOT VR",
            "iap_flag": "False",
            "price_local": 0.0,
            "net_sales": 0.0,
            "net_sales_approx": 0.0,
            "payment_instrument": "Unknown",
            "platform": "PC",
            "portal": "Steam",
            "price_usd": 0.0,
            "region": "Global",
            "report_id": 2,
            "retailer_tag": "NOT_APPLICABLE",
            "sale_modificator": "NOT_APPLICABLE",
            "sku_id": "1",
            "unique_sku_id": "1-steam:1",
            "store": "Steam",
            "store_id": "1",
            "studio_id": 1,
            "tax_type": "Unknown",
            "transaction_type": "Game",
            "units_returned": 0,
            "units_sold": 0,
            "portal_platform_region": "Steam:PC:Global",
        }
    ]


def test_steam_sales_invalid_currency_code_is_replaced(
    report_steam_sales_metadata_with_raw_file_factory,
):
    report_metadata = report_steam_sales_metadata_with_raw_file_factory(
        input_raw_file__sales_rows__currency="Unknown (0)",
    )

    result = SteamSalesConverter(report_metadata).convert()
    assert result.df.to_dicts() == [
        {
            "abbreviated_name": "Steam",
            "acquisition_origin": "RETAIL",
            "acquisition_platform": "Steam",
            "base_price_local": 24.99,
            "bundle_name": "SUPERHOT VR",
            "category": "Sale",
            "country_code": "USA",
            "currency_code": "XXX",
            "date": datetime.date(2024, 4, 1),
            "free_units": 0,
            "gross_returned": 0.0,
            "gross_sales": 24.99,
            "hash_acquisition_properties": "f13e608a5489fb90cf2f809a8e05d610",
            "human_name": "SUPERHOT VR",
            "iap_flag": "False",
            "net_sales": 24.99,
            "net_sales_approx": 17.49,
            "payment_instrument": "Unknown",
            "platform": "PC",
            "portal": "Steam",
            "portal_platform_region": "Steam:PC:Global",
            "price_local": 24.99,
            "price_usd": 24.99,
            "region": "Global",
            "report_id": 2,
            "retailer_tag": "VR",
            "sale_modificator": "NOT_APPLICABLE",
            "sku_id": "1",
            "store": "Steam",
            "store_id": "1",
            "studio_id": 1,
            "tax_type": "Unknown",
            "transaction_type": "Game",
            "unique_sku_id": "1-steam:1",
            "units_returned": 0,
            "units_sold": 1,
        },
    ]


def test_steam_sales_with_new_format_additional_data(
    report_steam_sales_metadata_with_raw_file_factory,
):
    report_metadata = report_steam_sales_metadata_with_raw_file_factory()

    converter = SteamSalesConverter(report_metadata)

    result = converter.convert()
    assert result.df.to_dicts() == [
        {
            "abbreviated_name": "Steam",
            "acquisition_origin": "RETAIL",
            "acquisition_platform": "Steam",
            "base_price_local": 24.99,
            "bundle_name": "SUPERHOT VR",
            "category": "Sale",
            "country_code": "USA",
            "currency_code": "USD",
            "date": datetime.date(2024, 4, 1),
            "free_units": 0,
            "gross_returned": 0.0,
            "gross_sales": 24.99,
            "hash_acquisition_properties": "f13e608a5489fb90cf2f809a8e05d610",
            "human_name": "SUPERHOT VR",
            "iap_flag": "False",
            "price_local": 24.99,
            "net_sales": 24.99,
            "net_sales_approx": 17.49,
            "payment_instrument": "Unknown",
            "platform": "PC",
            "portal": "Steam",
            "price_usd": 24.99,
            "region": "Global",
            "report_id": 2,
            "retailer_tag": "VR",
            "sale_modificator": "NOT_APPLICABLE",
            "sku_id": "1",
            "unique_sku_id": "1-steam:1",
            "store": "Steam",
            "store_id": "1",
            "studio_id": 1,
            "tax_type": "Unknown",
            "transaction_type": "Game",
            "units_returned": 0,
            "units_sold": 1,
            "portal_platform_region": "Steam:PC:Global",
        }
    ]


def test_steam_sales_recognize_free_units_new_format_additional_data(
    report_steam_sales_metadata_with_raw_file_factory,
):
    report_metadata = report_steam_sales_metadata_with_raw_file_factory(
        input_raw_file__sales_rows__base_price_local=0,
        input_raw_file__sales_rows__gross_steam_sales=0,
        input_raw_file__sales_rows__tag="",
    )

    converter = SteamSalesConverter(report_metadata)

    result = converter.convert()
    assert result.df.to_dicts() == [
        {
            "abbreviated_name": "Steam",
            "acquisition_origin": "MAIN_STORE",
            "acquisition_platform": "Steam",
            "base_price_local": 0.0,
            "bundle_name": "SUPERHOT VR",
            "category": "Free",
            "country_code": "USA",
            "currency_code": "USD",
            "date": datetime.date(2024, 4, 1),
            "free_units": 1,
            "gross_returned": 0.0,
            "gross_sales": 0.0,
            "hash_acquisition_properties": "d104e5b1cb17480f1da2c8ec87162aed",
            "human_name": "SUPERHOT VR",
            "iap_flag": "False",
            "price_local": 0.0,
            "net_sales": 0.0,
            "net_sales_approx": 0.0,
            "payment_instrument": "Unknown",
            "platform": "PC",
            "portal": "Steam",
            "price_usd": 0.0,
            "region": "Global",
            "report_id": 2,
            "retailer_tag": "NOT_APPLICABLE",
            "sale_modificator": "NOT_APPLICABLE",
            "sku_id": "1",
            "unique_sku_id": "1-steam:1",
            "store": "Steam",
            "store_id": "1",
            "studio_id": 1,
            "tax_type": "Unknown",
            "transaction_type": "Game",
            "units_returned": 0,
            "units_sold": 0,
            "portal_platform_region": "Steam:PC:Global",
        }
    ]


def test_steam_iap_sales(
    report_steam_sales_metadata_with_raw_file_factory,
):
    report_metadata = report_steam_sales_metadata_with_raw_file_factory(
        input_raw_file__with_iap=True,
        input_raw_file__with_sales=False,
    )

    converter = SteamSalesConverter(report_metadata)

    result = converter.convert()
    assert result.df.to_dicts() == [
        {
            "abbreviated_name": "Steam",
            "acquisition_origin": "MAIN_STORE",
            "acquisition_platform": "Unknown",
            "base_price_local": 0.0,
            "bundle_name": "NOT_APPLICABLE",
            "category": "Sale",
            "country_code": "USA",
            "currency_code": "USD",
            "date": datetime.date(2024, 4, 1),
            "free_units": 0,
            "gross_returned": 0.0,
            "gross_sales": 4.99,
            "hash_acquisition_properties": "bf3ff47014e49fde885503294c673e39",
            "human_name": "SUPERHOT VR-funny hat",
            "iap_flag": "True",
            "net_sales": 4.99,
            "net_sales_approx": 3.49,
            "payment_instrument": "Unknown",
            "platform": "PC",
            "portal": "Steam",
            "portal_platform_region": "Steam:PC:Global",
            "price_local": 4.99,
            "price_usd": 4.99,
            "region": "Global",
            "report_id": 2,
            "retailer_tag": "NOT_APPLICABLE",
            "sale_modificator": "NOT_APPLICABLE",
            "sku_id": "1-IAP-2",
            "store": "Steam",
            "store_id": "2",
            "studio_id": 1,
            "tax_type": "Unknown",
            "transaction_type": "InAppPurchase",
            "unique_sku_id": "1-IAP-2-steam:1",
            "units_returned": 0,
            "units_sold": 1,
        },
    ]


def test_steam_iap_sales_nullable_description_raw_column(
    report_steam_sales_metadata_with_raw_file_factory,
):
    report_metadata = report_steam_sales_metadata_with_raw_file_factory(
        input_raw_file__with_iap=True,
        input_raw_file__with_sales=False,
        input_raw_file__iap_rows__description="",
        input_raw_file__iap_rows__item_id=123456,
    )

    converter = SteamSalesConverter(report_metadata)

    result = converter.convert()
    assert result.df.to_dicts() == [
        {
            "abbreviated_name": "Steam",
            "acquisition_origin": "MAIN_STORE",
            "acquisition_platform": "Unknown",
            "base_price_local": 0.0,
            "bundle_name": "NOT_APPLICABLE",
            "category": "Sale",
            "country_code": "USA",
            "currency_code": "USD",
            "date": datetime.date(2024, 4, 1),
            "free_units": 0,
            "gross_returned": 0.0,
            "gross_sales": 4.99,
            "hash_acquisition_properties": "bf3ff47014e49fde885503294c673e39",
            "human_name": "SUPERHOT VR-123456",
            "iap_flag": "True",
            "net_sales": 4.99,
            "net_sales_approx": 3.49,
            "payment_instrument": "Unknown",
            "platform": "PC",
            "portal": "Steam",
            "portal_platform_region": "Steam:PC:Global",
            "price_local": 4.99,
            "price_usd": 4.99,
            "region": "Global",
            "report_id": 2,
            "retailer_tag": "NOT_APPLICABLE",
            "sale_modificator": "NOT_APPLICABLE",
            "sku_id": "1-IAP-123456",
            "store": "Steam",
            "store_id": "123456",
            "studio_id": 1,
            "tax_type": "Unknown",
            "transaction_type": "InAppPurchase",
            "unique_sku_id": "1-IAP-123456-steam:1",
            "units_returned": 0,
            "units_sold": 1,
        },
    ]


def test_steam_iap_sales_and_iap_returns(
    report_steam_sales_metadata_with_raw_file_factory,
):
    report_metadata = report_steam_sales_metadata_with_raw_file_factory(
        input_raw_file__with_iap=True,
        input_raw_file__with_iap_returns=True,
        input_raw_file__with_sales=False,
    )

    converter = SteamSalesConverter(report_metadata)

    result = converter.convert()
    assert result.df.to_dicts() == [
        {
            "sku_id": "1-IAP-2",
            "portal": "Steam",
            "platform": "PC",
            "region": "Global",
            "transaction_type": "InAppPurchase",
            "payment_instrument": "Unknown",
            "tax_type": "Unknown",
            "sale_modificator": "NOT_APPLICABLE",
            "acquisition_platform": "Unknown",
            "acquisition_origin": "MAIN_STORE",
            "iap_flag": "True",
            "date": datetime.date(2024, 4, 1),
            "retailer_tag": "NOT_APPLICABLE",
            "human_name": "SUPERHOT VR-funny hat",
            "store_id": "2",
            "base_price_local": 0.0,
            "bundle_name": "NOT_APPLICABLE",
            "net_sales": 4.99,
            "gross_returned": 0.0,
            "gross_sales": 4.99,
            "units_returned": 0,
            "units_sold": 1,
            "free_units": 0,
            "price_local": 4.99,
            "price_usd": 4.99,
            "store": "Steam",
            "abbreviated_name": "Steam",
            "net_sales_approx": 3.49,
            "category": "Sale",
            "studio_id": 1,
            "report_id": 2,
            "unique_sku_id": "1-IAP-2-steam:1",
            "country_code": "USA",
            "currency_code": "USD",
            "hash_acquisition_properties": "bf3ff47014e49fde885503294c673e39",
            "portal_platform_region": "Steam:PC:Global",
        },
        {
            "sku_id": "1-IAP-2",
            "portal": "Steam",
            "platform": "PC",
            "region": "Global",
            "transaction_type": "InAppPurchase",
            "payment_instrument": "Unknown",
            "tax_type": "Unknown",
            "sale_modificator": "NOT_APPLICABLE",
            "acquisition_platform": "Unknown",
            "acquisition_origin": "MAIN_STORE",
            "iap_flag": "True",
            "date": datetime.date(2024, 4, 1),
            "retailer_tag": "NOT_APPLICABLE",
            "human_name": "SUPERHOT VR-funny hat",
            "store_id": "2",
            "base_price_local": 0.0,
            "bundle_name": "NOT_APPLICABLE",
            "net_sales": -2.69,
            "gross_returned": 2.99,
            "gross_sales": -2.99,
            "units_returned": 1,
            "units_sold": 0,
            "free_units": 0,
            "price_local": 0.0,
            "price_usd": 0.0,
            "store": "Steam",
            "abbreviated_name": "Steam",
            "net_sales_approx": -1.88,
            "category": "Return",
            "studio_id": 1,
            "report_id": 2,
            "unique_sku_id": "1-IAP-2-steam:1",
            "country_code": "USA",
            "currency_code": "USD",
            "hash_acquisition_properties": "bf3ff47014e49fde885503294c673e39",
            "portal_platform_region": "Steam:PC:Global",
        },
    ]


def test_steam_sales_with_sales_and_returns(
    report_steam_sales_metadata_with_raw_file_factory,
):
    report_metadata = report_steam_sales_metadata_with_raw_file_factory(
        input_raw_file__with_returns=True,
    )

    converter = SteamSalesConverter(report_metadata)

    result = converter.convert()
    assert result.df.to_dicts() == [
        {
            "sku_id": "1",
            "portal": "Steam",
            "platform": "PC",
            "region": "Global",
            "transaction_type": "Game",
            "payment_instrument": "Unknown",
            "tax_type": "Unknown",
            "sale_modificator": "NOT_APPLICABLE",
            "acquisition_platform": "Steam",
            "acquisition_origin": "RETAIL",
            "iap_flag": "False",
            "date": datetime.date(2024, 4, 1),
            "retailer_tag": "VR",
            "human_name": "SUPERHOT VR",
            "store_id": "1",
            "base_price_local": 24.99,
            "bundle_name": "SUPERHOT VR",
            "net_sales": 24.99,
            "gross_returned": 0.0,
            "gross_sales": 24.99,
            "units_returned": 0,
            "units_sold": 1,
            "free_units": 0,
            "price_local": 24.99,
            "price_usd": 24.99,
            "store": "Steam",
            "abbreviated_name": "Steam",
            "net_sales_approx": 17.49,
            "category": "Sale",
            "studio_id": 1,
            "report_id": 2,
            "unique_sku_id": "1-steam:1",
            "country_code": "USA",
            "currency_code": "USD",
            "hash_acquisition_properties": "f13e608a5489fb90cf2f809a8e05d610",
            "portal_platform_region": "Steam:PC:Global",
        },
        {
            "sku_id": "1",
            "portal": "Steam",
            "platform": "PC",
            "region": "Global",
            "transaction_type": "Game",
            "payment_instrument": "Unknown",
            "tax_type": "Unknown",
            "sale_modificator": "NOT_APPLICABLE",
            "acquisition_platform": "Steam",
            "acquisition_origin": "RETAIL",
            "iap_flag": "False",
            "date": datetime.date(2024, 4, 1),
            "retailer_tag": "VR",
            "human_name": "SUPERHOT VR",
            "store_id": "1",
            "base_price_local": 24.99,
            "bundle_name": "SUPERHOT VR",
            "net_sales": -24.99,
            "gross_returned": 27.49,
            "gross_sales": -27.49,
            "units_returned": 1,
            "units_sold": -1,
            "free_units": 0,
            "price_local": 24.99,
            "price_usd": 27.49,
            "store": "Steam",
            "abbreviated_name": "Steam",
            "net_sales_approx": -17.49,
            "category": "Return",
            "studio_id": 1,
            "report_id": 2,
            "unique_sku_id": "1-steam:1",
            "country_code": "USA",
            "currency_code": "USD",
            "hash_acquisition_properties": "f13e608a5489fb90cf2f809a8e05d610",
            "portal_platform_region": "Steam:PC:Global",
        },
    ]


def test_steam_both_iap_and_sales(
    report_steam_sales_metadata_with_raw_file_factory,
):
    report_metadata = report_steam_sales_metadata_with_raw_file_factory(
        input_raw_file__with_iap=True,
    )

    converter = SteamSalesConverter(report_metadata)

    result = converter.convert()
    assert result.df.to_dicts() == [
        {
            "abbreviated_name": "Steam",
            "acquisition_origin": "RETAIL",
            "acquisition_platform": "Steam",
            "base_price_local": 24.99,
            "bundle_name": "SUPERHOT VR",
            "category": "Sale",
            "country_code": "USA",
            "currency_code": "USD",
            "date": datetime.date(2024, 4, 1),
            "free_units": 0,
            "gross_returned": 0.0,
            "gross_sales": 24.99,
            "hash_acquisition_properties": "f13e608a5489fb90cf2f809a8e05d610",
            "human_name": "SUPERHOT VR",
            "iap_flag": "False",
            "price_local": 24.99,
            "net_sales": 24.99,
            "net_sales_approx": 17.49,
            "payment_instrument": "Unknown",
            "platform": "PC",
            "portal": "Steam",
            "price_usd": 24.99,
            "region": "Global",
            "report_id": 2,
            "retailer_tag": "VR",
            "sale_modificator": "NOT_APPLICABLE",
            "sku_id": "1",
            "unique_sku_id": "1-steam:1",
            "store": "Steam",
            "store_id": "1",
            "studio_id": 1,
            "tax_type": "Unknown",
            "transaction_type": "Game",
            "units_returned": 0,
            "units_sold": 1,
            "portal_platform_region": "Steam:PC:Global",
        },
        {
            "abbreviated_name": "Steam",
            "acquisition_origin": "MAIN_STORE",
            "acquisition_platform": "Unknown",
            "base_price_local": 0.0,
            "bundle_name": "NOT_APPLICABLE",
            "category": "Sale",
            "country_code": "USA",
            "currency_code": "USD",
            "date": datetime.date(2024, 4, 1),
            "free_units": 0,
            "gross_returned": 0.0,
            "gross_sales": 4.99,
            "hash_acquisition_properties": "bf3ff47014e49fde885503294c673e39",
            "human_name": "SUPERHOT VR-funny hat",
            "iap_flag": "True",
            "net_sales": 4.99,
            "net_sales_approx": 3.49,
            "payment_instrument": "Unknown",
            "platform": "PC",
            "portal": "Steam",
            "portal_platform_region": "Steam:PC:Global",
            "price_local": 4.99,
            "price_usd": 4.99,
            "region": "Global",
            "report_id": 2,
            "retailer_tag": "NOT_APPLICABLE",
            "sale_modificator": "NOT_APPLICABLE",
            "sku_id": "1-IAP-2",
            "store": "Steam",
            "store_id": "2",
            "studio_id": 1,
            "tax_type": "Unknown",
            "transaction_type": "InAppPurchase",
            "unique_sku_id": "1-IAP-2-steam:1",
            "units_returned": 0,
            "units_sold": 1,
        },
    ]
