import datetime

from core_silver.dictionaries.vat import get_vat_rate
from core_silver.observation_converter.converters.nintendo_sales import (
    NintendoSalesConverter,
)
from core_silver.utils.math_tools import js_round
from tests.__mocks__.tables.currencies import (
    _currency_exchanges_hardcoded_in_the_past as _legacy_currency_exchange_dictionary,
)


def test_nintendo_sales__gross_sales_is_calculated_based_on_units_and_points_per_cost_and_currency_rate(
    generate_raw_nintendo_sales_report, external_currency_exchange_rates_table
):
    raw_report = generate_raw_nintendo_sales_report(
        custom_rows_data=[
            {
                "trait_us": True,
                "units_sold": 10,
                "points_cost": 107.00,
            },
            {
                "trait_pl": True,
                "units_sold": 10,
                "points_cost": 123.00,
            },
            {
                "trait_gb": True,
                "units_sold": 10,
                "points_cost": 120.00,
            },
        ]
    )

    pln_rate = _legacy_currency_exchange_dictionary["PLN"]
    gbp_rate = _legacy_currency_exchange_dictionary["GBP"]

    converter = NintendoSalesConverter(
        raw_report=raw_report,
        external_currency_exchange_rates_table=external_currency_exchange_rates_table,
    )
    result = converter.convert().df

    assert list(result["gross_sales"]) == [
        1070,
        js_round(1230 / pln_rate, precision=2),
        js_round(1200 / gbp_rate, precision=2),
    ]


def test_nintendo_sales__net_sales_is_calculated_based_on_gross_sales(
    generate_raw_nintendo_sales_report, external_currency_exchange_rates_table
):
    base_price = 100

    pln_rate = _legacy_currency_exchange_dictionary["PLN"]
    gbp_rate = _legacy_currency_exchange_dictionary["GBP"]

    pl_vat = get_vat_rate("POL")
    gbr_vat = get_vat_rate("GBR")

    pl_gross_price = base_price * (1 + pl_vat)
    gbr_gross_price = base_price * (1 + gbr_vat)

    pl_gross_price_in_usd = js_round(pl_gross_price / pln_rate, 2)
    gbr_gross_price_in_usd = js_round(gbr_gross_price / gbp_rate, 2)

    expected_pl_net_sales_in_usd = js_round(pl_gross_price_in_usd / (1 + pl_vat), 2)
    expected_gbr_net_sales_in_usd = js_round(gbr_gross_price_in_usd / (1 + gbr_vat), 2)

    raw_report = generate_raw_nintendo_sales_report(
        custom_rows_data=[
            {"trait_pl": True, "points_cost": pl_gross_price},
            {"trait_gb": True, "points_cost": gbr_gross_price},
        ]
    )

    converter = NintendoSalesConverter(
        raw_report,
        external_currency_exchange_rates_table,
    )
    result = converter.convert().df

    assert list(result["net_sales"]) == [
        expected_pl_net_sales_in_usd,
        expected_gbr_net_sales_in_usd,
    ]


def test_nintendo_sales__for_usa_and_japan_gross_sales_are_equal_net_sales(
    generate_raw_nintendo_sales_report, external_currency_exchange_rates_table
):
    base_price = 100

    us_vat = get_vat_rate("USA")
    jp_vat = get_vat_rate("JPN")

    us_gross_price = base_price * (1 + us_vat)
    jp_gross_price = base_price * (1 + jp_vat)
    jpy_rate = _legacy_currency_exchange_dictionary["JPY"]
    jp_gross_price_in_usd = js_round(jp_gross_price / jpy_rate, 2)

    raw_report = generate_raw_nintendo_sales_report(
        custom_rows_data=[
            {"trait_us": True, "points_cost": us_gross_price},
            {"trait_jp": True, "points_cost": jp_gross_price},
        ]
    )

    converter = NintendoSalesConverter(
        raw_report,
        external_currency_exchange_rates_table,
    )
    result = converter.convert().df

    assert list(result["gross_sales"]) == [us_gross_price, jp_gross_price_in_usd]
    assert list(result["net_sales"]) == [us_gross_price, jp_gross_price_in_usd]


def test_nintendo_sales__sets_usd_as_currency_for_records_with_0_total_sales(
    generate_raw_nintendo_sales_report, external_currency_exchange_rates_table
):
    raw_report = generate_raw_nintendo_sales_report(
        custom_rows_data=[
            {"trait_pl": True, "total_sales": 0},
            {"trait_gb": True, "total_sales": 0},
        ]
    )

    converter = NintendoSalesConverter(
        raw_report=raw_report,
        external_currency_exchange_rates_table=external_currency_exchange_rates_table,
    )
    result = converter.convert().df.to_dicts()
    assert [row["currency_code"] for row in result] == ["USD", "USD"]


def test_nintendo_sales__sets_usd_as_currency_for_records_with_eshop_pin_posa_eq_pin_and_currency_is_missing(
    generate_raw_nintendo_sales_report, external_currency_exchange_rates_table
):
    raw_report = generate_raw_nintendo_sales_report(
        custom_rows_data=[
            {"trait_pl": True, "eshop_pin_posa": "PIN", "currency": None},
            {"trait_gb": True, "eshop_pin_posa": "POSA", "currency": "GBP"},
        ]
    )

    converter = NintendoSalesConverter(
        raw_report=raw_report,
        external_currency_exchange_rates_table=external_currency_exchange_rates_table,
    )
    result = converter.convert().df.to_dicts()
    assert [row["currency_code"] for row in result] == ["USD", "GBP"]


def test_nintendo_sales_simples_report(
    nintendo_sales_report_metadata_with_raw_file_factory,
    external_currency_exchange_rates_table,
):
    report_metadata = nintendo_sales_report_metadata_with_raw_file_factory()

    converter = NintendoSalesConverter(
        report_metadata, external_currency_exchange_rates_table
    )

    result = converter.convert()
    assert result.df.to_dicts() == [
        {
            "sku_id": "HACPAURNA",
            "portal": "Nintendo",
            "platform": "Switch",
            "region": "Nintendo Europe/Australia",
            "transaction_type": "Unknown",
            "payment_instrument": "Unknown",
            "tax_type": "Unknown",
            "sale_modificator": "NOT_APPLICABLE",
            "acquisition_platform": "Unknown",
            "acquisition_origin": "eShop",
            "iap_flag": "False",
            "date": datetime.date(2024, 4, 1),
            "retailer_tag": "NOT_APPLICABLE",
            "human_name": "SUPERHOT",
            "store_id": "SUPERHOT-Switch",
            "base_price_local": 39.99,
            "bundle_name": "NOT_APPLICABLE",
            "net_sales": 9.64,
            "gross_returned": 0.0,
            "gross_sales": 11.86,
            "units_returned": 0,
            "units_sold": 1,
            "free_units": 0,
            "price_local": 39.99,
            "price_usd": 11.86,
            "store": "Nintendo Switch Europe/Australia",
            "abbreviated_name": "Switch EU/AU",
            "net_sales_approx": 6.75,
            "category": "Sale",
            "studio_id": 1,
            "report_id": 0,
            "unique_sku_id": "HACPAURNA-europe_australia-nintendo:1",
            "country_code": "POL",
            "currency_code": "PLN",
            "hash_acquisition_properties": "928a40b30d3853b6153d65a453be371c",
            "portal_platform_region": "Nintendo:Switch:Nintendo Europe/Australia",
        }
    ]


def test_nintendo_sales_simples_generates_now_rows_for_no_sales(
    nintendo_sales_report_metadata_with_raw_file_factory,
    external_currency_exchange_rates_table,
):
    report_metadata = nintendo_sales_report_metadata_with_raw_file_factory(
        input_raw_file__rows__units_sold=0
    )

    converter = NintendoSalesConverter(
        report_metadata, external_currency_exchange_rates_table
    )

    result = converter.convert()
    assert result.df.to_dicts() == []


def test_nintendo_sales_converts_report_with_switch_and_switch_2_sales_data(
    generate_raw_nintendo_sales_report, external_currency_exchange_rates_table
):
    raw_report = generate_raw_nintendo_sales_report(
        custom_rows_data=[
            {
                "platform": "Switch",
            },
            {
                "title_code": "BEEPAURNA",
                "platform": "Switch 2",
            },
        ]
    )

    converter = NintendoSalesConverter(
        raw_report=raw_report,
        external_currency_exchange_rates_table=external_currency_exchange_rates_table,
    )
    result = converter.convert().df

    assert result.to_dicts() == [
        {
            "sku_id": "HACPAURNA",
            "portal": "Nintendo",
            "platform": "Switch",
            "region": "Nintendo Europe/Australia",
            "transaction_type": "Unknown",
            "payment_instrument": "Unknown",
            "tax_type": "Unknown",
            "sale_modificator": "NOT_APPLICABLE",
            "acquisition_platform": "Unknown",
            "acquisition_origin": "eShop",
            "iap_flag": "False",
            "date": datetime.date(2024, 4, 1),
            "retailer_tag": "NOT_APPLICABLE",
            "human_name": "SUPERHOT",
            "store_id": "SUPERHOT-Switch",
            "base_price_local": 39.99,
            "bundle_name": "NOT_APPLICABLE",
            "net_sales": 9.64,
            "gross_returned": 0.0,
            "gross_sales": 11.86,
            "units_returned": 0,
            "units_sold": 1,
            "free_units": 0,
            "price_local": 39.99,
            "price_usd": 11.86,
            "store": "Nintendo Switch Europe/Australia",
            "abbreviated_name": "Switch EU/AU",
            "net_sales_approx": 6.75,
            "category": "Sale",
            "studio_id": 1,
            "report_id": 0,
            "unique_sku_id": "HACPAURNA-europe_australia-nintendo:1",
            "country_code": "POL",
            "currency_code": "PLN",
            "hash_acquisition_properties": "928a40b30d3853b6153d65a453be371c",
            "portal_platform_region": "Nintendo:Switch:Nintendo Europe/Australia",
        },
        {
            "sku_id": "BEEPAURNA",
            "portal": "Nintendo",
            "platform": "Switch 2",
            "region": "Nintendo Europe/Australia",
            "transaction_type": "Unknown",
            "payment_instrument": "Unknown",
            "tax_type": "Unknown",
            "sale_modificator": "NOT_APPLICABLE",
            "acquisition_platform": "Unknown",
            "acquisition_origin": "eShop",
            "iap_flag": "False",
            "date": datetime.date(2024, 4, 1),
            "retailer_tag": "NOT_APPLICABLE",
            "human_name": "SUPERHOT",
            "store_id": "SUPERHOT-Switch 2",
            "base_price_local": 39.99,
            "bundle_name": "NOT_APPLICABLE",
            "net_sales": 9.64,
            "gross_returned": 0.0,
            "gross_sales": 11.86,
            "units_returned": 0,
            "units_sold": 1,
            "free_units": 0,
            "price_local": 39.99,
            "price_usd": 11.86,
            "store": "Nintendo Switch 2 Europe/Australia",
            "abbreviated_name": "Switch 2 EU/AU",
            "net_sales_approx": 6.75,
            "category": "Sale",
            "studio_id": 1,
            "report_id": 0,
            "unique_sku_id": "BEEPAURNA-europe_australia-nintendo:1",
            "country_code": "POL",
            "currency_code": "PLN",
            "hash_acquisition_properties": "928a40b30d3853b6153d65a453be371c",
            "portal_platform_region": "Nintendo:Switch 2:Nintendo Europe/Australia",
        },
    ]


def test_nintendo_sales_simples_generates_now_rows_for_empty_report(
    generate_raw_nintendo_sales_report,
    external_currency_exchange_rates_table,
):
    raw_report = generate_raw_nintendo_sales_report(custom_rows_data=[])

    converter = NintendoSalesConverter(
        raw_report, external_currency_exchange_rates_table
    )

    result = converter.convert()
    assert result.df.to_dicts() == []


def test_nintendo_sales_extended_report(
    nintendo_sales_report_metadata_with_raw_file_factory,
    external_currency_exchange_rates_table,
):
    report_metadata = nintendo_sales_report_metadata_with_raw_file_factory(
        input_raw_file__rows__ns_uid="70010000020726",
        extended_schema_by_nsuid=True,
    )

    converter = NintendoSalesConverter(
        report_metadata, external_currency_exchange_rates_table
    )

    result = converter.convert()
    assert result.df.to_dicts() == [
        {
            "sku_id": "HACPAURNA",
            "portal": "Nintendo",
            "platform": "Switch",
            "region": "Nintendo Europe/Australia",
            "transaction_type": "Unknown",
            "payment_instrument": "Unknown",
            "tax_type": "Unknown",
            "sale_modificator": "NOT_APPLICABLE",
            "acquisition_platform": "Unknown",
            "acquisition_origin": "eShop",
            "iap_flag": "False",
            "date": datetime.date(2024, 4, 1),
            "retailer_tag": "NOT_APPLICABLE",
            "human_name": "SUPERHOT",
            "store_id": "70010000020726",
            "base_price_local": 39.99,
            "bundle_name": "NOT_APPLICABLE",
            "net_sales": 9.64,
            "gross_returned": 0.0,
            "gross_sales": 11.86,
            "units_returned": 0,
            "units_sold": 1,
            "free_units": 0,
            "price_local": 39.99,
            "price_usd": 11.86,
            "store": "Nintendo Switch Europe/Australia",
            "abbreviated_name": "Switch EU/AU",
            "net_sales_approx": 6.75,
            "category": "Sale",
            "studio_id": 1,
            "report_id": 0,
            "unique_sku_id": "HACPAURNA-europe_australia-nintendo:1",
            "country_code": "POL",
            "currency_code": "PLN",
            "hash_acquisition_properties": "928a40b30d3853b6153d65a453be371c",
            "portal_platform_region": "Nintendo:Switch:Nintendo Europe/Australia",
        }
    ]
