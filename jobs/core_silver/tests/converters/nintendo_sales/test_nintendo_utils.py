import pandas as pd

from core_silver.observation_converter.converters.nintendo_utils import (
    generate_nintendo_sales_unique_sku_id,
)
from data_sdk.domain.regions import Region


def test_generates_unique_sku_id_with_valid_data():
    data = pd.DataFrame({
        "sku_id": ["12345", "67890"],
        "region": [
            Region.NINTENDO_AMERICAS.value,
            Region.NINTENDO_EUROPE_AUSTRALIA.value,
        ],
        "studio_id": [1, 2],
    })

    result = generate_nintendo_sales_unique_sku_id("sku_id", data)

    assert result["unique_sku_id"].tolist() == [
        "12345-americas-nintendo:1",
        "67890-europe_australia-nintendo:2",
    ]


def test_generate_nintendo_unique_sku_id_removes_nintendo_prefix_from_region_name():
    data = pd.DataFrame({
        "sku_id": ["12345"],
        "region": ["Nintendo Fake Region"],
        "studio_id": [111],
    })

    result = generate_nintendo_sales_unique_sku_id("sku_id", data)

    assert result["unique_sku_id"].tolist() == ["12345-fake_region-nintendo:111"]


def test_generate_nintendo_unique_sku_id_removes_slashes_and_spaces_from_region():
    # Slashes in region should be replaced with underscores
    data = pd.DataFrame({
        "sku_id": ["12345"],
        "region": ["Nintendo Taiwan/Hong Kong"],
        "studio_id": [111],
    })

    result = generate_nintendo_sales_unique_sku_id("sku_id", data)

    assert result["unique_sku_id"].tolist() == ["12345-taiwan_hong_kong-nintendo:111"]
