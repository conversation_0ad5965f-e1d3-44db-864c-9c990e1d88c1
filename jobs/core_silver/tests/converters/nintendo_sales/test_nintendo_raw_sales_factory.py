from datetime import date


def test_generate_two_days_of_sales(nintendo_raw_sales_factory):
    nintendo_raw_sales = nintendo_raw_sales_factory(
        end_date=date(2024, 4, 2),
        rows__units_sold=2,
    )

    assert nintendo_raw_sales.raw_rows == [
        """"HACPAURNA","SUPERHOT","HACPAURNA","SUPERHOT","Europe","PL","Poland","Switch","Title","SUPERHOT","05/31/2021","39.99","PLN","0","4","eShop",,"0","2","2\"""",
    ]


def test_generate_two_days_of_sales_with_extended_schema_by_nsuid(
    nintendo_raw_sales_factory,
):
    nintendo_raw_sales = nintendo_raw_sales_factory(
        end_date=date(2024, 4, 2),
        rows__units_sold=2,
        rows__ns_uid="70010000020726",
        extended_schema_by_nsuid=True,
    )

    assert (
        nintendo_raw_sales.header
        == '"TitleCode","TitleName","ItemCode","ItemName","Region","Country","CountryName","Platform","ContentType","Publisher","StartTime","Points/Cost","Currency","First Week","Total Sales","eShop/PIN/POSA","Card Type","NsUid","Period Total","04/01/24","04/02/24"\n'
    )

    assert nintendo_raw_sales.raw_rows == [
        """"HACPAURNA","SUPERHOT","HACPAURNA","SUPERHOT","Europe","PL","Poland","Switch","Title","SUPERHOT","05/31/2021","39.99","PLN","0","4","eShop",,"70010000020726","0","2","2\"""",
    ]


def test_two_sales_can_be_added_together(nintendo_raw_sales_factory):
    nintendo_raw_sales = nintendo_raw_sales_factory(
        end_date=date(2024, 4, 2),
        rows__units_sold=2,
    )
    nintendo_raw_sales_2 = nintendo_raw_sales_factory(
        end_date=date(2024, 4, 2),
        rows__units_sold=4,
        rows__title_code="HACPATEST",
        rows__title_name="SUPER COLD",
    )

    nintendo_raw_sales += nintendo_raw_sales_2

    assert nintendo_raw_sales.raw_rows == [
        """"HACPAURNA","SUPERHOT","HACPAURNA","SUPERHOT","Europe","PL","Poland","Switch","Title","SUPERHOT","05/31/2021","39.99","PLN","0","4","eShop",,"0","2","2\"""",
        """"HACPATEST","SUPER COLD","HACPATEST","SUPER COLD","Europe","PL","Poland","Switch","Title","SUPERHOT","05/31/2021","39.99","PLN","0","8","eShop",,"0","4","4\"""",
    ]
