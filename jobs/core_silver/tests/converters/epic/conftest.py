import json
from datetime import datetime, timedelta, timezone

import factory
import pytest

from data_sdk.domain.domain_types import ReportMetadata, ReportMetadataWithRawFile
from data_sdk.domain.source import Source
from tests.utils import _mock_csv_file


class _EpicMetadataFactory(factory.Factory):
    class Meta:
        model = ReportMetadata

    class Params:
        number_of_days = 5

    source = Source.EPIC_SALES
    studio_id = 1
    report_id = 2
    upload_date = datetime(2024, 11, 11, 11, 10, 0, 277000, tzinfo=timezone.utc)
    blob_name = "epic_sales-2024-11-01_2024-11-12.zip"
    original_name = "epic_sales-2024-11-01_2024-11-12.zip"
    state = "PENDING"
    date_from = datetime(2000, 1, 1, 0, 0)
    date_to = factory.LazyAttribute(
        lambda o: o.date_from + timedelta(days=o.number_of_days - 1)
    )
    no_data = False


@pytest.fixture
def metadata_factory() -> type[factory.Factory]:
    return _EpicMetadataFactory


@pytest.fixture
def metadata(metadata_factory) -> ReportMetadata:
    return metadata_factory()


@pytest.fixture
def epic_sales_raw_report(metadata, mock_zip_file) -> ReportMetadataWithRawFile:
    zip_content = epic_sales_raw_report_zip_content
    with mock_zip_file(zip_content) as zip_file:
        return ReportMetadataWithRawFile(
            metadata=metadata, raw_file=zip_file.fp.getvalue()
        )


epic_sales_raw_report_zip_content = {
    "manifest.json": json.dumps({
        "dateFrom": "2024-11-01T00:00:00.000Z",
        "dateTo": "2024-11-12T00:00:00.000Z",
    }),
    "epic_sales-2024-11-01_2024-11-12-test-game.csv": _mock_csv_file(
        [
            "Id",
            "Developer",
            "Product Id",
            "Offer Id",
            "Offer Title",
            "Country",
            "Sale Day",
            "Currency Code",
            "Net Units",
            "Total Purchase Number",
            "Total Refund Number",
            "Total Chargeback Number",
            "Adjusted Base Revenue(USD)",
            "Total Purchase Amount(USD)",
            "Total Refund Amount(USD)",
            "Total Chargeback Amount(USD)",
            "Total Tax(USD)",
            "Total VAT(USD)",
        ],
        [
            {
                "Id": "8840cf5525e54aa8bfd78506a5c99044",
                "Developer": "o-t682p59y2u8tr9bxcm6cmw9w7mmz5t",
                "Product Id": "morpho",
                "Offer Id": "2259e15b6cd4436f9b918ae31b699fd5",
                "Offer Title": "Hello Neighbor: Hide and Seek Demo",
                "Country": "AE",
                "Sale Day": "2024-11-01",
                "Currency Code": "USD",
                "Net Units": 1,
                "Total Purchase Number": 1,
                "Total Refund Number": 0,
                "Total Chargeback Number": 0,
                "Adjusted Base Revenue(USD)": 0,
                "Total Purchase Amount(USD)": 0,
                "Total Refund Amount(USD)": 0,
                "Total Chargeback Amount(USD)": 0,
                "Total Tax(USD)": 0,
                "Total VAT(USD)": 0,
            },
            {
                "Id": "b235792ab5a7492caf1054b7b5312d18",
                "Developer": "o-t682p59y2u8tr9bxcm6cmw9w7mmz5t",
                "Product Id": "b9665613f2b94636bf4536a411002d22",
                "Offer Id": "e7e111c658584357a25ff993fca52a3c",
                "Offer Title": "RAWMEN: Food Fighter Arena 🍜",
                "Country": "AE",
                "Sale Day": "2024-11-01",
                "Currency Code": "USD",
                "Net Units": 1,
                "Total Purchase Number": 1,
                "Total Refund Number": 0,
                "Total Chargeback Number": 0,
                "Adjusted Base Revenue(USD)": 0,
                "Total Purchase Amount(USD)": 0,
                "Total Refund Amount(USD)": 0,
                "Total Chargeback Amount(USD)": 0,
                "Total Tax(USD)": 0,
                "Total VAT(USD)": 0,
            },
        ],
    ),
}
