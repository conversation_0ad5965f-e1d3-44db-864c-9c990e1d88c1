from datetime import date

import polars as pl

from core_silver.observation_converter.converters.meta_daily_active_users import (
    MetaDailyActiveUsersConverter,
)
from tests.comparisons import compare_polars_dataframes


def test_convert_meta_daily_active_users_empty(
    generate_raw_meta_daily_active_users_report,
):
    raw_report = generate_raw_meta_daily_active_users_report(rows=0)
    converter = MetaDailyActiveUsersConverter(raw_report)
    result = converter.convert()
    assert result.empty


def test_convert_meta_daily_active_users_basic_one_line_run(
    generate_raw_meta_daily_active_users_report,
):
    raw_report = generate_raw_meta_daily_active_users_report()
    converter = MetaDailyActiveUsersConverter(raw_report)
    result = converter.convert()

    expected_df = pl.DataFrame([
        {
            "platform": "Quest",
            "region": "Global",
            "portal": "Meta",
            "date": date(2000, 1, 1),
            "human_name": "Test product",
            "daily_active_users": 1,
            "sku_id": "Test_product",
            "store_id": "1012593518800600",
            "store": "Meta Quest",
            "abbreviated_name": "Quest",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "Test_product-quest-meta:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Meta:Quest:Global",
        }
    ])
    compare_polars_dataframes(result.df, expected_df)


def test_convert_meta_daily_active_users_with_multiple_rows_different_dates(
    generate_raw_meta_daily_active_users_report,
):
    raw_report = generate_raw_meta_daily_active_users_report(
        custom_rows_data=[
            {
                "start_date": date(2025, 6, 1),
                "date": "2025-06-01",
                "product": "SUPERHOT WINDOWS 10",
                "product_id": "1012593518800600",
                "count": 14,
            },
            {
                "start_date": date(2025, 6, 2),
                "date": "2025-06-02",
                "product": "SUPERHOT WINDOWS 10",
                "product_id": "1012593518800600",
                "count": 8,
            },
            {
                "start_date": date(2025, 6, 3),
                "date": "2025-06-03",
                "product": "SUPERHOT WINDOWS 10",
                "product_id": "1012593518800600",
                "count": 10,
                "platform": "Rift",
            },
        ]
    )
    converter = MetaDailyActiveUsersConverter(raw_report)
    result = converter.convert()

    expected_df = pl.DataFrame([
        {
            "platform": "Quest",
            "region": "Global",
            "portal": "Meta",
            "date": date(2025, 6, 1),
            "human_name": "SUPERHOT WINDOWS 10",
            "daily_active_users": 14,
            "sku_id": "SUPERHOT_WINDOWS_10",
            "store_id": "1012593518800600",
            "store": "Meta Quest",
            "abbreviated_name": "Quest",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "SUPERHOT_WINDOWS_10-quest-meta:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Meta:Quest:Global",
        },
        {
            "platform": "Quest",
            "region": "Global",
            "portal": "Meta",
            "date": date(2025, 6, 2),
            "human_name": "SUPERHOT WINDOWS 10",
            "daily_active_users": 8,
            "sku_id": "SUPERHOT_WINDOWS_10",
            "store_id": "1012593518800600",
            "store": "Meta Quest",
            "abbreviated_name": "Quest",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "SUPERHOT_WINDOWS_10-quest-meta:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Meta:Quest:Global",
        },
        {
            "platform": "Rift",
            "region": "Global",
            "portal": "Meta",
            "date": date(2025, 6, 3),
            "human_name": "SUPERHOT WINDOWS 10",
            "daily_active_users": 10,
            "sku_id": "SUPERHOT_WINDOWS_10",
            "store_id": "1012593518800600",
            "store": "Meta Rift",
            "abbreviated_name": "Rift",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "SUPERHOT_WINDOWS_10-rift-meta:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Meta:Rift:Global",
        },
    ])

    compare_polars_dataframes(result.df, expected_df)


def test_convert_meta_daily_active_users_with_multiple_skus_multiple_dates(
    meta_raw_daily_active_users_factory,
    meta_daily_active_users_metadata_with_raw_file_factory,
):
    input_raw_files = meta_raw_daily_active_users_factory(
        start_date=date(2024, 4, 1),
        end_date=date(2024, 4, 2),
        rows__product="SUPERHOT WINDOWS 10",
        rows__product_id="1012593518800600",
        rows__count=14,
    ) + meta_raw_daily_active_users_factory(
        start_date=date(2024, 4, 1),
        end_date=date(2024, 4, 2),
        rows__product="SUPERHOT VR WINDOWS 10",
        rows__product_id="1012593518800601",
        rows__count=5,
    )
    raw_report = meta_daily_active_users_metadata_with_raw_file_factory(
        input_raw_file=input_raw_files,
    )
    converter = MetaDailyActiveUsersConverter(raw_report)

    result = converter.convert()
    expected_df = pl.DataFrame([
        {
            "platform": "Quest",
            "region": "Global",
            "portal": "Meta",
            "date": date(2024, 4, 1),
            "human_name": "SUPERHOT WINDOWS 10",
            "daily_active_users": 14,
            "sku_id": "SUPERHOT_WINDOWS_10",
            "store_id": "1012593518800600",
            "store": "Meta Quest",
            "abbreviated_name": "Quest",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "SUPERHOT_WINDOWS_10-quest-meta:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Meta:Quest:Global",
        },
        {
            "platform": "Quest",
            "region": "Global",
            "portal": "Meta",
            "date": date(2024, 4, 2),
            "human_name": "SUPERHOT WINDOWS 10",
            "daily_active_users": 14,
            "sku_id": "SUPERHOT_WINDOWS_10",
            "store_id": "1012593518800600",
            "store": "Meta Quest",
            "abbreviated_name": "Quest",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "SUPERHOT_WINDOWS_10-quest-meta:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Meta:Quest:Global",
        },
        {
            "platform": "Quest",
            "region": "Global",
            "portal": "Meta",
            "date": date(2024, 4, 1),
            "human_name": "SUPERHOT VR WINDOWS 10",
            "daily_active_users": 5,
            "sku_id": "SUPERHOT_VR_WINDOWS_10",
            "store_id": "1012593518800601",
            "store": "Meta Quest",
            "abbreviated_name": "Quest",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "SUPERHOT_VR_WINDOWS_10-quest-meta:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Meta:Quest:Global",
        },
        {
            "platform": "Quest",
            "region": "Global",
            "portal": "Meta",
            "date": date(2024, 4, 2),
            "human_name": "SUPERHOT VR WINDOWS 10",
            "daily_active_users": 5,
            "sku_id": "SUPERHOT_VR_WINDOWS_10",
            "store_id": "1012593518800601",
            "store": "Meta Quest",
            "abbreviated_name": "Quest",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "SUPERHOT_VR_WINDOWS_10-quest-meta:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Meta:Quest:Global",
        },
    ])

    compare_polars_dataframes(result.df, expected_df)
