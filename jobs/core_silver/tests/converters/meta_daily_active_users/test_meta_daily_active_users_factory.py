from datetime import date


def test_generate_empty_meta_daily_active_users(
    meta_raw_daily_active_users_factory,
):
    meta_raw_dau = meta_raw_daily_active_users_factory(
        start_date=date(2023, 10, 1), end_date=date(2023, 10, 1), rows=[]
    )
    assert meta_raw_dau.start_date == date(2023, 10, 1)
    assert meta_raw_dau.end_date == date(2023, 10, 1)
    assert meta_raw_dau.manifest_json == {
        "manifest.json": {
            "dateFrom": "2023-10-01",
            "dateTo": "2023-10-01",
            "fileMetaData": {
                "meta_daily_active_users-2023-10-01-2023-10-01.csv": {
                    "dateFrom": "2023-10-01",
                    "dateTo": "2023-10-01",
                    "observationType": "daily_active_users",
                    "rawData": True,
                },
            },
        },
    }
    assert meta_raw_dau.daily_active_users_csv == {
        "meta_daily_active_users-2023-10-01-2023-10-01.csv": "account,portal,date,product,product_id,count,platform\n",
    }


def test_generate_one_day_of_meta_daily_active_users(
    meta_raw_daily_active_users_factory,
):
    result = meta_raw_daily_active_users_factory(
        start_date=date(2023, 10, 1),
        end_date=date(2023, 10, 1),
    )

    assert result.start_date == date(2023, 10, 1)
    assert result.end_date == date(2023, 10, 1)
    assert result.manifest_json == {
        "manifest.json": {
            "dateFrom": "2023-10-01",
            "dateTo": "2023-10-01",
            "fileMetaData": {
                "meta_daily_active_users-2023-10-01-2023-10-01.csv": {
                    "dateFrom": "2023-10-01",
                    "dateTo": "2023-10-01",
                    "observationType": "daily_active_users",
                    "rawData": True,
                },
            },
        },
    }
    assert result.daily_active_users_csv == {
        "meta_daily_active_users-2023-10-01-2023-10-01.csv": (
            "account,portal,date,product,product_id,count,platform\n"
            "Test account,meta,2023-10-01,Test product,****************,1.0,Quest\n"
        ),
    }


def test_generate_two_days_of_meta_daily_active_users(
    meta_raw_daily_active_users_factory,
):
    result = meta_raw_daily_active_users_factory(
        start_date=date(2024, 4, 1),
        end_date=date(2024, 4, 2),
    )

    assert result.start_date == date(2024, 4, 1)
    assert result.end_date == date(2024, 4, 2)
    assert result.daily_active_users_csv == {
        "meta_daily_active_users-2024-04-01-2024-04-02.csv": (
            "account,portal,date,product,product_id,count,platform\n"
            "Test account,meta,2024-04-01,Test product,****************,1.0,Quest\n"
            "Test account,meta,2024-04-02,Test product,****************,1.0,Quest\n"
        ),
    }


def test_generate_meta_daily_active_users_with_custom_data(
    meta_raw_daily_active_users_factory,
):
    result = meta_raw_daily_active_users_factory(
        start_date=date(2023, 10, 1),
        end_date=date(2023, 10, 1),
        rows__product="SUPERHOT WINDOWS 10",
        rows__product_id="****************",
        rows__count=14.0,
    )

    assert result.start_date == date(2023, 10, 1)
    assert result.end_date == date(2023, 10, 1)
    assert result.daily_active_users_csv == {
        "meta_daily_active_users-2023-10-01-2023-10-01.csv": (
            "account,portal,date,product,product_id,count,platform\n"
            "Test account,meta,2023-10-01,SUPERHOT WINDOWS 10,****************,14.0,Quest\n"
        ),
    }


def test_generate_meta_daily_active_users_multiple_products(
    meta_raw_daily_active_users_factory,
):
    from tests.converters.meta_daily_active_users.conftest import (
        MetaDailyActiveUsersRowFactory,
    )

    custom_rows = [
        MetaDailyActiveUsersRowFactory(
            date=date(2023, 10, 1),
            product="SUPERHOT WINDOWS 10",
            product_id="****************",
            count=14.0,
        ),
        MetaDailyActiveUsersRowFactory(
            date=date(2023, 10, 2),
            product="SUPERHOT WINDOWS 10",
            product_id="****************",
            count=8.1,
        ),
        MetaDailyActiveUsersRowFactory(
            date=date(2023, 10, 1),
            product="SUPERHOT VR WINDOWS 10",
            product_id="****************",
            count=5.0,
        ),
        MetaDailyActiveUsersRowFactory(
            date=date(2023, 10, 2),
            product="SUPERHOT VR WINDOWS 10",
            product_id="****************",
            count=3.0,
            platform="Rift",
        ),
    ]

    result = meta_raw_daily_active_users_factory(
        start_date=date(2023, 10, 1),
        end_date=date(2023, 10, 2),
        rows=custom_rows,
    )

    assert result.start_date == date(2023, 10, 1)
    assert result.end_date == date(2023, 10, 2)
    assert result.daily_active_users_csv == {
        "meta_daily_active_users-2023-10-01-2023-10-02.csv": (
            "account,portal,date,product,product_id,count,platform\n"
            "Test account,meta,2023-10-01,SUPERHOT WINDOWS 10,****************,14.0,Quest\n"
            "Test account,meta,2023-10-02,SUPERHOT WINDOWS 10,****************,8.1,Quest\n"
            "Test account,meta,2023-10-01,SUPERHOT VR WINDOWS 10,****************,5.0,Quest\n"
            "Test account,meta,2023-10-02,SUPERHOT VR WINDOWS 10,****************,3.0,Rift\n"
        ),
    }
