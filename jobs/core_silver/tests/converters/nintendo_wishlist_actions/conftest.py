from datetime import date, <PERSON><PERSON><PERSON>
from typing import Any

import factory
import pytest
from attr import dataclass

from data_sdk.domain import Portal
from data_sdk.domain.domain_types import ReportMetadataWithRawFile
from data_sdk.domain.observations import ObservationType
from data_sdk.domain.source import Source
from tests.conftest import (
    ExternalReportsFactory,
    ListBetterSubFactory,
    ListSubFactory,
)
from tests.utils import _mock_zip_file


class ExternalNintendoWishlistActionsReportsFactory(ExternalReportsFactory):
    source = Source.NINTENDO_WISHLIST_ACTIONS
    portal = Portal.NINTENDO
    observation_type = ObservationType.WISHLIST_ACTIONS


class ExternalNintendoCumulativeWishlistSalesReportsFactory(ExternalReportsFactory):
    source = Source.NINTENDO_CUMULATIVE_WISHLIST_SALES
    portal = Portal.NINTENDO
    observation_type = ObservationType.CUMULATIVE_WISHLIST_SALES


@pytest.fixture
def external_nintendo_wishlist_actions_reports_factory() -> (
    type[ExternalNintendoWishlistActionsReportsFactory]
):  # TODO why external? (copied from steam, need to find the origin)
    ExternalNintendoWishlistActionsReportsFactory.reset_sequence(force=True)
    return ExternalNintendoWishlistActionsReportsFactory


@pytest.fixture
def external_nintendo_cumulative_wishlist_sales_reports_factory() -> (  # TODO why external(copied from steam, need to find the origin)?
    type[ExternalNintendoCumulativeWishlistSalesReportsFactory]
):
    ExternalNintendoCumulativeWishlistSalesReportsFactory.reset_sequence(force=True)
    return ExternalNintendoCumulativeWishlistSalesReportsFactory


class NintendoWishlistActionsRowFactory(factory.StubFactory):
    wishlist_add = 1
    nsuid = "70010000020724"
    code = "HACPAURNA"
    name = "SUPERHOT"
    region = "Australia/Europe"
    platform = "Switch"
    publisher = "SUPERHOT"
    sales_total = factory.LazyAttribute(
        lambda o: ((o.factory_parent.end_date - o.factory_parent.start_date).days + 1)
        * o.wishlist_add
        * 100
    )
    sales_convertion_rate = "0.01%"
    ns_uid = None
    total = factory.LazyAttribute(
        lambda o: ((o.factory_parent.end_date - o.factory_parent.start_date).days + 1)
        * o.wishlist_add
        * 1000000
    )
    daily_wishlist_adds = factory.LazyAttribute(
        lambda o: [o.wishlist_add]
        * ((o.factory_parent.end_date - o.factory_parent.start_date).days + 1)
    )
    # period total is the sum of daily_wishlist_adds
    period_total = factory.LazyAttribute(lambda o: sum(o.daily_wishlist_adds))

    @factory.lazy_attribute
    def row(self):
        daily_wishlist_adds_columns = ",".join(
            f'"{add}"' for add in self.daily_wishlist_adds
        )

        fields = [
            self.nsuid,
            self.code,
            self.name,
            self.region,
            self.platform,
            self.publisher,
            self.sales_total,
            self.sales_convertion_rate,
            self.total,
            self.period_total,
        ]

        fields = (f'"{x}"' if x is not None else "" for x in fields)
        new = ",".join(fields) + f",{daily_wishlist_adds_columns}"

        return new


@dataclass
class NintendoRawWishlistActions:
    start_date: date
    end_date: date
    rows: Any

    @property
    def additional_data(self):
        return {
            "manifest.json": {
                "dateFrom": f"{self.start_date}T00:00:00.000Z",
                "dateTo": f"{self.end_date}T00:00:00.000Z",
                "fileMetaData": {
                    f"nintendo_wishlist_actions-{self.start_date}_{self.end_date}-0.csv": {
                        "dateFrom": f"{self.start_date}",
                        "dateTo": f"{self.end_date}",
                        "rawData": True,
                    }
                },
                "scraperVersion": "0.0.0",
                "manifestVersion": 1,
            }
        }

    @property
    def nintendo_wishlist_actions_csv(self):
        rows_content = "\n".join(self.raw_rows)
        ignored_footer = """\n"","","","","","All Total","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0\""""
        return {
            f"nintendo_wishlist_actions-{self.start_date}_{self.end_date}-0.csv": self.header
            + rows_content
            + ignored_footer
        }

    @property
    def header(self):
        header = '"NsUid","Code","Name","Region","Platform","Publisher","Sales Total","Sales Conversion Rate","Total","Period Total"'

        date_columns = ",".join(
            f'"{(self.start_date + timedelta(days=i)).strftime("%m/%d/%y")}"'
            for i in range((self.end_date - self.start_date).days + 1)
        )
        header += f",{date_columns}\n"
        return header

    @property
    def raw_rows(self) -> list[str]:
        return [row.row for row in self.rows]

    @property
    def zip_content(self):
        return {**self.additional_data, **self.nintendo_wishlist_actions_csv}

    @property
    def raw_zip_file(self):
        with _mock_zip_file(self.zip_content) as zip_file:
            return zip_file.fp.getvalue()

    def __add__(self, other):
        return NintendoRawWishlistActions(
            start_date=self.start_date,
            end_date=other.end_date,
            rows=self.rows + other.rows,
        )


class NintendoRawWishlistActionsFactory(factory.Factory):
    class Meta:
        model = NintendoRawWishlistActions

    start_date = date(2024, 4, 1)
    end_date = date(2024, 4, 1)

    rows = ListSubFactory(
        NintendoWishlistActionsRowFactory,
        start_date=factory.LazyAttribute(lambda row: row.factory_parent.start_date),
        end_date=factory.LazyAttribute(lambda row: row.factory_parent.end_date),
    )


@pytest.fixture
def nintendo_raw_wishlist_actions_factory():
    NintendoRawWishlistActionsFactory.reset_sequence(force=True)

    return NintendoRawWishlistActionsFactory


@pytest.fixture
def nintendo_wishlist_actions_metadata_with_raw_file_factory(
    nintendo_raw_wishlist_actions_factory,
    external_nintendo_wishlist_actions_reports_factory,
):
    class NintendoWishlistActionsReportMetadataWithRawFileFactory(factory.Factory):
        class Meta:
            model = ReportMetadataWithRawFile

        input_raw_file = factory.SubFactory(nintendo_raw_wishlist_actions_factory)
        metadata = factory.SubFactory(
            external_nintendo_wishlist_actions_reports_factory
        )

        @factory.lazy_attribute
        def raw_file(self):
            return self.input_raw_file.raw_zip_file

    return NintendoWishlistActionsReportMetadataWithRawFileFactory


@pytest.fixture
def generate_raw_nintendo_wishlist_actions_report(
    nintendo_wishlist_actions_metadata_with_raw_file_factory,
    nintendo_raw_wishlist_actions_factory,
):
    def generate_raw_report(rows: int | None = None, custom_rows_data=None):
        return nintendo_wishlist_actions_metadata_with_raw_file_factory(
            input_raw_file=nintendo_raw_wishlist_actions_factory(
                rows=ListBetterSubFactory(
                    NintendoWishlistActionsRowFactory,
                    size=len(custom_rows_data)
                    if custom_rows_data is not None and rows is None
                    else rows or 1,
                    custom_rows_data=custom_rows_data if custom_rows_data else [],
                )
            )
        )

    return generate_raw_report


@pytest.fixture
def generate_raw_nintendo_cumulative_wishlist_sales_report(
    nintendo_wishlist_actions_metadata_with_raw_file_factory,
    nintendo_raw_wishlist_actions_factory,
    external_nintendo_cumulative_wishlist_sales_reports_factory,
):
    def generate_raw_report(rows: int | None = None, custom_rows_data=None):
        return nintendo_wishlist_actions_metadata_with_raw_file_factory(
            input_raw_file=nintendo_raw_wishlist_actions_factory(
                rows=ListBetterSubFactory(
                    NintendoWishlistActionsRowFactory,
                    size=len(custom_rows_data)
                    if custom_rows_data is not None and rows is None
                    else rows or 1,
                    custom_rows_data=custom_rows_data if custom_rows_data else [],
                )
            ),
            metadata=external_nintendo_cumulative_wishlist_sales_reports_factory(),
        )

    return generate_raw_report
