from datetime import date

from core_silver.observation_converter.converters.nintendo_cumulative_wishlist_sales import (
    NintendoCumulativeWishlistSalesConverter,
)


def test_convert_for_wishlist_actions_run(
    generate_raw_nintendo_cumulative_wishlist_sales_report,
):
    raw_report = generate_raw_nintendo_cumulative_wishlist_sales_report()
    converter = NintendoCumulativeWishlistSalesConverter(raw_report)
    result = converter.convert()
    assert result


def test_convert_for_wishlist_actions_run_empty(
    generate_raw_nintendo_cumulative_wishlist_sales_report,
):
    raw_report = generate_raw_nintendo_cumulative_wishlist_sales_report(
        custom_rows_data=[]
    )

    converter = NintendoCumulativeWishlistSalesConverter(raw_report)
    result = converter.convert()
    assert result.df.is_empty()


def test_convert_for_three_days_and_two_rows(
    generate_raw_nintendo_cumulative_wishlist_sales_report,
):
    _expected_result = [
        {
            "abbreviated_name": "Switch EU/AU",
            "cumulative_wishlist_sales": 100,
            "date": date(2024, 4, 1),
            "human_name": "SUPERHOT",
            "platform": "Switch",
            "portal": "Nintendo",
            "portal_platform_region": "Nintendo:Switch:Nintendo Europe/Australia",
            "region": "Nintendo Europe/Australia",
            "report_id": 0,
            "sku_id": "HACPAURNA",
            "source_based_conversion_rate": "0.01%",
            "source_based_total_downloads": 1000000,
            "store": "Nintendo Switch Europe/Australia",
            "store_id": "70010000020724",
            "studio_id": 1,
            "unique_sku_id": "HACPAURNA-europe_australia-nintendo:1",
        }
    ]

    raw_report = generate_raw_nintendo_cumulative_wishlist_sales_report()

    converter = NintendoCumulativeWishlistSalesConverter(raw_report)
    result = converter.convert()
    assert result.df.to_dicts() == _expected_result
