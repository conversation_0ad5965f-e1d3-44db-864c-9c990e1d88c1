from datetime import date


def test_generate_two_days_of_wishlist_actions(
    nintendo_raw_wishlist_actions_factory,
):
    nintendo_raw_wishlist_actions = nintendo_raw_wishlist_actions_factory(
        end_date=date(2024, 4, 2),
        rows__wishlist_add=2,
    )
    assert nintendo_raw_wishlist_actions.header == (
        '"NsUid","Code","Name","Region","Platform","Publisher","Sales Total","Sales Conversion Rate","Total","Period Total","04/01/24","04/02/24"\n'
    )
    assert nintendo_raw_wishlist_actions.raw_rows == [
        """"70010000020724","HACPAURNA","SUPERHOT","Australia/Europe","Switch","SUPERHOT","400","0.01%","4000000","4","2","2\"""",
    ]


def test_generate_report_without_products(
    nintendo_raw_wishlist_actions_factory,
):
    nintendo_raw_wishlist_actions = nintendo_raw_wishlist_actions_factory(
        end_date=date(2024, 4, 2),
        rows__wishlist_add=0,
    )
    assert nintendo_raw_wishlist_actions.header == (
        '"NsUid","Code","Name","Region","Platform","Publisher","Sales Total","Sales Conversion Rate","Total","Period Total","04/01/24","04/02/24"\n'
    )
    assert nintendo_raw_wishlist_actions.raw_rows == [
        """"70010000020724","HACPAURNA","SUPERHOT","Australia/Europe","Switch","SUPERHOT","0","0.01%","0","0","0","0\"""",
    ]
