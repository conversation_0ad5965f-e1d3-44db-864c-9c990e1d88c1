import json

_metadata = [
    {
        "type": "DATE",
        "dataSourceId": "df419138-5a0b-4bb9-bc8e-03dee6e0cc4e",
        "maxLength": -1,
        "minLength": -1,
        "periodIndex": 0,
        "aggregated": False,
    },
    {
        "type": "STRING",
        "dataSourceId": "df419138-5a0b-4bb9-bc8e-03dee6e0cc4e",
        "maxLength": -1,
        "minLength": -1,
        "periodIndex": 0,
        "aggregated": False,
    },
    {
        "type": "LONG",
        "dataSourceId": "df419138-5a0b-4bb9-bc8e-03dee6e0cc4e",
        "maxLength": -1,
        "minLength": -1,
        "periodIndex": 0,
        "aggregated": False,
    },
    {
        "type": "STRING",
        "dataSourceId": "df419138-5a0b-4bb9-bc8e-03dee6e0cc4e",
        "maxLength": -1,
        "minLength": -1,
        "periodIndex": 0,
        "aggregated": False,
    },
    {
        "type": "STRING",
        "dataSourceId": "df419138-5a0b-4bb9-bc8e-03dee6e0cc4e",
        "maxLength": -1,
        "minLength": -1,
        "periodIndex": 0,
        "aggregated": False,
    },
    {
        "type": "STRING",
        "dataSourceId": "df419138-5a0b-4bb9-bc8e-03dee6e0cc4e",
        "maxLength": -1,
        "minLength": -1,
        "periodIndex": 0,
        "aggregated": False,
    },
    {
        "type": "STRING",
        "dataSourceId": "df419138-5a0b-4bb9-bc8e-03dee6e0cc4e",
        "maxLength": -1,
        "minLength": -1,
        "periodIndex": 0,
        "aggregated": False,
    },
    {
        "type": "STRING",
        "dataSourceId": "df419138-5a0b-4bb9-bc8e-03dee6e0cc4e",
        "maxLength": -1,
        "minLength": -1,
        "periodIndex": 0,
        "aggregated": False,
    },
    {
        "type": "STRING",
        "dataSourceId": "df419138-5a0b-4bb9-bc8e-03dee6e0cc4e",
        "maxLength": -1,
        "minLength": -1,
        "periodIndex": 0,
        "aggregated": False,
    },
    {
        "type": "STRING",
        "dataSourceId": "df419138-5a0b-4bb9-bc8e-03dee6e0cc4e",
        "maxLength": -1,
        "minLength": -1,
        "periodIndex": 0,
        "aggregated": False,
    },
    {
        "type": "STRING",
        "dataSourceId": "df419138-5a0b-4bb9-bc8e-03dee6e0cc4e",
        "maxLength": -1,
        "minLength": -1,
        "periodIndex": 0,
        "aggregated": False,
    },
    {
        "type": "STRING",
        "dataSourceId": "df419138-5a0b-4bb9-bc8e-03dee6e0cc4e",
        "maxLength": -1,
        "minLength": -1,
        "periodIndex": 0,
        "aggregated": False,
    },
    {
        "type": "STRING",
        "dataSourceId": "df419138-5a0b-4bb9-bc8e-03dee6e0cc4e",
        "maxLength": -1,
        "minLength": -1,
        "periodIndex": 0,
        "aggregated": False,
    },
    {
        "type": "STRING",
        "dataSourceId": "df419138-5a0b-4bb9-bc8e-03dee6e0cc4e",
        "maxLength": -1,
        "minLength": -1,
        "periodIndex": 0,
        "aggregated": False,
    },
    {
        "type": "STRING",
        "dataSourceId": "df419138-5a0b-4bb9-bc8e-03dee6e0cc4e",
        "maxLength": -1,
        "minLength": -1,
        "periodIndex": 0,
        "aggregated": False,
    },
    {
        "type": "LONG",
        "dataSourceId": "df419138-5a0b-4bb9-bc8e-03dee6e0cc4e",
        "maxLength": -1,
        "minLength": -1,
        "periodIndex": 0,
        "aggregated": False,
    },
    {
        "type": "LONG",
        "dataSourceId": "df419138-5a0b-4bb9-bc8e-03dee6e0cc4e",
        "maxLength": -1,
        "minLength": -1,
        "periodIndex": 0,
        "aggregated": False,
    },
    {
        "type": "LONG",
        "dataSourceId": "df419138-5a0b-4bb9-bc8e-03dee6e0cc4e",
        "maxLength": -1,
        "minLength": -1,
        "periodIndex": 0,
        "aggregated": False,
    },
    {
        "type": "LONG",
        "dataSourceId": "df419138-5a0b-4bb9-bc8e-03dee6e0cc4e",
        "maxLength": -1,
        "minLength": -1,
        "periodIndex": 0,
        "aggregated": False,
    },
    {
        "type": "LONG",
        "dataSourceId": "df419138-5a0b-4bb9-bc8e-03dee6e0cc4e",
        "maxLength": -1,
        "minLength": -1,
        "periodIndex": 0,
        "aggregated": False,
    },
]

_columns = [
    "Date",
    "Partner Name",
    "Concept ID",
    "Concept",
    "Title ID",
    "Product ID",
    "Product Name",
    "Product Primary Class",
    "Product Secondary Class",
    "Product Tertiary Class",
    "Title Platform",
    "SIE Region",
    "Country Code",
    "Country/Region",
    "Wishlist Platform",
    "Additions",
    "Deletions",
    "Deletions by Purchase",
    "Deletions by User",
    "Deletions Other",
]

ps_wishlists_raw_report_zip_content = {
    "manifest.json": json.dumps({
        "dateFrom": "2023-07-21T00:00:00.000Z",
        "dateTo": "2023-07-21T00:00:00.000Z",
    }),
    "playstation_wishlists_2023-07-21_2023-07-21.json": json.dumps({
        "datasource": "df419138-5a0b-4bb9-bc8e-03dee6e0cc4e",
        "columns": _columns,
        "metadata": _metadata,
        "rows": [
            [
                "2023-07-21",
                "SUPERHOT SP. Z O.O.",
                230315,
                "SUPERHOT VR",
                "CUSA08165_00",
                "EP2477-CUSA08165_00-EUSUPERHOTVRGAME",
                "SUPERHOT VR",
                "Premium Game",
                "Game",
                "Vr",
                "PS4",
                "SIEE",
                "ES",
                "Spain",
                "Web Store",
                1,
                0,
                0,
                0,
                0,
            ],
            [
                "2023-07-21",
                "SUPERHOT SP. Z O.O.",
                228844,
                "SUPERHOT",
                "CUSA08144_00",
                "EP2477-CUSA08144_00-EUSUPERHOTGAME00",
                "SUPERHOT",
                "Premium Game",
                "Game",
                "Not available",
                "PS4",
                "SIEAsia",
                "HK",
                "Hong Kong",
                "PS5",
                1,
                0,
                0,
                0,
                0,
            ],
        ],
        "numRows": 53,
        "numColumns": 20,
        "fromcache": False,
    }),
}

ps_wishlists_raw_report_zip_content_empty = {
    "manifest.json": json.dumps({
        "dateFrom": "2023-07-21T00:00:00.000Z",
        "dateTo": "2023-07-21T00:00:00.000Z",
    }),
    "playstation_wishlists_2023-07-21_2023-07-21.json": json.dumps({
        "datasource": "df419138-5a0b-4bb9-bc8e-03dee6e0cc4e",
        "columns": _columns,
        "metadata": _metadata,
        "rows": [],
        "numRows": 0,
        "numColumns": 20,
        "fromcache": False,
    }),
}


ps_wishlists_raw_report_zip_content_two_rows = {
    "manifest.json": json.dumps({
        "dateFrom": "2023-07-21T00:00:00.000Z",
        "dateTo": "2023-07-21T00:00:00.000Z",
    }),
    "playstation_wishlists_2023-07-21_2023-07-21.json": json.dumps({
        "datasource": "df419138-5a0b-4bb9-bc8e-03dee6e0cc4e",
        "columns": _columns,
        "metadata": _metadata,
        "rows": [
            [
                "2023-07-21",
                "SUPERHOT SP. Z O.O.",
                230315,
                "SUPERHOT VR",
                "CUSA08165_00",
                "EP2477-CUSA08165_00-EUSUPERHOTVRGAME",
                "SUPERHOT VR",
                "Premium Game",
                "Game",
                "Vr",
                "PS4",
                "SIEE",
                "ES",
                "Spain",
                "Web Store",
                1,
                0,
                0,
                0,
                0,
            ],
            [
                "2023-07-21",
                "SUPERHOT SP. Z O.O.",
                228844,
                "SUPERHOT",
                "CUSA08144_00",
                "EP2477-CUSA08144_00-EUSUPERHOTGAME00",
                "SUPERHOT",
                "Premium Game",
                "Game",
                "Not available",
                "PS4",
                "SIEAsia",
                "HK",
                "Hong Kong",
                "PS5",
                1,
                0,
                0,
                0,
                0,
            ],
        ],
        "numRows": 2,
        "numColumns": 20,
        "fromcache": False,
    }),
}

ps_wishlists_raw_report_zip_content_platform_not_applicable = {
    "manifest.json": json.dumps({
        "dateFrom": "2023-07-21T00:00:00.000Z",
        "dateTo": "2023-07-21T00:00:00.000Z",
    }),
    "playstation_wishlists_2023-07-21_2023-07-21.json": json.dumps({
        "datasource": "df419138-5a0b-4bb9-bc8e-03dee6e0cc4e",
        "columns": _columns,
        "metadata": _metadata,
        "rows": [
            [
                "2023-07-21",
                "SUPERHOT SP. Z O.O.",
                230315,
                "SUPERHOT VR",
                "CUSA08165_00",
                "EP2477-CUSA08165_00-EUSUPERHOTVRGAME",
                "SUPERHOT VR",
                "Premium Game",
                "Game",
                "Vr",
                "NOT APPLICABLE",
                "SIEE",
                "ES",
                "Spain",
                "Web Store",
                1,
                0,
                0,
                0,
                0,
            ],
        ],
        "numRows": 1,
        "numColumns": 20,
        "fromcache": False,
    }),
}

ps_wishlists_raw_report_no_region_and_country = {
    "manifest.json": json.dumps({
        "dateFrom": "2023-07-21T00:00:00.000Z",
        "dateTo": "2023-07-21T00:00:00.000Z",
    }),
    "playstation_wishlists_2023-07-21_2023-07-21.json": json.dumps({
        "datasource": "df419138-5a0b-4bb9-bc8e-03dee6e0cc4e",
        "columns": _columns,
        "metadata": _metadata,
        "rows": [
            [
                "2023-07-21",
                "SUPERHOT SP. Z O.O.",
                228844,
                "SUPERHOT",
                "CUSA08144_00",
                "EP2477-CUSA08144_00-EUSUPERHOTGAME00",
                "SUPERHOT",
                "Premium Game",
                "Game",
                "Not available",
                "PS4",
                "*",
                "*",
                "Not configured",
                "Web Store",
                1,
                0,
                0,
                0,
                0,
            ],
        ],
        "numRows": 1,
        "numColumns": 20,
        "fromcache": False,
    }),
}

ps_wishlists_raw_report_zip_content_concept_game_and_sales_title = {
    "manifest.json": json.dumps({
        "dateFrom": "2023-07-21T00:00:00.000Z",
        "dateTo": "2023-07-21T00:00:00.000Z",
    }),
    "playstation_wishlists_2023-07-21_2023-07-21.json": json.dumps({
        "datasource": "df419138-5a0b-4bb9-bc8e-03dee6e0cc4e",
        "columns": _columns,
        "metadata": _metadata,
        "rows": [
            [
                "2023-07-21",
                "SUPERHOT SP. Z O.O.",
                230315,
                "SUPERHOT VR",
                "CUSA08165_00",
                "EP2477-CUSA08165_00-EUSUPERHOTVRGAME",
                "SUPERHOT VR",
                "Premium Game",
                "Game",
                "Vr",
                "PS4",
                "SIEE",
                "ES",
                "Spain",
                "Web Store",
                1,
                0,
                0,
                0,
                0,
            ],
            [
                "2023-07-21",
                "SUPERHOT SP. Z O.O.",
                123456789,
                "SUPERCOLD CONCEPT GAME",
                "Not applicable",
                "Not applicable",
                "Not applicable",
                "Not available",
                "Not available",
                "Not available",
                "Not applicable",
                "SIEE",
                "ES",
                "Spain",
                "Web Store",
                1,
                0,
                0,
                0,
                0,
            ],
        ],
        "numRows": 2,
        "numColumns": 20,
        "fromcache": False,
    }),
}

ps_wishlists_raw_report_zip_content_one_concept_game_from_different_regions = {
    "manifest.json": json.dumps({
        "dateFrom": "2023-07-21T00:00:00.000Z",
        "dateTo": "2023-07-21T00:00:00.000Z",
    }),
    "playstation_wishlists_2023-07-21_2023-07-21.json": json.dumps({
        "datasource": "df419138-5a0b-4bb9-bc8e-03dee6e0cc4e",
        "columns": _columns,
        "metadata": _metadata,
        "rows": [
            [
                "2023-07-21",
                "SUPERHOT SP. Z O.O.",
                123456789,
                "SUPERCOLD CONCEPT GAME",
                "Not applicable",
                "Not applicable",
                "Not applicable",
                "Not available",
                "Not available",
                "Not available",
                "Not applicable",
                "SIEA",
                "US",
                "United States",
                "Web Store",
                1,
                0,
                0,
                0,
                0,
            ],
            [
                "2023-07-21",
                "SUPERHOT SP. Z O.O.",
                123456789,
                "SUPERCOLD CONCEPT GAME",
                "Not applicable",
                "Not applicable",
                "Not applicable",
                "Not available",
                "Not available",
                "Not available",
                "Not applicable",
                "SIEE",
                "ES",
                "Spain",
                "Web Store",
                1,
                0,
                0,
                0,
                0,
            ],
        ],
        "numRows": 2,
        "numColumns": 20,
        "fromcache": False,
    }),
}

ps_wishlists_raw_report_zip_content_manifest_only = {
    "manifest.json": json.dumps({
        "dateFrom": "2023-07-21T00:00:00.000Z",
        "dateTo": "2023-07-21T00:00:00.000Z",
    }),
}
