from datetime import datetime, timedelta, timezone

import factory
import pytest

from data_sdk.domain.domain_types import ReportMetadata, ReportMetadataWithRawFile
from data_sdk.domain.source import Source
from tests.converters.wishlist_actions.data import (
    ps_wishlists_raw_report_no_region_and_country,
    ps_wishlists_raw_report_zip_content,
    ps_wishlists_raw_report_zip_content_concept_game_and_sales_title,
    ps_wishlists_raw_report_zip_content_empty,
    ps_wishlists_raw_report_zip_content_manifest_only,
    ps_wishlists_raw_report_zip_content_one_concept_game_from_different_regions,
    ps_wishlists_raw_report_zip_content_platform_not_applicable,
    ps_wishlists_raw_report_zip_content_two_rows,
)


# TODO Make _MetadataFactory a common factory
class _MetadataFactory(factory.Factory):
    class Meta:
        model = ReportMetadata

    class Params:
        number_of_days = 5

    source = Source.PLAYSTATION_WISHLIST_ACTIONS
    studio_id = 1
    report_id = 2
    upload_date = datetime(2021, 1, 27, 11, 10, 0, 277000, tzinfo=timezone.utc)
    blob_name = "ps_wishlist_actions.zip"
    original_name = "ps_wishlist_actions.zip"
    state = "PENDING"
    date_from = datetime(2000, 1, 1, 0, 0)
    date_to = factory.LazyAttribute(
        lambda o: o.date_from + timedelta(days=o.number_of_days - 1)
    )
    no_data = False


@pytest.fixture
def metadata_factory() -> type[factory.Factory]:
    return _MetadataFactory


@pytest.fixture
def ps_wishlists_raw_report(
    metadata_factory, mock_csv_file, mock_zip_file
) -> ReportMetadataWithRawFile:
    metadata = metadata_factory(
        source=Source.PLAYSTATION_WISHLIST_ACTIONS,
        upload_date=datetime(2023, 10, 1, tzinfo=timezone.utc),
        blob_name="ps_wishlists.zip",
        original_name="ps_wishlists.zip",
        date_from=datetime(2023, 7, 21),
        date_to=datetime(2023, 7, 21),
        no_data=False,
    )

    with mock_zip_file(ps_wishlists_raw_report_zip_content) as zip_file:
        return ReportMetadataWithRawFile(
            metadata=metadata, raw_file=zip_file.fp.getvalue()
        )


@pytest.fixture
def ps_wishlists_raw_report_not_applicable_platform(
    metadata_factory, mock_csv_file, mock_zip_file
) -> ReportMetadataWithRawFile:
    metadata = metadata_factory(
        source=Source.PLAYSTATION_WISHLIST_ACTIONS,
        upload_date=datetime(2023, 10, 1, tzinfo=timezone.utc),
        blob_name="ps_wishlists.zip",
        original_name="ps_wishlists.zip",
        date_from=datetime(2023, 7, 21),
        date_to=datetime(2023, 7, 21),
        no_data=False,
    )

    with mock_zip_file(
        ps_wishlists_raw_report_zip_content_platform_not_applicable
    ) as zip_file:
        return ReportMetadataWithRawFile(
            metadata=metadata, raw_file=zip_file.fp.getvalue()
        )


@pytest.fixture
def ps_wishlists_raw_report_two_rows(
    metadata_factory, mock_csv_file, mock_zip_file
) -> ReportMetadataWithRawFile:
    metadata = metadata_factory(
        source=Source.PLAYSTATION_WISHLIST_ACTIONS,
        upload_date=datetime(2023, 10, 1, tzinfo=timezone.utc),
        blob_name="ps_wishlists.zip",
        original_name="ps_wishlists.zip",
        date_from=datetime(2023, 7, 21),
        date_to=datetime(2023, 7, 21),
        no_data=False,
    )

    with mock_zip_file(ps_wishlists_raw_report_zip_content_two_rows) as zip_file:
        return ReportMetadataWithRawFile(
            metadata=metadata, raw_file=zip_file.fp.getvalue()
        )


@pytest.fixture
def ps_wishlists_raw_report_one_line_with_missing_region_and_country(
    metadata_factory, mock_csv_file, mock_zip_file
) -> ReportMetadataWithRawFile:
    metadata = metadata_factory(
        source=Source.PLAYSTATION_WISHLIST_ACTIONS,
        upload_date=datetime(2023, 10, 1, tzinfo=timezone.utc),
        blob_name="ps_wishlists.zip",
        original_name="ps_wishlists.zip",
        date_from=datetime(2023, 7, 21),
        date_to=datetime(2023, 7, 21),
        no_data=False,
    )

    with mock_zip_file(ps_wishlists_raw_report_no_region_and_country) as zip_file:
        return ReportMetadataWithRawFile(
            metadata=metadata, raw_file=zip_file.fp.getvalue()
        )


@pytest.fixture
def ps_wishlists_raw_report_two_rows_concept_game_and_sales_title(
    metadata_factory, mock_csv_file, mock_zip_file
) -> ReportMetadataWithRawFile:
    metadata = metadata_factory(
        source=Source.PLAYSTATION_WISHLIST_ACTIONS,
        upload_date=datetime(2023, 10, 1, tzinfo=timezone.utc),
        blob_name="ps_wishlists.zip",
        original_name="ps_wishlists.zip",
        date_from=datetime(2023, 7, 21),
        date_to=datetime(2023, 7, 21),
        no_data=False,
    )

    with mock_zip_file(
        ps_wishlists_raw_report_zip_content_concept_game_and_sales_title
    ) as zip_file:
        return ReportMetadataWithRawFile(
            metadata=metadata, raw_file=zip_file.fp.getvalue()
        )


@pytest.fixture
def ps_wishlists_raw_report_two_rows_one_concept_game_from_different_regions(
    metadata_factory, mock_csv_file, mock_zip_file
) -> ReportMetadataWithRawFile:
    metadata = metadata_factory(
        source=Source.PLAYSTATION_WISHLIST_ACTIONS,
        upload_date=datetime(2023, 10, 1, tzinfo=timezone.utc),
        blob_name="ps_wishlists.zip",
        original_name="ps_wishlists.zip",
        date_from=datetime(2023, 7, 21),
        date_to=datetime(2023, 7, 21),
        no_data=False,
    )

    with mock_zip_file(
        ps_wishlists_raw_report_zip_content_one_concept_game_from_different_regions
    ) as zip_file:
        return ReportMetadataWithRawFile(
            metadata=metadata, raw_file=zip_file.fp.getvalue()
        )


@pytest.fixture
def ps_wishlists_raw_report_empty(
    metadata_factory, mock_csv_file, mock_zip_file
) -> ReportMetadataWithRawFile:
    metadata = metadata_factory(
        source=Source.PLAYSTATION_WISHLIST_ACTIONS,
        upload_date=datetime(2023, 10, 1, tzinfo=timezone.utc),
        blob_name="ps_wishlists.zip",
        original_name="ps_wishlists.zip",
        date_from=datetime(2023, 7, 21),
        date_to=datetime(2023, 7, 21),
        no_data=False,
    )

    with mock_zip_file(ps_wishlists_raw_report_zip_content_empty) as zip_file:
        return ReportMetadataWithRawFile(
            metadata=metadata, raw_file=zip_file.fp.getvalue()
        )


@pytest.fixture
def ps_wishlists_raw_report_manifest_only(
    metadata_factory, mock_csv_file, mock_zip_file
) -> ReportMetadataWithRawFile:
    metadata = metadata_factory(
        source=Source.PLAYSTATION_WISHLIST_ACTIONS,
        upload_date=datetime(2023, 10, 1, tzinfo=timezone.utc),
        blob_name="ps_wishlists.zip",
        original_name="ps_wishlists.zip",
        date_from=datetime(2023, 7, 21),
        date_to=datetime(2023, 7, 21),
        no_data=False,
    )

    with mock_zip_file(ps_wishlists_raw_report_zip_content_manifest_only) as zip_file:
        return ReportMetadataWithRawFile(
            metadata=metadata, raw_file=zip_file.fp.getvalue()
        )
