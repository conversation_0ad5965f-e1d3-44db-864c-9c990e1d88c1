from datetime import date

import polars as pl
from polars.testing import assert_frame_equal

from core_silver.observation_converter.converters.ps_wishlist_actions import (
    PsWishlistActionsConverter,
)
from data_sdk.domain import DisplayPortal
from data_sdk.domain.platform import DisplayPlatform
from data_sdk.domain.regions import Region
from data_sdk.domain.stores import Store


def test_convert_for_wishlist_actions_run(ps_wishlists_raw_report):
    result = PsWishlistActionsConverter(ps_wishlists_raw_report).convert()
    assert not result.df.is_empty()


def test_convert_for_wishlist_actions_run_empty(ps_wishlists_raw_report_empty):
    result = PsWishlistActionsConverter(ps_wishlists_raw_report_empty).convert()
    assert result.df.is_empty()


def test_convert_for_wishlist_actions_run_not_applicable_platform(
    converted_wishlist_actions_factory,
    ps_wishlists_raw_report_not_applicable_platform,
):
    result = PsWishlistActionsConverter(
        ps_wishlists_raw_report_not_applicable_platform
    ).convert()

    expected_df = converted_wishlist_actions_factory.build_df_batch(
        size=1,
        report_id=2,
        platform=DisplayPlatform.UNKNOWN,
        region=Region.PLAYSTATION_EUROPE,
        portal=DisplayPortal.PLAYSTATION,
        date=date(2023, 7, 21),
        human_name="SUPERHOT VR",
        adds=1,
        sku_id="EP2477-CUSA08165_00-EUSUPERHOTVRGAME",
        unique_sku_id="EP2477-CUSA08165_00-EUSUPERHOTVRGAME-SIEE-playstation-store:1",
        store_id="CUSA08165_00",
        store=Store.PLAYSTATION_EUROPE,
        abbreviated_name=Store.PLAYSTATION_EUROPE.abbreviate(),
        country_code="ESP",
    )

    assert_frame_equal(result.df, expected_df.select(result.df.columns))


def test_convert_based_on_two_rows_of_data(
    converted_wishlist_actions_factory, ps_wishlists_raw_report_two_rows
):
    result = PsWishlistActionsConverter(ps_wishlists_raw_report_two_rows).convert()

    expected_df_1 = converted_wishlist_actions_factory.build_df_batch(
        size=1,
        report_id=2,
        platform=DisplayPlatform.PS_4,
        region=Region.PLAYSTATION_EUROPE,
        portal=DisplayPortal.PLAYSTATION,
        date=date(2023, 7, 21),
        human_name="SUPERHOT VR",
        adds=1,
        sku_id="EP2477-CUSA08165_00-EUSUPERHOTVRGAME",
        unique_sku_id="EP2477-CUSA08165_00-EUSUPERHOTVRGAME-SIEE-playstation-store:1",
        store_id="CUSA08165_00",
        store=Store.PLAYSTATION_EUROPE,
        abbreviated_name=Store.PLAYSTATION_EUROPE.abbreviate(),
        country_code="ESP",
    )
    expected_df_2 = converted_wishlist_actions_factory.build_df_batch(
        size=1,
        report_id=2,
        platform=DisplayPlatform.PS_4,
        region=Region.PLAYSTATION_ASIA,
        portal=DisplayPortal.PLAYSTATION,
        date=date(2023, 7, 21),
        human_name="SUPERHOT",
        adds=1,
        sku_id="EP2477-CUSA08144_00-EUSUPERHOTGAME00",
        unique_sku_id="EP2477-CUSA08144_00-EUSUPERHOTGAME00-SIEAsia-playstation-store:1",
        store_id="CUSA08144_00",
        store=Store.PLAYSTATION_ASIA,
        abbreviated_name=Store.PLAYSTATION_ASIA.abbreviate(),
        country_code="HKG",
    )
    expected_df = pl.concat([expected_df_1, expected_df_2])

    assert_frame_equal(result.df, expected_df.select(result.df.columns))


def test_convert_with_no_region_and_country(
    converted_wishlist_actions_factory,
    ps_wishlists_raw_report_one_line_with_missing_region_and_country,
):
    result = PsWishlistActionsConverter(
        ps_wishlists_raw_report_one_line_with_missing_region_and_country
    ).convert()

    expected_df = converted_wishlist_actions_factory.build_df_batch(
        size=1,
        report_id=2,
        platform=DisplayPlatform.PS_4,
        region=Region.PLAYSTATION_UNKNOWN,
        portal=DisplayPortal.PLAYSTATION,
        date=date(2023, 7, 21),
        human_name="SUPERHOT",
        adds=1,
        sku_id="EP2477-CUSA08144_00-EUSUPERHOTGAME00",
        unique_sku_id="EP2477-CUSA08144_00-EUSUPERHOTGAME00-Unknown-playstation-store:1",
        store_id="CUSA08144_00",
        store=Store.PLAYSTATION_UNKNOWN,
        abbreviated_name=Store.PLAYSTATION_UNKNOWN.abbreviate(),
        country_code="YYY",
    )

    assert_frame_equal(result.df, expected_df.select(result.df.columns))


def test_convert_based_on_two_rows_of_data_concept_game_and_sales_title(
    converted_wishlist_actions_factory,
    ps_wishlists_raw_report_two_rows_concept_game_and_sales_title,
):
    result = PsWishlistActionsConverter(
        ps_wishlists_raw_report_two_rows_concept_game_and_sales_title
    ).convert()

    expected_df_1 = converted_wishlist_actions_factory.build_df_batch(
        size=1,
        report_id=2,
        platform=DisplayPlatform.PS_4,
        region=Region.PLAYSTATION_EUROPE,
        portal=DisplayPortal.PLAYSTATION,
        date=date(2023, 7, 21),
        human_name="SUPERHOT VR",
        adds=1,
        sku_id="EP2477-CUSA08165_00-EUSUPERHOTVRGAME",
        unique_sku_id="EP2477-CUSA08165_00-EUSUPERHOTVRGAME-SIEE-playstation-store:1",
        store_id="CUSA08165_00",
        store=Store.PLAYSTATION_EUROPE,
        abbreviated_name=Store.PLAYSTATION_EUROPE.abbreviate(),
        country_code="ESP",
    )
    expected_df_2 = converted_wishlist_actions_factory.build_df_batch(
        size=1,
        report_id=2,
        platform=DisplayPlatform.UNKNOWN,
        region=Region.PLAYSTATION_EUROPE,
        portal=DisplayPortal.PLAYSTATION,
        date=date(2023, 7, 21),
        human_name="SUPERCOLD CONCEPT GAME",
        adds=1,
        sku_id="123456789",
        unique_sku_id="123456789-SIEE-playstation-store:1",
        store_id="123456789",
        store=Store.PLAYSTATION_EUROPE,
        abbreviated_name=Store.PLAYSTATION_EUROPE.abbreviate(),
        country_code="ESP",
    )
    expected_df = pl.concat([expected_df_1, expected_df_2])

    assert_frame_equal(result.df, expected_df.select(result.df.columns))


def test_convert_one_concept_game_from_different_regions_creates_two_unique_sku_ids(
    converted_wishlist_actions_factory,
    ps_wishlists_raw_report_two_rows_one_concept_game_from_different_regions,
):
    result = PsWishlistActionsConverter(
        ps_wishlists_raw_report_two_rows_one_concept_game_from_different_regions
    ).convert()
    expected_df_1 = converted_wishlist_actions_factory.build_df_batch(
        size=1,
        report_id=2,
        platform=DisplayPlatform.UNKNOWN,
        region=Region.PLAYSTATION_AMERICA,
        portal=DisplayPortal.PLAYSTATION,
        date=date(2023, 7, 21),
        human_name="SUPERCOLD CONCEPT GAME",
        adds=1,
        sku_id="123456789",
        unique_sku_id="123456789-SIEA-playstation-store:1",
        store_id="123456789",
        store=Store.PLAYSTATION_AMERICA,
        abbreviated_name=Store.PLAYSTATION_AMERICA.abbreviate(),
        country_code="USA",
        portal_platform_region="PlayStation:Unknown:PS America",
    )
    expected_df_2 = converted_wishlist_actions_factory.build_df_batch(
        size=1,
        report_id=2,
        platform=DisplayPlatform.UNKNOWN,
        region=Region.PLAYSTATION_EUROPE,
        portal=DisplayPortal.PLAYSTATION,
        date=date(2023, 7, 21),
        human_name="SUPERCOLD CONCEPT GAME",
        adds=1,
        sku_id="123456789",
        unique_sku_id="123456789-SIEE-playstation-store:1",
        store_id="123456789",
        store=Store.PLAYSTATION_EUROPE,
        abbreviated_name=Store.PLAYSTATION_EUROPE.abbreviate(),
        country_code="ESP",
        portal_platform_region="PlayStation:Unknown:PS Europe",
    )
    expected_df = pl.concat([expected_df_1, expected_df_2])

    assert_frame_equal(result.df, expected_df.select(result.df.columns))


def test_convert_for_wishlist_actions_run_manifest_only(
    ps_wishlists_raw_report_manifest_only,
):
    result = PsWishlistActionsConverter(ps_wishlists_raw_report_manifest_only).convert()
    assert result.df.is_empty()
