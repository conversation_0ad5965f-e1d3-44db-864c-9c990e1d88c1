import calendar

import factory
import pytest
from attr import dataclass

from data_sdk.domain import Portal
from data_sdk.domain.domain_types import ReportMetadataWithRawFile
from data_sdk.domain.observations import ObservationType
from data_sdk.domain.source import Source
from tests.conftest import ExternalReportsFactory, ListBetterSubFactory, ListSubFactory
from tests.utils import _raw_zip_file


class MicrosoftMonthlyActiveUsersRowFactory(factory.StubFactory):
    class Params:
        start_year = 2024
        start_month = 1

    year = factory.LazyAttributeSequence(
        lambda o, n: o.start_year + (o.start_month + n - 1) // 12
    )
    month = factory.LazyAttributeSequence(lambda o, n: (o.start_month + n - 1) % 12 + 1)

    account = "Test account"
    portal = "microsoft"
    product = "Test product"
    product_id = "9NV17MJB26PG"
    count = 100

    @factory.lazy_attribute
    def csv_row(self):
        return f"{self.account},{self.portal},{self.year},{self.month},{self.product},{self.product_id},{self.count}\n"


@dataclass
class MicrosoftRawMonthlyActiveUsers:
    start_year: int
    start_month: int
    end_year: int
    end_month: int
    rows: list

    @property
    def manifest_json(self):
        last_day_of_month = calendar.monthrange(self.end_year, self.end_month)[1]

        return {
            "manifest.json": {
                "dateFrom": f"{self.start_year}-{self.start_month:02d}-01",
                "dateTo": f"{self.end_year}-{self.end_month:02d}-{last_day_of_month}",
                "fileMetaData": {
                    f"microsoft_monthly_active_users-{self.start_year}-{self.start_month:02d}-01-{self.end_year}-{self.end_month:02d}-{last_day_of_month}.csv": {
                        "dateFrom": f"{self.start_year}-{self.start_month:02d}-01",
                        "dateTo": f"{self.end_year}-{self.end_month:02d}-{last_day_of_month}",
                    },
                },
            }
        }

    @property
    def monthly_active_users_csv(self):
        csvs = {}

        last_day_of_month = calendar.monthrange(self.end_year, self.end_month)[1]
        monthly_header = "account,portal,year,month,product,product_id,count\n"
        monthly_filename = f"microsoft_monthly_active_users-{self.start_year}-{self.start_month:02d}-01-{self.end_year}-{self.end_month:02d}-{last_day_of_month}.csv"
        csvs[monthly_filename] = monthly_header
        for row in self.rows:
            csvs[monthly_filename] += row.csv_row

        return csvs

    @property
    def zip_content(self):
        return {**self.manifest_json, **self.monthly_active_users_csv}

    @property
    def raw_zip_file(self):
        return _raw_zip_file(self.zip_content)

    def __add__(self, other):
        return MicrosoftRawMonthlyActiveUsersFactory(
            start_year=self.start_year,
            start_month=self.start_month,
            end_year=other.end_year,
            end_month=other.end_month,
            rows=self.rows + other.rows,
        )


class MicrosoftRawMonthlyActiveUsersFactory(factory.Factory):
    class Meta:
        model = MicrosoftRawMonthlyActiveUsers

    start_year = 2024
    start_month = 4
    end_year = 2024
    end_month = 4

    rows = ListSubFactory(
        MicrosoftMonthlyActiveUsersRowFactory,
        size=lambda o: (o.end_year - o.start_year) * 12
        + (o.end_month - o.start_month)
        + 1,
        start_year=factory.LazyAttribute(lambda row: row.factory_parent.start_year),
        start_month=factory.LazyAttribute(lambda row: row.factory_parent.start_month),
    )


@pytest.fixture
def microsoft_raw_monthly_active_users_factory():
    return MicrosoftRawMonthlyActiveUsersFactory


@pytest.fixture
def external_microsoft_monthly_active_users_reports_factory():
    class ExternalMicrosoftMonthlyActiveUsersReportsFactory(ExternalReportsFactory):
        source = Source.MICROSOFT_MONTHLY_ACTIVE_USERS
        portal = Portal.MICROSOFT
        observation_type = ObservationType.MONTHLY_ACTIVE_USERS

    return ExternalMicrosoftMonthlyActiveUsersReportsFactory


@pytest.fixture
def microsoft_monthly_active_users_metadata_with_raw_file_factory(
    microsoft_raw_monthly_active_users_factory,
    external_microsoft_monthly_active_users_reports_factory,
):
    class MicrosoftMonthlyActiveUsersReportMetadataWithRawFileFactory(factory.Factory):
        class Meta:
            model = ReportMetadataWithRawFile

        input_raw_file = factory.SubFactory(microsoft_raw_monthly_active_users_factory)
        metadata = factory.SubFactory(
            external_microsoft_monthly_active_users_reports_factory
        )

        @factory.lazy_attribute
        def raw_file(self):
            return self.input_raw_file.raw_zip_file

    return MicrosoftMonthlyActiveUsersReportMetadataWithRawFileFactory


@pytest.fixture
def generate_raw_microsoft_monthly_active_users_report(
    microsoft_monthly_active_users_metadata_with_raw_file_factory,
    microsoft_raw_monthly_active_users_factory,
    external_microsoft_monthly_active_users_reports_factory,
):
    def generate_raw_report(
        rows: int | None = None,
        custom_rows_data=None,
        year: int = 2024,
        month: int = 1,
        metadata_date_from=None,
        metadata_date_to=None,
    ):
        from calendar import monthrange
        from datetime import date

        # Set metadata to full month if not explicitly provided
        metadata_kwargs = {}
        if metadata_date_from and metadata_date_to:
            metadata_kwargs["date_from"] = metadata_date_from
            metadata_kwargs["date_to"] = metadata_date_to
        else:
            # Auto-generate metadata for full month
            _, last_day = monthrange(year, month)
            metadata_kwargs["date_from"] = date(year, month, 1)
            metadata_kwargs["date_to"] = date(year, month, last_day)

        # Create raw file with consistent year/month
        raw_file = microsoft_raw_monthly_active_users_factory(
            start_year=year,
            start_month=month,
            end_year=year,
            end_month=month,
            rows=ListBetterSubFactory(
                MicrosoftMonthlyActiveUsersRowFactory,
                size=len(custom_rows_data)
                if custom_rows_data is not None and rows is None
                else rows or 1,
                start_year=year,
                start_month=month,
                custom_rows_data=custom_rows_data if custom_rows_data else [],
            ),
        )

        return microsoft_monthly_active_users_metadata_with_raw_file_factory(
            input_raw_file=raw_file,
            metadata=external_microsoft_monthly_active_users_reports_factory(
                **metadata_kwargs
            ),
        )

    return generate_raw_report
