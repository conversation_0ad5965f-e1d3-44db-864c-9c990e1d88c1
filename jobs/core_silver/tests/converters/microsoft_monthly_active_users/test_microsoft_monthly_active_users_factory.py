import json
import zipfile
from datetime import date, datetime, timezone
from io import BytesIO

import pytest

from data_sdk.domain.domain_types import (
    ReportMetadata,
    ReportMetadataWithRawFile,
    ReportState,
    StudioId,
)
from data_sdk.domain.source import Source


def test_generate_empty_microsoft_monthly_active_users(
    microsoft_raw_monthly_active_users_factory,
):
    result = microsoft_raw_monthly_active_users_factory(
        start_year=2023, start_month=12, end_year=2023, end_month=12, rows=[]
    )
    assert result.start_year == 2023
    assert result.start_month == 12
    assert result.end_year == 2023
    assert result.end_month == 12
    assert result.manifest_json == {
        "manifest.json": {
            "dateFrom": "2023-12-01",
            "dateTo": "2023-12-31",
            "fileMetaData": {
                "microsoft_monthly_active_users-2023-12-01-2023-12-31.csv": {
                    "dateFrom": "2023-12-01",
                    "dateTo": "2023-12-31",
                },
            },
        },
    }
    assert result.monthly_active_users_csv == {
        "microsoft_monthly_active_users-2023-12-01-2023-12-31.csv": "account,portal,year,month,product,product_id,count\n",
    }


def test_generate_one_month_of_microsoft_monthly_active_users(
    microsoft_raw_monthly_active_users_factory,
):
    result = microsoft_raw_monthly_active_users_factory(
        start_year=2024,
        start_month=4,
        end_year=2024,
        end_month=4,
    )

    assert result.start_year == 2024
    assert result.start_month == 4
    assert result.end_year == 2024
    assert result.end_month == 4
    assert result.manifest_json == {
        "manifest.json": {
            "dateFrom": "2024-04-01",
            "dateTo": "2024-04-30",
            "fileMetaData": {
                "microsoft_monthly_active_users-2024-04-01-2024-04-30.csv": {
                    "dateFrom": "2024-04-01",
                    "dateTo": "2024-04-30",
                },
            },
        },
    }
    assert result.monthly_active_users_csv == {
        "microsoft_monthly_active_users-2024-04-01-2024-04-30.csv": (
            "account,portal,year,month,product,product_id,count\n"
            "Test account,microsoft,2024,4,Test product,9NV17MJB26PG,100\n"
        ),
    }


def test_generate_microsoft_monthly_active_users_with_custom_data(
    microsoft_raw_monthly_active_users_factory,
):
    result = microsoft_raw_monthly_active_users_factory(
        start_year=2023,
        start_month=12,
        end_year=2023,
        end_month=12,
        rows__product="SUPERHOT WINDOWS 10",
        rows__product_id="9NV17MJB26PG",
        rows__count=1500,
    )

    assert result.start_year == 2023
    assert result.start_month == 12
    assert result.end_year == 2023
    assert result.end_month == 12
    assert result.manifest_json == {
        "manifest.json": {
            "dateFrom": "2023-12-01",
            "dateTo": "2023-12-31",
            "fileMetaData": {
                "microsoft_monthly_active_users-2023-12-01-2023-12-31.csv": {
                    "dateFrom": "2023-12-01",
                    "dateTo": "2023-12-31",
                },
            },
        },
    }
    assert result.monthly_active_users_csv == {
        "microsoft_monthly_active_users-2023-12-01-2023-12-31.csv": (
            "account,portal,year,month,product,product_id,count\n"
            "Test account,microsoft,2023,12,SUPERHOT WINDOWS 10,9NV17MJB26PG,1500\n"
        ),
    }


def test_generate_multiple_months_of_microsoft_monthly_active_users(
    microsoft_raw_monthly_active_users_factory,
):
    result = microsoft_raw_monthly_active_users_factory(
        start_year=2024,
        start_month=3,
        end_year=2024,
        end_month=5,
        rows__size=3,  # 3 months: March, April, May
    )

    assert result.start_year == 2024
    assert result.start_month == 3
    assert result.end_year == 2024
    assert result.end_month == 5

    assert len(result.rows) == 3

    csv_content = result.monthly_active_users_csv[
        "microsoft_monthly_active_users-2024-03-01-2024-05-31.csv"
    ]
    assert "Test account,microsoft,2024,3,Test product,9NV17MJB26PG,100" in csv_content
    assert "Test account,microsoft,2024,4,Test product,9NV17MJB26PG,100" in csv_content
    assert "Test account,microsoft,2024,5,Test product,9NV17MJB26PG,100" in csv_content


def test_microsoft_monthly_active_users_can_be_added_together(
    microsoft_raw_monthly_active_users_factory,
):
    first_month = microsoft_raw_monthly_active_users_factory(
        start_year=2024, start_month=1, end_year=2024, end_month=1
    )
    second_month = microsoft_raw_monthly_active_users_factory(
        start_year=2024, start_month=2, end_year=2024, end_month=2
    )

    combined = first_month + second_month

    assert combined.start_year == 2024
    assert combined.start_month == 1
    assert combined.end_year == 2024
    assert combined.end_month == 2
    assert len(combined.rows) == 2


def test_generate_raw_report_with_default_parameters(
    generate_raw_microsoft_monthly_active_users_report,
    mock_csv_file,
    raw_zip_file,
):
    """Test wrapper with default parameters - should generate complete report for January 2024."""
    actual_raw_report = generate_raw_microsoft_monthly_active_users_report()

    expected_raw_report = ReportMetadataWithRawFile(
        metadata=ReportMetadata(
            source=Source.MICROSOFT_MONTHLY_ACTIVE_USERS,
            studio_id=StudioId(1),
            id=actual_raw_report.metadata.report_id,
            upload_date=datetime(2024, 1, 31, tzinfo=timezone.utc),
            file_path_raw="MICROSOFT-2024-01-01_2024-01-31.zip",
            original_name="MICROSOFT-2024-01-01_2024-01-31.zip",
            state=ReportState.PENDING,
            no_data=False,
            date_from=date(2024, 1, 1),
            date_to=date(2024, 1, 31),
        ),
        raw_file=raw_zip_file({
            "manifest.json": json.dumps({
                "dateFrom": "2024-01-01",
                "dateTo": "2024-01-31",
                "fileMetaData": {
                    "microsoft_monthly_active_users-2024-01-01-2024-01-31.csv": {
                        "dateFrom": "2024-01-01",
                        "dateTo": "2024-01-31",
                    }
                },
            }),
            "microsoft_monthly_active_users-2024-01-01-2024-01-31.csv": mock_csv_file(
                [
                    "account",
                    "portal",
                    "year",
                    "month",
                    "product",
                    "product_id",
                    "count",
                ],
                [
                    {
                        "account": "Test account",
                        "portal": "microsoft",
                        "year": 2024,
                        "month": 1,
                        "product": "Test product",
                        "product_id": "9NV17MJB26PG",
                        "count": 100,
                    }
                ],
            ),
        }),
    )

    assert actual_raw_report.model_dump() == expected_raw_report.model_dump()


@pytest.mark.parametrize(
    ("year_month", "expected_last_day"),
    [
        ((2024, 6), 30),  # June 2024 (30 days)
        ((2024, 2), 29),  # February 2024 (29 days - leap year)
        ((2023, 2), 28),  # February 2023 (28 days - non-leap year)
        ((2024, 1), 31),  # January 2024 (31 days)
        ((2024, 4), 30),  # April 2024 (30 days)
    ],
)
def test_generate_raw_report_with_custom_year_month(
    generate_raw_microsoft_monthly_active_users_report,
    year_month,
    expected_last_day,
):
    year, month = year_month
    raw_report = generate_raw_microsoft_monthly_active_users_report(
        year=year, month=month
    )

    assert raw_report.metadata.date_from == date(year, month, 1)
    assert raw_report.metadata.date_to == date(year, month, expected_last_day)


def test_generate_raw_report_with_explicit_metadata_dates(
    generate_raw_microsoft_monthly_active_users_report,
):
    """Test wrapper with explicitly provided metadata dates (should override auto-generation)."""
    raw_report = generate_raw_microsoft_monthly_active_users_report(
        year=2024,
        month=6,
        metadata_date_from=date(2024, 6, 15),
        metadata_date_to=date(2024, 6, 20),
    )

    # Should use explicit dates, not auto-generated full month
    assert raw_report.metadata.date_from == date(2024, 6, 15)
    assert raw_report.metadata.date_to == date(2024, 6, 20)


def test_generate_raw_report_with_custom_rows_data(
    generate_raw_microsoft_monthly_active_users_report,
):
    raw_report = generate_raw_microsoft_monthly_active_users_report(
        year=2024,
        month=3,
        custom_rows_data=[
            {
                "year": 2024,
                "month": 3,
                "product": "Custom Product",
                "product_id": "CUSTOM123",
                "count": 999,
            }
        ],
    )

    with zipfile.ZipFile(BytesIO(raw_report.raw_file), "r") as zip_file:
        manifest_content = json.loads(zip_file.read("manifest.json").decode("utf-8"))
        csv_filename = list(manifest_content["fileMetaData"].keys())[0]

        csv_content = zip_file.read(csv_filename).decode("utf-8")

    expected_csv_content = (
        "account,portal,year,month,product,product_id,count\n"
        "Test account,microsoft,2024,3,Custom Product,CUSTOM123,999\n"
    )

    assert csv_content == expected_csv_content
