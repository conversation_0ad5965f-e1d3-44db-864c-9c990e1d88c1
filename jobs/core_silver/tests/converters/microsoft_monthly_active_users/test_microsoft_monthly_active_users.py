import json
from datetime import date

import polars as pl
import pytest

from core_silver.observation_converter.converters.exceptions import (
    FileExtractionError,
    FileSchemaError,
)
from core_silver.observation_converter.converters.microsoft_monthly_active_users import (
    MicrosoftMonthlyActiveUsersConverter,
)


def test_convert_microsoft_monthly_active_users_empty(
    generate_raw_microsoft_monthly_active_users_report,
):
    raw_report = generate_raw_microsoft_monthly_active_users_report(rows=0)
    converter = MicrosoftMonthlyActiveUsersConverter(raw_report)
    result = converter.convert()
    assert result.empty


def test_convert_microsoft_monthly_active_users_basic(
    generate_raw_microsoft_monthly_active_users_report,
):
    raw_report = generate_raw_microsoft_monthly_active_users_report(year=2024, month=1)
    days_of_month = 31
    converter = MicrosoftMonthlyActiveUsersConverter(raw_report)
    result = converter.convert()

    expected_df = pl.DataFrame([
        {
            "platform": "PC",
            "region": "Global",
            "portal": "Microsoft",
            "date": date(2024, 1, day_index + 1),
            "human_name": "Test product",
            "monthly_active_users": 100,
            "sku_id": "9NV17MJB26PG",
            "store_id": "9NV17MJB26PG",
            "store": "Microsoft",
            "abbreviated_name": "Microsoft",
            "report_id": raw_report.metadata.report_id,
            "studio_id": 1,
            "unique_sku_id": "9NV17MJB26PG-microsoft:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Microsoft:PC:Global",
        }
        for day_index in range(0, days_of_month)
    ])

    assert result.df.equals(expected_df)


def test_convert_microsoft_monthly_active_users_with_multiple_skus(
    generate_raw_microsoft_monthly_active_users_report,
):
    custom_data = [
        {
            "year": 2025,
            "month": 6,
            "product": "SUPERHOT WINDOWS 10",
            "product_id": "9NV17MJB26PG",
            "count": 162,
        },
        {
            "year": 2025,
            "month": 6,
            "product": "SUPERHOT VR",
            "product_id": "9NT60N3XPF7T",
            "count": 13,
        },
        {
            "year": 2025,
            "month": 6,
            "product": "SUPERHOT: MIND CONTROL DELETE",
            "product_id": "9NRH78B682L8",
            "count": 149063,
        },
    ]
    raw_report = generate_raw_microsoft_monthly_active_users_report(
        year=2025,
        month=6,
        custom_rows_data=custom_data,
    )
    days_of_month = 30
    converter = MicrosoftMonthlyActiveUsersConverter(raw_report)
    result = converter.convert()

    expected_df = pl.DataFrame([
        {
            "platform": "PC",
            "region": "Global",
            "portal": "Microsoft",
            "date": date(2025, 6, day_index + 1),
            "human_name": row["product"],
            "monthly_active_users": row["count"],
            "sku_id": row["product_id"],
            "store_id": row["product_id"],
            "store": "Microsoft",
            "abbreviated_name": "Microsoft",
            "report_id": raw_report.metadata.report_id,
            "studio_id": 1,
            "unique_sku_id": f"{row['product_id']}-microsoft:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Microsoft:PC:Global",
        }
        for row in custom_data
        for day_index in range(0, days_of_month)
    ])

    assert result.df.equals(expected_df)


def test_convert_microsoft_monthly_active_users_with_multiple_skus_and_not_full_month(
    generate_raw_microsoft_monthly_active_users_report,
):
    custom_data = [
        {
            "year": 2025,
            "month": 6,
            "product": "SUPERHOT WINDOWS 10",
            "product_id": "9NV17MJB26PG",
            "count": 162,
        },
        {
            "year": 2025,
            "month": 6,
            "product": "SUPERHOT VR",
            "product_id": "9NT60N3XPF7T",
            "count": 13,
        },
    ]
    number_of_days = 3
    raw_report = generate_raw_microsoft_monthly_active_users_report(
        year=2025,
        month=6,
        custom_rows_data=custom_data,
        metadata_date_from=date(2025, 6, 1),
        metadata_date_to=date(2025, 6, number_of_days),
    )
    converter = MicrosoftMonthlyActiveUsersConverter(raw_report)
    result = converter.convert()

    expected_df = pl.DataFrame([
        {
            "platform": "PC",
            "region": "Global",
            "portal": "Microsoft",
            "date": date(2025, 6, day_index + 1),
            "human_name": row["product"],
            "monthly_active_users": row["count"],
            "sku_id": row["product_id"],
            "store_id": row["product_id"],
            "store": "Microsoft",
            "abbreviated_name": "Microsoft",
            "report_id": raw_report.metadata.report_id,
            "studio_id": 1,
            "unique_sku_id": f"{row['product_id']}-microsoft:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Microsoft:PC:Global",
        }
        for row in custom_data
        for day_index in range(0, number_of_days)
    ])

    assert result.df.equals(expected_df)


def test_parse_invalid_file_raises_file_extraction_error(
    generate_raw_microsoft_monthly_active_users_report,
):
    raw_report = generate_raw_microsoft_monthly_active_users_report()
    raw_report.raw_file = b"invalid zip content"

    converter = MicrosoftMonthlyActiveUsersConverter(raw_report)

    with pytest.raises(FileExtractionError):
        converter.parse(raw_report.raw_file)


def test_parse_invalid_schema_raises_file_schema_error(
    generate_raw_microsoft_monthly_active_users_report, raw_zip_file, mock_csv_file
):
    invalid_raw_file = raw_zip_file({
        "manifest.json": json.dumps({
            "dateFrom": "2024-01-01",
            "dateTo": "2024-01-31",
            "fileMetaData": {
                "microsoft_monthly_active_users-2024-01-01-2024-01-31.csv": {
                    "dateFrom": "2024-01-01",
                    "dateTo": "2024-01-31",
                }
            },
        }),
        "microsoft_monthly_active_users-2024-01-01-2024-01-31.csv": mock_csv_file(
            [
                "invalid",
                "header",
                "structure",
            ],
            [
                {
                    "invalid": "data",
                    "header": "without",
                    "structure": "proper",
                }
            ],
        ),
    })

    raw_report = generate_raw_microsoft_monthly_active_users_report()
    raw_report.raw_file = invalid_raw_file
    converter = MicrosoftMonthlyActiveUsersConverter(raw_report)

    with pytest.raises(FileSchemaError):
        converter.parse(invalid_raw_file)
