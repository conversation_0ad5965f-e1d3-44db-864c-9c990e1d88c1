import datetime

import polars as pl

from core_silver.observation_converter.converters.discount_sanitize_functions import (
    deduplicate_items_and_combine_name,
    deduplicate_items_with_fully_overlapping_time_but_different_discount_depth,
    filter_out_data_older_than_2016,
    round_datetimes_to_nearest_30_minutes,
)


def test_data_older_than_2016_is_filtered(converted_discounts_factory):
    discount = converted_discounts_factory.build(size=1)
    discount["datetime_from"] = datetime.datetime(2015, 1, 1, 0, 0)

    result = filter_out_data_older_than_2016(pl.DataFrame([discount]))

    assert len(result) == 0


def test_deduplication_with_name_combination(converted_discounts_factory):
    event_a = converted_discounts_factory.build(
        size=1, event_name="Steam Summer Sale 2024"
    )
    event_b = converted_discounts_factory.build(
        size=1,
        event_name="Custom Sale",
        datetime_from=event_a["datetime_from"],
        datetime_to=event_a["datetime_to"],
    )
    event_c = converted_discounts_factory.build(size=1, unique_sku_id="other")
    df = pl.DataFrame([event_a, event_b, event_c])
    expected_discount_with_deduplicated_name = event_a.copy()
    expected_discount_with_deduplicated_name["event_name"] = (
        f"{event_a['event_name']} / {event_b['event_name']}"
    )

    result = deduplicate_items_and_combine_name(df)

    assert len(result) == 2
    assert sorted(result.to_dicts(), key=lambda x: x["event_name"]) == sorted(
        [
            expected_discount_with_deduplicated_name,
            event_c,
        ],
        key=lambda x: x["event_name"],
    )


def test_deduplication_with_name_combination_three_events(converted_discounts_factory):
    event_a = converted_discounts_factory.build(
        size=1, event_name="Steam Summer Sale 2024"
    )
    event_b = converted_discounts_factory.build(
        size=1,
        event_name="Custom Sale",
        datetime_from=event_a["datetime_from"],
        datetime_to=event_a["datetime_to"],
    )
    event_c = converted_discounts_factory.build(
        size=1,
        event_name="Custom Sale 2",
        datetime_from=event_a["datetime_from"],
        datetime_to=event_a["datetime_to"],
    )
    df = pl.DataFrame([event_a, event_b, event_c])
    expected_discount_with_deduplicated_name = event_a.copy()
    expected_discount_with_deduplicated_name["event_name"] = (
        f"{event_a['event_name']} / {event_b['event_name']} / {event_c['event_name']}"
    )

    result = deduplicate_items_and_combine_name(df)

    assert len(result) == 1
    assert sorted(result.to_dicts(), key=lambda x: x["event_name"]) == sorted(
        [
            expected_discount_with_deduplicated_name,
        ],
        key=lambda x: x["event_name"],
    )


def test_deduplicate_items_with_overlapping_time_but_different_discount_depth(
    converted_discounts_factory,
):
    event_a = converted_discounts_factory.build(size=1)
    modified_discount_depth = converted_discounts_factory.build(size=1)
    modified_discount_depth["discount_depth"] = 0.13
    df = pl.DataFrame([modified_discount_depth, event_a])
    result = deduplicate_items_with_fully_overlapping_time_but_different_discount_depth(
        df
    )
    assert len(result) == 1
    assert result.to_dicts() == ([event_a])


def test_should_round_all_datetimes_to_nearest_30_minutes(
    converted_discounts_factory,
):
    discount_1 = converted_discounts_factory.build(
        datetime_from=datetime.datetime(2024, 6, 27, 17, 14),
        datetime_to=datetime.datetime(2024, 6, 30, 14, 45),
    )
    discount_2 = converted_discounts_factory.build(
        datetime_from=datetime.datetime(2024, 6, 27, 16, 58),
        datetime_to=datetime.datetime(2024, 6, 30, 15, 1),
    )

    raw_result = round_datetimes_to_nearest_30_minutes(
        pl.DataFrame([discount_1, discount_2])
    )

    result = raw_result.to_dict(as_series=False)
    assert len(raw_result) == 2
    assert result["datetime_from"] == [
        datetime.datetime(2024, 6, 27, 17, 0),
        datetime.datetime(2024, 6, 27, 17, 0),
    ]
    assert result["datetime_to"] == [
        datetime.datetime(2024, 6, 30, 15, 0),
        datetime.datetime(2024, 6, 30, 15, 0),
    ]
