from datetime import date

import factory
import pytest
from attr import dataclass

from data_sdk.domain import Portal
from data_sdk.domain.domain_types import ReportMetadataWithRawFile
from data_sdk.domain.observations import ObservationType
from data_sdk.domain.source import Source
from tests.conftest import ExternalReportsFactory, ListBetterSubFactory, ListSubFactory
from tests.utils import _mock_zip_file


class PlaystationMonthlyActiveUsersRowFactory(factory.StubFactory):
    class Params:
        start_date = date(year=2000, month=1, day=1)

    year = "2025"
    month = "06"

    account = "Test account"
    portal = "playstation"
    product = "Test product"
    product_id = "********"
    count = 1
    region = "SIEA"

    @factory.lazy_attribute
    def csv_row(self):
        return f"{self.account},{self.portal},{self.year},{self.month},{self.product},{self.product_id},{self.count},{self.region}\n"


@dataclass
class PlaystationRawMonthlyActiveUsers:
    start_date: date
    end_date: date
    rows: list

    @property
    def manifest_json(self):
        return {
            "manifest.json": {
                "dateFrom": f"{self.start_date}",
                "dateTo": f"{self.end_date}",
                "fileMetaData": {
                    f"playstation_monthly_active_users-{self.start_date}-{self.end_date}.csv": {
                        "dateFrom": f"{self.start_date}",
                        "dateTo": f"{self.end_date}",
                        "observationType": "monthly_active_users",
                        "rawData": True,
                    },
                },
            }
        }

    @property
    def monthly_active_users_csv(self):
        csvs = {}

        # Add monthly file (always exists, even if empty)
        monthly_header = "account,portal,year,month,product,product_id,count,region\n"
        monthly_filename = (
            f"playstation_monthly_active_users-{self.start_date}-{self.end_date}.csv"
        )
        csvs[monthly_filename] = monthly_header
        for row in self.rows:
            csvs[monthly_filename] += row.csv_row

        return csvs

    @property
    def zip_content(self):
        return {**self.manifest_json, **self.monthly_active_users_csv}

    @property
    def raw_zip_file(self):
        with _mock_zip_file(self.zip_content) as zip_file:
            return zip_file.fp.getvalue()

    def __add__(self, other):
        return PlaystationRawMonthlyActiveUsersFactory(
            start_date=self.start_date,
            end_date=other.end_date,
            rows=self.rows + other.rows,
        )


class PlaystationRawMonthlyActiveUsersFactory(factory.Factory):
    class Meta:
        model = PlaystationRawMonthlyActiveUsers

    start_date = date(2025, 6, 1)
    end_date = date(2025, 6, 1)

    rows = ListSubFactory(
        PlaystationMonthlyActiveUsersRowFactory,
        size=lambda o: (o.end_date - o.start_date).days + 1,
        start_date=factory.LazyAttribute(lambda row: row.factory_parent.start_date),
        year=factory.LazyAttribute(
            lambda row: row.factory_parent.start_date.strftime("%Y")
        ),
        month=factory.LazyAttribute(
            lambda row: row.factory_parent.start_date.strftime("%m")
        ),
    )


@pytest.fixture
def playstation_raw_monthly_active_users_factory():
    PlaystationRawMonthlyActiveUsersFactory.reset_sequence(force=True)
    return PlaystationRawMonthlyActiveUsersFactory


class ExternalPlaystationMonthlyActiveUsersReportsFactory(ExternalReportsFactory):
    source = Source.PLAYSTATION_DAILY_ACTIVE_USERS
    portal = Portal.PLAYSTATION
    observation_type = ObservationType.DAILY_ACTIVE_USERS
    date_from = date(2025, 6, 1)
    date_to = date(2025, 6, 1)


@pytest.fixture
def external_playstation_monthly_active_users_reports_factory() -> (
    type[ExternalPlaystationMonthlyActiveUsersReportsFactory]
):
    ExternalPlaystationMonthlyActiveUsersReportsFactory.reset_sequence(force=True)
    return ExternalPlaystationMonthlyActiveUsersReportsFactory


@pytest.fixture
def playstation_monthly_active_users_metadata_with_raw_file_factory(
    playstation_raw_monthly_active_users_factory,
    external_playstation_monthly_active_users_reports_factory,
):
    class PlaystationMonthlyActiveUsersReportMetadataWithRawFileFactory(
        factory.Factory
    ):
        class Meta:
            model = ReportMetadataWithRawFile

        input_raw_file = factory.SubFactory(
            playstation_raw_monthly_active_users_factory
        )
        metadata = factory.SubFactory(
            external_playstation_monthly_active_users_reports_factory
        )

        @factory.lazy_attribute
        def raw_file(self):
            return self.input_raw_file.raw_zip_file

    return PlaystationMonthlyActiveUsersReportMetadataWithRawFileFactory


@pytest.fixture
def generate_raw_playstation_monthly_active_users_report(
    playstation_monthly_active_users_metadata_with_raw_file_factory,
    playstation_raw_monthly_active_users_factory,
    external_playstation_monthly_active_users_reports_factory,
):
    def generate_raw_report(rows: int | None = None, custom_rows_data=None):
        return playstation_monthly_active_users_metadata_with_raw_file_factory(
            input_raw_file=playstation_raw_monthly_active_users_factory(
                rows=ListBetterSubFactory(
                    PlaystationMonthlyActiveUsersRowFactory,
                    size=len(custom_rows_data)
                    if custom_rows_data is not None and rows is None
                    else rows or 1,
                    custom_rows_data=custom_rows_data if custom_rows_data else [],
                )
            ),
            metadata=external_playstation_monthly_active_users_reports_factory(),
        )

    return generate_raw_report
