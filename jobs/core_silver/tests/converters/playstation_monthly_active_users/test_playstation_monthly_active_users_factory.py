from datetime import date


def test_generate_empty_playstation_monthly_active_users(
    playstation_raw_monthly_active_users_factory,
):
    playstation_raw_dau = playstation_raw_monthly_active_users_factory(
        start_date=date(2023, 10, 1), end_date=date(2023, 10, 1), rows=[]
    )
    assert playstation_raw_dau.start_date == date(2023, 10, 1)
    assert playstation_raw_dau.end_date == date(2023, 10, 1)
    assert playstation_raw_dau.manifest_json == {
        "manifest.json": {
            "dateFrom": "2023-10-01",
            "dateTo": "2023-10-01",
            "fileMetaData": {
                "playstation_monthly_active_users-2023-10-01-2023-10-01.csv": {
                    "dateFrom": "2023-10-01",
                    "dateTo": "2023-10-01",
                    "observationType": "monthly_active_users",
                    "rawData": True,
                },
            },
        },
    }
    assert playstation_raw_dau.monthly_active_users_csv == {
        "playstation_monthly_active_users-2023-10-01-2023-10-01.csv": "account,portal,year,month,product,product_id,count,region\n",
    }


def test_generate_one_day_of_playstation_monthly_active_users_siea_region(
    playstation_raw_monthly_active_users_factory,
):
    result = playstation_raw_monthly_active_users_factory(
        start_date=date(2025, 6, 1),
        end_date=date(2025, 6, 1),
    )

    assert result.start_date == date(2025, 6, 1)
    assert result.end_date == date(2025, 6, 1)
    assert result.manifest_json == {
        "manifest.json": {
            "dateFrom": "2025-06-01",
            "dateTo": "2025-06-01",
            "fileMetaData": {
                "playstation_monthly_active_users-2025-06-01-2025-06-01.csv": {
                    "dateFrom": "2025-06-01",
                    "dateTo": "2025-06-01",
                    "observationType": "monthly_active_users",
                    "rawData": True,
                },
            },
        },
    }
    assert result.monthly_active_users_csv == {
        "playstation_monthly_active_users-2025-06-01-2025-06-01.csv": (
            "account,portal,year,month,product,product_id,count,region\n"
            "Test account,playstation,2025,06,Test product,********,1,SIEA\n"
        ),
    }


def test_generate_one_day_of_playstation_monthly_active_users_multiple_regions(
    playstation_raw_monthly_active_users_factory,
):
    result = playstation_raw_monthly_active_users_factory(
        start_date=date(2023, 10, 1), end_date=date(2023, 10, 1), rows__region="SIEA"
    ) + playstation_raw_monthly_active_users_factory(
        start_date=date(2023, 10, 1), end_date=date(2023, 10, 1), rows__region="SIEE"
    )

    assert result.start_date == date(2023, 10, 1)
    assert result.end_date == date(2023, 10, 1)
    assert result.manifest_json == {
        "manifest.json": {
            "dateFrom": "2023-10-01",
            "dateTo": "2023-10-01",
            "fileMetaData": {
                "playstation_monthly_active_users-2023-10-01-2023-10-01.csv": {
                    "dateFrom": "2023-10-01",
                    "dateTo": "2023-10-01",
                    "observationType": "monthly_active_users",
                    "rawData": True,
                },
            },
        },
    }
    assert result.monthly_active_users_csv == {
        "playstation_monthly_active_users-2023-10-01-2023-10-01.csv": (
            "account,portal,year,month,product,product_id,count,region\n"
            "Test account,playstation,2023,10,Test product,********,1,SIEA\n"
            "Test account,playstation,2023,10,Test product,********,1,SIEE\n"
        ),
    }


def test_generate_two_days_of_playstation_monthly_active_users_multiple_regions(
    playstation_raw_monthly_active_users_factory,
):
    result = playstation_raw_monthly_active_users_factory(
        start_date=date(2024, 4, 1), end_date=date(2024, 4, 2), rows__region="SIEA"
    ) + playstation_raw_monthly_active_users_factory(
        start_date=date(2024, 4, 1), end_date=date(2024, 4, 2), rows__region="SIEE"
    )

    assert result.start_date == date(2024, 4, 1)
    assert result.end_date == date(2024, 4, 2)
    assert result.monthly_active_users_csv == {
        "playstation_monthly_active_users-2024-04-01-2024-04-02.csv": (
            "account,portal,year,month,product,product_id,count,region\n"
            "Test account,playstation,2024,04,Test product,********,1,SIEA\n"
            "Test account,playstation,2024,04,Test product,********,1,SIEA\n"
            "Test account,playstation,2024,04,Test product,********,1,SIEE\n"
            "Test account,playstation,2024,04,Test product,********,1,SIEE\n"
        ),
    }


def test_generate_two_months_of_playstation_monthly_active_users_siea_region(
    playstation_raw_monthly_active_users_factory,
):
    result = playstation_raw_monthly_active_users_factory(
        start_date=date(2024, 4, 1), end_date=date(2024, 4, 1)
    ) + playstation_raw_monthly_active_users_factory(
        start_date=date(2024, 5, 1), end_date=date(2024, 5, 1)
    )

    assert result.start_date == date(2024, 4, 1)
    assert result.end_date == date(2024, 5, 1)
    assert result.monthly_active_users_csv == {
        "playstation_monthly_active_users-2024-04-01-2024-05-01.csv": (
            "account,portal,year,month,product,product_id,count,region\n"
            "Test account,playstation,2024,04,Test product,********,1,SIEA\n"
            "Test account,playstation,2024,05,Test product,********,1,SIEA\n"
        ),
    }


def test_generate_playstation_monthly_active_users_multiple_products_multiple_regions(
    playstation_raw_monthly_active_users_factory,
):
    result = (
        playstation_raw_monthly_active_users_factory(
            start_date=date(2024, 4, 1),
            end_date=date(2024, 4, 2),
            rows__region="SIEA",
            rows__product="Test product",
        )
        + playstation_raw_monthly_active_users_factory(
            start_date=date(2024, 4, 1),
            end_date=date(2024, 4, 2),
            rows__region="SIEA",
            rows__product="Awesome test product",
        )
        + playstation_raw_monthly_active_users_factory(
            start_date=date(2024, 4, 1),
            end_date=date(2024, 4, 2),
            rows__region="SIEE",
            rows__product="Awesome test product",
        )
    )

    assert result.start_date == date(2024, 4, 1)
    assert result.end_date == date(2024, 4, 2)
    assert result.monthly_active_users_csv == {
        "playstation_monthly_active_users-2024-04-01-2024-04-02.csv": (
            "account,portal,year,month,product,product_id,count,region\n"
            "Test account,playstation,2024,04,Test product,********,1,SIEA\n"
            "Test account,playstation,2024,04,Test product,********,1,SIEA\n"
            "Test account,playstation,2024,04,Awesome test product,********,1,SIEA\n"
            "Test account,playstation,2024,04,Awesome test product,********,1,SIEA\n"
            "Test account,playstation,2024,04,Awesome test product,********,1,SIEE\n"
            "Test account,playstation,2024,04,Awesome test product,********,1,SIEE\n"
        ),
    }
