from datetime import date

import polars as pl

from core_silver.observation_converter.converters.playstation_monthly_active_users import (
    PlaystationMonthlyActiveUsersConverter,
)


def test_convert_playstation_monthly_active_users_empty(
    generate_raw_playstation_monthly_active_users_report,
):
    raw_report = generate_raw_playstation_monthly_active_users_report(rows=0)
    converter = PlaystationMonthlyActiveUsersConverter(raw_report)
    result = converter.convert()
    assert result.empty


def test_convert_playstation_monthly_active_users_basic_one_line_run(
    generate_raw_playstation_monthly_active_users_report,
):
    raw_report = generate_raw_playstation_monthly_active_users_report()
    converter = PlaystationMonthlyActiveUsersConverter(raw_report)
    result = converter.convert()

    _expected_df = pl.DataFrame([
        {
            "platform": "Unknown",
            "region": "PS America",
            "portal": "PlayStation",
            "date": date(2025, 6, 1),
            "human_name": "Test product",
            "monthly_active_users": 1,
            "sku_id": "10001234",
            "store_id": "10001234",
            "store": "PlayStation America",
            "abbreviated_name": "PS US",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "10001234-SIEA-playstation-store:1",
            "country_code": "ZZZ",
            "portal_platform_region": "PlayStation:Unknown:PS America",
        }
    ])
    assert result.df.equals(_expected_df)


def test_convert_playstation_monthly_active_users_with_multiple_rows_different_dates(
    generate_raw_playstation_monthly_active_users_report,
):
    raw_report = generate_raw_playstation_monthly_active_users_report(
        custom_rows_data=[
            {
                "start_date": date(2025, 6, 1),
                "date": "2025-06-01",
                "count": 14,
            },
            {
                "start_date": date(2025, 6, 2),
                "date": "2025-06-02",
                "count": 8,
            },
            # TODO add implicitly information about data range in manifest
        ]
    )
    converter = PlaystationMonthlyActiveUsersConverter(raw_report)
    result = converter.convert()

    _expected_df = pl.DataFrame([
        {
            "platform": "Unknown",
            "region": "PS America",
            "portal": "PlayStation",
            "date": date(2025, 6, 1),
            "human_name": "Test product",
            "monthly_active_users": 14,
            "sku_id": "10001234",
            "store_id": "10001234",
            "store": "PlayStation America",
            "abbreviated_name": "PS US",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "10001234-SIEA-playstation-store:1",
            "country_code": "ZZZ",
            "portal_platform_region": "PlayStation:Unknown:PS America",
        },
        {
            "platform": "Unknown",
            "region": "PS America",
            "portal": "PlayStation",
            "date": date(2025, 6, 1),
            "human_name": "Test product",
            "monthly_active_users": 8,
            "sku_id": "10001234",
            "store_id": "10001234",
            "store": "PlayStation America",
            "abbreviated_name": "PS US",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "10001234-SIEA-playstation-store:1",
            "country_code": "ZZZ",
            "portal_platform_region": "PlayStation:Unknown:PS America",
        },
    ])

    assert result.df.equals(_expected_df)
