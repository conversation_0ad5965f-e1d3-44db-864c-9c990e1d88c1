import datetime

import pytest

from core_silver.observation_converter.converters.microsoft import MicrosoftConverter


class DateRange:
    def __init__(self, start_date: datetime.date, end_date: datetime.date):
        self.start_date = start_date
        self.end_date = end_date


@pytest.fixture
def one_day_coverage() -> DateRange:
    return DateRange(datetime.date(2025, 4, 1), datetime.date(2025, 4, 1))


def test_zip_content_is_correctly_created(microsoft_raw_sales_factory):
    normal_sales = microsoft_raw_sales_factory()
    in_apps = microsoft_raw_sales_factory(in_app=True)

    result = normal_sales + in_apps
    assert result.manifest == {
        "manifest.json": {
            "dateFrom": "2025-04-01T00:00:00.000Z",
            "dateTo": "2025-04-04T00:00:00.000Z",
            "metadata": {
                "fileMetaData": {
                    "microsoft_sales-2025-04-01_2025-04-04-A2AAAA2222AA-CLIENT-ORGANIZATION.json": {
                        "humanName": "Test Name",
                        "skuId": "A2AAAA2222AA",
                        "parentSkuId": "A2AAAA2222AA",
                    },
                    "microsoft_sales-2025-04-01_2025-04-04-INAPP2222AA-CLIENT-ORGANIZATION.json": {
                        "humanName": "Test Name DELUXE Upgrade - Test Name",
                        "skuId": "INAPP2222AA",
                        "parentSkuId": "A2AAAA2222AA",
                    },
                }
            },
        }
    }
    assert list(result.rows_by_files.keys()) == [
        "microsoft_sales-2025-04-01_2025-04-04-A2AAAA2222AA-CLIENT-ORGANIZATION.json",
        "microsoft_sales-2025-04-01_2025-04-04-INAPP2222AA-CLIENT-ORGANIZATION.json",
    ]

    assert all(
        row["skuDisplayName"] == "Test Name"
        for row in result.rows_by_files[
            "microsoft_sales-2025-04-01_2025-04-04-A2AAAA2222AA-CLIENT-ORGANIZATION.json"
        ]
    )

    assert all(
        row["skuDisplayName"] == "Test Name DELUXE Upgrade - Test Name"
        for row in result.rows_by_files[
            "microsoft_sales-2025-04-01_2025-04-04-INAPP2222AA-CLIENT-ORGANIZATION.json"
        ]
    )


def test_microsoft_sales_are_converted_correctly(
    one_day_coverage,
    report_microsoft_sales_metadata_with_raw_file_factory,
):
    report_metadata = report_microsoft_sales_metadata_with_raw_file_factory(
        input_raw_file__start_date=one_day_coverage.start_date,
        input_raw_file__end_date=one_day_coverage.end_date,
    )

    result_dict = MicrosoftConverter(report_metadata).convert().df.to_dicts()
    assert result_dict == [
        {
            "sku_id": "A2AAAA2222AA",
            "portal": "Microsoft",
            "platform": "Microsoft",
            "region": "Global",
            "transaction_type": "Paid",
            "payment_instrument": "MS Balance",
            "tax_type": "Unknown",
            "sale_modificator": "NOT_APPLICABLE",
            "acquisition_platform": "Console-Xbox Series X|S",
            "acquisition_origin": "Microsoft Store (client)",
            "iap_flag": "False",
            "date": datetime.date(2025, 4, 1),
            "retailer_tag": "NOT_APPLICABLE",
            "human_name": "Test Name",
            "store_id": "A2AAAA2222AA",
            "base_price_local": None,
            "bundle_name": "NOT_APPLICABLE",
            "net_sales": 9.99,
            "gross_returned": 0.0,
            "gross_sales": 9.99,
            "units_returned": 0,
            "units_sold": 1,
            "free_units": 0,
            "price_local": 9.99,
            "price_usd": 9.99,
            "store": "Microsoft",
            "abbreviated_name": "Microsoft",
            "net_sales_approx": 6.99,
            "category": "Sale",
            "studio_id": 1,
            "report_id": 2,
            "unique_sku_id": "A2AAAA2222AA-microsoft:1",
            "country_code": "USA",
            "currency_code": "USD",
            "hash_acquisition_properties": "b11393dc4adb961b8ac708c10b1f60fa",
            "portal_platform_region": "Microsoft:Microsoft:Global",
        },
    ]


def test_microsoft_sales_and_in_app_sales_are_converted_correctly(
    one_day_coverage,
    microsoft_raw_sales_factory,
    report_microsoft_sales_metadata_with_raw_file_factory,
):
    microsoft_raw_sales_row = microsoft_raw_sales_factory(
        start_date=one_day_coverage.start_date,
        end_date=one_day_coverage.end_date,
    )
    microsoft_raw_sales_row_in_app = microsoft_raw_sales_factory(
        start_date=one_day_coverage.start_date,
        end_date=one_day_coverage.end_date,
        in_app=True,
    )
    report_metadata = report_microsoft_sales_metadata_with_raw_file_factory(
        input_raw_file__start_date=one_day_coverage.start_date,
        input_raw_file__end_date=one_day_coverage.end_date,
        input_raw_file__rows=microsoft_raw_sales_row.rows
        + microsoft_raw_sales_row_in_app.rows,
    )

    result_dict = MicrosoftConverter(report_metadata).convert().df.to_dicts()

    assert result_dict == [
        {
            "sku_id": "A2AAAA2222AA",
            "portal": "Microsoft",
            "platform": "Microsoft",
            "region": "Global",
            "transaction_type": "Paid",
            "payment_instrument": "MS Balance",
            "tax_type": "Unknown",
            "sale_modificator": "NOT_APPLICABLE",
            "acquisition_platform": "Console-Xbox Series X|S",
            "acquisition_origin": "Microsoft Store (client)",
            "iap_flag": "False",
            "date": datetime.date(2025, 4, 1),
            "retailer_tag": "NOT_APPLICABLE",
            "human_name": "Test Name",
            "store_id": "A2AAAA2222AA",
            "base_price_local": None,
            "bundle_name": "NOT_APPLICABLE",
            "net_sales": 9.99,
            "gross_returned": 0.0,
            "gross_sales": 9.99,
            "units_returned": 0,
            "units_sold": 1,
            "free_units": 0,
            "price_local": 9.99,
            "price_usd": 9.99,
            "store": "Microsoft",
            "abbreviated_name": "Microsoft",
            "net_sales_approx": 6.99,
            "category": "Sale",
            "studio_id": 1,
            "report_id": 2,
            "unique_sku_id": "A2AAAA2222AA-microsoft:1",
            "country_code": "USA",
            "currency_code": "USD",
            "hash_acquisition_properties": "b11393dc4adb961b8ac708c10b1f60fa",
            "portal_platform_region": "Microsoft:Microsoft:Global",
        },
        {
            "sku_id": "INAPP2222AA",
            "portal": "Microsoft",
            "platform": "Microsoft",
            "region": "Global",
            "transaction_type": "Iap",
            "payment_instrument": "MS Balance",
            "tax_type": "Unknown",
            "sale_modificator": "NOT_APPLICABLE",
            "acquisition_platform": "Console-Xbox Series X|S",
            "acquisition_origin": "Microsoft Store (client)",
            "iap_flag": "True",
            "date": datetime.date(2025, 4, 1),
            "retailer_tag": "NOT_APPLICABLE",
            "human_name": "Test Name DELUXE Upgrade - Test Name",
            "store_id": "INAPP2222AA",
            "base_price_local": None,
            "bundle_name": "NOT_APPLICABLE",
            "net_sales": 9.99,
            "gross_returned": 0.0,
            "gross_sales": 9.99,
            "units_returned": 0,
            "units_sold": 1,
            "free_units": 0,
            "price_local": 9.99,
            "price_usd": 9.99,
            "store": "Microsoft",
            "abbreviated_name": "Microsoft",
            "net_sales_approx": 6.99,
            "category": "Sale",
            "studio_id": 1,
            "report_id": 2,
            "unique_sku_id": "INAPP2222AA-microsoft:1",
            "country_code": "USA",
            "currency_code": "USD",
            "hash_acquisition_properties": "1797841df16a88d71dfb98209e2a043b",
            "portal_platform_region": "Microsoft:Microsoft:Global",
        },
    ]


def test_microsoft_sales_raw_report_one_row_with_neutral_country_code_is_mapped_to_zzz(
    report_microsoft_sales_metadata_with_raw_file_factory,
):
    report_metadata = report_microsoft_sales_metadata_with_raw_file_factory(
        input_raw_file__rows__market="NEUTRAL",
    )

    result_dict = MicrosoftConverter(report_metadata).convert().df.to_dicts()

    assert result_dict[0]["country_code"] == "ZZZ"
