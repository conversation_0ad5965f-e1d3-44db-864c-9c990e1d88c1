from collections import defaultdict
from datetime import date, datetime, timedelta, timezone

import factory
import pytest
from attr import dataclass

from data_sdk.domain.domain_types import ReportMetadata, ReportMetadataWithRawFile
from data_sdk.domain.source import Source
from tests.conftest import ListSubFactory
from tests.utils import _mock_zip_file


class _MetadataFactory(factory.Factory):
    class Meta:
        model = ReportMetadata

    class Params:
        number_of_days = 5

    source = Source.MICROSOFT_SALES
    studio_id = 1
    report_id = 2
    upload_date = datetime(2021, 1, 27, 11, 10, 0, 277000, tzinfo=timezone.utc)
    blob_name = "microsoft_sales-2024-11-29_2024-12-03.zip"
    original_name = "microsoft_sales-2024-11-29_2024-12-03.zip"
    state = "PENDING"
    date_from = datetime(2024, 11, 29, 0, 0)
    date_to = factory.LazyAttribute(
        lambda o: o.date_from + timedelta(days=o.number_of_days - 1)
    )
    no_data = False


@pytest.fixture
def metadata_factory() -> type[factory.Factory]:
    return _MetadataFactory


class MicrosoftSalesRowFactory(factory.DictFactory):
    class Params:
        start_date = date(year=2024, month=11, day=29)
        in_app_name = None

    real_application_id: str = "A2AAAA2222AA"
    """
    real_application_id: The in-apps rows don't have the actual application ID,
    so we store it in a separate column. The ID is only present in the manifest
    and in the file name.
    """

    market: str = "US"
    applicationName: str = "Test Name"  # noqa: N815
    acquisitionType = factory.LazyAttribute(  # noqa: N815
        lambda o: "Paid" if o.in_app_name is None else "Iap"
    )
    osVersion: str = "Windows 11"  # noqa: N815
    age: str = "18-24"
    deviceType: str = "Console-Xbox Series X|S"  # noqa: N815
    gender: str = "Unknown"
    paymentInstrumentType: str = "MS Balance"  # noqa: N815
    sandboxId: str = "RETAIL"  # noqa: N815
    storeClient: str = "Microsoft Store (client)"  # noqa: N815
    xboxTitleId = factory.LazyAttribute(  # noqa: N815
        lambda o: "111A1B1C" if o.in_app_name is None else ""
    )
    localCurrencyCode: str = "USD"  # noqa: N815
    xboxProductId: str = "AAAAAAAA-1111-1111-1111-111111111111"  # noqa: N815
    availabilityId: str = "A1AAAA1111AA"  # noqa: N815
    skuId: str = "0010"  # noqa: N815
    skuDisplayName = factory.LazyAttribute(  # noqa: N815
        lambda o: (f"{o.in_app_name} - " if o.in_app_name is not None else "")
        + o.applicationName
    )
    xboxParentProductId: str = "PARENTID-1111-1111-1111-111111111111"  # noqa: N815
    parentProductName: str = "Test Name"  # noqa: N815
    applicationId: str = "A2AAAA2222AA"  # noqa: N815
    date = factory.LazyAttributeSequence(
        lambda o, n: str(o.start_date + timedelta(days=n))
    )
    acquisitionQuantity: int = 1  # noqa: N815
    purchasePriceUSDAmount: float = 9.99  # noqa: N815
    purchasePriceLocalAmount: float = 9.99  # noqa: N815
    purchaseTaxUSDAmount: str = ""  # noqa: N815
    purchaseTaxLocalAmount: str = ""  # noqa: N815
    inAppProductName = factory.LazyAttribute(  # noqa: N815
        lambda o: o.skuDisplayName if o.in_app_name is not None else ""
    )


@dataclass
class MicrosoftRawSales:
    start_date: date
    end_date: date
    rows: list

    def __add__(self, other):
        return MicrosoftRawSalesFactory(
            start_date=self.start_date,
            end_date=other.end_date,
            rows=self.rows + other.rows,
        )

    @property
    def rows_by_files(self) -> dict[str, list]:
        result = defaultdict(list)
        for row in self.rows:
            file_name = f"microsoft_sales-{self.start_date}_{self.end_date}-{row['real_application_id']}-CLIENT-ORGANIZATION.json"
            zip_row = row.copy()
            del zip_row[
                "real_application_id"
            ]  # remove field that is use only in manifest
            result[file_name].append(zip_row)
        return result

    @property
    def manifest(self):
        files_metadata = {
            f"microsoft_sales-{self.start_date}_{self.end_date}-{row['real_application_id']}-CLIENT-ORGANIZATION.json": {
                "humanName": row["skuDisplayName"],
                "skuId": row["real_application_id"],
                "parentSkuId": row["applicationId"],
            }
            for row in self.rows
        }

        return {
            "manifest.json": {
                "dateFrom": f"{self.start_date}T00:00:00.000Z",
                "dateTo": f"{self.end_date}T00:00:00.000Z",
                "metadata": {"fileMetaData": files_metadata},
            },
        }

    @property
    def zip_content(self):
        return {**self.manifest, **self.rows_by_files}

    @property
    def raw_zip_file(self):
        with _mock_zip_file(self.zip_content) as zip_file:
            return zip_file.fp.getvalue()


class MicrosoftRawSalesFactory(factory.Factory):
    class Meta:
        model = MicrosoftRawSales

    class Params:
        in_app = factory.Trait(
            rows__in_app_name="Test Name DELUXE Upgrade",
            rows__real_application_id="INAPP2222AA",
        )

    start_date = date(2025, 4, 1)
    end_date = date(2025, 4, 4)
    rows = ListSubFactory(
        MicrosoftSalesRowFactory,
        size=lambda o: (o.end_date - o.start_date).days + 1,
        start_date=factory.LazyAttribute(lambda row: row.factory_parent.start_date),
    )


@pytest.fixture
def microsoft_raw_sales_factory():
    return MicrosoftRawSalesFactory


@pytest.fixture
def report_microsoft_sales_metadata_with_raw_file_factory(
    metadata_factory,
    microsoft_raw_sales_factory,
):
    class ReportMetadataWithRawFileFactory(factory.Factory):
        class Meta:
            model = ReportMetadataWithRawFile

        input_raw_file = factory.SubFactory(microsoft_raw_sales_factory)
        metadata = factory.SubFactory(metadata_factory)

        @factory.lazy_attribute
        def raw_file(self):
            return self.input_raw_file.raw_zip_file

    return ReportMetadataWithRawFileFactory
