import collections
import inspect

import pandas as pd
import pytest

from core_silver.dictionaries.constants import Constant
from core_silver.observation_converter.converters.sale_category import (
    SaleCategoryClassifier,
)
from data_sdk.domain import DisplayPortal, SaleCategory
from data_sdk.domain.platform import DisplayPlatform
from tests.comparisons import compare_dataframes

Case = collections.namedtuple("Case", ["name", "input", "output"])


DEFAULTS = {
    "free_units": 0,
    "gross_returned": 0,
    "gross_sales": 0,
    "price_local": 0,
    "platform": DisplayPlatform.PC.value,
    "portal": DisplayPortal.STEAM.value,
    "retailer_tag": Constant.NOT_APPLICABLE.value,
    "sale_modificator": Constant.NOT_APPLICABLE.value,
    "units_returned": 0,
    "units_sold": 0,
}


def make_sale_observation(name, input_fields, output):
    input_data = DEFAULTS.copy()
    input_data.update(input_fields)
    return Case(name, input_data, output)


CASES = [
    make_sale_observation(
        "sale",
        {"price_local": 10.000, "units_sold": 5, "gross_sales": 113.600},
        {"_is_sale": True, "category": SaleCategory.SALE.value},
    ),
    make_sale_observation(
        "sale_with_return",
        {
            "price_local": 20.000,
            "units_sold": 10,
            "gross_sales": -200.000,
            "gross_returned": 33.330,
            "units_returned": 5,
        },
        {"_is_sale": True, "category": SaleCategory.SALE.value},
    ),
    make_sale_observation(
        "return",
        {
            "price_local": 10.000,
            "gross_sales": 10.000,
            "units_returned": 1,
        },
        {"_is_return": True, "category": SaleCategory.RETURN.value},
    ),
    make_sale_observation(
        "blank",
        {},
        {"_is_blank": True, "category": SaleCategory.BLANK_RECORD.value},
    ),
    make_sale_observation(
        "sale_adjustment",
        {
            "gross_sales": 20.000,
        },
        {"_is_sale_adjustment": True, "category": SaleCategory.SALE_ADJUSTMENT.value},
    ),
    make_sale_observation(
        "non_billable_epic",
        {
            "gross_sales": 1.0,
            "price_local": 0.1,
            "portal": DisplayPortal.EPIC.value,
        },
        {
            "_is_non_billable_epic": True,
            "category": f"{SaleCategory.NON_BILLABLE.value}: {DisplayPortal.EPIC.value}",
        },
    ),
    make_sale_observation(
        "epic_portal_low_price",
        {"portal": DisplayPortal.EPIC.value, "price_local": 0.1},
        {
            "_is_non_billable_epic": True,
            "category": f"{SaleCategory.NON_BILLABLE.value}: {DisplayPortal.EPIC.value}",
        },
    ),
    make_sale_observation(
        "non_billable_other",
        {
            "units_sold": 1,
            "sale_modificator": "Other",
        },
        {
            "_is_non_billable_other": True,
            "category": f"{SaleCategory.NON_BILLABLE.value}: PC",
        },
    ),
    make_sale_observation(
        "retail_sale_modificator",
        {
            "units_sold": 1,
            "sale_modificator": "Disc",
        },
        {
            "_is_retail_sale_modificator": True,
            "category": f"{SaleCategory.RETAIL.value}: Disc",
        },
    ),
    make_sale_observation(
        "non_billable_unknown",
        {
            "units_sold": 1,
        },
        {
            "_is_non_billable_unknown": True,
            "category": f"{SaleCategory.NON_BILLABLE.value}: Unknown Sale Type",
        },
    ),
    make_sale_observation(
        "negative_units_sold",
        {"units_sold": -1, "units_returned": 1},
        {
            "_is_non_billable_unknown": True,
            "category": f"{SaleCategory.NON_BILLABLE.value}: Unknown Sale Type",
        },
    ),
    make_sale_observation(
        "retail_steam",
        {
            "units_sold": 1,
            "retailer_tag": "Some Retailer",
        },
        {
            "_is_retail_steam": True,
            "category": f"{SaleCategory.RETAIL.value}: {DisplayPortal.STEAM.value}",
        },
    ),
    make_sale_observation(
        "invalid_free",
        {
            "free_units": 1,
            "units_sold": 1,
        },
        {"_is_invalid_free": True, "category": SaleCategory.FREE_INVALID.value},
    ),
    make_sale_observation(
        "valid_free",
        {
            "free_units": 1,
        },
        {
            "_is_invalid_free": True,
            "_is_free": True,
            "category": SaleCategory.FREE.value,
        },
        # All free sales are also invalid, which probably shouldn't be the case
    ),
    make_sale_observation(
        "free_with_return",
        {
            "units_returned": 1,
            "free_units": 1,
            "gross_sales": -1,
        },
        {
            "_is_free_with_return": True,
            "_is_invalid_free": True,
            "category": SaleCategory.FREE_AND_RETURN.value,
        },
    ),
    make_sale_observation(
        "free_with_sale",
        {
            "free_units": 1,
            "units_sold": 1,
            "gross_sales": 1,
        },
        {
            "_is_free_with_sale": True,
            "_is_invalid_free": True,
            "category": SaleCategory.FREE_AND_SALE.value,
        },
    ),
    make_sale_observation(
        "return_invalid",
        {"units_returned": -1},
        {"_is_invalid_return": True, "category": SaleCategory.RETURN_INVALID.value},
    ),
]


@pytest.fixture
def sale_cases():
    raw_inputs = []
    raw_outputs = []
    index = []
    for case in CASES:
        raw_inputs.append(case.input)
        raw_outputs.append(case.output)
        index.append(case.name)

    input_ = pd.DataFrame(raw_inputs, index=index)
    output = pd.DataFrame(raw_outputs, index=index).fillna(value=False)

    for column in output.columns:
        assert output[
            column
        ].any(), f"No positive test case for method {column}, add a positive example."

    class_methods = [
        method
        for method in dir(SaleCategoryClassifier)
        if (
            inspect.isfunction(getattr(SaleCategoryClassifier, method))
            and method.startswith("_is")
        )
    ]
    method_diff = set(class_methods).difference(set(output.columns))
    assert len(method_diff) == 0, f"Missing test case for methods: {method_diff}"

    tested_categories = set(output["category"].str.split(":", expand=True)[0].unique())
    all_categories = {member.value for member in SaleCategory.__members__.values()}
    all_categories.remove(
        SaleCategory.SALE_INVALID.value
    )  # Not able to reproduce this category
    category_diff = all_categories.difference(tested_categories)
    assert len(category_diff) == 0, f"Missing test case for categories: {category_diff}"

    return input_, output


def test_categories(sale_cases):
    input_, expected = sale_cases
    actual = pd.DataFrame(index=expected.index)
    flag_columns = [column for column in expected.columns if column != "category"]
    for method in flag_columns:
        result = getattr(SaleCategoryClassifier, method)(input_)
        actual[method] = result

    assert (
        actual[flag_columns].dtypes == "bool"
    ).all(), "All _is methods must return a boolean value."

    compare_dataframes(actual.astype(int), expected[flag_columns].astype(int))

    category = pd.DataFrame(SaleCategoryClassifier.classify(input_))
    compare_dataframes(category, pd.DataFrame(expected["category"]))
