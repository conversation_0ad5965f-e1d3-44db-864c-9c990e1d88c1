import json
from datetime import date

import polars as pl
import pytest

from core_silver.observation_converter.converters.exceptions import (
    FileExtractionError,
    FileSchemaError,
)
from core_silver.observation_converter.converters.meta_monthly_active_users import (
    MetaMonthlyActiveUsersConverter,
)
from tests.comparisons import compare_polars_dataframes


def test_convert_meta_monthly_active_users_empty(
    generate_raw_meta_monthly_active_users_report,
):
    raw_report = generate_raw_meta_monthly_active_users_report(rows=0)
    converter = MetaMonthlyActiveUsersConverter(raw_report)
    result = converter.convert()
    assert result.empty


def test_convert_meta_monthly_active_users_basic(
    generate_raw_meta_monthly_active_users_report,
):
    raw_report = generate_raw_meta_monthly_active_users_report(year=2024, month=1)
    days_of_month = 31
    converter = MetaMonthlyActiveUsersConverter(raw_report)
    result = converter.convert()

    expected_df = pl.DataFrame([
        {
            "platform": "Rift",
            "region": "Global",
            "portal": "Meta",
            "date": date(2024, 1, day_index + 1),
            "human_name": "Test product",
            "monthly_active_users": 100,
            "sku_id": "Test_product",
            "store_id": "1012593518800600",
            "store": "Meta Rift",
            "abbreviated_name": "Rift",
            "report_id": raw_report.metadata.report_id,
            "studio_id": 1,
            "unique_sku_id": "Test_product-rift-meta:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Meta:Rift:Global",
        }
        for day_index in range(0, days_of_month)
    ])
    assert compare_polars_dataframes(result.df, expected_df)


def test_convert_meta_monthly_active_users_with_multiple_skus(
    generate_raw_meta_monthly_active_users_report,
):
    custom_data = [
        {
            "year": 2025,
            "month": 6,
            "product": "SUPERHOT WINDOWS 10",
            "sku_id": "SUPERHOT_WINDOWS_10",
            "product_id": "1012593518800600",
            "count": 162,
            "platform": "Rift",
        },
        {
            "year": 2025,
            "month": 6,
            "product": "SUPERHOT VR",
            "sku_id": "SUPERHOT_VR",
            "product_id": "1012593518800601",
            "count": 13,
            "platform": "Rift",
        },
        {
            "year": 2025,
            "month": 6,
            "product": "SUPERHOT: MIND CONTROL DELETE",
            "sku_id": "SUPERHOT_MIND_CONTROL_DELETE",
            "product_id": "1012593518800602",
            "count": 149063,
            "platform": "Quest",
        },
    ]
    raw_report = generate_raw_meta_monthly_active_users_report(
        year=2025,
        month=6,
        custom_rows_data=custom_data,
    )
    days_of_month = 30
    converter = MetaMonthlyActiveUsersConverter(raw_report)
    result = converter.convert()

    expected_df = pl.DataFrame(
        data=[
            {
                "platform": row["platform"],
                "region": "Global",
                "portal": "Meta",
                "date": date(2025, 6, day_index + 1),
                "human_name": row["product"],
                "monthly_active_users": row["count"],
                "sku_id": row["sku_id"],
                "store_id": row["product_id"],
                "store": "Meta " + row["platform"],
                "abbreviated_name": row["platform"],
                "report_id": raw_report.metadata.report_id,
                "studio_id": 1,
                "unique_sku_id": f"{row['sku_id']}-{row['platform'].lower()}-meta:1",
                "country_code": "ZZZ",
                "portal_platform_region": f"Meta:{row['platform']}:Global",
            }
            for row in custom_data
            for day_index in range(0, days_of_month)
        ],
    )
    assert compare_polars_dataframes(result.df, expected_df)


def test_convert_meta_monthly_active_users_with_multiple_skus_and_not_full_month(
    generate_raw_meta_monthly_active_users_report,
):
    custom_data = [
        {
            "year": 2025,
            "month": 6,
            "product": "SUPERHOT WINDOWS 10",
            "sku_id": "SUPERHOT_WINDOWS_10",
            "product_id": "1012593518800600",
            "count": 162,
            "platform": "Rift",
        },
        {
            "year": 2025,
            "month": 6,
            "product": "SUPERHOT VR",
            "sku_id": "SUPERHOT_VR",
            "product_id": "1012593518800601",
            "count": 13,
            "platform": "Quest",
        },
    ]
    number_of_days = 3
    raw_report = generate_raw_meta_monthly_active_users_report(
        year=2025,
        month=6,
        custom_rows_data=custom_data,
        metadata_date_from=date(2025, 6, 1),
        metadata_date_to=date(2025, 6, number_of_days),
    )
    converter = MetaMonthlyActiveUsersConverter(raw_report)
    result = converter.convert()

    expected_df = pl.DataFrame([
        {
            "platform": row["platform"],
            "region": "Global",
            "portal": "Meta",
            "date": date(2025, 6, day_index + 1),
            "human_name": row["product"],
            "monthly_active_users": row["count"],
            "sku_id": row["sku_id"],
            "store_id": row["product_id"],
            "store": "Meta " + row["platform"],
            "abbreviated_name": row["platform"],
            "report_id": raw_report.metadata.report_id,
            "studio_id": 1,
            "unique_sku_id": f"{row['sku_id']}-{row['platform'].lower()}-meta:1",
            "country_code": "ZZZ",
            "portal_platform_region": f"Meta:{row['platform']}:Global",
        }
        for row in custom_data
        for day_index in range(0, number_of_days)
    ])

    compare_polars_dataframes(result.df, expected_df)


def test_parse_invalid_file_raises_file_extraction_error(
    generate_raw_meta_monthly_active_users_report,
):
    raw_report = generate_raw_meta_monthly_active_users_report()
    raw_report.raw_file = b"invalid zip content"

    converter = MetaMonthlyActiveUsersConverter(raw_report)

    with pytest.raises(FileExtractionError):
        converter.parse(raw_report.raw_file)


def test_parse_invalid_schema_raises_file_schema_error(
    generate_raw_meta_monthly_active_users_report, raw_zip_file, mock_csv_file
):
    invalid_raw_file = raw_zip_file({
        "manifest.json": json.dumps({
            "dateFrom": "2024-01-01",
            "dateTo": "2024-01-31",
            "fileMetaData": {
                "meta_monthly_active_users-2024-01-01-2024-01-31.csv": {
                    "dateFrom": "2024-01-01",
                    "dateTo": "2024-01-31",
                }
            },
        }),
        "meta_monthly_active_users-2024-01-01-2024-01-31.csv": mock_csv_file(
            [
                "invalid",
                "header",
                "structure",
            ],
            [
                {
                    "invalid": "data",
                    "header": "without",
                    "structure": "proper",
                }
            ],
        ),
    })

    raw_report = generate_raw_meta_monthly_active_users_report()
    raw_report.raw_file = invalid_raw_file
    converter = MetaMonthlyActiveUsersConverter(raw_report)

    with pytest.raises(FileSchemaError):
        converter.parse(invalid_raw_file)
