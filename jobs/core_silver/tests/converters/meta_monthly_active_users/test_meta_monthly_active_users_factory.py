import json
import zipfile
from datetime import date
from io import BytesIO

import pytest


def test_generate_empty_meta_monthly_active_users(
    meta_raw_monthly_active_users_factory,
):
    result = meta_raw_monthly_active_users_factory(
        start_year=2023, start_month=12, end_year=2023, end_month=12, rows=[]
    )
    assert result.start_year == 2023
    assert result.start_month == 12
    assert result.end_year == 2023
    assert result.end_month == 12
    assert result.manifest_json == {
        "manifest.json": {
            "dateFrom": "2023-12-01",
            "dateTo": "2023-12-31",
            "fileMetaData": {
                "meta_monthly_active_users-2023-12-01-2023-12-31.csv": {
                    "dateFrom": "2023-12-01",
                    "dateTo": "2023-12-31",
                },
            },
        },
    }
    assert result.monthly_active_users_csv == {
        "meta_monthly_active_users-2023-12-01-2023-12-31.csv": "account,portal,year,month,product,product_id,count,platform\n",
    }


def test_generate_one_month_of_meta_monthly_active_users(
    meta_raw_monthly_active_users_factory,
):
    result = meta_raw_monthly_active_users_factory(
        start_year=2024,
        start_month=4,
        end_year=2024,
        end_month=4,
    )

    assert result.start_year == 2024
    assert result.start_month == 4
    assert result.end_year == 2024
    assert result.end_month == 4
    assert result.manifest_json == {
        "manifest.json": {
            "dateFrom": "2024-04-01",
            "dateTo": "2024-04-30",
            "fileMetaData": {
                "meta_monthly_active_users-2024-04-01-2024-04-30.csv": {
                    "dateFrom": "2024-04-01",
                    "dateTo": "2024-04-30",
                },
            },
        },
    }
    assert result.monthly_active_users_csv == {
        "meta_monthly_active_users-2024-04-01-2024-04-30.csv": (
            "account,portal,year,month,product,product_id,count,platform\n"
            "Test account,meta,2024,4,Test product,****************,100,Rift\n"
        ),
    }


def test_generate_meta_monthly_active_users_with_custom_data(
    meta_raw_monthly_active_users_factory,
):
    result = meta_raw_monthly_active_users_factory(
        start_year=2023,
        start_month=12,
        end_year=2023,
        end_month=12,
        rows__product="SUPERHOT WINDOWS 10",
        rows__product_id="****************",
        rows__count=1500,
    )

    assert result.start_year == 2023
    assert result.start_month == 12
    assert result.end_year == 2023
    assert result.end_month == 12
    assert result.manifest_json == {
        "manifest.json": {
            "dateFrom": "2023-12-01",
            "dateTo": "2023-12-31",
            "fileMetaData": {
                "meta_monthly_active_users-2023-12-01-2023-12-31.csv": {
                    "dateFrom": "2023-12-01",
                    "dateTo": "2023-12-31",
                },
            },
        },
    }
    assert result.monthly_active_users_csv == {
        "meta_monthly_active_users-2023-12-01-2023-12-31.csv": (
            "account,portal,year,month,product,product_id,count,platform\n"
            "Test account,meta,2023,12,SUPERHOT WINDOWS 10,****************,1500,Rift\n"
        ),
    }


def test_generate_multiple_months_of_meta_monthly_active_users(
    meta_raw_monthly_active_users_factory,
):
    result = meta_raw_monthly_active_users_factory(
        start_year=2024,
        start_month=3,
        end_year=2024,
        end_month=5,
        rows__size=3,  # 3 months: March, April, May
    )

    assert result.start_year == 2024
    assert result.start_month == 3
    assert result.end_year == 2024
    assert result.end_month == 5

    assert len(result.rows) == 3

    csv_content = result.monthly_active_users_csv[
        "meta_monthly_active_users-2024-03-01-2024-05-31.csv"
    ]
    assert (
        "Test account,meta,2024,3,Test product,****************,100,Rift" in csv_content
    )
    assert (
        "Test account,meta,2024,4,Test product,****************,100,Rift" in csv_content
    )
    assert (
        "Test account,meta,2024,5,Test product,****************,100,Rift" in csv_content
    )


def test_meta_monthly_active_users_can_be_added_together(
    meta_raw_monthly_active_users_factory,
):
    first_month = meta_raw_monthly_active_users_factory(
        start_year=2024, start_month=1, end_year=2024, end_month=1
    )
    second_month = meta_raw_monthly_active_users_factory(
        start_year=2024, start_month=2, end_year=2024, end_month=2
    )

    combined = first_month + second_month

    assert combined.start_year == 2024
    assert combined.start_month == 1
    assert combined.end_year == 2024
    assert combined.end_month == 2
    assert len(combined.rows) == 2


@pytest.mark.parametrize(
    ("year_month", "expected_last_day"),
    [
        ((2024, 6), 30),  # June 2024 (30 days)
        ((2024, 2), 29),  # February 2024 (29 days - leap year)
        ((2023, 2), 28),  # February 2023 (28 days - non-leap year)
        ((2024, 1), 31),  # January 2024 (31 days)
        ((2024, 4), 30),  # April 2024 (30 days)
    ],
)
def test_generate_raw_report_with_custom_year_month(
    generate_raw_meta_monthly_active_users_report,
    year_month,
    expected_last_day,
):
    year, month = year_month
    raw_report = generate_raw_meta_monthly_active_users_report(year=year, month=month)

    assert raw_report.metadata.date_from == date(year, month, 1)
    assert raw_report.metadata.date_to == date(year, month, expected_last_day)


def test_generate_raw_report_with_explicit_metadata_dates(
    generate_raw_meta_monthly_active_users_report,
):
    """Test wrapper with explicitly provided metadata dates (should override auto-generation)."""
    raw_report = generate_raw_meta_monthly_active_users_report(
        year=2024,
        month=6,
        metadata_date_from=date(2024, 6, 15),
        metadata_date_to=date(2024, 6, 20),
    )

    # Should use explicit dates, not auto-generated full month
    assert raw_report.metadata.date_from == date(2024, 6, 15)
    assert raw_report.metadata.date_to == date(2024, 6, 20)


def test_generate_raw_report_with_custom_rows_data(
    generate_raw_meta_monthly_active_users_report,
):
    raw_report = generate_raw_meta_monthly_active_users_report(
        year=2024,
        month=3,
        custom_rows_data=[
            {
                "year": 2024,
                "month": 3,
                "product": "Custom Product",
                "product_id": "CUSTOM123",
                "count": 999,
                "platform": "Quest",
            }
        ],
    )

    with zipfile.ZipFile(BytesIO(raw_report.raw_file), "r") as zip_file:
        manifest_content = json.loads(zip_file.read("manifest.json").decode("utf-8"))
        csv_filename = list(manifest_content["fileMetaData"].keys())[0]

        csv_content = zip_file.read(csv_filename).decode("utf-8")

    expected_csv_content = (
        "account,portal,year,month,product,product_id,count,platform\n"
        "Test account,meta,2024,3,Custom Product,CUSTOM123,999,Quest\n"
    )

    assert csv_content == expected_csv_content
