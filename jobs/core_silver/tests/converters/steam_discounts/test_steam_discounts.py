from datetime import datetime
from unittest.mock import patch

from zoneinfo import ZoneInfo

from core_silver.observation_converter.converters.steam_discounts import (
    SteamDiscountsConverter,
)
from data_sdk.domain import DisplayPortal
from data_sdk.domain.discounts import DiscountType
from data_sdk.domain.platform import DisplayPlatform
from data_sdk.domain.regions import Region


def test_convert_returns_non_empty_result(steam_discounts_raw_report):
    result = SteamDiscountsConverter(steam_discounts_raw_report).convert()
    assert not result.df.is_empty()


def test_parse_reads_all_necessary_files(steam_discounts_raw_report):
    converter = SteamDiscountsConverter(steam_discounts_raw_report)

    dfs = converter._parse(steam_discounts_raw_report.raw_file)

    assert dfs.keys() == {
        "priceIncreaseTimes.csv",
        "userinfo.csv",
        "discountEvents.csv",
        "discountHistory.csv",
        "discounts_all_3204.csv",
        "discounts_all_123147.csv",
    }


def test_parse_works_correctly_without_observation_files(
    steam_discounts_raw_report__no_observations,
):
    converter = SteamDiscountsConverter(steam_discounts_raw_report__no_observations)

    dfs = converter._parse(steam_discounts_raw_report__no_observations.raw_file)

    assert dfs.keys() == {
        "priceIncreaseTimes.csv",
        "userinfo.csv",
        "discountEvents.csv",
        "discountHistory.csv",
    }
    assert all(df.empty for df in dfs.values())


def test_parse_works_correctly_with_empty_observation_files(
    steam_discounts_raw_report__empty_observations_csvs,
):
    converter = SteamDiscountsConverter(
        steam_discounts_raw_report__empty_observations_csvs
    )

    dfs = converter._parse(steam_discounts_raw_report__empty_observations_csvs.raw_file)

    assert dfs.keys() == {
        "priceIncreaseTimes.csv",
        "userinfo.csv",
        "discountEvents.csv",
        "discountHistory.csv",
        "discounts_all_3204.csv",
    }

    for filename, df in dfs.items():
        if filename != "userinfo.csv":
            assert df.empty
        else:
            assert not df.empty


def test_convert_works_correctly_without_observation_files(
    steam_discounts_raw_report__no_observations,
):
    result = SteamDiscountsConverter(
        steam_discounts_raw_report__no_observations
    ).convert()
    assert result.df.is_empty()
    assert result.df.columns == [
        "create_time",
        "update_time",
        "platform",
        "region",
        "portal",
        "event_name",
        "datetime_from",
        "datetime_to",
        "is_event_joined",
        "discount_depth",
        "unique_event_id",
        "base_event_id",
        "group_id",
        "base_sku_id",
        "source_specific_discount_sku_id",
        "triggers_cooldown",
        "major",
        "discount_type",
        "max_discount_percentage",
        "price_increase_time",
        "promo_length",
        "sales_unique_sku_id",
        "studio_id",
        "unique_sku_id",
        "report_id",
        "portal_platform_region",
    ]


def test_convert_works_correctly_with_empty_observation_files(
    steam_discounts_raw_report__empty_observations_csvs,
):
    result = SteamDiscountsConverter(
        steam_discounts_raw_report__empty_observations_csvs
    ).convert()
    assert result.df.is_empty()


def test_convert_when_user_joined_no_events(
    steam_discounts_raw_report__no_joined_events,
):
    result = SteamDiscountsConverter(
        steam_discounts_raw_report__no_joined_events
    ).convert()

    assert result.df.is_empty()


def test_convert_based_on_full_set_of_data(steam_discounts_raw_report):
    result = SteamDiscountsConverter(steam_discounts_raw_report).convert()

    expected_row = {
        "report_id": 2,
        "create_time": datetime(2023, 10, 1, 0, 0, tzinfo=ZoneInfo("UTC")),
        "update_time": datetime(2023, 10, 1, 0, 0, tzinfo=ZoneInfo("UTC")),
        "studio_id": 1,
        "platform": DisplayPlatform.PC.value,
        "region": Region.GLOBAL.value,
        "portal": DisplayPortal.STEAM.value,
        "portal_platform_region": "Steam:PC:Global",
        "event_name": "Custom Discount",
        "datetime_from": datetime(2023, 7, 14, 17, 0, tzinfo=ZoneInfo("UTC")),
        "datetime_to": datetime(2023, 7, 28, 17, 0, tzinfo=ZoneInfo("UTC")),
        "is_event_joined": True,
        "discount_depth": 70,
        "unique_event_id": "20230714T170000Z:CustomDiscount:427502",
        "base_event_id": "3441163",
        "source_specific_discount_sku_id": "427502",
        "group_id": "",
        "base_sku_id": "427502",
        "sales_unique_sku_id": "427502",
        "unique_sku_id": "427502-steam:1",
        "triggers_cooldown": True,
        "major": False,
        "discount_type": DiscountType.CUSTOM.value,
        "max_discount_percentage": 70,
        "price_increase_time": datetime(2023, 5, 1, 14, 24, 7, tzinfo=ZoneInfo("UTC")),
        "promo_length": 1209600,
    }
    assert len(result.df) == 7
    assert result.df.to_dicts()[0] == expected_row


def test_convert_based_on_full_set_of_data_with_additional_not_required_columns(
    steam_discounts_raw_report_with_not_required_columns,
):
    result = SteamDiscountsConverter(
        steam_discounts_raw_report_with_not_required_columns
    ).convert()

    expected_row = {
        "event_name": "Custom Discount",
        "datetime_from": datetime(2023, 7, 14, 17, 0, tzinfo=ZoneInfo("UTC")),
        "datetime_to": datetime(2023, 7, 28, 17, 0, tzinfo=ZoneInfo("UTC")),
        "is_event_joined": True,
        "discount_depth": 70,
        "unique_event_id": "20230714T170000Z:CustomDiscount:427502",
        "base_event_id": "3441163",
        "group_id": "",
        "base_sku_id": "427502",
        "sales_unique_sku_id": "427502",
        "source_specific_discount_sku_id": "427502",
        "unique_sku_id": "427502-steam:1",
        "triggers_cooldown": True,
        "major": False,
        "discount_type": DiscountType.CUSTOM.value,
        "max_discount_percentage": 70,
        "price_increase_time": datetime(2023, 5, 1, 14, 24, 7, tzinfo=ZoneInfo("UTC")),
        "promo_length": 1209600,
        "report_id": 2,
        "create_time": datetime(2023, 10, 1, 0, 0, tzinfo=ZoneInfo("UTC")),
        "update_time": datetime(2023, 10, 1, 0, 0, tzinfo=ZoneInfo("UTC")),
        "studio_id": 1,
        "platform": DisplayPlatform.PC.value,
        "region": Region.GLOBAL.value,
        "portal": DisplayPortal.STEAM.value,
        "portal_platform_region": "Steam:PC:Global",
    }

    assert result.df.to_dicts()[0] == expected_row


def test_convert_no_price_increase_history_for_427502(
    steam_discounts_raw_report_no_price_increase_info,
):
    result = SteamDiscountsConverter(
        steam_discounts_raw_report_no_price_increase_info
    ).convert()

    expected_row = {
        "report_id": 2,
        "create_time": datetime(2023, 10, 1, 0, 0, tzinfo=ZoneInfo("UTC")),
        "update_time": datetime(2023, 10, 1, 0, 0, tzinfo=ZoneInfo("UTC")),
        "studio_id": 1,
        "platform": DisplayPlatform.PC.value,
        "region": Region.GLOBAL.value,
        "portal": DisplayPortal.STEAM.value,
        "portal_platform_region": "Steam:PC:Global",
        "event_name": "Custom Discount",
        "datetime_from": datetime(2023, 7, 14, 17, 0, tzinfo=ZoneInfo("UTC")),
        "datetime_to": datetime(2023, 7, 28, 17, 0, tzinfo=ZoneInfo("UTC")),
        "is_event_joined": True,
        "discount_depth": 70,
        "unique_event_id": "20230714T170000Z:CustomDiscount:427502",
        "base_event_id": "3441163",
        "group_id": "",
        "base_sku_id": "427502",
        "sales_unique_sku_id": "427502",
        "source_specific_discount_sku_id": "427502",
        "unique_sku_id": "427502-steam:1",
        "triggers_cooldown": True,
        "major": False,
        "discount_type": DiscountType.CUSTOM.value,
        "max_discount_percentage": 70,
        "price_increase_time": datetime(1970, 1, 1, 0, 0, tzinfo=ZoneInfo("UTC")),
        "promo_length": 1209600,
    }

    assert result.df.to_dicts()[0] == expected_row


# todo: rewrite this test
# test_convert_no_price_increase_history_for_427502_no_max_discount_percentage


# todo: rewrite this test
# test_steam_discounts_edge_case_with_no_store_opportunities_in_future_events


# TODO: this deserves checking if its not only called, but used, but It needs better factories for that
@patch("core_silver.observation_converter.converters.steam_discounts.sanitize")
def test_if_steam_sanitizes_discount(mock_sanitize, steam_discounts_raw_report):
    converter = SteamDiscountsConverter(steam_discounts_raw_report)

    converter.convert()
    mock_sanitize.assert_called_once()


def test_convert_deduplicates_joined_store_events(
    steam_discounts_raw_report_joined_store_events,
):
    result = SteamDiscountsConverter(
        steam_discounts_raw_report_joined_store_events
    ).convert()

    expected_results = [
        {
            "create_time": datetime(2023, 10, 1, 0, 0, tzinfo=ZoneInfo(key="UTC")),
            "update_time": datetime(2023, 10, 1, 0, 0, tzinfo=ZoneInfo(key="UTC")),
            "platform": "PC",
            "region": "Global",
            "portal": "Steam",
            "event_name": "Autumn Sale 2023",
            "datetime_from": datetime(2023, 11, 21, 18, 0, tzinfo=ZoneInfo(key="UTC")),
            "datetime_to": datetime(2023, 11, 28, 18, 0, tzinfo=ZoneInfo(key="UTC")),
            "is_event_joined": True,
            "discount_depth": 70,
            "unique_event_id": "20231121T180000Z:AutumnSale2023:165761",
            "base_event_id": "4210548",
            "group_id": "9405",
            "base_sku_id": "165761",
            "sales_unique_sku_id": "165761",
            "source_specific_discount_sku_id": "165761",
            "triggers_cooldown": False,
            "major": True,
            "discount_type": "store",
            "max_discount_percentage": 70,
            "price_increase_time": datetime(
                1970, 1, 1, 0, 0, tzinfo=ZoneInfo(key="UTC")
            ),
            "promo_length": 604800,
            "studio_id": 1,
            "unique_sku_id": "165761-steam:1",
            "report_id": 2,
            "portal_platform_region": "Steam:PC:Global",
        },
        {
            "create_time": datetime(2023, 10, 1, 0, 0, tzinfo=ZoneInfo(key="UTC")),
            "update_time": datetime(2023, 10, 1, 0, 0, tzinfo=ZoneInfo(key="UTC")),
            "platform": "PC",
            "region": "Global",
            "portal": "Steam",
            "event_name": "Autumn Sale 2023",
            "datetime_from": datetime(2023, 11, 21, 18, 0, tzinfo=ZoneInfo(key="UTC")),
            "datetime_to": datetime(2023, 11, 28, 18, 0, tzinfo=ZoneInfo(key="UTC")),
            "is_event_joined": True,
            "discount_depth": 60,
            "unique_event_id": "20231121T180000Z:AutumnSale2023:427502",
            "base_event_id": "4210544",
            "group_id": "9405",
            "base_sku_id": "427502",
            "sales_unique_sku_id": "427502",
            "source_specific_discount_sku_id": "427502",
            "triggers_cooldown": False,
            "major": True,
            "discount_type": "store",
            "max_discount_percentage": 60,
            "price_increase_time": datetime(
                1970, 1, 1, 0, 0, tzinfo=ZoneInfo(key="UTC")
            ),
            "promo_length": 604800,
            "studio_id": 1,
            "unique_sku_id": "427502-steam:1",
            "report_id": 2,
            "portal_platform_region": "Steam:PC:Global",
        },
    ]
    assert result.df.to_dicts() == expected_results
