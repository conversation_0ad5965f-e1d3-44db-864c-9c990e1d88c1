import json
from datetime import datetime, timedelta, timezone

import factory
import pytest

from data_sdk.domain.domain_types import ReportMetadata, ReportMetadataWithRawFile
from data_sdk.domain.source import Source
from tests.converters.steam_discounts.data import (
    steam_discounts_raw_report_with_not_required_columns_zip_content,
    steam_discounts_raw_report_zip_content,
)


class _MetadataFactory(factory.Factory):
    class Meta:
        model = ReportMetadata

    class Params:
        number_of_days = 5

    source = Source.STEAM_DISCOUNTS
    studio_id = 1
    report_id = 2
    upload_date = datetime(2021, 1, 27, 11, 10, 0, 277000)
    blob_name = "STEAM-2018-08-08_2019-07-25.zip"
    original_name = "STEAM-2018-08-08_2019-07-25.zip"
    state = "PENDING"
    date_from = datetime(2000, 1, 1, 0, 0)
    date_to = factory.LazyAttribute(
        lambda o: o.date_from + timedelta(days=o.number_of_days - 1)
    )
    no_data = False


@pytest.fixture
def metadata_factory() -> type[factory.Factory]:
    return _MetadataFactory


@pytest.fixture
def steam_discounts_raw_report(
    metadata_factory, mock_zip_file
) -> ReportMetadataWithRawFile:
    metadata = metadata_factory(
        source="steam_discounts",
        upload_date=datetime(2023, 10, 1, tzinfo=timezone.utc),
        blob_name="steam_discounts.zip",
        original_name="steam_discounts.zip",
        date_from=datetime(2010, 1, 1),
        date_to=datetime(2023, 10, 1),
        no_data=False,
    )

    with mock_zip_file(steam_discounts_raw_report_zip_content) as zip_file:
        return ReportMetadataWithRawFile(
            metadata=metadata, raw_file=zip_file.fp.getvalue()
        )


@pytest.fixture
def steam_discounts_raw_report_with_not_required_columns(
    metadata_factory, mock_csv_file, mock_zip_file
) -> ReportMetadataWithRawFile:
    metadata = metadata_factory(
        source="steam_discounts",
        upload_date=datetime(2023, 10, 1, tzinfo=timezone.utc),
        blob_name="steam_discounts.zip",
        original_name="steam_discounts.zip",
        date_from=datetime(2010, 1, 1),
        date_to=datetime(2023, 10, 1),
        no_data=False,
    )

    with mock_zip_file(
        steam_discounts_raw_report_with_not_required_columns_zip_content
    ) as zip_file:
        return ReportMetadataWithRawFile(
            metadata=metadata, raw_file=zip_file.fp.getvalue()
        )


@pytest.fixture
def steam_discounts_raw_report__no_observations(
    metadata_factory, mock_csv_file, mock_zip_file
) -> ReportMetadataWithRawFile:
    metadata = metadata_factory(
        source="steam_discounts",
        upload_date=datetime(2023, 10, 1, tzinfo=timezone.utc),
        blob_name="steam_discounts.zip",
        original_name="steam_discounts.zip",
        date_from=datetime(2010, 1, 1),
        date_to=datetime(2023, 10, 1),
        no_data=False,
    )

    zip_content = {
        "manifest.json": json.dumps({
            "dateFrom": "2010-01-01T00:00:00.000Z",
            "dateTo": "2023-09-26T00:00:00.000Z",
            "metadata": {
                "discountManagementData.json": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": True,
                },
                "discountHistory.json": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": True,
                },
                "basePrices.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "discountEvents.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "maxDiscountPercentages.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "packageDiscounts.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "packageIds.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "priceIncreaseTimes.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "userinfo.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "discountHistory.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
            },
        }),
        "priceIncreaseTimes.csv": mock_csv_file(
            ["publisherId", "packageId", "priceIncreaseTime"],
            [],
        ),
        "userinfo.csv": mock_csv_file(
            [
                "publisherId",
                "loggedIn",
                "steamid",
                "accountid",
                "accountName",
                "isSupport",
                "isLimited",
                "isPartnerMember",
                "countryCode",
            ],
            [],
        ),
        "discountEvents.csv": mock_csv_file(
            [
                "publisherId",
                "name",
                "startDate",
                "endDate",
                "description",
                "collisionType",
                "event",
                "header",
                "tooltip",
                "type",
                "preventWeeklong",
                "appids",
                "optInName",
                "id",
            ],
            [],
        ),
        "discountHistory.csv": mock_csv_file(
            [
                "name",
                "description",
                "startDate",
                "dateStr",
                "endDate",
                "endDateStr",
                "percent",
                "quantity",
                "amount",
                "id",
                "group",
                "class",
                "productId",
                "publisherId",
            ],
            [],
        ),
        "maxDiscountPercentages.csv": mock_csv_file(
            ["publisherId", "packageId", "maxDiscountPercentage"],
            [],
        ),
    }

    with mock_zip_file(zip_content) as zip_file:
        return ReportMetadataWithRawFile(
            metadata=metadata, raw_file=zip_file.fp.getvalue()
        )


@pytest.fixture
def steam_discounts_raw_report__empty_observations_csvs(
    metadata_factory, mock_csv_file, mock_zip_file
) -> ReportMetadataWithRawFile:
    metadata = metadata_factory(
        source="steam_discounts",
        upload_date=datetime(2023, 10, 1, 10, tzinfo=timezone.utc),
        blob_name="steam_discounts.zip",
        original_name="steam_discounts.zip",
        date_from=datetime(2010, 1, 1),
        date_to=datetime(2023, 10, 1),
        no_data=False,
    )

    zip_content = {
        "manifest.json": json.dumps({
            "dateFrom": "2010-01-01T00:00:00.000Z",
            "dateTo": "2023-09-26T00:00:00.000Z",
            "metadata": {
                "discounts_all_3204.csv": {
                    "organization": "3204",
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": True,
                },
                "discountManagementData.json": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": True,
                },
                "discountHistory.json": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": True,
                },
                "basePrices.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "discountEvents.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "maxDiscountPercentages.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "packageDiscounts.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "packageIds.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "priceIncreaseTimes.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "userinfo.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                    "excludedContentDescriptors": '[{"contentDescriptorid":3,"timestampAdded":0}]',
                },
                "discountHistory.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
            },
        }),
        "priceIncreaseTimes.csv": mock_csv_file(
            ["publisherId", "packageId", "priceIncreaseTime"],
            [],
        ),
        "userinfo.csv": mock_csv_file(
            [
                "publisherId",
                "loggedIn",
                "steamid",
                "accountid",
                "accountName",
                "isSupport",
                "isLimited",
                "isPartnerMember",
                "countryCode",
                "excludedContentDescriptors",
            ],
            [
                {
                    "publisherId": 3204,
                    "loggedIn": "true",
                    "steamid": *****************,
                    "accountid": **********,
                    "accountName": "supersuperdata_superhotonly",
                    "isSupport": "false",
                    "isLimited": "false",
                    "isPartnerMember": "true",
                    "countryCode": "PL",
                    "excludedContentDescriptors": '[{"contentDescriptorid":3,"timestampAdded":0}]',
                }
            ],
        ),
        "discounts_all_3204.csv": mock_csv_file(
            [
                "Package Name",
                "ID",
                "Base Price",
                "Steam SHMUP Fest 2023",
                "Weeklong deal for Sep 25",
                "Weeklong deal for Oct 02",
                "Weeklong deal for Oct 09",
            ],
            [],
        ),
        "discountEvents.csv": mock_csv_file(
            [
                "publisherId",
                "name",
                "startDate",
                "endDate",
                "description",
                "collisionType",
                "event",
                "header",
                "tooltip",
                "type",
                "preventWeeklong",
                "appids",
                "optInName",
                "id",
            ],
            [],
        ),
        "discountHistory.csv": mock_csv_file(
            [
                "name",
                "description",
                "startDate",
                "dateStr",
                "endDate",
                "endDateStr",
                "percent",
                "quantity",
                "amount",
                "id",
                "group",
                "class",
                "productId",
                "publisherId",
            ],
            [],
        ),
        "maxDiscountPercentages.csv": mock_csv_file(
            ["publisherId", "packageId", "maxDiscountPercentage"],
            [],
        ),
    }

    with mock_zip_file(zip_content) as zip_file:
        return ReportMetadataWithRawFile(
            metadata=metadata, raw_file=zip_file.fp.getvalue()
        )


@pytest.fixture
def steam_discounts_raw_report__no_joined_events(
    metadata_factory, mock_csv_file, mock_zip_file
) -> ReportMetadataWithRawFile:
    metadata = metadata_factory(
        source="steam_discounts",
        upload_date=datetime(2023, 10, 1, tzinfo=timezone.utc),
        blob_name="steam_discounts.zip",
        original_name="steam_discounts.zip",
        date_from=datetime(2010, 1, 1),
        date_to=datetime(2023, 10, 1),
        no_data=False,
    )

    zip_content = {
        "manifest.json": json.dumps({
            "dateFrom": "2010-01-01T00:00:00.000Z",
            "dateTo": "2023-09-26T00:00:00.000Z",
            "metadata": {
                "discounts_all_3204.csv": {
                    "organization": "3204",
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": True,
                },
                "discountManagementData.json": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": True,
                },
                "discountHistory.json": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": True,
                },
                "basePrices.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "discountEvents.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "maxDiscountPercentages.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "packageDiscounts.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "packageIds.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "priceIncreaseTimes.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "userinfo.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                    "excludedContentDescriptors": '[{"contentDescriptorid":3,"timestampAdded":0}]',
                },
                "discountHistory.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
            },
        }),
        "priceIncreaseTimes.csv": mock_csv_file(
            ["publisherId", "packageId", "priceIncreaseTime"],
            [
                {
                    "publisherId": 3204,
                    "packageId": 165761,
                    "priceIncreaseTime": **********,
                },
            ],
        ),
        "userinfo.csv": mock_csv_file(
            [
                "publisherId",
                "loggedIn",
                "steamid",
                "accountid",
                "accountName",
                "isSupport",
                "isLimited",
                "isPartnerMember",
                "countryCode",
                "excludedContentDescriptors",
            ],
            [
                {
                    "publisherId": 3204,
                    "loggedIn": "true",
                    "steamid": *****************,
                    "accountid": **********,
                    "accountName": "supersuperdata_superhotonly",
                    "isSupport": "false",
                    "isLimited": "false",
                    "isPartnerMember": "true",
                    "countryCode": "PL",
                    "excludedContentDescriptors": '[{"contentDescriptorid":3,"timestampAdded":0}]',
                },
            ],
        ),
        "discounts_all_3204.csv": mock_csv_file(
            [
                "Package Name",
                "ID",
                "Base Price",
                "Steam SHMUP Fest 2023",
                "Weeklong deal for Sep 25",
                "Weeklong deal for Oct 02",
                "Weeklong deal for Oct 09",
            ],
            [
                {
                    "Package Name": "SUPERHOT VR",
                    "ID": 165761,
                    "Base Price": "$24.99",
                    "Steam SHMUP Fest 2023": "N/A",
                    "Weeklong deal for Sep 25": "N/A",
                    "Weeklong deal for Oct 02": "N/A",
                    "Weeklong deal for Oct 09": "N/A",
                },
            ],
        ),
        "discountEvents.csv": mock_csv_file(
            [
                "publisherId",
                "name",
                "startDate",
                "endDate",
                "description",
                "collisionType",
                "event",
                "header",
                "tooltip",
                "type",
                "preventWeeklong",
                "appids",
                "optInName",
                "id",
            ],
            [],
        ),
        "discountHistory.csv": mock_csv_file(
            [
                "name",
                "description",
                "startDate",
                "dateStr",
                "endDate",
                "endDateStr",
                "percent",
                "quantity",
                "amount",
                "id",
                "group",
                "class",
                "productId",
                "publisherId",
            ],
            [],
        ),
        "maxDiscountPercentages.csv": mock_csv_file(
            ["publisherId", "packageId", "maxDiscountPercentage"],
            [
                {
                    "publisherId": 3204,
                    "packageId": 165761,
                    "maxDiscountPercentage": 0,
                }
            ],
        ),
    }

    with mock_zip_file(zip_content) as zip_file:
        return ReportMetadataWithRawFile(
            metadata=metadata, raw_file=zip_file.fp.getvalue()
        )


@pytest.fixture
def steam_discounts_raw_report_no_price_increase_info(
    metadata_factory, mock_csv_file, mock_zip_file
) -> ReportMetadataWithRawFile:
    metadata = metadata_factory(
        source="steam_discounts",
        upload_date=datetime(2023, 10, 1, tzinfo=timezone.utc),
        blob_name="steam_discounts.zip",
        original_name="steam_discounts.zip",
        date_from=datetime(2010, 1, 1),
        date_to=datetime(2023, 10, 1),
        no_data=False,
    )

    zip_content = {
        "manifest.json": json.dumps({
            "dateFrom": "2010-01-01T00:00:00.000Z",
            "dateTo": "2023-09-26T00:00:00.000Z",
            "metadata": {
                "discounts_all_3204.csv": {
                    "organization": "3204",
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": True,
                },
                "discounts_all_123147.csv": {
                    "organization": "123147",
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": True,
                },
                "discountManagementData.json": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": True,
                },
                "discountHistory.json": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": True,
                },
                "basePrices.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "discountEvents.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "maxDiscountPercentages.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "packageDiscounts.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "packageIds.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "priceIncreaseTimes.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "userinfo.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                    "excludedContentDescriptors": '[{"contentDescriptorid":3,"timestampAdded":0}]',
                },
                "discountHistory.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
            },
        }),
        "priceIncreaseTimes.csv": mock_csv_file(
            ["publisherId", "packageId", "priceIncreaseTime"],
            [
                {
                    "publisherId": 3204,
                    "packageId": 165761,
                    "priceIncreaseTime": **********,
                },
            ],
        ),
        "userinfo.csv": mock_csv_file(
            [
                "publisherId",
                "loggedIn",
                "steamid",
                "accountid",
                "accountName",
                "isSupport",
                "isLimited",
                "isPartnerMember",
                "countryCode",
                "excludedContentDescriptors",
            ],
            [
                {
                    "publisherId": 3204,
                    "loggedIn": "true",
                    "steamid": *****************,
                    "accountid": **********,
                    "accountName": "supersuperdata_superhotonly",
                    "isSupport": "false",
                    "isLimited": "false",
                    "isPartnerMember": "true",
                    "countryCode": "PL",
                    "excludedContentDescriptors": '[{"contentDescriptorid":3,"timestampAdded":0}]',
                },
            ],
        ),
        "discounts_all_3204.csv": mock_csv_file(
            [
                "Package Name",
                "ID",
                "Base Price",
                "Steam SHMUP Fest 2023",
                "Weeklong deal for Sep 25",
                "Weeklong deal for Oct 02",
                "Weeklong deal for Oct 09",
                "Weekend Deal - 10/12/2023 - THE COMPLETE SUPERHOT BUNDLE",
                "Weeklong deal for Oct 16",
                "Weeklong deal for Oct 23",
                "Steam Autumn Sale 2023",
            ],
            [
                {
                    "Package Name": "SUPERHOT",
                    "ID": "427502",
                    "Base Price": "$24.99",
                    "Steam SHMUP Fest 2023": "N/A",
                    "Weeklong deal for Sep 25": "N/A",
                    "Weeklong deal for Oct 02": "N/A",
                    "Weeklong deal for Oct 09": "N/A",
                    "Weekend Deal - 10/12/2023 - THE COMPLETE SUPERHOT BUNDLE": "70",
                    "Weeklong deal for Oct 16": "N/A",
                    "Weeklong deal for Oct 23": "0",
                    "Steam Autumn Sale 2023": "0",
                },
                {
                    "Package Name": "SUPERHOT VR",
                    "ID": "165761",
                    "Base Price": "$24.99",
                    "Steam SHMUP Fest 2023": "N/A",
                    "Weeklong deal for Sep 25": "N/A",
                    "Weeklong deal for Oct 02": "N/A",
                    "Weeklong deal for Oct 09": "N/A",
                    "Weekend Deal - 10/12/2023 - THE COMPLETE SUPERHOT BUNDLE": "60",
                    "Weeklong deal for Oct 16": "N/A",
                    "Weeklong deal for Oct 23": "0",
                    "Steam Autumn Sale 2023": "0",
                },
            ],
        ),
        "discountEvents.csv": mock_csv_file(
            [
                "publisherId",
                "name",
                "startDate",
                "endDate",
                "description",
                "collisionType",
                "event",
                "header",
                "tooltip",
                "type",
                "preventWeeklong",
                "appids",
                "optInName",
                "id",
            ],
            [
                {
                    "publisherId": "3204",
                    "name": "Steam Autumn Sale 2023",
                    "startDate": "1700589600",
                    "endDate": "1701194400",
                    "description": "#discount_desc_preset_special",
                    "collisionType": "unique",
                    "event": "1",
                    "header": "Steam Autumn Sale 2023",
                    "tooltip": "SaleEvent_DurationDiscount_Tooltip",
                    "type": "discount",
                    "preventWeeklong": "on",
                    "appids": "",
                    "optInName": "",
                    "id": "8757",
                },
                {
                    "publisherId": "3204",
                    "name": "Weekend Deal - 10/12/2023 - THE COMPLETE SUPERHOT BUNDLE",
                    "startDate": "1697130000",
                    "endDate": "1697734800",
                    "description": "#discount_desc_preset_weekend",
                    "collisionType": "proximity",
                    "event": "1",
                    "header": "Weekend Deal - 10/12/2023 - THE COMPLETE SUPERHOT BUNDLE",
                    "tooltip": "SaleEvent_DurationDiscount_Tooltip",
                    "type": "discount",
                    "preventWeeklong": "",
                    "appids": "322500,617830,690040",
                    "optInName": "",
                    "id": "9405",
                },
                {
                    "publisherId": "3204",
                    "name": "Steam SHMUP Fest 2023",
                    "startDate": "1695661200",
                    "endDate": "1696266000",
                    "description": "#discount_desc_preset_special",
                    "collisionType": "proximity",
                    "event": "1",
                    "header": "Steam SHMUP Fest 2023",
                    "tooltip": "SaleEvent_DurationDiscount_Tooltip",
                    "type": "discount",
                    "preventWeeklong": "",
                    "appids": "",
                    "optInName": "sale_shmup_2023",
                    "id": "8728",
                },
                {
                    "publisherId": "3204",
                    "name": "Steam Strategy Fest",
                    "startDate": "1693242000",
                    "endDate": "1693846800",
                    "description": "",
                    "collisionType": "proximity",
                    "event": "1",
                    "header": "Steam Strategy Fest 2023",
                    "tooltip": "SaleEvent_DurationDiscount_Tooltip",
                    "type": "discount",
                    "preventWeeklong": "",
                    "appids": "",
                    "optInName": "sale_strategy_2023",
                    "id": "6566",
                },
                {
                    "publisherId": "3204",
                    "name": "Weeklong deal for Sep 11",
                    "startDate": "1694451600",
                    "endDate": "1695056400",
                    "description": "#discount_desc_preset_weeklong",
                    "collisionType": "proximity",
                    "event": "",
                    "header": "",
                    "tooltip": "",
                    "type": "",
                    "preventWeeklong": "",
                    "appids": "",
                    "optInName": "",
                    "id": "weeklongdeal_09_11_23",
                },
                {
                    "publisherId": "3204",
                    "name": "Weeklong deal for Sep 18",
                    "startDate": "1695056400",
                    "endDate": "1695661200",
                    "description": "#discount_desc_preset_weeklong",
                    "collisionType": "proximity",
                    "event": "",
                    "header": "",
                    "tooltip": "",
                    "type": "",
                    "preventWeeklong": "",
                    "appids": "",
                    "optInName": "",
                    "id": "weeklongdeal_09_18_23",
                },
                {
                    "publisherId": "3204",
                    "name": "Weeklong deal for Sep 25",
                    "startDate": "1695661200",
                    "endDate": "1696266000",
                    "description": "#discount_desc_preset_weeklong",
                    "collisionType": "proximity",
                    "event": "",
                    "header": "",
                    "tooltip": "",
                    "type": "",
                    "preventWeeklong": "",
                    "appids": "",
                    "optInName": "",
                    "id": "weeklongdeal_09_25_23",
                },
                {
                    "publisherId": "3204",
                    "name": "Weeklong deal for Oct 02",
                    "startDate": "1696266000",
                    "endDate": "1696870800",
                    "description": "#discount_desc_preset_weeklong",
                    "collisionType": "proximity",
                    "event": "",
                    "header": "",
                    "tooltip": "",
                    "type": "",
                    "preventWeeklong": "",
                    "appids": "",
                    "optInName": "",
                    "id": "weeklongdeal_10_02_23",
                },
                {
                    "publisherId": "3204",
                    "name": "Weeklong deal for Oct 09",
                    "startDate": "1696870800",
                    "endDate": "1697475600",
                    "description": "#discount_desc_preset_weeklong",
                    "collisionType": "proximity",
                    "event": "",
                    "header": "",
                    "tooltip": "",
                    "type": "",
                    "preventWeeklong": "",
                    "appids": "",
                    "optInName": "",
                    "id": "weeklongdeal_10_09_23",
                },
                {
                    "publisherId": "3204",
                    "name": "Weeklong deal for Oct 16",
                    "startDate": "1697475600",
                    "endDate": "1698080400",
                    "description": "#discount_desc_preset_weeklong",
                    "collisionType": "proximity",
                    "event": "",
                    "header": "",
                    "tooltip": "",
                    "type": "",
                    "preventWeeklong": "",
                    "appids": "",
                    "optInName": "",
                    "id": "weeklongdeal_10_16_23",
                },
                {
                    "publisherId": "3204",
                    "name": "Weeklong deal for Oct 23",
                    "startDate": "1698080400",
                    "endDate": "1698685200",
                    "description": "#discount_desc_preset_weeklong",
                    "collisionType": "proximity",
                    "event": "",
                    "header": "",
                    "tooltip": "",
                    "type": "",
                    "preventWeeklong": "",
                    "appids": "",
                    "optInName": "",
                    "id": "weeklongdeal_10_23_23",
                },
            ],
        ),
        "discountHistory.csv": mock_csv_file(
            [
                "name",
                "description",
                "startDate",
                "dateStr",
                "endDate",
                "endDateStr",
                "percent",
                "quantity",
                "amount",
                "id",
                "group",
                "class",
                "productId",
                "publisherId",
            ],
            [
                {
                    "name": "Weekend Deal - 10/12/2023 - THE COMPLETE SUPERHOT BUNDLE",
                    "description": "#discount_desc_preset_weekend",
                    "startDate": 1697130000,
                    "dateStr": "10/12/23 10:00",
                    "endDate": 1697734800,
                    "endDateStr": "10/19/23 10:00",
                    "percent": 70,
                    "quantity": 1,
                    "amount": 1750,
                    "id": 3510964,
                    "group": "9405",
                    "class": "released",
                    "productId": 427502,
                    "publisherId": 3204,
                },
                {
                    "name": "Custom Discount",
                    "description": "#discount_desc_preset_special",
                    "startDate": 1693155600,
                    "dateStr": "08/27/23 10:00",
                    "endDate": 1694365200,
                    "endDateStr": "09/10/23 10:00",
                    "percent": 70,
                    "quantity": 1,
                    "amount": 1750,
                    "id": 3441135,
                    "group": "false",
                    "class": "unavailable",
                    "productId": 427502,
                    "publisherId": 3204,
                },
                {
                    "name": "Custom Discount",
                    "description": "#discount_desc_preset_special",
                    "startDate": 1689354000,
                    "dateStr": "07/14/23 10:00",
                    "endDate": 1690563600,
                    "endDateStr": "07/28/23 10:00",
                    "percent": 70,
                    "quantity": 1,
                    "amount": 1750,
                    "id": 3441163,
                    "group": "false",
                    "class": "unavailable",
                    "productId": 427502,
                    "publisherId": 3204,
                },
                {
                    "name": "Weekend Deal - 10/12/2023 - THE COMPLETE SUPERHOT BUNDLE",
                    "description": "#discount_desc_preset_weekend",
                    "startDate": 1697130000,
                    "dateStr": "10/12/23 10:00",
                    "endDate": 1697734800,
                    "endDateStr": "10/19/23 10:00",
                    "percent": 60,
                    "quantity": 1,
                    "amount": 1500,
                    "id": 3510966,
                    "group": "9405",
                    "class": "released",
                    "productId": 165761,
                    "publisherId": 3204,
                },
            ],
        ),
        "maxDiscountPercentages.csv": mock_csv_file(
            ["publisherId", "packageId", "maxDiscountPercentage"],
            [
                {
                    "publisherId": 3204,
                    "packageId": 165761,
                    "maxDiscountPercentage": 90,
                },
                {
                    "publisherId": 3204,
                    "packageId": 197766,
                    "maxDiscountPercentage": 90,
                },
                {
                    "publisherId": 3204,
                    "packageId": 427502,
                    "maxDiscountPercentage": 90,
                },
            ],
        ),
    }

    with mock_zip_file(zip_content) as zip_file:
        return ReportMetadataWithRawFile(
            metadata=metadata, raw_file=zip_file.fp.getvalue()
        )


@pytest.fixture
def steam_discounts_raw_report_joined_store_events(
    metadata_factory, mock_csv_file, mock_zip_file
) -> ReportMetadataWithRawFile:
    metadata = metadata_factory(
        source="steam_discounts",
        upload_date=datetime(2023, 10, 1, tzinfo=timezone.utc),
        blob_name="steam_discounts.zip",
        original_name="steam_discounts.zip",
        date_from=datetime(2010, 1, 1),
        date_to=datetime(2023, 10, 1),
        no_data=False,
    )

    zip_content = {
        "manifest.json": json.dumps({
            "dateFrom": "2010-01-01T00:00:00.000Z",
            "dateTo": "2023-09-26T00:00:00.000Z",
            "metadata": {
                "discounts_all_3204.csv": {
                    "organization": "3204",
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": True,
                },
                "discountManagementData.json": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": True,
                },
                "discountHistory.json": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": True,
                },
                "basePrices.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "discountEvents.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "maxDiscountPercentages.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "packageDiscounts.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "packageIds.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "priceIncreaseTimes.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
                "userinfo.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                    "excludedContentDescriptors": '[{"contentDescriptorid":3,"timestampAdded":0}]',
                },
                "discountHistory.csv": {
                    "dateFrom": "2010-01-01T00:00:00.000Z",
                    "dateTo": "2023-09-26T00:00:00.000Z",
                    "rawData": False,
                },
            },
        }),
        "priceIncreaseTimes.csv": mock_csv_file(
            ["publisherId", "packageId", "priceIncreaseTime"],
            [],
        ),
        "userinfo.csv": mock_csv_file(
            [
                "publisherId",
                "loggedIn",
                "steamid",
                "accountid",
                "accountName",
                "isSupport",
                "isLimited",
                "isPartnerMember",
                "countryCode",
                "excludedContentDescriptors",
            ],
            [
                {
                    "publisherId": 3204,
                    "loggedIn": "true",
                    "steamid": *****************,
                    "accountid": **********,
                    "accountName": "supersuperdata_superhotonly",
                    "isSupport": "false",
                    "isLimited": "false",
                    "isPartnerMember": "true",
                    "countryCode": "PL",
                    "excludedContentDescriptors": '[{"contentDescriptorid":3,"timestampAdded":0}]',
                },
            ],
        ),
        "discounts_all_3204.csv": mock_csv_file(
            [
                "Package Name",
                "ID",
                "Base Price",
                "Steam Autumn Sale 2023",
            ],
            [
                {
                    "Package Name": "SUPERHOT VR",
                    "ID": "165761",
                    "Base Price": "$24.99",
                    "Steam Autumn Sale 2023": "70",
                },
                {
                    "Package Name": "SUPERHOT",
                    "ID": "427502",
                    "Base Price": "$24.99",
                    "Steam Autumn Sale 2023": "60",
                },
            ],
        ),
        "discountEvents.csv": mock_csv_file(
            [
                "publisherId",
                "name",
                "startDate",
                "endDate",
                "description",
                "collisionType",
                "event",
                "header",
                "tooltip",
                "type",
                "preventWeeklong",
                "appids",
                "optInName",
                "id",
            ],
            [
                {
                    "publisherId": "3204",
                    "name": "Steam Autumn Sale 2023",
                    "startDate": "1700589600",
                    "endDate": "1701194400",
                    "description": "#discount_desc_preset_special",
                    "collisionType": "unique",
                    "event": "1",
                    "header": "Steam Autumn Sale 2023",
                    "tooltip": "SaleEvent_DurationDiscount_Tooltip",
                    "type": "discount",
                    "preventWeeklong": "on",
                    "appids": "",
                    "optInName": "",
                    "id": "8757",
                },
            ],
        ),
        "discountHistory.csv": mock_csv_file(
            [
                "name",
                "description",
                "startDate",
                "dateStr",
                "endDate",
                "endDateStr",
                "percent",
                "quantity",
                "amount",
                "id",
                "group",
                "class",
                "productId",
                "publisherId",
            ],
            [
                {
                    "name": "Autumn Sale 2023",
                    "description": "",
                    "startDate": 1700589600,
                    "dateStr": "11/21/23 10:00",
                    "endDate": 1701194400,
                    "endDateStr": "11/28/23 10:00",
                    "percent": 70,
                    "quantity": 1,
                    "amount": 1750,
                    "id": 4210548,
                    "group": "9405",
                    "class": "released",
                    "productId": 165761,
                    "publisherId": 3204,
                },
                {
                    "name": "Autumn Sale 2023",
                    "description": "",
                    "startDate": 1700589600,
                    "dateStr": "11/21/23 10:00",
                    "endDate": 1701194400,
                    "endDateStr": "11/28/23 10:00",
                    "percent": 60,
                    "quantity": 1,
                    "amount": 1750,
                    "id": 4210544,
                    "group": "9405",
                    "class": "released",
                    "productId": 427502,
                    "publisherId": 3204,
                },
            ],
        ),
        "maxDiscountPercentages.csv": mock_csv_file(
            ["publisherId", "packageId", "maxDiscountPercentage"],
            [
                {
                    "publisherId": 3204,
                    "packageId": 165761,
                    "maxDiscountPercentage": 90,
                },
                {
                    "publisherId": 3204,
                    "packageId": 427502,
                    "maxDiscountPercentage": 90,
                },
            ],
        ),
    }

    with mock_zip_file(zip_content) as zip_file:
        return ReportMetadataWithRawFile(
            metadata=metadata, raw_file=zip_file.fp.getvalue()
        )
