from datetime import date

import polars as pl

from core_silver.observation_converter.converters.steam_daily_active_users import (
    SteamDailyActiveUsersConverter,
)


def test_convert_steam_daily_active_users_empty(
    generate_raw_steam_daily_active_users_report,
):
    raw_report = generate_raw_steam_daily_active_users_report(rows=0)
    converter = SteamDailyActiveUsersConverter(raw_report)
    result = converter.convert()
    assert result.empty


def test_convert_steam_daily_active_users_basic_one_line_run(
    generate_raw_steam_daily_active_users_report,
):
    raw_report = generate_raw_steam_daily_active_users_report()
    converter = SteamDailyActiveUsersConverter(raw_report)
    result = converter.convert()

    _expected_df = pl.DataFrame([
        {
            "platform": "PC",
            "region": "Global",
            "portal": "Steam",
            "date": date(2000, 1, 1),
            "human_name": "Test product",
            "daily_active_users": 1,
            "sku_id": "42",
            "store_id": "42",
            "store": "Steam",
            "abbreviated_name": "Steam",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "42-steam:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Steam:PC:Global",
        }
    ])
    assert result.df.equals(_expected_df)


def test_convert_steam_daily_active_users_with_multiple_rows_different_dates(
    generate_raw_steam_daily_active_users_report,
):
    raw_report = generate_raw_steam_daily_active_users_report(
        custom_rows_data=[
            {
                "start_date": date(2025, 1, 1),
                "date": "2025-01-01",
                "product": "SUPERHOT",
                "product_id": "322500",
                "count": 100,
            },
            {
                "start_date": date(2025, 1, 2),
                "date": "2025-01-02",
                "product": "SUPERHOT",
                "product_id": "322500",
                "count": 150,
            },
            {
                "start_date": date(2025, 1, 3),
                "date": "2025-01-03",
                "product": "SUPERHOT",
                "product_id": "322500",
                "count": 200,
            },
        ]
    )
    converter = SteamDailyActiveUsersConverter(raw_report)
    result = converter.convert()

    _expected_df = pl.DataFrame([
        {
            "platform": "PC",
            "region": "Global",
            "portal": "Steam",
            "date": date(2025, 1, 1),
            "human_name": "SUPERHOT",
            "daily_active_users": 100,
            "sku_id": "322500",
            "store_id": "322500",
            "store": "Steam",
            "abbreviated_name": "Steam",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "322500-steam:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Steam:PC:Global",
        },
        {
            "platform": "PC",
            "region": "Global",
            "portal": "Steam",
            "date": date(2025, 1, 2),
            "human_name": "SUPERHOT",
            "daily_active_users": 150,
            "sku_id": "322500",
            "store_id": "322500",
            "store": "Steam",
            "abbreviated_name": "Steam",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "322500-steam:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Steam:PC:Global",
        },
        {
            "platform": "PC",
            "region": "Global",
            "portal": "Steam",
            "date": date(2025, 1, 3),
            "human_name": "SUPERHOT",
            "daily_active_users": 200,
            "sku_id": "322500",
            "store_id": "322500",
            "store": "Steam",
            "abbreviated_name": "Steam",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "322500-steam:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Steam:PC:Global",
        },
    ])

    assert result.df.equals(_expected_df)


def test_convert_steam_daily_active_users_with_multiple_skus_multiple_dates(
    steam_raw_daily_active_users_factory,
    steam_daily_active_users_metadata_with_raw_file_factory,
):
    input_raw_files = steam_raw_daily_active_users_factory(
        start_date=date(2024, 4, 1),
        end_date=date(2024, 4, 2),
        rows__product="SUPERHOT",
        rows__product_id="322500",
        rows__count=100,
    ) + steam_raw_daily_active_users_factory(
        start_date=date(2024, 4, 1),
        end_date=date(2024, 4, 2),
        rows__product="SUPERHOT VR",
        rows__product_id="123456",
        rows__count=50,
    )
    raw_report = steam_daily_active_users_metadata_with_raw_file_factory(
        input_raw_file=input_raw_files,
    )
    converter = SteamDailyActiveUsersConverter(raw_report)

    result = converter.convert()
    _expected_df = pl.DataFrame([
        {
            "platform": "PC",
            "region": "Global",
            "portal": "Steam",
            "date": date(2024, 4, 1),
            "human_name": "SUPERHOT",
            "daily_active_users": 100,
            "sku_id": "322500",
            "store_id": "322500",
            "store": "Steam",
            "abbreviated_name": "Steam",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "322500-steam:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Steam:PC:Global",
        },
        {
            "platform": "PC",
            "region": "Global",
            "portal": "Steam",
            "date": date(2024, 4, 2),
            "human_name": "SUPERHOT",
            "daily_active_users": 100,
            "sku_id": "322500",
            "store_id": "322500",
            "store": "Steam",
            "abbreviated_name": "Steam",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "322500-steam:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Steam:PC:Global",
        },
        {
            "platform": "PC",
            "region": "Global",
            "portal": "Steam",
            "date": date(2024, 4, 1),
            "human_name": "SUPERHOT VR",
            "daily_active_users": 50,
            "sku_id": "123456",
            "store_id": "123456",
            "store": "Steam",
            "abbreviated_name": "Steam",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "123456-steam:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Steam:PC:Global",
        },
        {
            "platform": "PC",
            "region": "Global",
            "portal": "Steam",
            "date": date(2024, 4, 2),
            "human_name": "SUPERHOT VR",
            "daily_active_users": 50,
            "sku_id": "123456",
            "store_id": "123456",
            "store": "Steam",
            "abbreviated_name": "Steam",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "123456-steam:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Steam:PC:Global",
        },
    ])

    assert result.df.equals(_expected_df)
