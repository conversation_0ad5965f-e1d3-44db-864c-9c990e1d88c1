from datetime import date, timedelta

import factory
import pytest
from attr import dataclass

from data_sdk.domain import Portal
from data_sdk.domain.domain_types import ReportMetadataWithRawFile
from data_sdk.domain.observations import ObservationType
from data_sdk.domain.source import Source
from tests.conftest import ExternalReportsFactory, ListBetterSubFactory, ListSubFactory
from tests.utils import _mock_zip_file


class SteamDailyActiveUsersRowFactory(factory.StubFactory):
    class Params:
        start_date = date(year=2000, month=1, day=1)

    date = factory.LazyAttributeSequence(
        lambda o, n: str(o.start_date + timedelta(days=n))
    )

    account = "Test account"
    portal = "steam"
    product = "Test product"
    product_id = 42
    count = 1

    @factory.lazy_attribute
    def csv_row(self):
        return f"{self.account},{self.portal},{self.date},{self.product},{self.product_id},{self.count}\n"


@dataclass
class SteamRawDailyActiveUsers:
    start_date: date
    end_date: date
    rows: list

    @property
    def manifest_json(self):
        return {
            "manifest.json": {
                "dateFrom": f"{self.start_date}",
                "dateTo": f"{self.end_date}",
                "fileMetaData": {
                    f"steam_daily_active_users-{self.start_date}-{self.end_date}.csv": {
                        "dateFrom": f"{self.start_date}",
                        "dateTo": f"{self.end_date}",
                        "observationType": "daily_active_users",
                        "rawData": True,
                    },
                },
            }
        }

    @property
    def daily_active_users_csv(self):
        csvs = {}

        # Add daily file (always exists, even if empty)
        daily_header = "account,portal,date,product,product_id,count\n"

        daily_filename = (
            f"steam_daily_active_users-{self.start_date}-{self.end_date}.csv"
        )
        csvs[daily_filename] = daily_header
        for row in self.rows:
            csvs[daily_filename] += row.csv_row

        return csvs

    @property
    def zip_content(self):
        return {**self.manifest_json, **self.daily_active_users_csv}

    @property
    def raw_zip_file(self):
        with _mock_zip_file(self.zip_content) as zip_file:
            return zip_file.fp.getvalue()

    def __add__(self, other):
        return SteamRawDailyActiveUsersFactory(
            start_date=self.start_date,
            end_date=other.end_date,
            rows=self.rows + other.rows,
        )


class SteamRawDailyActiveUsersFactory(factory.Factory):
    class Meta:
        model = SteamRawDailyActiveUsers

    start_date = date(2024, 4, 1)
    end_date = date(2024, 4, 2)

    rows = ListSubFactory(
        SteamDailyActiveUsersRowFactory,
        size=lambda o: (o.end_date - o.start_date).days + 1,
        start_date=factory.LazyAttribute(lambda row: row.factory_parent.start_date),
    )


@pytest.fixture
def steam_raw_daily_active_users_factory():
    SteamRawDailyActiveUsersFactory.reset_sequence(force=True)
    return SteamRawDailyActiveUsersFactory


class ExternalSteamDailyActiveUsersReportsFactory(ExternalReportsFactory):
    source = Source.STEAM_DAILY_ACTIVE_USERS
    portal = Portal.STEAM
    observation_type = ObservationType.DAILY_ACTIVE_USERS


@pytest.fixture
def external_steam_daily_active_users_reports_factory() -> (
    type[ExternalSteamDailyActiveUsersReportsFactory]
):
    ExternalSteamDailyActiveUsersReportsFactory.reset_sequence(force=True)
    return ExternalSteamDailyActiveUsersReportsFactory


@pytest.fixture
def steam_daily_active_users_metadata_with_raw_file_factory(
    steam_raw_daily_active_users_factory,
    external_steam_daily_active_users_reports_factory,
):
    class SteamDailyActiveUsersReportMetadataWithRawFileFactory(factory.Factory):
        class Meta:
            model = ReportMetadataWithRawFile

        input_raw_file = factory.SubFactory(steam_raw_daily_active_users_factory)
        metadata = factory.SubFactory(external_steam_daily_active_users_reports_factory)

        @factory.lazy_attribute
        def raw_file(self):
            return self.input_raw_file.raw_zip_file

    return SteamDailyActiveUsersReportMetadataWithRawFileFactory


@pytest.fixture
def generate_raw_steam_daily_active_users_report(
    steam_daily_active_users_metadata_with_raw_file_factory,
    steam_raw_daily_active_users_factory,
    external_steam_daily_active_users_reports_factory,
):
    def generate_raw_report(rows: int | None = None, custom_rows_data=None):
        return steam_daily_active_users_metadata_with_raw_file_factory(
            input_raw_file=steam_raw_daily_active_users_factory(
                rows=ListBetterSubFactory(
                    SteamDailyActiveUsersRowFactory,
                    size=len(custom_rows_data)
                    if custom_rows_data is not None and rows is None
                    else rows or 1,
                    custom_rows_data=custom_rows_data if custom_rows_data else [],
                )
            ),
            metadata=external_steam_daily_active_users_reports_factory(),
        )

    return generate_raw_report
