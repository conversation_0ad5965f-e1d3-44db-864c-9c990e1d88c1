from datetime import date

import polars as pl

from core_silver.observation_converter.converters.playstation_daily_active_users import (
    PlaystationDailyActiveUsersConverter,
)


def test_convert_playstation_daily_active_users_empty(
    generate_raw_playstation_daily_active_users_report,
):
    raw_report = generate_raw_playstation_daily_active_users_report(rows=0)
    converter = PlaystationDailyActiveUsersConverter(raw_report)
    result = converter.convert()
    assert result.empty


def test_convert_playstation_daily_active_users_basic_one_line_run(
    generate_raw_playstation_daily_active_users_report,
):
    raw_report = generate_raw_playstation_daily_active_users_report()
    converter = PlaystationDailyActiveUsersConverter(raw_report)
    result = converter.convert()

    _expected_df = pl.DataFrame([
        {
            "platform": "Unknown",
            "region": "PS America",
            "portal": "PlayStation",
            "date": date(2000, 1, 1),
            "human_name": "Test product",
            "daily_active_users": 1,
            "sku_id": "10001234",
            "store_id": "10001234",
            "store": "PlayStation America",
            "abbreviated_name": "PS US",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "10001234-SIEA-playstation-store:1",
            "country_code": "ZZZ",
            "portal_platform_region": "PlayStation:Unknown:PS America",
        }
    ])
    assert result.df.equals(_expected_df)
