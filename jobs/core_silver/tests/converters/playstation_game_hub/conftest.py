from datetime import date, timedelta

import factory
import pytest
from attr import dataclass

from data_sdk.domain import Portal
from data_sdk.domain.domain_types import ReportMetadataWithRawFile
from data_sdk.domain.observations import ObservationType
from data_sdk.domain.source import Source
from tests.conftest import ExternalReportsFactory, ListSubFactory
from tests.utils import _mock_zip_file

visibility_columns = [
    "Global Partner ID",
    "Partner Name",
    "Concept ID",
    "Concept",
    "Product Edition Type",
    "External ID",
    "Title ID",
    "Product ID",
    "Product Name",
    "Full Product Name",
    "Product Primary Classification",
    "Product Secondary Classification",
    "Product Tertiary Classification",
    "Country Code",
    "Country/Region",
    "SIE Sub Region",
    "SIE Region",
    "GSBO Sub Territory",
    "GSBO Territory",
    "GSBO Region",
    "GSBO Hub",
    "Date",
    "Month",
    "Month Start Date",
    "Week Start Date",
    "Impressions Game Hub All Visits",
    "Number Of Accounts Impressions Game Hub All Visits",
    "Interactions All Visits",
    "Number Of Accounts Interactions All Visits",
    "Impressions Pre Media",
    "Number Of Accounts Impressions Pre Media",
    "Interactions Media",
    "Number Of Accounts Interactions Media",
    "Impressions Highlights",
    "Number Of Accounts Impressions Highlights",
    "Interactions Highlights",
    "Number Of Accounts Interactions Highlights",
    "Impressions Pre News Story",
    "Number Of Accounts Impressions Pre News Story",
    "Interactions Pre News Story",
    "Number Of Accounts Interactions Pre News Story",
    "Impressions Post News Story",
    "Number Of Accounts Impressions Post News Story",
    "Interactions Post News Story",
    "Number Of Accounts Interactions Post News Story",
    "Impressions Add To Cart/Buy",
    "Number Of Accounts Impressions Add To Cart/Buy",
    "Interactions Add To Cart/Buy",
    "Number Of Accounts Interactions Add To Cart/Buy",
    "Impressions Downloads",
    "Number Of Accounts Impressions Downloads",
    "Interactions Downloads",
    "Number Of Accounts Interactions Downloads",
    "Impressions Trophy",
    "Number Of Accounts Impressions Trophy",
    "Interactions Trophy",
    "Number Of Accounts Interactions Trophy",
    "Impressions Pre View All Add Ons",
    "Number Of Accounts Impressions Pre View All Add Ons",
    "Interactions Pre View All Add Ons",
    "Number Of Accounts Interactions Pre View All Add Ons",
    "Impressions Post View All Add Ons",
    "Number Of Accounts Impressions Post View All Add Ons",
    "Interactions Post View All Add Ons",
    "Number Of Accounts Interactions Post View All Add Ons",
    "Impressions Pre Add On",
    "Number Of Accounts Impressions Pre Add On",
    "Interactions Pre Add On",
    "Number Of Accounts Interactions Pre Add On",
    "Impressions Post Add On",
    "Number Of Accounts Impressions Post Add On",
    "Interactions Post Add On",
    "Number Of Accounts Interactions Post Add On",
]

engagements_columns = [
    "Global Partner ID",
    "Partner Name",
    "Concept ID",
    "Concept",
    "External ID",
    "Title ID",
    "Product ID",
    "Product Name",
    "Full Product Name",
    "Country Code",
    "Country/Region",
    "SIE Sub Region",
    "SIE Region",
    "GSBO Sub Territory",
    "GSBO Territory",
    "GSBO Region",
    "GSBO Hub",
    "Date",
    "Month",
    "Month Start Date",
    "Week Start Date",
    "Media Content Type",
    "Media Content Type Thumbnail",
    "Impressions",
    "Number Of Accounts Impressions",
    "Interactions",
    "Number Of Accounts Interactions",
    "Impressions Game Highlights",
    "Number Of Accounts Impressions Game Highlights",
    "Interactions Game Highlights",
    "Number Of Accounts Interactions Game Highlights",
]


class PlaystationVisibilitySingleEntryFactory(factory.StubFactory):
    class Params:
        start_date = date(year=2025, month=1, day=1)

    date = factory.LazyAttributeSequence(
        lambda o, n: str(o.start_date + timedelta(days=n))
    )
    global_partner_id = 123456
    partner_name = "Partner Name TEST"
    concept_id = 112233
    concept = "Concept TEST"
    product_edition_type = "Standard"
    external_id = "Not available"
    title_id = "CUSA11111_00"
    product_id = "EP1111-CUSA11111_00-EUCONCEPTTESTGAME00"
    product_name = "Concept Test Game"
    full_product_name = "Concept Test Game"
    product_primary_classification = "Premium Game"
    product_secondary_classification = "Game"
    product_tertiary_classification = "Not available"
    country_code = "BE"
    country_region = "Belgium"
    sie_sub_region = "Benelux"
    sie_region = "SIEE"
    gsbo_sub_territory = "BNL"
    gsbo_territory = "BNL"
    gsbo_region = "Europe"
    gsbo_hub = "EMEA"
    month = factory.LazyAttribute(lambda o: date.strftime(o.start_date, "%b-%y"))
    month_start_date = factory.LazyAttribute(
        lambda o: o.start_date.replace(day=1).strftime("%Y-%m-%d")
    )
    week_start_date = factory.LazyAttribute(
        lambda o: (o.start_date - timedelta(days=o.start_date.weekday())).strftime(
            "%Y-%m-%d"
        )
    )
    impressions_game_hub_all_visits = factory.LazyAttribute(
        lambda o: sum(
            getattr(o, key)
            for key in (
                "impressions_pre_media",
                "impressions_highlights",
                "impressions_pre_news_story",
                "impressions_post_news_story",
                "impressions_add_to_cart_buy",
                "impressions_downloads",
                "impressions_trophy",
                "impressions_pre_view_all_add_ons",
                "impressions_post_view_all_add_ons",
                "impressions_pre_add_on",
                "impressions_post_add_on",
            )
        )
    )
    number_of_accounts_impressions_game_hub_all_visits = factory.LazyAttribute(
        lambda o: sum(
            getattr(o, key)
            for key in (
                "number_of_accounts_impressions_pre_media",
                "number_of_accounts_impressions_highlights",
                "number_of_accounts_impressions_pre_news_story",
                "number_of_accounts_impressions_post_news_story",
                "number_of_accounts_impressions_add_to_cart_buy",
                "number_of_accounts_impressions_downloads",
                "number_of_accounts_impressions_trophy",
                "number_of_accounts_impressions_pre_view_all_add_ons",
                "number_of_accounts_impressions_post_view_all_add_ons",
                "number_of_accounts_impressions_pre_add_on",
                "number_of_accounts_impressions_post_add_on",
            )
        )
    )
    interactions_all_visits = factory.LazyAttribute(
        lambda o: sum(
            getattr(o, key)
            for key in (
                "interactions_media",
                "interactions_highlights",
                "interactions_pre_news_story",
                "interactions_post_news_story",
                "interactions_add_to_cart_buy",
                "interactions_downloads",
                "interactions_trophy",
                "interactions_pre_view_all_add_ons",
                "interactions_post_view_all_add_ons",
                "interactions_pre_add_on",
                "interactions_post_add_on",
            )
        )
    )
    number_of_accounts_interactions_all_visits = factory.LazyAttribute(
        lambda o: sum(
            getattr(o, key)
            for key in (
                "number_of_accounts_interactions_media",
                "number_of_accounts_interactions_highlights",
                "number_of_accounts_interactions_pre_news_story",
                "number_of_accounts_interactions_post_news_story",
                "number_of_accounts_interactions_add_to_cart_buy",
                "number_of_accounts_interactions_downloads",
                "number_of_accounts_interactions_trophy",
                "number_of_accounts_interactions_pre_view_all_add_ons",
                "number_of_accounts_interactions_post_view_all_add_ons",
                "number_of_accounts_interactions_pre_add_on",
                "number_of_accounts_interactions_post_add_on",
            )
        )
    )

    impressions_pre_media = 20
    number_of_accounts_impressions_pre_media = 10
    interactions_media = 2
    number_of_accounts_interactions_media = 1
    impressions_highlights = 0
    number_of_accounts_impressions_highlights = 0
    interactions_highlights = 0
    number_of_accounts_interactions_highlights = 0
    impressions_pre_news_story = 0
    number_of_accounts_impressions_pre_news_story = 0
    interactions_pre_news_story = 0
    number_of_accounts_interactions_pre_news_story = 0
    impressions_post_news_story = 0
    number_of_accounts_impressions_post_news_story = 0
    interactions_post_news_story = 0
    number_of_accounts_interactions_post_news_story = 0
    impressions_add_to_cart_buy = 15
    number_of_accounts_impressions_add_to_cart_buy = 9
    interactions_add_to_cart_buy = 10
    number_of_accounts_interactions_add_to_cart_buy = 7
    impressions_downloads = 3
    number_of_accounts_impressions_downloads = 3
    interactions_downloads = 0
    number_of_accounts_interactions_downloads = 0
    impressions_trophy = 0
    number_of_accounts_impressions_trophy = 0
    interactions_trophy = 0
    number_of_accounts_interactions_trophy = 0
    impressions_pre_view_all_add_ons = 0
    number_of_accounts_impressions_pre_view_all_add_ons = 0
    interactions_pre_view_all_add_ons = 0
    number_of_accounts_interactions_pre_view_all_add_ons = 0
    impressions_post_view_all_add_ons = 0
    number_of_accounts_impressions_post_view_all_add_ons = 0
    interactions_post_view_all_add_ons = 0
    number_of_accounts_interactions_post_view_all_add_ons = 0
    impressions_pre_add_on = 0
    number_of_accounts_impressions_pre_add_on = 0
    interactions_pre_add_on = 0
    number_of_accounts_interactions_pre_add_on = 0
    impressions_post_add_on = 0
    number_of_accounts_impressions_post_add_on = 0
    interactions_post_add_on = 0
    number_of_accounts_interactions_post_add_on = 0

    @factory.lazy_attribute
    def values(self) -> list:
        return [
            self.global_partner_id,
            self.partner_name,
            self.concept_id,
            self.concept,
            self.product_edition_type,
            self.external_id,
            self.title_id,
            self.product_id,
            self.product_name,
            self.full_product_name,
            self.product_primary_classification,
            self.product_secondary_classification,
            self.product_tertiary_classification,
            self.country_code,
            self.country_region,
            self.sie_sub_region,
            self.sie_region,
            self.gsbo_sub_territory,
            self.gsbo_territory,
            self.gsbo_region,
            self.gsbo_hub,
            self.date,
            self.month,
            self.month_start_date,
            self.week_start_date,
            self.impressions_game_hub_all_visits,
            self.number_of_accounts_impressions_game_hub_all_visits,
            self.interactions_all_visits,
            self.number_of_accounts_interactions_all_visits,
            self.impressions_pre_media,
            self.number_of_accounts_impressions_pre_media,
            self.interactions_media,
            self.number_of_accounts_interactions_media,
            self.impressions_highlights,
            self.number_of_accounts_impressions_highlights,
            self.interactions_highlights,
            self.number_of_accounts_interactions_highlights,
            self.impressions_pre_news_story,
            self.number_of_accounts_impressions_pre_news_story,
            self.interactions_pre_news_story,
            self.number_of_accounts_interactions_pre_news_story,
            self.impressions_post_news_story,
            self.number_of_accounts_impressions_post_news_story,
            self.interactions_post_news_story,
            self.number_of_accounts_interactions_post_news_story,
            self.impressions_add_to_cart_buy,
            self.number_of_accounts_impressions_add_to_cart_buy,
            self.interactions_add_to_cart_buy,
            self.number_of_accounts_interactions_add_to_cart_buy,
            self.impressions_downloads,
            self.number_of_accounts_impressions_downloads,
            self.interactions_downloads,
            self.number_of_accounts_interactions_downloads,
            self.impressions_trophy,
            self.number_of_accounts_impressions_trophy,
            self.interactions_trophy,
            self.number_of_accounts_interactions_trophy,
            self.impressions_pre_view_all_add_ons,
            self.number_of_accounts_impressions_pre_view_all_add_ons,
            self.interactions_pre_view_all_add_ons,
            self.number_of_accounts_interactions_pre_view_all_add_ons,
            self.impressions_post_view_all_add_ons,
            self.number_of_accounts_impressions_post_view_all_add_ons,
            self.interactions_post_view_all_add_ons,
            self.number_of_accounts_interactions_post_view_all_add_ons,
            self.impressions_pre_add_on,
            self.number_of_accounts_impressions_pre_add_on,
            self.interactions_pre_add_on,
            self.number_of_accounts_interactions_pre_add_on,
            self.impressions_post_add_on,
            self.number_of_accounts_impressions_post_add_on,
            self.interactions_post_add_on,
            self.number_of_accounts_interactions_post_add_on,
        ]


@dataclass
class PlaystationEngagementsSingleEntryFactory(factory.StubFactory):
    class Params:
        start_date = date(year=2025, month=1, day=1)

    date = factory.LazyAttributeSequence(
        lambda o, n: str(o.start_date + timedelta(days=n))
    )

    global_partner_id = 123456
    partner_name = "Partner Name TEST"
    concept_id = 112233
    concept = "Concept TEST"
    external_id = "Not available"
    title_id = "CUSA11111_00"
    product_id = "EP1111-CUSA11111_00-EUCONCEPTTESTGAME00"
    product_name = "Concept Test Game"
    full_product_name = "Concept Test Game"
    country_code = "BE"
    country_region = "Belgium"
    sie_sub_region = "Benelux"
    sie_region = "SIEE"
    gsbo_sub_territory = "BNL"
    gsbo_territory = "BNL"
    gsbo_region = "Europe"
    gsbo_hub = "EMEA"
    month = factory.LazyAttribute(lambda o: date.strftime(o.start_date, "%b-%y"))
    month_start_date = factory.LazyAttribute(
        lambda o: o.start_date.replace(day=1).strftime("%Y-%m-%d")
    )
    week_start_date = factory.LazyAttribute(
        lambda o: (o.start_date - timedelta(days=o.start_date.weekday())).strftime(
            "%Y-%m-%d"
        )
    )
    media_content_type = "https://sample.url/test.jpg"
    media_content_type_thumbnail = "https://sample.url/test.jpg"
    impressions = 5
    number_of_accounts_impressions = 5
    interactions = 1
    number_of_accounts_interactions = 1
    impressions_game_highlights = 0
    number_of_accounts_impressions_game_highlights = 0
    interactions_game_highlights = 0
    number_of_accounts_interactions_game_highlights = 0

    @factory.lazy_attribute
    def values(self) -> list:
        return [
            self.global_partner_id,
            self.partner_name,
            self.concept_id,
            self.concept,
            self.external_id,
            self.title_id,
            self.product_id,
            self.product_name,
            self.full_product_name,
            self.country_code,
            self.country_region,
            self.sie_sub_region,
            self.sie_region,
            self.gsbo_sub_territory,
            self.gsbo_territory,
            self.gsbo_region,
            self.gsbo_hub,
            self.date,
            self.month,
            self.month_start_date,
            self.week_start_date,
            self.media_content_type,
            self.media_content_type_thumbnail,
            self.impressions,
            self.number_of_accounts_impressions,
            self.interactions,
            self.number_of_accounts_interactions,
            self.impressions_game_highlights,
            self.number_of_accounts_impressions_game_highlights,
            self.interactions_game_highlights,
            self.number_of_accounts_interactions_game_highlights,
        ]


@dataclass
class PlaystationRawGameHub:
    start_date: date
    end_date: date
    visibility_entries: list
    engagements_entries: list

    @property
    def manifest_json(self):
        return {
            "manifest.json": {
                "dateFrom": f"{self.start_date}",
                "dateTo": f"{self.end_date}",
                "fileMetaData": {
                    f"playstation_visibility-{self.start_date}_{self.end_date}.json": {
                        "dateFrom": f"{self.start_date}",
                        "dateTo": f"{self.end_date}",
                        "observationType": "visibility",
                        "rawData": True,
                    },
                    f"playstation_engagements-{self.start_date}_{self.end_date}.json": {
                        "dateFrom": f"{self.start_date}",
                        "dateTo": f"{self.end_date}",
                        "observationType": "engagements",
                        "rawData": True,
                    },
                },
                "manifestVersion": 1,
                "scraperVersion": "1.0.0",
            }
        }

    @property
    def playstation_game_hub_jsons(self):
        def build_json(factory_cls, prefix):
            columns = globals()[f"{prefix}_columns"]
            return {
                "datasource": "9eaf2161-2bdd-4d67-a829-c5166794154a",
                "columns": columns,
                "metadata": ["...metadata_details..."],
                "rows": [x.values for x in getattr(self, f"{prefix}_entries")],
                "numRows": len(self.__dict__[f"{prefix}_entries"]),
                "numColumns": len(columns),
                "from_cache": False,
            }

        jsons = {
            f"playstation_visibility-{self.start_date}_{self.end_date}.json": str(
                build_json(PlaystationVisibilitySingleEntryFactory, "visibility")
            ),
            f"playstation_engagements-{self.start_date}_{self.end_date}.json": str(
                build_json(PlaystationEngagementsSingleEntryFactory, "engagements")
            ),
        }
        return jsons

    @property
    def zip_content(self):
        return {**self.manifest_json, **self.playstation_game_hub_jsons}

    @property
    def raw_zip_file(self):
        with _mock_zip_file(self.zip_content) as zip_file:
            return zip_file.fp.getvalue()


class PlaystationRawGameHubFactory(factory.StubFactory):
    class Meta:
        model = PlaystationRawGameHub

    start_date = date(2024, 4, 1)
    end_date = date(2024, 4, 2)

    visibility_entries = ListSubFactory(
        PlaystationVisibilitySingleEntryFactory,
        size=lambda o: (o.end_date - o.start_date).days + 1,
        start_date=factory.LazyAttribute(lambda row: row.factory_parent.start_date),
    )
    engagements_entries = ListSubFactory(
        PlaystationEngagementsSingleEntryFactory,
        size=lambda o: (o.end_date - o.start_date).days + 1,
        start_date=factory.LazyAttribute(lambda row: row.factory_parent.start_date),
    )


@pytest.fixture
def playstation_raw_game_hub_factory():
    PlaystationRawGameHubFactory.reset_sequence(force=True)
    return PlaystationRawGameHubFactory


class ExternalPlaystationRawImpressionsFactory(ExternalReportsFactory):
    source = Source.PLAYSTATION_IMPRESSIONS
    portal = Portal.PLAYSTATION
    observation_type = ObservationType.VISIBILITY


@pytest.fixture
def external_playstation_raw_impressions_factory() -> (
    type[ExternalPlaystationRawImpressionsFactory]
):
    ExternalPlaystationRawImpressionsFactory.reset_sequence(force=True)
    return ExternalPlaystationRawImpressionsFactory


@pytest.fixture
def playstation_impressions_metadata_with_raw_file_factory(
    playstation_raw_game_hub_factory,
    external_playstation_raw_impressions_factory,
):
    class PlayStationImpressionsReportMetadataWithRawFile(factory.Factory):
        class Meta:
            model = ReportMetadataWithRawFile

        input_raw_file = factory.SubFactory(playstation_raw_game_hub_factory)
        metadata = factory.SubFactory(external_playstation_raw_impressions_factory)

        @factory.lazy_attribute
        def raw_file(self):
            return self.input_raw_file.raw_zip_file

    return PlayStationImpressionsReportMetadataWithRawFile


class ExternalPlaystationRawEngagementsFactory(ExternalReportsFactory):
    source = Source.PLAYSTATION_ENGAGEMENTS
    portal = Portal.PLAYSTATION
    observation_type = ObservationType.ENGAGEMENTS


@pytest.fixture
def external_playstation_raw_engagements_factory() -> (
    type[ExternalPlaystationRawEngagementsFactory]
):
    ExternalPlaystationRawEngagementsFactory.reset_sequence(force=True)
    return ExternalPlaystationRawEngagementsFactory


@pytest.fixture
def playstation_engagements_metadata_with_raw_file_factory(
    playstation_raw_game_hub_factory,
    external_playstation_raw_engagements_factory,
):
    class PlayStationEngagementsReportMetadataWithRawFile(factory.Factory):
        class Meta:
            model = ReportMetadataWithRawFile

        input_raw_file = factory.SubFactory(playstation_raw_game_hub_factory)
        metadata = factory.SubFactory(external_playstation_raw_engagements_factory)

        @factory.lazy_attribute
        def raw_file(self):
            return self.input_raw_file.raw_zip_file

    return PlayStationEngagementsReportMetadataWithRawFile
