from datetime import date

from tests.converters.playstation_game_hub.conftest import (
    engagements_columns,
    visibility_columns,
)


def test_generate_empty_playstation_game_hub_file(
    playstation_raw_game_hub_factory,
):
    playstation_raw_game_hub = playstation_raw_game_hub_factory(
        start_date=date(2025, 6, 1),
        end_date=date(2025, 6, 5),
        visibility_entries=[],
        engagements_entries=[],
    )

    assert playstation_raw_game_hub.start_date == date(2025, 6, 1)
    assert playstation_raw_game_hub.end_date == date(2025, 6, 5)
    assert playstation_raw_game_hub.manifest_json == {
        "manifest.json": {
            "dateFrom": "2025-06-01",
            "dateTo": "2025-06-05",
            "fileMetaData": {
                "playstation_visibility-2025-06-01_2025-06-05.json": {
                    "dateFrom": "2025-06-01",
                    "dateTo": "2025-06-05",
                    "observationType": "visibility",
                    "rawData": True,
                },
                "playstation_engagements-2025-06-01_2025-06-05.json": {
                    "dateFrom": "2025-06-01",
                    "dateTo": "2025-06-05",
                    "observationType": "engagements",
                    "rawData": True,
                },
            },
            "manifestVersion": 1,
            "scraperVersion": "1.0.0",
        }
    }

    assert playstation_raw_game_hub.playstation_game_hub_jsons[
        "playstation_visibility-2025-06-01_2025-06-05.json"
    ] == str({
        "datasource": "9eaf2161-2bdd-4d67-a829-c5166794154a",
        "columns": visibility_columns,
        "metadata": ["...metadata_details..."],
        "rows": [],
        "numRows": 0,
        "numColumns": 73,
        "from_cache": False,
    })
    assert playstation_raw_game_hub.playstation_game_hub_jsons[
        "playstation_engagements-2025-06-01_2025-06-05.json"
    ] == str({
        "datasource": "9eaf2161-2bdd-4d67-a829-c5166794154a",
        "columns": engagements_columns,
        "metadata": ["...metadata_details..."],
        "rows": [],
        "numRows": 0,
        "numColumns": 31,
        "from_cache": False,
    })


def test_generate_one_day_of_playstation_game_hub_file(
    playstation_raw_game_hub_factory,
):
    playstation_raw_game_hub = playstation_raw_game_hub_factory(
        start_date=date(2025, 6, 1),
        end_date=date(2025, 6, 1),
    )
    assert playstation_raw_game_hub.start_date == date(2025, 6, 1)
    assert playstation_raw_game_hub.end_date == date(2025, 6, 1)
    assert playstation_raw_game_hub.manifest_json == {
        "manifest.json": {
            "dateFrom": "2025-06-01",
            "dateTo": "2025-06-01",
            "fileMetaData": {
                "playstation_visibility-2025-06-01_2025-06-01.json": {
                    "dateFrom": "2025-06-01",
                    "dateTo": "2025-06-01",
                    "observationType": "visibility",
                    "rawData": True,
                },
                "playstation_engagements-2025-06-01_2025-06-01.json": {
                    "dateFrom": "2025-06-01",
                    "dateTo": "2025-06-01",
                    "observationType": "engagements",
                    "rawData": True,
                },
            },
            "manifestVersion": 1,
            "scraperVersion": "1.0.0",
        }
    }

    expected_rows = [[123456, "Partner Name TEST"]]

    assert playstation_raw_game_hub.playstation_game_hub_jsons[
        "playstation_visibility-2025-06-01_2025-06-01.json"
    ] == str({
        "datasource": "9eaf2161-2bdd-4d67-a829-c5166794154a",
        "columns": visibility_columns,
        "metadata": ["...metadata_details..."],
        "rows": expected_rows,
        "numRows": len(expected_rows),
        "numColumns": 73,
        "from_cache": False,
    })
