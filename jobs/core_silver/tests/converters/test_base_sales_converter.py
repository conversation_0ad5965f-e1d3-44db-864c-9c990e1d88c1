from unittest.mock import patch

import pandas as pd
import pytest

from core_silver.observation_converter.converters.base_converter import (
    BaseSalesConverter,
)
from data_sdk.domain import SaleCategory


class _TestBaseSalesConverter(BaseSalesConverter):
    """A test subclass of abstract BaseSalesConverter used to test the base methods."""

    def _convert(self):
        raise NotImplementedError


@pytest.fixture
def base_sales_converter():
    return _TestBaseSalesConverter(raw_report=None)


@pytest.fixture
def sales():
    """Example sales observations before adding common columns"""
    converted_rows = [
        {
            "country_code": "USA",
            "currency_code": "USD",
            "studio_id": 1,
            "sku_id": "1",
            "portal": "Steam",
            "platform": "PC",
            "region": "Global",
            "transaction_type": "Game",
            "payment_instrument": "Unknown",
            "tax_type": "Unknown",
            "sale_modificator": "NOT_APPLICABLE",
            "acquisition_platform": "Steam",
            "acquisition_origin": "RETAIL",
            "iap_flag": "False",
            "date": "2000-01-01",
            "report_id": 2,
            "retailer_tag": "France",
            "human_name": "SUPERHOT VR",
            "store_id": "Unknown",
            "base_price_local": 10.0,
            "bundle_name": "Direct Package Sale",
            "net_sales": 113.6,
            "gross_returned": 0.0,
            "gross_sales": 113.6,
            "units_returned": 0,
            "units_sold": 5,
            "free_units": 0,
            "price_local": 10.0,
            "price_usd": 10.0,
            "unique_sku_id": "505511-sales:1",
            "store": "Steam",
            "abbreviated_name": "Steam",
            "net_sales_approx": 79.52,
            "portal_platform_region": "Steam:PC:Global",
        }
    ]
    return pd.DataFrame(data=converted_rows)


def test_add_common_columns(base_sales_converter, sales):
    assert "hash_acquisition_properties" not in sales.columns
    assert "category" not in sales.columns
    result = base_sales_converter._add_common_columns(sales)
    assert result["hash_acquisition_properties"].equals(
        pd.Series(["f13e608a5489fb90cf2f809a8e05d610"])
    )
    assert result["category"].equals(pd.Series(SaleCategory.SALE.value))


def test_convert(base_sales_converter, sales):
    assert "hash_acquisition_properties" not in sales.columns
    assert "category" not in sales.columns

    with patch.object(base_sales_converter, "_convert", new=lambda: sales):
        result = base_sales_converter.convert()
        assert "hash_acquisition_properties" in result.df.columns
        assert "category" in result.df.columns
