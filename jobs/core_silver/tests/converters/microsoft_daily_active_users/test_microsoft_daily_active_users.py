from datetime import date

import polars as pl

from core_silver.observation_converter.converters.microsoft_daily_active_users import (
    MicrosoftDailyActiveUsersConverter,
)


def test_convert_microsoft_daily_active_users_empty(
    generate_raw_microsoft_daily_active_users_report,
):
    raw_report = generate_raw_microsoft_daily_active_users_report(rows=0)
    converter = MicrosoftDailyActiveUsersConverter(raw_report)
    result = converter.convert()
    assert result.empty


def test_convert_microsoft_daily_active_users_basic_one_line_run(
    generate_raw_microsoft_daily_active_users_report,
):
    raw_report = generate_raw_microsoft_daily_active_users_report()
    converter = MicrosoftDailyActiveUsersConverter(raw_report)
    result = converter.convert()

    _expected_df = pl.DataFrame([
        {
            "platform": "PC",
            "region": "Global",
            "portal": "Microsoft",
            "date": date(2000, 1, 1),
            "human_name": "Test product",
            "daily_active_users": 1,
            "sku_id": "9NV17MJB26PG",
            "store_id": "9NV17MJB26PG",
            "store": "Microsoft",
            "abbreviated_name": "Microsoft",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "9NV17MJB26PG-microsoft:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Microsoft:PC:Global",
        }
    ])
    assert result.df.equals(_expected_df)


def test_convert_microsoft_daily_active_users_with_multiple_rows_different_dates(
    generate_raw_microsoft_daily_active_users_report,
):
    raw_report = generate_raw_microsoft_daily_active_users_report(
        custom_rows_data=[
            {
                "start_date": date(2025, 6, 1),
                "date": "2025-06-01",
                "product": "SUPERHOT WINDOWS 10",
                "product_id": "9NV17MJB26PG",
                "count": 14,
            },
            {
                "start_date": date(2025, 6, 2),
                "date": "2025-06-02",
                "product": "SUPERHOT WINDOWS 10",
                "product_id": "9NV17MJB26PG",
                "count": 8,
            },
            {
                "start_date": date(2025, 6, 3),
                "date": "2025-06-03",
                "product": "SUPERHOT WINDOWS 10",
                "product_id": "9NV17MJB26PG",
                "count": 10,
            },
        ]
    )
    converter = MicrosoftDailyActiveUsersConverter(raw_report)
    result = converter.convert()

    _expected_df = pl.DataFrame([
        {
            "platform": "PC",
            "region": "Global",
            "portal": "Microsoft",
            "date": date(2025, 6, 1),
            "human_name": "SUPERHOT WINDOWS 10",
            "daily_active_users": 14,
            "sku_id": "9NV17MJB26PG",
            "store_id": "9NV17MJB26PG",
            "store": "Microsoft",
            "abbreviated_name": "Microsoft",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "9NV17MJB26PG-microsoft:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Microsoft:PC:Global",
        },
        {
            "platform": "PC",
            "region": "Global",
            "portal": "Microsoft",
            "date": date(2025, 6, 2),
            "human_name": "SUPERHOT WINDOWS 10",
            "daily_active_users": 8,
            "sku_id": "9NV17MJB26PG",
            "store_id": "9NV17MJB26PG",
            "store": "Microsoft",
            "abbreviated_name": "Microsoft",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "9NV17MJB26PG-microsoft:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Microsoft:PC:Global",
        },
        {
            "platform": "PC",
            "region": "Global",
            "portal": "Microsoft",
            "date": date(2025, 6, 3),
            "human_name": "SUPERHOT WINDOWS 10",
            "daily_active_users": 10,
            "sku_id": "9NV17MJB26PG",
            "store_id": "9NV17MJB26PG",
            "store": "Microsoft",
            "abbreviated_name": "Microsoft",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "9NV17MJB26PG-microsoft:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Microsoft:PC:Global",
        },
    ])

    assert result.df.equals(_expected_df)


def test_convert_microsoft_daily_active_users_with_multiple_skus_multiple_dates(
    microsoft_raw_daily_active_users_factory,
    microsoft_daily_active_users_metadata_with_raw_file_factory,
):
    input_raw_files = microsoft_raw_daily_active_users_factory(
        start_date=date(2024, 4, 1),
        end_date=date(2024, 4, 2),
        rows__product="SUPERHOT WINDOWS 10",
        rows__product_id="9NV17MJB26PG",
        rows__count=14,
    ) + microsoft_raw_daily_active_users_factory(
        start_date=date(2024, 4, 1),
        end_date=date(2024, 4, 2),
        rows__product="SUPERHOT VR WINDOWS 10",
        rows__product_id="9NBLGGH5FV99",
        rows__count=5,
    )
    raw_report = microsoft_daily_active_users_metadata_with_raw_file_factory(
        input_raw_file=input_raw_files,
    )
    converter = MicrosoftDailyActiveUsersConverter(raw_report)

    result = converter.convert()
    _expected_df = pl.DataFrame([
        {
            "platform": "PC",
            "region": "Global",
            "portal": "Microsoft",
            "date": date(2024, 4, 1),
            "human_name": "SUPERHOT WINDOWS 10",
            "daily_active_users": 14,
            "sku_id": "9NV17MJB26PG",
            "store_id": "9NV17MJB26PG",
            "store": "Microsoft",
            "abbreviated_name": "Microsoft",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "9NV17MJB26PG-microsoft:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Microsoft:PC:Global",
        },
        {
            "platform": "PC",
            "region": "Global",
            "portal": "Microsoft",
            "date": date(2024, 4, 2),
            "human_name": "SUPERHOT WINDOWS 10",
            "daily_active_users": 14,
            "sku_id": "9NV17MJB26PG",
            "store_id": "9NV17MJB26PG",
            "store": "Microsoft",
            "abbreviated_name": "Microsoft",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "9NV17MJB26PG-microsoft:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Microsoft:PC:Global",
        },
        {
            "platform": "PC",
            "region": "Global",
            "portal": "Microsoft",
            "date": date(2024, 4, 1),
            "human_name": "SUPERHOT VR WINDOWS 10",
            "daily_active_users": 5,
            "sku_id": "9NBLGGH5FV99",
            "store_id": "9NBLGGH5FV99",
            "store": "Microsoft",
            "abbreviated_name": "Microsoft",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "9NBLGGH5FV99-microsoft:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Microsoft:PC:Global",
        },
        {
            "platform": "PC",
            "region": "Global",
            "portal": "Microsoft",
            "date": date(2024, 4, 2),
            "human_name": "SUPERHOT VR WINDOWS 10",
            "daily_active_users": 5,
            "sku_id": "9NBLGGH5FV99",
            "store_id": "9NBLGGH5FV99",
            "store": "Microsoft",
            "abbreviated_name": "Microsoft",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "9NBLGGH5FV99-microsoft:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Microsoft:PC:Global",
        },
    ])

    assert result.df.equals(_expected_df)
