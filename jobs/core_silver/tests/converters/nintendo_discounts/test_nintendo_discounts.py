import datetime
from unittest.mock import patch

import pytest
from zoneinfo import ZoneInfo

from core_silver.observation_converter.converters.nintendo_discounts import (
    NintendoDiscountsConverter,
)
from core_silver.observation_converter.exceptions import FileNotFoundInZip


# TODO: Rewrite it all to use factories
def test_no_observed_discounts_report(
    nintendo_discounts_raw_report_empty_csv, external_feature_flags_table_no_flags
):
    result = NintendoDiscountsConverter(
        nintendo_discounts_raw_report_empty_csv, external_feature_flags_table_no_flags
    ).convert()
    assert result.df.is_empty()


def test_no_discount_csv_in_report(
    nintendo_discounts_raw_report_no_discount_csv, external_feature_flags_table_no_flags
):
    with pytest.raises(FileNotFoundInZip):
        NintendoDiscountsConverter(
            nintendo_discounts_raw_report_no_discount_csv,
            external_feature_flags_table_no_flags,
        ).convert()


def test_correct_number_of_rows_nintendo_discounts(
    nintendo_discounts_raw_report, external_feature_flags_table_no_flags
):
    result = NintendoDiscountsConverter(
        nintendo_discounts_raw_report, external_feature_flags_table_no_flags
    ).convert()
    assert len(result.df) == 11


def test_correct_content_nintendo_discounts_report(
    nintendo_discounts_raw_report,
    external_feature_flags_table_deduplicate_nintendo_discounts,
):
    result = NintendoDiscountsConverter(
        nintendo_discounts_raw_report,
        external_feature_flags_table_deduplicate_nintendo_discounts,
    ).convert()

    expected_row = {
        "create_time": datetime.datetime(2023, 11, 1, 0, 0, tzinfo=ZoneInfo("UTC")),
        "update_time": datetime.datetime(2023, 11, 1, 0, 0, tzinfo=ZoneInfo("UTC")),
        "platform": "Switch",
        "region": "Nintendo Europe/Australia",
        "portal": "Nintendo",
        "event_name": "SUPERHOT Sale",
        "datetime_from": datetime.datetime(2023, 7, 6, 13, 0, tzinfo=ZoneInfo("UTC")),
        "datetime_to": datetime.datetime(2023, 8, 4, 22, 0, tzinfo=ZoneInfo("UTC")),
        "is_event_joined": True,
        "discount_depth": 50,
        "unique_event_id": "20230706T130000Z:SUPERHOTSale:HACPAURNA-europe_australia-nintendo:1",
        "base_event_id": "224490",
        "group_id": "224490",
        "base_sku_id": "HACPAURNA",
        "source_specific_discount_sku_id": "70010000020724",
        "triggers_cooldown": True,
        "major": False,
        "discount_type": "custom",
        "max_discount_percentage": 60,
        "price_increase_time": datetime.datetime(
            1970, 1, 1, 0, 0, tzinfo=ZoneInfo("UTC")
        ),
        "promo_length": 2537999,
        "studio_id": 1,
        "unique_sku_id": "HACPAURNA-europe_australia-nintendo:1",
        "report_id": 2,
        "portal_platform_region": "Nintendo:Switch:Nintendo Europe/Australia",
        "sales_unique_sku_id": "HACPAURNA",
    }
    assert result.df.to_dicts()[0] == expected_row
    assert len(result.df) == 11


def test_price_increase_change_nintendo_discounts(
    nintendo_discounts_raw_report_with_price_change,
    external_feature_flags_table_deduplicate_nintendo_discounts,
):
    result = NintendoDiscountsConverter(
        nintendo_discounts_raw_report_with_price_change,
        external_feature_flags_table_deduplicate_nintendo_discounts,
    ).convert()

    expected_row = {
        "create_time": datetime.datetime(2023, 11, 1, 0, 0, tzinfo=ZoneInfo("UTC")),
        "update_time": datetime.datetime(2023, 11, 1, 0, 0, tzinfo=ZoneInfo("UTC")),
        "platform": "Switch",
        "region": "Nintendo Americas",
        "portal": "Nintendo",
        "event_name": "SUPERHOT Sale",
        "datetime_from": datetime.datetime(2019, 10, 31, 22, 0, tzinfo=ZoneInfo("UTC")),
        "datetime_to": datetime.datetime(2019, 11, 8, 8, 0, tzinfo=ZoneInfo("UTC")),
        "is_event_joined": True,
        "discount_depth": 20,
        "unique_event_id": "20191031T220000Z:SUPERHOTSale:HACPAURNA-americas-nintendo:1",
        "base_event_id": "69822",
        "group_id": "69822",
        "base_sku_id": "HACPAURNA",
        "source_specific_discount_sku_id": "70010000020724",
        "triggers_cooldown": True,
        "major": False,
        "discount_type": "custom",
        "max_discount_percentage": 30,
        "price_increase_time": datetime.datetime(
            1970, 1, 1, 0, 0, tzinfo=ZoneInfo("UTC")
        ),
        "promo_length": 640799,
        "studio_id": 1,
        "unique_sku_id": "HACPAURNA-americas-nintendo:1",
        "report_id": 2,
        "portal_platform_region": "Nintendo:Switch:Nintendo Americas",
        "sales_unique_sku_id": "HACPAURNA",
    }
    assert result.df.to_dicts()[0] == expected_row
    assert len(result.df) == 5


# TODO: wtf? why is it not deterministic
def test_different_discount_depth_within_a_region_nintendo_discounts(
    nintendo_discounts_raw_report_different_discount_depth_within_a_region,
    external_feature_flags_table_no_flags,
):
    result = NintendoDiscountsConverter(
        nintendo_discounts_raw_report_different_discount_depth_within_a_region,
        external_feature_flags_table_no_flags,
    ).convert()

    expected_data = [
        {
            "create_time": datetime.datetime(2023, 11, 1, 0, 0, tzinfo=ZoneInfo("UTC")),
            "update_time": datetime.datetime(2023, 11, 1, 0, 0, tzinfo=ZoneInfo("UTC")),
            "platform": "Switch",
            "region": "Nintendo Europe/Australia",
            "portal": "Nintendo",
            "event_name": "SUPERHOT Sale",
            "datetime_from": datetime.datetime(
                2023, 12, 23, 14, 0, tzinfo=ZoneInfo("UTC")
            ),
            "datetime_to": datetime.datetime(
                2024, 1, 21, 23, minute=0, tzinfo=ZoneInfo("UTC")
            ),
            "is_event_joined": True,
            "discount_depth": 30,
            "unique_event_id": "20231223T140000Z:SUPERHOTSale:HACPAURNA-europe_australia-nintendo:1",
            "base_event_id": "243107",
            "group_id": "243107",
            "base_sku_id": "HACPAURNA",
            "source_specific_discount_sku_id": "70010000020724",
            "triggers_cooldown": True,
            "major": False,
            "discount_type": "custom",
            "max_discount_percentage": 30,
            "price_increase_time": datetime.datetime(
                1970, 1, 1, 0, 0, tzinfo=ZoneInfo("UTC")
            ),
            "promo_length": 2537999,
            "studio_id": 1,
            "unique_sku_id": "HACPAURNA-europe_australia-nintendo:1",
            "report_id": 2,
            "portal_platform_region": "Nintendo:Switch:Nintendo Europe/Australia",
            "sales_unique_sku_id": "HACPAURNA",
        },
        {
            "create_time": datetime.datetime(2023, 11, 1, 0, 0, tzinfo=ZoneInfo("UTC")),
            "update_time": datetime.datetime(2023, 11, 1, 0, 0, tzinfo=ZoneInfo("UTC")),
            "platform": "Switch",
            "region": "Nintendo Europe/Australia",
            "portal": "Nintendo",
            "event_name": "SUPERHOT_2 Sale",
            "datetime_from": datetime.datetime(
                2023, 12, 23, 14, 0, tzinfo=ZoneInfo("UTC")
            ),
            "datetime_to": datetime.datetime(
                2024, 1, 21, 23, minute=0, tzinfo=ZoneInfo("UTC")
            ),
            "is_event_joined": True,
            "discount_depth": 60,
            "unique_event_id": "20231223T140000Z:SUPERHOT2Sale:HACPAURNA2-europe_australia-nintendo:1",
            "base_event_id": "243108",
            "group_id": "243108",
            "base_sku_id": "HACPAURNA2",
            "source_specific_discount_sku_id": "70010000020724",
            "triggers_cooldown": True,
            "major": False,
            "discount_type": "custom",
            "max_discount_percentage": 60,
            "price_increase_time": datetime.datetime(
                1970, 1, 1, 0, 0, tzinfo=ZoneInfo("UTC")
            ),
            "promo_length": 2537999,
            "studio_id": 1,
            "unique_sku_id": "HACPAURNA2-europe_australia-nintendo:1",
            "report_id": 2,
            "portal_platform_region": "Nintendo:Switch:Nintendo Europe/Australia",
            "sales_unique_sku_id": "HACPAURNA2",
        },
    ]
    assert result.df.to_dicts() == expected_data


def test_different_discount_depth_within_a_region_no_top_country_nintendo_discounts(
    nintendo_discounts_raw_report_different_discount_depth_within_a_region_no_top_country,
    external_feature_flags_table_no_flags,
):
    result = NintendoDiscountsConverter(
        nintendo_discounts_raw_report_different_discount_depth_within_a_region_no_top_country,
        external_feature_flags_table_no_flags,
    ).convert()

    expected_data = [
        {
            "create_time": datetime.datetime(2023, 11, 1, 0, 0, tzinfo=ZoneInfo("UTC")),
            "update_time": datetime.datetime(2023, 11, 1, 0, 0, tzinfo=ZoneInfo("UTC")),
            "platform": "Switch",
            "region": "Nintendo Europe/Australia",
            "portal": "Nintendo",
            "event_name": "SUPERHOT Sale",
            "datetime_from": datetime.datetime(
                2023, 12, 23, 14, 0, tzinfo=ZoneInfo("UTC")
            ),
            "datetime_to": datetime.datetime(
                2024, 1, 21, 23, 0, 0, tzinfo=ZoneInfo("UTC")
            ),
            "is_event_joined": True,
            "discount_depth": 40,
            "unique_event_id": "20231223T140000Z:SUPERHOTSale:HACPAURNA-europe_australia-nintendo:1",
            "base_event_id": "243107",
            "group_id": "243107",
            "base_sku_id": "HACPAURNA",
            "source_specific_discount_sku_id": "70010000020724",
            "triggers_cooldown": True,
            "major": False,
            "discount_type": "custom",
            "max_discount_percentage": 40,
            "price_increase_time": datetime.datetime(
                1970, 1, 1, 0, 0, tzinfo=ZoneInfo("UTC")
            ),
            "promo_length": 2537999,
            "studio_id": 1,
            "unique_sku_id": "HACPAURNA-europe_australia-nintendo:1",
            "report_id": 2,
            "portal_platform_region": "Nintendo:Switch:Nintendo Europe/Australia",
            "sales_unique_sku_id": "HACPAURNA",
        },
        {
            "create_time": datetime.datetime(2023, 11, 1, 0, 0, tzinfo=ZoneInfo("UTC")),
            "update_time": datetime.datetime(2023, 11, 1, 0, 0, tzinfo=ZoneInfo("UTC")),
            "platform": "Switch",
            "region": "Nintendo Europe/Australia",
            "portal": "Nintendo",
            "event_name": "SUPERHOT_2 Sale",
            "datetime_from": datetime.datetime(
                2023, 12, 23, 14, 0, tzinfo=ZoneInfo("UTC")
            ),
            "datetime_to": datetime.datetime(
                2024, 1, 21, 23, 0, 0, tzinfo=ZoneInfo("UTC")
            ),
            "is_event_joined": True,
            "discount_depth": 60,
            "unique_event_id": "20231223T140000Z:SUPERHOT2Sale:HACPAURNA2-europe_australia-nintendo:1",
            "base_event_id": "243108",
            "group_id": "243108",
            "base_sku_id": "HACPAURNA2",
            "source_specific_discount_sku_id": "70010000020724",
            "triggers_cooldown": True,
            "major": False,
            "discount_type": "custom",
            "max_discount_percentage": 60,
            "price_increase_time": datetime.datetime(
                1970, 1, 1, 0, 0, tzinfo=ZoneInfo("UTC")
            ),
            "promo_length": 2537999,
            "studio_id": 1,
            "unique_sku_id": "HACPAURNA2-europe_australia-nintendo:1",
            "report_id": 2,
            "portal_platform_region": "Nintendo:Switch:Nintendo Europe/Australia",
            "sales_unique_sku_id": "HACPAURNA2",
        },
    ]

    assert result.df.to_dicts() == expected_data


@patch("core_silver.observation_converter.converters.nintendo_discounts.sanitize")
@patch("core_silver.observation_converter.converters.nintendo_discounts.deduplicate")
def test_if_nintendo_sanitizes_discounts(
    mock_deduplicate,
    mock_sanitize,
    nintendo_discounts_raw_report,
    external_feature_flags_table_no_flags,
):
    converter = NintendoDiscountsConverter(
        nintendo_discounts_raw_report, external_feature_flags_table_no_flags
    )

    converter.convert()
    mock_sanitize.assert_called_once()
    mock_deduplicate.assert_not_called()


@patch("core_silver.observation_converter.converters.nintendo_discounts.sanitize")
@patch("core_silver.observation_converter.converters.nintendo_discounts.deduplicate")
def test_if_nintendo_deduplicate_discounts_when_deduplicate_flag_is_enabled(
    mock_deduplicate,
    mock_sanitize,
    nintendo_discounts_raw_report_with_duplicates,
    external_feature_flags_table_deduplicate_nintendo_discounts,
):
    converter = NintendoDiscountsConverter(
        nintendo_discounts_raw_report_with_duplicates,
        external_feature_flags_table_deduplicate_nintendo_discounts,
    )

    converter.convert()

    mock_sanitize.assert_called_once()
    mock_deduplicate.assert_called_once()


def test_if_nintendo_deduplicate_discounts(
    nintendo_discounts_raw_report_with_duplicates,
    external_feature_flags_table_deduplicate_nintendo_discounts,
):
    converter = NintendoDiscountsConverter(
        nintendo_discounts_raw_report_with_duplicates,
        external_feature_flags_table_deduplicate_nintendo_discounts,
    )

    expected_data = [
        {
            "create_time": datetime.datetime(2023, 11, 1, 0, 0, tzinfo=ZoneInfo("UTC")),
            "update_time": datetime.datetime(2023, 11, 1, 0, 0, tzinfo=ZoneInfo("UTC")),
            "platform": "Switch",
            "region": "Nintendo Europe/Australia",
            "portal": "Nintendo",
            "event_name": "SUPERHOT Sale",
            "datetime_from": datetime.datetime(
                2023, 12, 23, 14, 0, tzinfo=ZoneInfo("UTC")
            ),
            "datetime_to": datetime.datetime(
                2024, 1, 21, 23, minute=0, tzinfo=ZoneInfo("UTC")
            ),
            "is_event_joined": True,
            "discount_depth": 60,
            "unique_event_id": "20231223T140000Z:SUPERHOTSale:HACPAURNA-europe_australia-nintendo:1",
            "base_event_id": "243107",
            "group_id": "243107",
            "base_sku_id": "HACPAURNA",
            "source_specific_discount_sku_id": "70010000020724",
            "triggers_cooldown": True,
            "major": False,
            "discount_type": "custom",
            "max_discount_percentage": 60,
            "price_increase_time": datetime.datetime(
                1970, 1, 1, 0, 0, tzinfo=ZoneInfo("UTC")
            ),
            "promo_length": 2537999,
            "studio_id": 1,
            "unique_sku_id": "HACPAURNA-europe_australia-nintendo:1",
            "report_id": 2,
            "portal_platform_region": "Nintendo:Switch:Nintendo Europe/Australia",
            "sales_unique_sku_id": "HACPAURNA",
        }
    ]

    result = converter.convert()

    assert result.df.to_dicts() == expected_data
