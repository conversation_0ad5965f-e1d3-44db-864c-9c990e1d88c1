import json
from datetime import datetime, timezone

import factory
import pytest

from data_sdk.domain.domain_types import (
    ReportMetadata,
    ReportMetadataWithRawFile,
    ReportState,
)
from data_sdk.domain.source import Source


class _MetadataFactory(factory.Factory):
    class Meta:
        model = ReportMetadata

    source = Source.NINTENDO_DISCOUNTS
    studio_id = 1
    report_id = 2
    upload_date = datetime(2023, 11, 1, tzinfo=timezone.utc)
    blob_name = "nintendo_discounts.zip"
    original_name = "nintendo_discounts.zip"
    state = ReportState.PENDING
    date_from = datetime(2010, 1, 1)
    date_to = datetime(2023, 11, 1)
    no_data = False


@pytest.fixture
def metadata_factory() -> type[factory.Factory]:
    return _MetadataFactory


@pytest.fixture
def metadata(metadata_factory) -> ReportMetadata:
    return metadata_factory()


@pytest.fixture
def nintendo_discounts_raw_report_no_discount_csv(
    metadata, mock_csv_file, mock_zip_file
) -> ReportMetadataWithRawFile:
    zip_content = {
        "manifest.json": json.dumps({
            "dateFrom": "2010-01-01",
            "dateTo": "2023-11-01",
            "fileMetaData": {
                "prices.json": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": True,
                },
                "content_lists.json": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": True,
                },
                "discount_groups.json": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": True,
                },
                "country_currency_mapping.json": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": True,
                },
            },
        }),
    }

    with mock_zip_file(zip_content) as zip_file:
        return ReportMetadataWithRawFile(
            metadata=metadata, raw_file=zip_file.fp.getvalue()
        )


@pytest.fixture
def nintendo_discounts_raw_report_empty_csv(
    metadata, mock_csv_file, mock_zip_file
) -> ReportMetadataWithRawFile:
    zip_content = {
        "manifest.json": json.dumps({
            "dateFrom": "2010-01-01",
            "dateTo": "2023-11-01",
            "fileMetaData": {
                "prices.json": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": True,
                },
                "content_lists.json": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": True,
                },
                "discount_groups.json": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": True,
                },
                "country_currency_mapping.json": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": True,
                },
                "discounts.csv": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": False,
                },
            },
        }),
        "discounts.csv": mock_csv_file(
            [
                "submission_id",
                "sale_name",
                "discount_group_id",
                "product_code",
                "ns_uid",
                "discount_id",
                "country_code",
                "start_datetime",
                "end_datetime",
                "currency",
                "discount_value",
                "regular_price",
            ],
            [],
        ),
    }

    with mock_zip_file(zip_content) as zip_file:
        return ReportMetadataWithRawFile(
            metadata=metadata, raw_file=zip_file.fp.getvalue()
        )


@pytest.fixture
def nintendo_discounts_raw_report(
    metadata, mock_csv_file, mock_zip_file
) -> ReportMetadataWithRawFile:
    zip_content = {
        "manifest.json": json.dumps({
            "dateFrom": "2010-01-01",
            "dateTo": "2023-11-01",
            "fileMetaData": {
                "prices.json": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": True,
                },
                "content_lists.json": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": True,
                },
                "discount_groups.json": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": True,
                },
                "country_currency_mapping.json": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": True,
                },
                "discounts.csv": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": False,
                },
            },
        }),
        "discounts.csv": mock_csv_file(
            [
                "submission_id",
                "sale_name",
                "discount_group_id",
                "product_code",
                "ns_uid",
                "discount_id",
                "country_code",
                "start_datetime",
                "end_datetime",
                "currency",
                "discount_value",
                "regular_price",
                "price_start_datetime",
                "price_end_datetime",
                "platform_name",
            ],
            [
                {
                    "submission_id": 190374,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 243107,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 5951830,
                    "country_code": "AU",
                    "start_datetime": "2023-12-23T14:00:00+0000",
                    "end_datetime": "2024-01-21T12:59:59+0000",
                    "currency": "AUD",
                    "discount_value": 21,
                    "regular_price": 34.99,
                    "price_start_datetime": "2020-01-24T04:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 190374,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 243107,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 5951799,
                    "country_code": "BE",
                    "start_datetime": "2023-12-23T14:00:00+0000",
                    "end_datetime": "2024-01-21T22:59:59+0000",
                    "currency": "EUR",
                    "discount_value": 13.8,
                    "regular_price": 22.99,
                    "price_start_datetime": "2019-08-19T14:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 190374,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 243107,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 5951819,
                    "country_code": "PL",
                    "start_datetime": "2023-12-23T14:00:00+0000",
                    "end_datetime": "2024-01-21T22:59:59+0000",
                    "currency": "PLN",
                    "discount_value": 60,
                    "regular_price": 99.99,
                    "price_start_datetime": "2020-01-24T14:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 190372,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 243105,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 5951788,
                    "country_code": "AU",
                    "start_datetime": "2023-10-26T13:00:00+0000",
                    "end_datetime": "2023-11-24T12:59:59+0000",
                    "currency": "AUD",
                    "discount_value": 21,
                    "regular_price": 34.99,
                    "price_start_datetime": "2020-01-24T04:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 190372,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 243105,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 5951757,
                    "country_code": "BE",
                    "start_datetime": "2023-10-26T13:00:00+0000",
                    "end_datetime": "2023-11-24T22:59:59+0000",
                    "currency": "EUR",
                    "discount_value": 13.8,
                    "regular_price": 22.99,
                    "price_start_datetime": "2019-08-19T14:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 190372,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 243105,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 5951777,
                    "country_code": "PL",
                    "start_datetime": "2023-10-26T13:00:00+0000",
                    "end_datetime": "2023-11-24T22:59:59+0000",
                    "currency": "PLN",
                    "discount_value": 60,
                    "regular_price": 99.99,
                    "price_start_datetime": "2020-01-24T14:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 190370,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 243103,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 5951684,
                    "country_code": "US",
                    "start_datetime": "2023-12-21T17:00:00+0000",
                    "end_datetime": "2024-01-04T07:59:59+0000",
                    "currency": "USD",
                    "discount_value": 15,
                    "regular_price": 24.99,
                    "price_start_datetime": "2019-08-19T14:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 190366,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 243099,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 5951644,
                    "country_code": "US",
                    "start_datetime": "2023-11-23T17:00:00+0000",
                    "end_datetime": "2023-12-06T07:59:59+0000",
                    "currency": "USD",
                    "discount_value": 15,
                    "regular_price": 24.99,
                    "price_start_datetime": "2019-08-19T14:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 190363,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 243096,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 5951598,
                    "country_code": "US",
                    "start_datetime": "2023-10-26T16:00:00+0000",
                    "end_datetime": "2023-11-09T07:59:59+0000",
                    "currency": "USD",
                    "discount_value": 15,
                    "regular_price": 24.99,
                    "price_start_datetime": "2019-08-19T14:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 181850,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 234384,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 5676938,
                    "country_code": "US",
                    "start_datetime": "2023-09-27T16:00:00+0000",
                    "end_datetime": "2023-10-11T06:59:59+0000",
                    "currency": "USD",
                    "discount_value": 15,
                    "regular_price": 24.99,
                    "price_start_datetime": "2019-08-19T14:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 181849,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 234383,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 5676934,
                    "country_code": "US",
                    "start_datetime": "2023-08-31T16:00:00+0000",
                    "end_datetime": "2023-09-12T06:59:59+0000",
                    "currency": "USD",
                    "discount_value": 15,
                    "regular_price": 24.99,
                    "price_start_datetime": "2019-08-19T14:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 181848,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 234382,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 5676932,
                    "country_code": "AU",
                    "start_datetime": "2023-09-02T13:00:00+0000",
                    "end_datetime": "2023-09-27T13:59:59+0000",
                    "currency": "AUD",
                    "discount_value": 21,
                    "regular_price": 34.99,
                    "price_start_datetime": "2020-01-24T04:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 181848,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 234382,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 5676901,
                    "country_code": "BE",
                    "start_datetime": "2023-09-02T13:00:00+0000",
                    "end_datetime": "2023-09-27T21:59:59+0000",
                    "currency": "EUR",
                    "discount_value": 11.5,
                    "regular_price": 22.99,
                    "price_start_datetime": "2019-08-19T14:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 181848,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 234382,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 5676921,
                    "country_code": "PL",
                    "start_datetime": "2023-09-02T13:00:00+0000",
                    "end_datetime": "2023-09-27T21:59:59+0000",
                    "currency": "PLN",
                    "discount_value": 50,
                    "regular_price": 99.99,
                    "price_start_datetime": "2020-01-24T14:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 172171,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 224496,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 5351767,
                    "country_code": "US",
                    "start_datetime": "2023-08-04T16:00:00+0000",
                    "end_datetime": "2023-08-18T06:59:59+0000",
                    "currency": "USD",
                    "discount_value": 12.5,
                    "regular_price": 24.99,
                    "price_start_datetime": "2019-08-19T14:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 172169,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 224494,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 5351755,
                    "country_code": "US",
                    "start_datetime": "2023-07-06T16:00:00+0000",
                    "end_datetime": "2023-07-20T06:59:59+0000",
                    "currency": "USD",
                    "discount_value": 12.5,
                    "regular_price": 24.99,
                    "price_start_datetime": "2019-08-19T14:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 172165,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 224490,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 5351729,
                    "country_code": "AU",
                    "start_datetime": "2023-07-06T13:00:00+0000",
                    "end_datetime": "2023-08-04T13:59:59+0000",
                    "currency": "AUD",
                    "discount_value": 17.5,
                    "regular_price": 34.99,
                    "price_start_datetime": "2020-01-24T04:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 172165,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 224490,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 5351698,
                    "country_code": "BE",
                    "start_datetime": "2023-07-06T13:00:00+0000",
                    "end_datetime": "2023-08-04T21:59:59+0000",
                    "currency": "EUR",
                    "discount_value": 11.5,
                    "regular_price": 22.99,
                    "price_start_datetime": "2019-08-19T14:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 172165,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 224490,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 5351718,
                    "country_code": "PL",
                    "start_datetime": "2023-07-06T13:00:00+0000",
                    "end_datetime": "2023-08-04T21:59:59+0000",
                    "currency": "PLN",
                    "discount_value": 50,
                    "regular_price": 99.99,
                    "price_start_datetime": "2020-01-24T14:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
            ],
        ),
    }

    with mock_zip_file(zip_content) as zip_file:
        return ReportMetadataWithRawFile(
            metadata=metadata, raw_file=zip_file.fp.getvalue()
        )


@pytest.fixture
def nintendo_discounts_raw_report_with_price_change(
    metadata, mock_csv_file, mock_zip_file
) -> ReportMetadataWithRawFile:
    zip_content = {
        "manifest.json": json.dumps({
            "dateFrom": "2010-01-01",
            "dateTo": "2023-11-01",
            "fileMetaData": {
                "prices.json": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": True,
                },
                "content_lists.json": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": True,
                },
                "discount_groups.json": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": True,
                },
                "country_currency_mapping.json": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": True,
                },
                "discounts.csv": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": False,
                },
            },
        }),
        "discounts.csv": mock_csv_file(
            [
                "submission_id",
                "sale_name",
                "discount_group_id",
                "product_code",
                "ns_uid",
                "discount_id",
                "country_code",
                "start_datetime",
                "end_datetime",
                "currency",
                "discount_value",
                "regular_price",
                "price_start_datetime",
                "price_end_datetime",
                "platform_name",
            ],
            [
                {
                    "submission_id": 29260,
                    "sale_name": "SUPERHOT SALE",
                    "discount_group_id": 76890,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 962759,
                    "country_code": "BE",
                    "start_datetime": "2020-03-09T11:00:00+0000",
                    "end_datetime": "2020-03-15T22:59:59+0000",
                    "currency": "EUR",
                    "discount_value": 6.9,
                    "regular_price": 22.99,
                    "price_start_datetime": "2019-08-19T14:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 29260,
                    "sale_name": "SUPERHOT SALE",
                    "discount_group_id": 76890,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 962779,
                    "country_code": "PL",
                    "start_datetime": "2020-03-09T11:00:00+0000",
                    "end_datetime": "2020-03-15T22:59:59+0000",
                    "currency": "PLN",
                    "discount_value": 30,
                    "regular_price": 99.99,
                    "price_start_datetime": "2020-01-24T14:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 29261,
                    "sale_name": "SUPERHOT SALE",
                    "discount_group_id": 76891,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 962792,
                    "country_code": "US",
                    "start_datetime": "2020-03-09T19:00:00+0000",
                    "end_datetime": "2020-03-15T19:59:59+0000",
                    "currency": "USD",
                    "discount_value": 7.5,
                    "regular_price": 24.99,
                    "price_start_datetime": "2019-08-19T14:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 23153,
                    "sale_name": "Festive Offers",
                    "discount_group_id": 69830,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 785076,
                    "country_code": "BE",
                    "start_datetime": "2019-12-19T14:00:00+0000",
                    "end_datetime": "2020-01-02T22:59:59+0000",
                    "currency": "EUR",
                    "discount_value": 6.9,
                    "regular_price": 22.99,
                    "price_start_datetime": "2019-08-19T14:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 23153,
                    "sale_name": "Festive Offers",
                    "discount_group_id": 69830,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 785096,
                    "country_code": "PL",
                    "start_datetime": "2019-12-19T14:00:00+0000",
                    "end_datetime": "2020-01-02T22:59:59+0000",
                    "currency": "PLN",
                    "discount_value": 9,
                    "regular_price": 29.99,
                    "price_start_datetime": "2019-08-19T14:00:00+0000",
                    "price_end_datetime": "2020-01-24T13:59:59+0000",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 23152,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 69829,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 785042,
                    "country_code": "BE",
                    "start_datetime": "2019-11-14T11:00:00+0000",
                    "end_datetime": "2019-11-20T22:59:59+0000",
                    "currency": "EUR",
                    "discount_value": 4.6,
                    "regular_price": 22.99,
                    "price_start_datetime": "2019-08-19T14:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 23152,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 69829,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 785062,
                    "country_code": "PL",
                    "start_datetime": "2019-11-14T11:00:00+0000",
                    "end_datetime": "2019-11-20T22:59:59+0000",
                    "currency": "PLN",
                    "discount_value": 6,
                    "regular_price": 29.99,
                    "price_start_datetime": "2019-08-19T14:00:00+0000",
                    "price_end_datetime": "2020-01-24T13:59:59+0000",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 23145,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 69822,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 784796,
                    "country_code": "US",
                    "start_datetime": "2019-10-31T22:00:00+0000",
                    "end_datetime": "2019-11-08T07:59:59+0000",
                    "currency": "USD",
                    "discount_value": 5,
                    "regular_price": 24.99,
                    "price_start_datetime": "2019-08-19T14:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
            ],
        ),
    }

    with mock_zip_file(zip_content) as zip_file:
        return ReportMetadataWithRawFile(
            metadata=metadata, raw_file=zip_file.fp.getvalue()
        )


@pytest.fixture
def nintendo_discounts_raw_report_different_discount_depth_within_a_region(
    metadata, mock_csv_file, mock_zip_file
) -> ReportMetadataWithRawFile:
    zip_content = {
        "manifest.json": json.dumps({
            "dateFrom": "2010-01-01",
            "dateTo": "2023-11-01",
            "fileMetaData": {
                "prices.json": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": True,
                },
                "content_lists.json": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": True,
                },
                "discount_groups.json": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": True,
                },
                "country_currency_mapping.json": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": True,
                },
                "discounts.csv": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": False,
                },
            },
        }),
        "discounts.csv": mock_csv_file(
            [
                "submission_id",
                "sale_name",
                "discount_group_id",
                "product_code",
                "ns_uid",
                "discount_id",
                "country_code",
                "start_datetime",
                "end_datetime",
                "currency",
                "discount_value",
                "regular_price",
                "price_start_datetime",
                "price_end_datetime",
                "platform_name",
            ],
            [
                {
                    "submission_id": 190374,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 243107,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 5951830,
                    "country_code": "AU",
                    "start_datetime": "2023-12-23T14:00:00+0000",
                    "end_datetime": "2024-01-21T12:59:59+0000",
                    "currency": "AUD",
                    "discount_value": 14,
                    "regular_price": 34.99,
                    "price_start_datetime": "2020-01-24T04:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 190374,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 243107,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 5951819,
                    "country_code": "PL",
                    "start_datetime": "2023-12-23T14:00:00+0000",
                    "end_datetime": "2024-01-21T22:59:59+0000",
                    "currency": "PLN",
                    "discount_value": 60,
                    "regular_price": 99.99,
                    "price_start_datetime": "2020-01-24T14:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 190374,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 243107,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 5951815,
                    "country_code": "GB",
                    "start_datetime": "2023-12-23T14:00:00+0000",
                    "end_datetime": "2024-01-21T22:59:59+0000",
                    "currency": "PLN",
                    "discount_value": 6,
                    "regular_price": 19.99,
                    "price_start_datetime": "2020-01-24T14:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 190375,
                    "sale_name": "SUPERHOT_2 Sale",
                    "discount_group_id": 243108,
                    "product_code": "HAC-P-AURNA-2",
                    "ns_uid": 70010000020724,
                    "discount_id": 5951830222,
                    "country_code": "AU",
                    "start_datetime": "2023-12-23T14:00:00+0000",
                    "end_datetime": "2024-01-21T12:59:59+0000",
                    "currency": "AUD",
                    "discount_value": 21,
                    "regular_price": 34.99,
                    "price_start_datetime": "2020-01-24T04:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 190375,
                    "sale_name": "SUPERHOT_2 Sale",
                    "discount_group_id": 243108,
                    "product_code": "HAC-P-AURNA-2",
                    "ns_uid": 70010000020724,
                    "discount_id": 5951819222,
                    "country_code": "PL",
                    "start_datetime": "2023-12-23T14:00:00+0000",
                    "end_datetime": "2024-01-21T22:59:59+0000",
                    "currency": "PLN",
                    "discount_value": 90,
                    "regular_price": 99.99,
                    "price_start_datetime": "2020-01-24T14:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 190375,
                    "sale_name": "SUPERHOT_2 Sale",
                    "discount_group_id": 243108,
                    "product_code": "HAC-P-AURNA-2",
                    "ns_uid": 70010000020724,
                    "discount_id": 5951815222,
                    "country_code": "GB",
                    "start_datetime": "2023-12-23T14:00:00+0000",
                    "end_datetime": "2024-01-21T22:59:59+0000",
                    "currency": "PLN",
                    "discount_value": 12,
                    "regular_price": 19.99,
                    "price_start_datetime": "2020-01-24T14:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
            ],
        ),
    }

    with mock_zip_file(zip_content) as zip_file:
        return ReportMetadataWithRawFile(
            metadata=metadata, raw_file=zip_file.fp.getvalue()
        )


@pytest.fixture
def nintendo_discounts_raw_report_different_discount_depth_within_a_region_no_top_country(
    metadata, mock_csv_file, mock_zip_file
) -> ReportMetadataWithRawFile:
    zip_content = {
        "manifest.json": json.dumps({
            "dateFrom": "2010-01-01",
            "dateTo": "2023-11-01",
            "fileMetaData": {
                "prices.json": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": True,
                },
                "content_lists.json": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": True,
                },
                "discount_groups.json": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": True,
                },
                "country_currency_mapping.json": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": True,
                },
                "discounts.csv": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": False,
                },
            },
        }),
        "discounts.csv": mock_csv_file(
            [
                "submission_id",
                "sale_name",
                "discount_group_id",
                "product_code",
                "ns_uid",
                "discount_id",
                "country_code",
                "start_datetime",
                "end_datetime",
                "currency",
                "discount_value",
                "regular_price",
                "price_start_datetime",
                "price_end_datetime",
                "platform_name",
            ],
            [
                {
                    "submission_id": 190374,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 243107,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 5951830,
                    "country_code": "AU",
                    "start_datetime": "2023-12-23T14:00:00+0000",
                    "end_datetime": "2024-01-21T12:59:59+0000",
                    "currency": "AUD",
                    "discount_value": 14,
                    "regular_price": 34.99,
                    "price_start_datetime": "2020-01-24T04:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 190374,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 243107,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 5951819,
                    "country_code": "PL",
                    "start_datetime": "2023-12-23T14:00:00+0000",
                    "end_datetime": "2024-01-21T22:59:59+0000",
                    "currency": "PLN",
                    "discount_value": 60,
                    "regular_price": 99.99,
                    "price_start_datetime": "2020-01-24T14:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 190375,
                    "sale_name": "SUPERHOT_2 Sale",
                    "discount_group_id": 243108,
                    "product_code": "HAC-P-AURNA-2",
                    "ns_uid": 70010000020724,
                    "discount_id": 5951830222,
                    "country_code": "AU",
                    "start_datetime": "2023-12-23T14:00:00+0000",
                    "end_datetime": "2024-01-21T12:59:59+0000",
                    "currency": "AUD",
                    "discount_value": 21,
                    "regular_price": 34.99,
                    "price_start_datetime": "2020-01-24T04:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 190375,
                    "sale_name": "SUPERHOT_2 Sale",
                    "discount_group_id": 243108,
                    "product_code": "HAC-P-AURNA-2",
                    "ns_uid": 70010000020724,
                    "discount_id": 5951819222,
                    "country_code": "PL",
                    "start_datetime": "2023-12-23T14:00:00+0000",
                    "end_datetime": "2024-01-21T22:59:59+0000",
                    "currency": "PLN",
                    "discount_value": 90,
                    "regular_price": 99.99,
                    "price_start_datetime": "2020-01-24T14:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
            ],
        ),
    }

    with mock_zip_file(zip_content) as zip_file:
        return ReportMetadataWithRawFile(
            metadata=metadata, raw_file=zip_file.fp.getvalue()
        )


@pytest.fixture
def nintendo_discounts_raw_report_with_duplicates(
    metadata, mock_csv_file, mock_zip_file
) -> ReportMetadataWithRawFile:
    zip_content = {
        "manifest.json": json.dumps({
            "dateFrom": "2010-01-01",
            "dateTo": "2023-11-01",
            "fileMetaData": {
                "prices.json": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": True,
                },
                "content_lists.json": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": True,
                },
                "discount_groups.json": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": True,
                },
                "country_currency_mapping.json": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": True,
                },
                "discounts.csv": {
                    "dateFrom": "2010-01-01",
                    "dateTo": "2023-11-01",
                    "rawData": False,
                },
            },
        }),
        "discounts.csv": mock_csv_file(
            [
                "submission_id",
                "sale_name",
                "discount_group_id",
                "product_code",
                "ns_uid",
                "discount_id",
                "country_code",
                "start_datetime",
                "end_datetime",
                "currency",
                "discount_value",
                "regular_price",
                "price_start_datetime",
                "price_end_datetime",
                "platform_name",
            ],
            [
                {
                    "submission_id": 190374,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 243107,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020724,
                    "discount_id": 5951830,
                    "country_code": "AU",
                    "start_datetime": "2023-12-23T14:00:00+0000",
                    "end_datetime": "2024-01-21T12:59:59+0000",
                    "currency": "AUD",
                    "discount_value": 21,
                    "regular_price": 34.99,
                    "price_start_datetime": "2020-01-24T04:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 190375,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 243107,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020725,
                    "discount_id": 5951799,
                    "country_code": "BE",
                    "start_datetime": "2023-12-23T14:00:00+0000",
                    "end_datetime": "2024-01-21T22:59:59+0000",
                    "currency": "EUR",
                    "discount_value": 13.8,
                    "regular_price": 22.99,
                    "price_start_datetime": "2019-08-19T14:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
                {
                    "submission_id": 190376,
                    "sale_name": "SUPERHOT Sale",
                    "discount_group_id": 243107,
                    "product_code": "HAC-P-AURNA",
                    "ns_uid": 70010000020726,
                    "discount_id": 5951819,
                    "country_code": "PL",
                    "start_datetime": "2023-12-23T14:00:00+0000",
                    "end_datetime": "2024-01-21T22:59:59+0000",
                    "currency": "PLN",
                    "discount_value": 60,
                    "regular_price": 99.99,
                    "price_start_datetime": "2020-01-24T14:00:00+0000",
                    "price_end_datetime": "",
                    "platform_name": "Nintendo Switch downloadable software",
                },
            ],
        ),
    }

    with mock_zip_file(zip_content) as zip_file:
        return ReportMetadataWithRawFile(
            metadata=metadata, raw_file=zip_file.fp.getvalue()
        )
