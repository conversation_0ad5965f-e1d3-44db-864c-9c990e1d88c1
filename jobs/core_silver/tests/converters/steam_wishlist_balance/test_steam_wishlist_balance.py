from datetime import date

import polars as pl

from core_silver.observation_converter.converters.steam_wishlist_balance import (
    SteamWishlistBalanceConverter,
)


def test_convert_steam_wishlist_balance_empty(
    generate_raw_steam_wishlist_balance_report,
):
    raw_report = generate_raw_steam_wishlist_balance_report(rows=0)
    converter = SteamWishlistBalanceConverter(raw_report)
    result = converter.convert()
    assert result.empty


def test_convert_steam_wishlist_balance_basic_one_line_run(
    generate_raw_steam_wishlist_balance_report,
):
    raw_report = generate_raw_steam_wishlist_balance_report()
    converter = SteamWishlistBalanceConverter(raw_report)
    result = converter.convert()

    _expected_df = pl.DataFrame([
        {
            "platform": "PC",
            "region": "Global",
            "portal": "Steam",
            "date": date(2000, 1, 1),
            "human_name": "SUPERHOT",
            "balance": 1,
            "sku_id": "322500",
            "store_id": "322500",
            "store": "Steam",
            "abbreviated_name": "Steam",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "322500-steam:1",
            "country_code": "USA",
            "portal_platform_region": "Steam:PC:Global",
        }
    ])
    assert result.df.equals(_expected_df)


def test_convert_steam_wishlist_balance_specific_negative_values_formatting(
    generate_raw_steam_wishlist_balance_report,
):
    raw_report = generate_raw_steam_wishlist_balance_report(
        custom_rows_data=[
            {
                "start_date": date(2025, 1, 1),
                "wishlist_balance": "(122)",
                "organization_name": "Sample-Organization",
                "app_name": "SUPERHOT",
                "app_id": "322500",
                "country": "Poland",
            }
        ]
    )
    converter = SteamWishlistBalanceConverter(raw_report)
    result = converter.convert()
    _expected_df = pl.DataFrame([
        {
            "platform": "PC",
            "region": "Global",
            "portal": "Steam",
            "date": date(2025, 1, 1),
            "human_name": "SUPERHOT",
            "balance": -122,
            "sku_id": "322500",
            "store_id": "322500",
            "store": "Steam",
            "abbreviated_name": "Steam",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "322500-steam:1",
            "country_code": "POL",
            "portal_platform_region": "Steam:PC:Global",
        }
    ])

    assert result.df.equals(_expected_df)


def test_convert_steam_wishlist_balance_with_multiple_rows(
    generate_raw_steam_wishlist_balance_report,
):
    raw_report = generate_raw_steam_wishlist_balance_report(
        custom_rows_data=[
            {
                "start_date": date(2025, 1, 1),
                "date": date(2025, 1, 1),
                "wishlist_balance": "10",
                "organization_name": "Sample-Organization",
                "app_name": "SUPERHOT",
                "app_id": "322500",
                "country": "Poland",
            },
            {
                "start_date": date(2025, 1, 1),
                "date": date(2025, 1, 1),
                "wishlist_balance": "5",
                "organization_name": "Sample-Organization",
                "app_name": "SUPERHOT",
                "app_id": "322500",
                "country": "United States",
            },
            {
                "start_date": date(2025, 1, 2),
                "date": date(2025, 1, 2),
                "wishlist_balance": "(5)",
                "organization_name": "Sample-Organization",
                "app_name": "SUPERHOT",
                "app_id": "322500",
                "country": "Poland",
            },
        ]
    )
    converter = SteamWishlistBalanceConverter(raw_report)
    result = converter.convert()
    _expected_df = pl.DataFrame([
        {
            "platform": "PC",
            "region": "Global",
            "portal": "Steam",
            "date": date(2025, 1, 1),
            "human_name": "SUPERHOT",
            "balance": 10,
            "sku_id": "322500",
            "store_id": "322500",
            "store": "Steam",
            "abbreviated_name": "Steam",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "322500-steam:1",
            "country_code": "POL",
            "portal_platform_region": "Steam:PC:Global",
        },
        {
            "platform": "PC",
            "region": "Global",
            "portal": "Steam",
            "date": date(2025, 1, 1),
            "human_name": "SUPERHOT",
            "balance": 5,
            "sku_id": "322500",
            "store_id": "322500",
            "store": "Steam",
            "abbreviated_name": "Steam",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "322500-steam:1",
            "country_code": "USA",
            "portal_platform_region": "Steam:PC:Global",
        },
        {
            "platform": "PC",
            "region": "Global",
            "portal": "Steam",
            "date": date(2025, 1, 2),
            "human_name": "SUPERHOT",
            "balance": -5,
            "sku_id": "322500",
            "store_id": "322500",
            "store": "Steam",
            "abbreviated_name": "Steam",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "322500-steam:1",
            "country_code": "POL",
            "portal_platform_region": "Steam:PC:Global",
        },
    ])

    assert result.df.equals(_expected_df)


def test_convert_steam_wishlist_balance_with_multiple_csvs_for_multiple_skus(
    steam_raw_wishlist_balance_factory,
    steam_wishlist_balance_metadata_with_raw_file_factory,
):
    input_raw_files = (
        steam_raw_wishlist_balance_factory()
        + steam_raw_wishlist_balance_factory(
            rows__app_name="Game2",
            rows__app_id="123456",
        )
    )
    raw_report = steam_wishlist_balance_metadata_with_raw_file_factory(
        input_raw_file=input_raw_files,
    )
    converter = SteamWishlistBalanceConverter(raw_report)

    result = converter.convert()
    _expected_df = pl.DataFrame([
        {
            "platform": "PC",
            "region": "Global",
            "portal": "Steam",
            "date": date(2024, 4, 1),
            "human_name": "SUPERHOT",
            "balance": 1,
            "sku_id": "322500",
            "store_id": "322500",
            "store": "Steam",
            "abbreviated_name": "Steam",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "322500-steam:1",
            "country_code": "USA",
            "portal_platform_region": "Steam:PC:Global",
        },
        {
            "platform": "PC",
            "region": "Global",
            "portal": "Steam",
            "date": date(2024, 4, 2),
            "human_name": "SUPERHOT",
            "balance": 1,
            "sku_id": "322500",
            "store_id": "322500",
            "store": "Steam",
            "abbreviated_name": "Steam",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "322500-steam:1",
            "country_code": "USA",
            "portal_platform_region": "Steam:PC:Global",
        },
        {
            "platform": "PC",
            "region": "Global",
            "portal": "Steam",
            "date": date(2024, 4, 1),
            "human_name": "Game2",
            "balance": 1,
            "sku_id": "123456",
            "store_id": "123456",
            "store": "Steam",
            "abbreviated_name": "Steam",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "123456-steam:1",
            "country_code": "USA",
            "portal_platform_region": "Steam:PC:Global",
        },
        {
            "platform": "PC",
            "region": "Global",
            "portal": "Steam",
            "date": date(2024, 4, 2),
            "human_name": "Game2",
            "balance": 1,
            "sku_id": "123456",
            "store_id": "123456",
            "store": "Steam",
            "abbreviated_name": "Steam",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "123456-steam:1",
            "country_code": "USA",
            "portal_platform_region": "Steam:PC:Global",
        },
    ])

    assert result.df.equals(_expected_df)
