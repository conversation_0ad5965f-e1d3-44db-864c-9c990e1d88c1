from datetime import date


def test_generate_empty_steam_wishlist_balance(steam_raw_wishlist_balance_factory):
    steam_raw_wishlist_balance = steam_raw_wishlist_balance_factory(
        start_date=date(2023, 10, 1), end_date=date(2023, 10, 1), rows=[]
    )
    assert steam_raw_wishlist_balance.start_date == date(2023, 10, 1)
    assert steam_raw_wishlist_balance.end_date == date(2023, 10, 1)
    assert steam_raw_wishlist_balance.skus == []
    assert steam_raw_wishlist_balance.manifest_json == {
        "manifest.json": {
            "dateFrom": "2023-10-01T00:00:00.000Z",
            "dateTo": "2023-10-01T00:00:00.000Z",
            "metadata": {},
        },
    }
    assert not steam_raw_wishlist_balance.wishlist_balance_csv


def test_generate_two_days_of_steam_wishlist_balance_for_two_skus(
    steam_raw_wishlist_balance_factory,
):
    result = steam_raw_wishlist_balance_factory() + steam_raw_wishlist_balance_factory(
        rows__app_name="Game2",
        rows__app_id="123456",
    )

    assert result.start_date == date(2024, 4, 1)
    assert result.end_date == date(2024, 4, 2)
    assert sorted(result.skus) == [
        ("Game2", "123456"),
        ("SUPERHOT", "322500"),
    ]

    assert result.manifest_json == {
        "manifest.json": {
            "dateFrom": "2024-04-01T00:00:00.000Z",
            "dateTo": "2024-04-02T00:00:00.000Z",
            "metadata": {
                "steam_wishlist_balance-2024-04-01_2024-04-02-Sample-Organization-Game2-123456.csv": {
                    "organization": "Sample Organization",
                    "productId": "123456",
                    "productName": "Game2",
                },
                "steam_wishlist_balance-2024-04-01_2024-04-02-Sample-Organization-SUPERHOT-322500.csv": {
                    "organization": "Sample Organization",
                    "productId": "322500",
                    "productName": "SUPERHOT",
                },
            },
        },
    }

    assert (
        result.wishlist_balance_csv[
            "steam_wishlist_balance-2024-04-01_2024-04-02-Sample-Organization-SUPERHOT-322500.csv"
        ]
        == "Date,App ID,Country,Wishlist balance\n"
        "2024-04-01,322500,United States,1\n"
        "2024-04-02,322500,United States,1\n"
    )


def test_generate_one_day_of_steam_wishlist_balance_for_two_skus(
    steam_raw_wishlist_balance_factory,
):
    result = steam_raw_wishlist_balance_factory(
        start_date=date(2023, 10, 1),
        end_date=date(2023, 10, 1),
    ) + steam_raw_wishlist_balance_factory(
        start_date=date(2023, 10, 1),
        end_date=date(2023, 10, 1),
        rows__app_name="Game2",
        rows__app_id="123456",
    )

    assert result.start_date == date(2023, 10, 1)
    assert result.end_date == date(2023, 10, 1)
    assert sorted(result.skus) == [
        ("Game2", "123456"),
        ("SUPERHOT", "322500"),
    ]
    assert result.manifest_json == {
        "manifest.json": {
            "dateFrom": "2023-10-01T00:00:00.000Z",
            "dateTo": "2023-10-01T00:00:00.000Z",
            "metadata": {
                "steam_wishlist_balance-2023-10-01_2023-10-01-Sample-Organization-Game2-123456.csv": {
                    "organization": "Sample Organization",
                    "productId": "123456",
                    "productName": "Game2",
                },
                "steam_wishlist_balance-2023-10-01_2023-10-01-Sample-Organization-SUPERHOT-322500.csv": {
                    "organization": "Sample Organization",
                    "productId": "322500",
                    "productName": "SUPERHOT",
                },
            },
        },
    }
    assert (
        result.wishlist_balance_csv[
            "steam_wishlist_balance-2023-10-01_2023-10-01-Sample-Organization-SUPERHOT-322500.csv"
        ]
        == "Date,App ID,Country,Wishlist balance\n"
        "2023-10-01,322500,United States,1\n"
    )


def test_generate_steam_wishlist_balance_with_one_custom_sku_for_one_day(
    steam_raw_wishlist_balance_factory,
):
    result = steam_raw_wishlist_balance_factory(
        start_date=date(2023, 10, 1),
        end_date=date(2023, 10, 1),
        rows__app_name="Game2",
        rows__app_id="123456",
        rows__wishlist_balance="(65)",
    )

    assert result.skus == [("Game2", "123456")]
    assert result.start_date == date(2023, 10, 1)
    assert result.end_date == date(2023, 10, 1)
    assert result.manifest_json == {
        "manifest.json": {
            "dateFrom": "2023-10-01T00:00:00.000Z",
            "dateTo": "2023-10-01T00:00:00.000Z",
            "metadata": {
                "steam_wishlist_balance-2023-10-01_2023-10-01-Sample-Organization-Game2-123456.csv": {
                    "organization": "Sample Organization",
                    "productId": "123456",
                    "productName": "Game2",
                },
            },
        },
    }
    assert result.wishlist_balance_csv == {
        "steam_wishlist_balance-2023-10-01_2023-10-01-Sample-Organization-Game2-123456.csv": (
            "Date,App ID,Country,Wishlist balance\n"
            "2023-10-01,123456,United States,(65)\n"
        )
    }


def test_multiple_rows_per_day_for_one_sku(steam_raw_wishlist_balance_factory):
    result = steam_raw_wishlist_balance_factory(
        start_date=date(2023, 10, 1),
        end_date=date(2023, 10, 1),
        rows__app_name="Game2",
        rows__app_id="123456",
        rows__wishlist_balance="(65)",
    ) + steam_raw_wishlist_balance_factory(
        start_date=date(2023, 10, 1),
        end_date=date(2023, 10, 1),
        rows__app_name="Game2",
        rows__app_id="123456",
        rows__country="Poland",
        rows__wishlist_balance="(70)",
    )

    assert result.start_date == date(2023, 10, 1)
    assert result.end_date == date(2023, 10, 1)
    assert result.manifest_json == {
        "manifest.json": {
            "dateFrom": "2023-10-01T00:00:00.000Z",
            "dateTo": "2023-10-01T00:00:00.000Z",
            "metadata": {
                "steam_wishlist_balance-2023-10-01_2023-10-01-Sample-Organization-Game2-123456.csv": {
                    "organization": "Sample Organization",
                    "productId": "123456",
                    "productName": "Game2",
                },
            },
        },
    }
    assert result.wishlist_balance_csv == {
        "steam_wishlist_balance-2023-10-01_2023-10-01-Sample-Organization-Game2-123456.csv": (
            "Date,App ID,Country,Wishlist balance\n"
            "2023-10-01,123456,United States,(65)\n"
            "2023-10-01,123456,Poland,(70)\n"
        )
    }
