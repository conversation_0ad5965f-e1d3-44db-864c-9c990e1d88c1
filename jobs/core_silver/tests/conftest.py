import hashlib
from datetime import date, datetime, timedelta, timezone
from pathlib import Path
from typing import Any, Callable
from unittest.mock import mock_open, patch
from zipfile import ZipFile

import factory
import pandas as pd
import polars as pl
import pytest
from attr import dataclass
from factory.declarations import BaseDeclaration, Sequence, _FactoryWrapper

from core_silver.aggregators.skus import SilverSKUsTable
from core_silver.config import Config
from core_silver.dictionaries.constants import RevenueFactor
from core_silver.external_sources.connectors.user_service import (
    FeatureFlag,
    StaticUserServiceClient,
    UserServiceClient,
)
from core_silver.external_sources.country_codes import CountryCodesExternalProcessor
from core_silver.external_sources.steam_events import SteamEventsExternalProcessor
from core_silver.external_sources.studios import StudiosExternalProcessor
from core_silver.observation_converter.common_columns import (
    generate_portal_platform_region,
)
from core_silver.utils.math_tools import js_round
from data_sdk.config import LocalConfig
from data_sdk.domain import Navigation, Portal
from data_sdk.domain.domain_types import (
    ReportMetadataWithRawFile,
    ReportState,
    StudioId,
)
from data_sdk.domain.observations import ObservationType
from data_sdk.domain.source import Source, source_to_observation_type, source_to_portal
from data_sdk.domain.tables import (
    ExternalCountryCodesTable,
    ExternalFeatureFlagsTable,
    ExternalReportsTable,
    ExternalSharedTable,
    ExternalSKUsTable,
    ExternalSteamEventsTable,
    ExternalStudiosTable,
    ObservationDiscountsTable,
    ObservationSalesTable,
    ObservationVisibilityTable,
    ObservationWishlistActionsTable,
    ObservationWishlistCohortsTable,
)
from tests.__mocks__.tables import currencies
from tests.utils import (
    _csv_file_factory,
    _mock_csv_file,
    _mock_zip_file,
    _raw_zip_file,
    _to_utf8_for_parquet,
)


def pytest_addoption(parser):
    parser.addoption(
        "--runslow", action="store_true", default=False, help="run slow tests"
    )


def pytest_configure(config):
    config.addinivalue_line("markers", "slow: mark test as slow to run")


def pytest_collection_modifyitems(config, items):
    if config.getoption("--runslow"):
        # --runslow given in cli: run only slow tests
        skip_non_slow = pytest.mark.skip(reason="only slow tests selected")
        for item in items:
            if "slow" not in item.keywords:
                item.add_marker(skip_non_slow)
    else:
        # --runslow not given in cli: skip slow tests
        skip_slow = pytest.mark.skip(reason="need --runslow option to run")
        for item in items:
            if "slow" in item.keywords:
                item.add_marker(skip_slow)


@pytest.fixture
def local_config(tmp_path):
    return Config(
        input_cfg=LocalConfig(local_dir=tmp_path.absolute() / "raw"),
        converted_reports_cfg=LocalConfig(local_dir=tmp_path.absolute() / "converted"),
        output_cfg=LocalConfig(local_dir=tmp_path.absolute() / "result"),
    )


@pytest.fixture
def mock_csv_file():
    return _mock_csv_file


@pytest.fixture
def mock_zip_file() -> Callable[[dict[str, str | dict]], ZipFile]:
    return _mock_zip_file


@pytest.fixture
def raw_zip_file():
    return _raw_zip_file


@pytest.fixture
def csv_file_factory() -> Callable[..., str]:
    return _csv_file_factory


@pytest.fixture
def to_utf8_for_parquet():
    return _to_utf8_for_parquet


@pytest.fixture
def user_service_client() -> UserServiceClient:
    return StaticUserServiceClient()


class _ConvertedSalesFactory(factory.DictFactory):
    class Params:
        start_date = date(year=2000, month=1, day=1)

    country_code = "USA"
    currency_code = "USD"
    studio_id = 1
    sku_id = "505511"
    portal = "Steam"
    platform = "PC"
    region = "Global"
    transaction_type = "Retail"
    payment_instrument = "Unknown"
    tax_type = "Unknown"
    sale_modificator = "NOT_APPLICABLE"
    acquisition_platform = "Windows"
    acquisition_origin = "MAIN_STORE"
    iap_flag = "False"
    date = factory.LazyAttributeSequence(lambda o, n: o.start_date + timedelta(days=n))
    report_id = factory.Sequence(lambda n: n)
    retailer_tag = "France"
    human_name = "SUPERHOT"
    store_id = "505511"
    base_price_local = 10.00
    bundle_name = "Direct Package Sale"
    net_sales = factory.LazyAttribute(
        lambda o: js_round(o.price_local * o.units_sold, 2)
    )
    gross_returned = 0.0
    gross_sales = factory.LazyAttribute(
        lambda o: js_round(o.price_local * o.units_sold, 2)
    )
    units_returned = 0
    units_sold = 5
    free_units = 0
    price_local = 22.72
    price_usd = 24.99
    unique_sku_id = factory.LazyAttribute(
        lambda o: f"{o.sku_id}-{o.portal.lower()}:{o.studio_id}"
    )
    store = "Steam"
    abbreviated_name = "Steam"
    net_sales_approx = factory.LazyAttribute(
        lambda o: js_round(o.net_sales * RevenueFactor.STD.value, 2)
    )
    hash_acquisition_properties = factory.LazyAttribute(
        lambda o: hashlib.md5(  # noqa: S324
            "".join(
                getattr(o, name)
                for name in [
                    "transaction_type",
                    "tax_type",
                    "sale_modificator",
                    "acquisition_platform",
                    "acquisition_origin",
                    "iap_flag",
                ]
            ).encode("utf-8")
        ).hexdigest()
    )
    portal_platform_region = factory.LazyAttribute(
        lambda o: generate_portal_platform_region(o.portal, o.platform, o.region)
    )
    category = "Sale"

    @classmethod
    def build_table(cls, size: int = 1, **kwargs) -> ObservationSalesTable:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls.converted_rows_to_table(raw_rows)

    @classmethod
    def build_df_batch(cls, size: int = 1, **kwargs) -> pl.DataFrame:
        """Build a batch of instances of the given class, with overridden attrs.

        Args:
            size (int): the number of instances to build

        Returns:
            object pd.DataFrame: the built instances with js_round(ed) values
        """
        raw_rows = cls.build_batch(size=size, **kwargs)
        table = cls.converted_rows_to_table(raw_rows)
        return table.df

    @staticmethod
    def converted_rows_to_table(converted_rows: list[dict]) -> ObservationSalesTable:
        ordered_columns = list(ObservationSalesTable.model.to_schema().columns.keys())
        if not converted_rows:
            return ObservationSalesTable(df=pl.DataFrame())
        df = pl.DataFrame(converted_rows).select(ordered_columns)
        return ObservationSalesTable(df=df)


@pytest.fixture
def converted_sales_factory() -> type[factory.Factory]:
    _ConvertedSalesFactory.reset_sequence(force=True)
    return _ConvertedSalesFactory


class _ConvertedVisibilityFactory(factory.DictFactory):
    class Params:
        start_date = date(year=2000, month=1, day=1)

    page_category = "Browse Search Results"
    page_feature = "Browse Search Results"
    impressions = 369
    visits = 0
    owner_impressions = 0
    owner_visits = 0
    sku_id = "583710"
    date = factory.LazyAttributeSequence(lambda o, n: o.start_date + timedelta(days=n))
    human_name = "Dimension Hunter Demo"
    studio_id = 1
    platform = "PC"
    hash_traffic_source = "8006adebacb4c27c389fd872110fcc7c"
    report_id = 3
    portal = "Steam"
    store_id = "583710"
    region = "Global"
    unique_sku_id = factory.LazyAttribute(lambda o: f"{o.sku_id}-store:{o.studio_id}")
    portal_platform_region = factory.LazyAttribute(
        lambda o: generate_portal_platform_region(o.portal, o.platform, o.region)
    )
    store = "Steam"
    abbreviated_name = "Steam"
    navigation = Navigation.DIRECT

    @classmethod
    def build_table(cls, size: int = 1, **kwargs) -> ObservationVisibilityTable:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows)

    @classmethod
    def build_df_batch(cls, size, **kwargs) -> pl.DataFrame:
        """Build a batch of instances of the given class, with overridden attrs.

        Args:
            size (int): the number of instances to build

        Returns:
            object pd.DataFrame: the built instances with js_round(ed) values
        """
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows).df

    @staticmethod
    def _converted_rows_to_table(
        converted_rows: list[dict],
    ) -> ObservationVisibilityTable:
        return ObservationVisibilityTable(df=pl.DataFrame(converted_rows))


@pytest.fixture
def converted_visibility_factory() -> type[factory.Factory]:
    _ConvertedVisibilityFactory.reset_sequence(force=True)
    return _ConvertedVisibilityFactory


class _ConvertedWishlistActionsFactory(factory.DictFactory):
    class Params:
        start_date = date(year=2000, month=1, day=1)

    report_id = 4
    studio_id = 1
    platform = "PC"
    region = "Global"
    portal = "Steam"
    date = factory.LazyAttributeSequence(lambda o, n: o.start_date + timedelta(days=n))
    human_name = "Dimension Hunter Demo"
    adds = 369
    deletes = 0
    purchases_and_activations = 0
    gifts = 0
    sku_id = "583710"
    store_id = "505511"
    unique_sku_id = factory.LazyAttribute(lambda o: f"{o.sku_id}-store:{o.studio_id}")
    portal_platform_region = factory.LazyAttribute(
        lambda o: generate_portal_platform_region(o.portal, o.platform, o.region)
    )
    store = "Steam"
    abbreviated_name = "Steam"
    country_code = "YYY"

    @classmethod
    def build_table(cls, size: int = 1, **kwargs) -> ObservationWishlistActionsTable:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows)

    @classmethod
    def build_df_batch(cls, size, **kwargs) -> pl.DataFrame:
        """Build a batch of instances of the given class, with overridden attrs.

        Args:
            size (int): the number of instances to build

        Returns:
            object pd.DataFrame: the built instances with js_round(ed) values
        """
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows).df

    @staticmethod
    def _converted_rows_to_table(
        converted_rows: list[dict],
    ) -> ObservationWishlistActionsTable:
        return ObservationWishlistActionsTable(df=pl.DataFrame(converted_rows))


@pytest.fixture
def converted_wishlist_actions_factory() -> type[factory.Factory]:
    _ConvertedWishlistActionsFactory.reset_sequence(force=True)
    return _ConvertedWishlistActionsFactory


class _ConvertedWishlistCohortsFactory(factory.DictFactory):
    class Params:
        start_date = date(year=2000, month=1, day=1)

    report_id = 5
    studio_id = 1
    platform = "PC"
    region = "Global"
    portal = "Steam"
    date = factory.LazyAttributeSequence(lambda o, n: o.start_date + timedelta(days=n))
    human_name = "Dimension Hunter Demo"
    month_cohort = "369"
    total_conversions = 0
    purchases_and_activations = 0
    gifts = 0
    sku_id = "583710"
    store_id = "505511"
    unique_sku_id = factory.LazyAttribute(lambda o: f"{o.sku_id}-store:{o.studio_id}")
    portal_platform_region = factory.LazyAttribute(
        lambda o: generate_portal_platform_region(o.portal, o.platform, o.region)
    )
    store = "Steam"
    abbreviated_name = "Steam"

    @classmethod
    def build_table(cls, size: int = 1, **kwargs) -> ObservationWishlistCohortsTable:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows)

    @classmethod
    def build_df_batch(cls, size, **kwargs) -> pl.DataFrame:
        """Build a batch of instances of the given class, with overridden attrs.

        Args:
            size (int): the number of instances to build

        Returns:
            object pd.DataFrame: the built instances with js_round(ed) values
        """
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows).df

    @staticmethod
    def _converted_rows_to_table(
        converted_rows: list[dict],
    ) -> ObservationWishlistCohortsTable:
        return ObservationWishlistCohortsTable(df=pl.DataFrame(converted_rows))


# NOTE: this might not return perfect data, I was in hurry to finish this
class _ConvertedDiscountsFactory(factory.DictFactory):
    class Params:
        start_date = date(year=2000, month=1, day=1)

    create_time = datetime(year=2024, month=4, day=1, hour=10, minute=0)
    update_time = datetime(year=2024, month=4, day=1, hour=10, minute=0)
    platform = "PC"
    region = "Global"
    portal = "Steam"
    event_name = "Dimension Hunter Demo event"
    datetime_from = datetime(year=2024, month=4, day=1, hour=10, minute=0)
    datetime_to = datetime(year=2024, month=4, day=1, hour=10, minute=0)
    is_event_joined = False
    discount_depth = 0.3
    unique_event_id = "583710"
    base_event_id = "505511"
    group_id = "505511"
    base_sku_id = "583710"
    source_specific_discount_sku_id = "583710"
    sales_unique_sku_id = "583710"
    triggers_cooldown = False
    major = False
    discount_type = "custom"
    max_discount_percentage = 30
    price_increase_time = datetime(year=2024, month=4, day=1, hour=10, minute=0)
    promo_length = 30
    studio_id = 1
    unique_sku_id = factory.LazyAttribute(
        lambda o: f"{o.base_sku_id}-{o.portal.lower()}:{o.studio_id}"
    )
    report_id = 6
    portal_platform_region = factory.LazyAttribute(
        lambda o: generate_portal_platform_region(o.portal, o.platform, o.region)
    )

    @classmethod
    def build_table(cls, size: int = 1, **kwargs) -> ObservationDiscountsTable:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows)

    @classmethod
    def build_df_batch(cls, size, **kwargs) -> pl.DataFrame:
        """Build a batch of instances of the given class, with overridden attrs.

        Args:
            size (int): the number of instances to build

        Returns:
            object pd.DataFrame: the built instances with js_round(ed) values
        """
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows).df

    @staticmethod
    def _converted_rows_to_table(
        converted_rows: list[dict],
    ) -> ObservationDiscountsTable:
        return ObservationDiscountsTable(df=pl.DataFrame(converted_rows))


@pytest.fixture
def converted_discounts_factory() -> type[factory.Factory]:
    _ConvertedDiscountsFactory.reset_sequence(force=True)
    return _ConvertedDiscountsFactory


@pytest.fixture
def converted_wishlist_cohorts_factory() -> type[factory.Factory]:
    _ConvertedWishlistCohortsFactory.reset_sequence(force=True)
    return _ConvertedWishlistCohortsFactory


class SilverSKUSSalesFactory(factory.DictFactory):
    unique_sku_id = factory.LazyAttribute(
        lambda o: f"{o.base_sku_id}-steam:{o.studio_id}"
    )
    base_sku_id = Sequence(lambda n: str(122222 + n))
    human_name = "SUPERHOT actions"
    human_name_indicator = ObservationType.SALES.value
    portal_platform_region = "Steam:PC:Global"
    product_name = None
    product_type = None
    release_date = None
    sku_type = "SALES"
    store_id = "Unknown"
    studio_id = 1

    @classmethod
    def build_table(cls, size: int = 1, **kwargs) -> SilverSKUsTable:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows)

    @classmethod
    def build_df_batch(cls, size, **kwargs) -> pl.DataFrame:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows).df

    @staticmethod
    def _converted_rows_to_table(converted_rows: list[dict]) -> SilverSKUsTable:
        return SilverSKUsTable(df=pl.DataFrame(converted_rows))


@pytest.fixture
def silver_skus_sales_factory() -> type[SilverSKUSSalesFactory]:
    SilverSKUSSalesFactory.reset_sequence(force=True)
    return SilverSKUSSalesFactory


class SilverSKUSStoreFactory(SilverSKUSSalesFactory):
    unique_sku_id = factory.LazyAttribute(
        lambda o: f"{o.base_sku_id}-{o.sku_type.lower()}:{o.studio_id}"
    )
    sku_type = "STORE"
    human_name_indicator = ObservationType.WISHLIST_ACTIONS.value
    store_id = "505511"


@pytest.fixture
def silver_skus_store_factory():
    SilverSKUSStoreFactory.reset_sequence(force=True)
    return SilverSKUSStoreFactory


class ExternalSKUSSalesFactory(factory.DictFactory):
    unique_sku_id = factory.LazyAttribute(
        lambda o: f"{o.base_sku_id}-{o.portal}:{o.studio_id}"
    )
    base_sku_id = Sequence(lambda n: str(15109 + n))
    custom_group = None
    human_name = "SUPERHOT actions"
    human_name_indicator = ObservationType.SALES.value
    package_name = None
    portal = "steam"
    product_name = None
    product_type = None
    ratio = 1
    sku_type = "SALES"
    store_id = "Unknown"
    studio_id = 1
    is_discountable = False

    @classmethod
    def build_table(cls, size: int = 1, **kwargs) -> ExternalSKUsTable:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows)

    @classmethod
    def build_df_batch(cls, size, **kwargs) -> pl.DataFrame:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows).df

    @staticmethod
    def _converted_rows_to_table(converted_rows: list[dict]) -> ExternalSKUsTable:
        return ExternalSKUsTable(df=pl.DataFrame(converted_rows))


@pytest.fixture
def external_skus_sales_factory() -> type[ExternalSKUSSalesFactory]:
    ExternalSKUSSalesFactory.reset_sequence(force=True)
    return ExternalSKUSSalesFactory


class ExternalSKUSStoreFactory(ExternalSKUSSalesFactory):
    unique_sku_id = factory.LazyAttribute(
        lambda o: f"{o.base_sku_id}-{o.sku_type.lower()}:{o.studio_id}"
    )
    sku_type = "STORE"
    human_name_indicator = ObservationType.WISHLIST_ACTIONS.value
    store_id = "505511"


@pytest.fixture
def external_skus_store_factory():
    ExternalSKUSStoreFactory.reset_sequence(force=True)
    return ExternalSKUSStoreFactory


class ExternalReportsFactory(factory.DictFactory):  # TODO why external
    report_id = factory.Sequence(lambda n: n)
    studio_id = 1
    source = Source.STEAM_SALES
    portal = factory.LazyAttribute(lambda o: source_to_portal[o.source])
    observation_type = factory.LazyAttribute(
        lambda o: source_to_observation_type[o.source]
    )

    date_from = date(2024, 4, 1)
    date_to = date(2024, 4, 1)

    upload_date = factory.LazyAttribute(
        lambda o: datetime(
            o.date_to.year, o.date_to.month, o.date_to.day, tzinfo=timezone.utc
        )
    )
    blob_name = factory.LazyAttribute(
        lambda o: f"{o.portal.upper()}-{o.date_from}_{o.date_to}.zip"
    )
    original_name = factory.LazyAttribute(lambda o: o.blob_name)

    state = ReportState.PENDING
    no_data = False

    @classmethod
    def build_table(cls, size: int = 1, **kwargs) -> ExternalReportsTable:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows)

    @classmethod
    def build_df_batch(cls, size, **kwargs) -> pl.DataFrame:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows).df

    @staticmethod
    def _converted_rows_to_table(converted_rows: list[dict]) -> ExternalReportsTable:
        df = pl.DataFrame(data=converted_rows)
        if len(converted_rows):
            df = df.with_columns([
                pl.col("upload_date")
                .dt.replace_time_zone("UTC")
                .alias(
                    "upload_date"
                ),  # This looks like a bug that should be resolved but is not https://github.com/pola-rs/polars/issues/4174
            ])
        return ExternalReportsTable(df=df)


@pytest.fixture
def external_steam_reports_factory() -> type[ExternalReportsFactory]:
    ExternalReportsFactory.reset_sequence(force=True)
    return ExternalReportsFactory


class ListSubFactory(BaseDeclaration):
    def __init__(self, factory, size: int | Callable = 1, **kwargs):
        super().__init__(**kwargs)
        self.factory_wrapper = _FactoryWrapper(factory)
        self.size = size

    def get_factory(self):
        """Retrieve the wrapped factory.Factory subclass."""
        return self.factory_wrapper.get()

    def evaluate(self, instance, step, extra):
        """Evaluate the current definition and fill its attributes.

        Args:
            step: a factory.builder.BuildStep
            params (dict): additional, call-time added kwargs
                for the step.
        """
        subfactory = self.get_factory()
        subfactory.reset_sequence(force=True)
        extra = extra if isinstance(extra, dict) else extra.__dict__
        return [
            step.recurse(subfactory, extra)
            for i in range(
                self.size if isinstance(self.size, int) else self.size(instance)
            )
        ]


class ListBetterSubFactory(BaseDeclaration):
    def __init__(self, factory, size=1, **kwargs):
        super().__init__(**kwargs)
        self.factory_wrapper = _FactoryWrapper(factory)
        self.size = size

    def get_factory(self):
        """Retrieve the wrapped factory.Factory subclass."""
        return self.factory_wrapper.get()

    def evaluate(self, instance, step, extra):
        """Evaluate the current definition and fill its attributes.

        Args:
            step: a factory.builder.BuildStep
            params (dict): additional, call-time added kwargs
                for the step.
        """
        subfactory = self.get_factory()
        subfactory.reset_sequence(force=True)
        extra = extra if isinstance(extra, dict) else extra.__dict__

        ret = []

        for i in range(
            self.size if isinstance(self.size, int) else self.size(instance)
        ):
            custom_rows_data = extra.get("custom_rows_data", [])

            if i < len(custom_rows_data):
                _extra = {**extra, **custom_rows_data[i]}
            else:
                _extra = extra

            ret.append(step.recurse(subfactory, _extra))

        return ret


class SteamSalesRowFactory(factory.StubFactory):
    class Params:
        start_date = date(year=2000, month=1, day=1)

    date = factory.LazyAttributeSequence(
        lambda o, n: str(o.start_date + timedelta(days=n))
    )
    gross_units_sold = 1

    bundle_id = 1
    bundle_name = "SUPERHOT VR"
    product_id = 1
    product_name = "SUPERHOT VR"
    type = "Game"
    game = "SUPERHOT VR"
    platform = "Steam"
    country_code = "US"
    country = "United States"
    region = "North America"
    chargeback_returns = 0
    net_units_sold = factory.LazyAttribute(lambda o: o.gross_units_sold)
    base_price_local = 24.99
    sale_price = factory.LazyAttribute(lambda o: o.base_price_local)
    currency = "USD"
    gross_steam_sales = factory.LazyAttribute(
        lambda o: o.sale_price * o.gross_units_sold
    )
    chargeback_returns_usd = 0
    vat_tax_usd = 0
    net_steam_sales_usd = factory.LazyAttribute(lambda o: o.gross_steam_sales)
    tag = "VR"

    @factory.lazy_attribute
    def row(self):
        return (
            f"{self.date},{self.bundle_id},{self.bundle_name},{self.product_id},{self.product_name},"
            f"{self.type},{self.game},{self.platform},{self.country_code},{self.country},{self.region},"
            f"{self.gross_units_sold},{self.chargeback_returns},{self.net_units_sold},{self.base_price_local:.2f},"
            f"{self.sale_price:.2f},{self.currency},{self.gross_steam_sales:.2f},{self.chargeback_returns_usd},"
            f"{self.vat_tax_usd},{self.net_steam_sales_usd:.2f},{self.tag}"
        )


class SteamReturnRowFactory(factory.StubFactory):
    class Params:
        start_date = date(year=2000, month=1, day=1)

    date = factory.LazyAttributeSequence(
        lambda o, n: str(o.start_date + timedelta(days=n))
    )
    gross_units_sold = 0

    bundle_id = 1
    bundle_name = "SUPERHOT VR"
    product_id = 1
    product_name = "SUPERHOT VR"
    type = "Game"
    game = "SUPERHOT VR"
    platform = "Steam"
    country_code = "US"
    country = "United States"
    region = "North America"
    chargeback_returns = 1
    net_units_sold = factory.LazyAttribute(lambda o: -o.chargeback_returns)
    base_price_local = 24.99
    sale_price = factory.LazyAttribute(lambda o: o.base_price_local)
    currency = "USD"
    gross_steam_sales = factory.LazyAttribute(
        lambda o: o.sale_price * o.gross_units_sold
    )
    chargeback_returns_usd = factory.LazyAttribute(
        lambda o: (o.sale_price + o.vat_tax_usd) * o.chargeback_returns
    )
    vat_tax_usd = factory.LazyAttribute(
        lambda o: o.sale_price * o.chargeback_returns * 0.1
    )
    net_steam_sales_usd = factory.LazyAttribute(
        lambda o: -o.sale_price * o.chargeback_returns
    )
    tag = "VR"

    @factory.lazy_attribute
    def row(self):
        return (
            f"{self.date},{self.bundle_id},{self.bundle_name},{self.product_id},{self.product_name},"
            f"{self.type},{self.game},{self.platform},{self.country_code},{self.country},{self.region},"
            f"{self.gross_units_sold},{self.chargeback_returns},{self.net_units_sold},{self.base_price_local:.2f},"
            f"{self.sale_price:.2f},{self.currency},{self.gross_steam_sales:.2f},{self.chargeback_returns_usd},"
            f"{self.vat_tax_usd},{self.net_steam_sales_usd:.2f},{self.tag}"
        )


class SteamIAPRowFactory(factory.StubFactory):
    class Params:
        start_date = date(year=2000, month=1, day=1)

    date = factory.LazyAttributeSequence(
        lambda o, n: str(o.start_date + timedelta(days=n))
    )

    product_id = 1
    game = "SUPERHOT VR"
    item_id = 2
    description = "funny hat"
    category = ""
    country_code = "US"
    country = "United States"
    region = "North America"
    gross_units_sold = 1
    chargeback_returns = 0
    net_units_sold = factory.LazyAttribute(lambda o: o.gross_units_sold)
    average_price = 4.99
    currency = "USD"
    gross_steam_sales = factory.LazyAttribute(
        lambda o: o.average_price * o.gross_units_sold
    )
    chargeback_returns_usd = 0
    vat_tax_usd = 0
    net_steam_sales_usd = factory.LazyAttribute(lambda o: o.gross_steam_sales)

    @factory.lazy_attribute
    def row(self):
        return (
            f"{self.date},{self.product_id},{self.game},{self.item_id},{self.description},"
            f"{self.category},{self.country_code},{self.country},{self.region},"
            f"{self.gross_units_sold},{self.chargeback_returns},{self.net_units_sold},{self.average_price:.2f},"
            f"{self.currency},{self.gross_steam_sales:.2f},{self.chargeback_returns_usd},"
            f"{self.vat_tax_usd},{self.net_steam_sales_usd:.2f}"
        )


class SteamIAPReturnsRowFactory(factory.StubFactory):
    class Params:
        start_date = date(year=2000, month=1, day=1)

    date = factory.LazyAttributeSequence(
        lambda o, n: str(o.start_date + timedelta(days=n))
    )

    product_id = 1
    game = "SUPERHOT VR"
    item_id = 2
    description = "funny hat"
    category = ""
    country_code = "US"
    country = "United States"
    region = "North America"
    gross_units_sold = 0
    chargeback_returns = -1
    net_units_sold = factory.LazyAttribute(lambda o: o.chargeback_returns)
    average_price = 0.0
    currency = "USD"
    gross_steam_sales = factory.LazyAttribute(
        lambda o: o.average_price * o.gross_units_sold
    )
    chargeback_returns_usd = -2.99
    vat_tax_usd = factory.LazyAttribute(lambda o: -o.chargeback_returns_usd * 0.1)
    net_steam_sales_usd = factory.LazyAttribute(
        lambda o: o.chargeback_returns_usd + o.vat_tax_usd
    )

    @factory.lazy_attribute
    def row(self):
        return (
            f"{self.date},{self.product_id},{self.game},{self.item_id},{self.description},"
            f"{self.category},{self.country_code},{self.country},{self.region},"
            f"{self.gross_units_sold},{self.chargeback_returns},{self.net_units_sold},{self.average_price:.2f},"
            f"{self.currency},{self.gross_steam_sales:.2f},{self.chargeback_returns_usd},"
            f"{self.vat_tax_usd},{self.net_steam_sales_usd:.2f}"
        )


@dataclass
class LegacySteamRawSales:
    start_date: date
    end_date: date
    rows: list

    @property
    def additional_data(self):
        return {
            f"additionalData_steam_sales_{self.start_date}_{self.end_date}.json": {
                "scraper": "steam_sales",
                "startDate": f"{self.start_date}T00:00:00.000Z",
                "endDate": f"{self.end_date}T00:00:00.000Z",
                "fileMetaData": {
                    f"steam_sales-{self.start_date}_{self.end_date}-1.csv": {
                        "checksum": {
                            "unitsSold": sum(row.gross_units_sold for row in self.rows)
                        }
                    }
                },
            }
        }

    @property
    def steam_sales_csv(self):
        header = (
            "sep=,\n"
            f"Steam Sales data for SUPERHOT Sp. z o.o.: {self.start_date} - {self.end_date}\n\n"
            "Date, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,"
            "Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,"
            "Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),"
            "VAT/Tax (USD),Net Steam Sales (USD),Tag\n"
        )
        rows_content = "\n".join(self.raw_rows)
        return {
            f"steam_sales-{self.start_date}_{self.end_date}-1.csv": header
            + rows_content
        }

    @property
    def raw_rows(self) -> list[str]:
        return [row.row for row in self.rows]

    @property
    def zip_content(self):
        return {**self.additional_data, **self.steam_sales_csv}

    @property
    def raw_zip_file(self):
        with _mock_zip_file(self.zip_content) as zip_file:
            return zip_file.fp.getvalue()

    def __add__(self, other):
        return LegacySteamRawSalesFactory(
            start_date=self.start_date,
            end_date=other.end_date,
            rows=self.rows + other.rows,
        )


@dataclass
class SteamRawSales:
    start_date: date
    end_date: date
    sales_rows: list
    returns_rows: list
    iap_rows: list
    iap_returns_rows: list
    with_iap: bool = False
    with_iap_returns: bool = False
    with_sales: bool = True
    with_returns: bool = False

    @property
    def additional_data(self):
        return {
            f"additionalData_steam_sales_{self.start_date}_{self.end_date}.json": {
                "scraper": "steam_sales",
                "startDate": f"{self.start_date}T00:00:00.000Z",
                "endDate": f"{self.end_date}T00:00:00.000Z",
                "fileMetaData": {
                    f"steam_sales-{self.start_date}_{self.end_date}-1.csv": {
                        "checksum": {
                            "unitsSold": sum(
                                row.gross_units_sold
                                for row in (self.sales_rows + self.returns_rows)
                            )
                        },
                        "contentType": "sales",
                    },
                    f"steam_sales-{self.start_date}_{self.end_date}-2.csv": {
                        "checksum": {
                            "unitsSold": sum(
                                row.gross_units_sold
                                for row in (self.iap_rows + self.iap_returns_rows)
                            )
                        },
                        "contentType": "in-app-sales",
                    },
                },
            }
        }

    @property
    def steam_sales_csv(self):
        header = (
            "sep=,\n"
            f"Steam Sales data for SUPERHOT Sp. z o.o.: {self.start_date} - {self.end_date}\n\n"
            "Date, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,"
            "Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,"
            "Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),"
            "VAT/Tax (USD),Net Steam Sales (USD),Tag\n"
        )
        rows_content = "\n".join(self.raw_sales_rows + self.raw_returns_rows)
        return {
            f"steam_sales-{self.start_date}_{self.end_date}-1.csv": header
            + rows_content
        }

    @property
    def steam_iap_csv(self):
        header = (
            "sep=,\n"
            f"Steam In-Game Sales data for {self.start_date} - {self.end_date}\n\n"
            "Date,Product(ID#),Game,Item(ID#),Description,Category,Country Code,Country,Region,"
            "Gross Units Sold,Chargeback/Returns,Net Units Sold, Average Price,Currency,"
            "Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD)\n"
        )
        rows_content = "\n".join(self.raw_iap_rows + self.raw_iap_returns_rows)
        return {
            f"steam_sales-{self.start_date}_{self.end_date}-2.csv": header
            + rows_content
        }

    @property
    def raw_sales_rows(self) -> list[str]:
        return [row.row for row in self.sales_rows]

    @property
    def raw_returns_rows(self) -> list[str]:
        return [row.row for row in self.returns_rows]

    @property
    def raw_iap_rows(self) -> list[str]:
        return [row.row for row in self.iap_rows]

    @property
    def raw_iap_returns_rows(self) -> list[str]:
        return [row.row for row in self.iap_returns_rows]

    @property
    def zip_content(self):
        return {**self.additional_data, **self.steam_sales_csv, **self.steam_iap_csv}

    @property
    def raw_zip_file(self):
        with _mock_zip_file(self.zip_content) as zip_file:
            return zip_file.fp.getvalue()

    def __add__(self, other):
        return SteamRawSalesFactory(
            start_date=self.start_date,
            end_date=other.end_date,
            sales_rows=self.sales_rows + other.sales_rows,
            returns_rows=self.returns_rows + other.returns_rows,
            iap_rows=self.iap_rows + other.iap_rows,
            iap_returns_rows=self.iap_returns_rows + other.iap_returns_rows,
        )


class LegacySteamRawSalesFactory(factory.Factory):
    class Meta:
        model = LegacySteamRawSales

    start_date = date(2024, 4, 1)
    end_date = date(2024, 4, 1)

    rows = ListSubFactory(
        SteamSalesRowFactory,
        size=lambda o: (o.end_date - o.start_date).days + 1,
        start_date=factory.LazyAttribute(lambda row: row.factory_parent.start_date),
    )


class SteamRawSalesFactory(factory.Factory):
    class Meta:
        model = SteamRawSales

    start_date = date(2024, 4, 1)
    end_date = date(2024, 4, 1)
    with_sales = True
    with_returns = False
    with_iap = False
    with_iap_returns = False
    sales_rows = factory.Maybe(
        "with_sales",
        ListSubFactory(
            SteamSalesRowFactory,
            size=lambda o: (o.end_date - o.start_date).days + 1,
            start_date=factory.LazyAttribute(lambda row: row.factory_parent.start_date),
        ),
        [],
    )
    returns_rows = factory.Maybe(
        "with_returns",
        ListSubFactory(
            SteamReturnRowFactory,
            size=lambda o: (o.end_date - o.start_date).days + 1,
            start_date=factory.LazyAttribute(lambda row: row.factory_parent.start_date),
        ),
        [],
    )
    iap_rows = factory.Maybe(
        "with_iap",
        ListSubFactory(
            SteamIAPRowFactory,
            size=lambda o: (o.end_date - o.start_date).days + 1,
            start_date=factory.LazyAttribute(lambda row: row.factory_parent.start_date),
        ),
        [],
    )
    iap_returns_rows = factory.Maybe(
        "with_iap_returns",
        ListSubFactory(
            SteamIAPReturnsRowFactory,
            size=lambda o: (o.end_date - o.start_date).days + 1,
            start_date=factory.LazyAttribute(lambda row: row.factory_parent.start_date),
        ),
        [],
    )


@pytest.fixture
def legacy_steam_raw_sales_factory():
    return LegacySteamRawSalesFactory


@pytest.fixture
def steam_raw_sales_factory():
    return SteamRawSalesFactory


@pytest.fixture
def legacy_report_steam_sales_metadata_with_raw_file_factory(
    report_metadata_factory, legacy_steam_raw_sales_factory
):
    class ReportMetadataWithRawFileFactory(factory.Factory):
        class Meta:
            model = ReportMetadataWithRawFile

        input_raw_file = factory.SubFactory(factory=legacy_steam_raw_sales_factory)
        metadata = factory.SubFactory(report_metadata_factory)

        @factory.lazy_attribute
        def raw_file(self):
            return self.input_raw_file.raw_zip_file

    return ReportMetadataWithRawFileFactory


@pytest.fixture
def report_steam_sales_metadata_with_raw_file_factory(
    report_metadata_factory, steam_raw_sales_factory
):
    class ReportMetadataWithRawFileFactory(factory.Factory):
        class Meta:
            model = ReportMetadataWithRawFile

        input_raw_file = factory.SubFactory(steam_raw_sales_factory)
        metadata = factory.SubFactory(report_metadata_factory)

        @factory.lazy_attribute
        def raw_file(self):
            return self.input_raw_file.raw_zip_file

    return ReportMetadataWithRawFileFactory


@pytest.fixture
def country_codes() -> ExternalCountryCodesTable:
    return CountryCodesExternalProcessor().process(studio_id=StudioId(1))


@pytest.fixture
def steam_events() -> ExternalSteamEventsTable:
    return SteamEventsExternalProcessor().process(studio_id=StudioId(1))


@pytest.fixture
def external_studios(user_service_client: UserServiceClient) -> ExternalStudiosTable:
    return StudiosExternalProcessor(user_service_client=user_service_client).process(
        studio_id=StudioId(1)
    )


@pytest.fixture
def external_shared() -> ExternalSharedTable:
    return ExternalSharedTable(
        df=pl.DataFrame({
            "id": [1, 2, 3, 5],
            "shared_with_id": [2, 2, 2, 4],
            "shared_by_id": [1, 1, 1, 1],
            "date_from": [datetime(2021, 1, 1)] * 4,
            "date_to": [datetime(2021, 1, 1)] * 4,
            "portal_id": [None] * 4,
            "game_id": [None] * 4,
            "product_name": [
                None,
                "SUPERHOT",
                "product without assignment",
                "SUPERHOT",
            ],
        })
    )


@pytest.fixture
def get_tmp_path_files(tmp_path: Path):
    def _get_tmp_path_files() -> list[str]:
        return sorted([
            str(x.relative_to(tmp_path)) for x in (tmp_path).glob("**/*") if x.is_file()
        ])

    return _get_tmp_path_files


@pytest.fixture
def _mock_customer_rules_json():
    mock_json = pd.DataFrame([
        {
            "studio_id": 1,
            "type": "sku_filters",
            "value": {
                "filters": [
                    {"sku": "filter_this_id", "date_to": "2099-01-01"},
                    {"sku": "filter_this_id_too", "date_to": "2099-01-01"},
                    {"sku": "999999999999", "date_to": "2099-01-01"},
                ]
            },
        }
    ]).to_json(orient="records")

    mock_file = mock_open(read_data=mock_json)

    with (
        patch(
            "core_silver.observation_converter.customer_rules.filters.open",
            mock_file,
        ),
        patch("pandas.read_json", return_value=pd.read_json(mock_json)),
    ):
        yield


@pytest.fixture
def external_currency_exchange_rates_table():
    return currencies.external_currency_exchange_rates_table


# TODO move it to separate file


class ExternalNintendoReportsFactory(ExternalReportsFactory):
    source = Source.NINTENDO_SALES
    portal = Portal.NINTENDO


@pytest.fixture
def external_nintendo_reports_factory() -> type[ExternalNintendoReportsFactory]:
    ExternalNintendoReportsFactory.reset_sequence(force=True)
    return ExternalNintendoReportsFactory


class NintendoSalesRowFactory(factory.StubFactory):
    trait_pl = True  # triggers the PL trait

    class Params:
        units_sold = 1
        extended_schema_by_nsuid: bool = False

        # list of Country based traits: trait_{country_code_alpha2}
        trait_pl = factory.Trait(
            region="Europe",
            country="PL",
            country_name="Poland",
            currency="PLN",
        )
        trait_us = factory.Trait(
            region="Americas",
            country="US",
            country_name="USA",
            currency="USD",
        )
        trait_gb = factory.Trait(
            region="Europe",
            country="GB",
            country_name="United Kingdom",
            currency="GBP",
        )
        trait_jp = factory.Trait(
            region="Japan",
            country="JP",
            country_name="Japan",
            currency="JPY",
        )

    title_code = "HACPAURNA"
    title_name = "SUPERHOT"
    item_code = factory.LazyAttribute(lambda o: o.title_code)
    item_name = factory.LazyAttribute(lambda o: o.title_name)
    platform = "Switch"
    content_type = "Title"
    publisher = "SUPERHOT"
    start_time = "05/31/2021"
    points_cost = 39.99
    first_week = 0
    total_sales = factory.LazyAttribute(
        lambda o: ((o.factory_parent.end_date - o.factory_parent.start_date).days + 1)
        * o.units_sold
    )
    eshop_pin_posa = "eShop"
    card_type = None
    ns_uid = None
    period_total = 0
    daily_sales = factory.LazyAttribute(
        lambda o: [o.units_sold]
        * ((o.factory_parent.end_date - o.factory_parent.start_date).days + 1)
    )

    @factory.lazy_attribute
    def row(self):
        daily_sales_columns = ",".join(f'"{sale}"' for sale in self.daily_sales)

        fields = [
            self.title_code,
            self.title_name,
            self.item_code,
            self.item_name,
            self.region,
            self.country,
            self.country_name,
            self.platform,
            self.content_type,
            self.publisher,
            self.start_time,
            self.points_cost,
            self.currency,
            self.first_week,
            self.total_sales,
            self.eshop_pin_posa,
            self.card_type,
            *([self.ns_uid] if self.extended_schema_by_nsuid else []),
            self.period_total,
        ]

        # on following input:
        #       a="a"; b=None; c="c"        # noqa: ERA001
        # should return:
        #       "a",,"c"
        # to avoid:
        #       "a","","c"                  # noqa: ERA001

        fields = (f'"{x}"' if x is not None else "" for x in fields)
        new = ",".join(fields) + f",{daily_sales_columns}"

        return new


@dataclass
class NintendoRawSales:
    start_date: date
    end_date: date
    extended_schema_by_nsuid: bool
    rows: Any

    @property
    def additional_data(self):
        return {
            "manifest.json": {
                "dateFrom": f"{self.start_date}T00:00:00.000Z",
                "dateTo": f"{self.end_date}T00:00:00.000Z",
            }
        }

    @property
    def nintendo_sales_csv(self):
        rows_content = "\n".join(self.raw_rows)
        ignored_footer = """\n"","","","","","","","","","","","","","","0","","All Total","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0","0\""""
        return {
            f"nintendo_sales-{self.start_date}_{self.end_date}-1.csv": self.header
            + rows_content
            + ignored_footer
        }

    @property
    def header(self):
        header = '"TitleCode","TitleName","ItemCode","ItemName","Region","Country","CountryName","Platform","ContentType","Publisher","StartTime","Points/Cost","Currency","First Week","Total Sales","eShop/PIN/POSA","Card Type","Period Total"'

        if self.extended_schema_by_nsuid:
            header = header.replace('"Card Type"', '"Card Type","NsUid"')

        date_columns = ",".join(
            f'"{(self.start_date + timedelta(days=i)).strftime("%m/%d/%y")}"'
            for i in range((self.end_date - self.start_date).days + 1)
        )
        header += f",{date_columns}\n"
        return header

    @property
    def raw_rows(self) -> list[str]:
        return [row.row for row in self.rows]

    @property
    def zip_content(self):
        return {**self.additional_data, **self.nintendo_sales_csv}

    @property
    def raw_zip_file(self):
        with _mock_zip_file(self.zip_content) as zip_file:
            return zip_file.fp.getvalue()

    def __add__(self, other):
        assert (
            self.extended_schema_by_nsuid == other.extended_schema_by_nsuid
        ), "Cannot add NintendoRawSales with different extended_schema_by_nsuid values"
        return NintendoRawSales(
            start_date=self.start_date,
            end_date=other.end_date,
            rows=self.rows + other.rows,
            extended_schema_by_nsuid=self.extended_schema_by_nsuid,
        )


class NintendoRawSalesFactory(factory.Factory):
    class Meta:
        model = NintendoRawSales

    start_date = date(2024, 4, 1)
    end_date = date(2024, 4, 1)
    extended_schema_by_nsuid = False

    rows = ListSubFactory(
        NintendoSalesRowFactory,
        size=1,
        start_date=factory.LazyAttribute(lambda row: row.factory_parent.start_date),
        end_date=factory.LazyAttribute(lambda row: row.factory_parent.end_date),
        extended_schema_by_nsuid=factory.LazyAttribute(
            lambda row: row.factory_parent.extended_schema_by_nsuid
        ),
    )


@pytest.fixture
def nintendo_raw_sales_factory():
    NintendoRawSalesFactory.reset_sequence(force=True)
    return NintendoRawSalesFactory


@pytest.fixture
def nintendo_sales_report_metadata_with_raw_file_factory(
    nintendo_raw_sales_factory, external_nintendo_reports_factory
):
    class NintendoReportMetadataWithRawFileFactory(factory.Factory):
        class Params:
            extended_schema_by_nsuid: bool = False

        class Meta:
            model = ReportMetadataWithRawFile

        input_raw_file = factory.SubFactory(
            nintendo_raw_sales_factory,
            extended_schema_by_nsuid=factory.LazyAttribute(
                lambda row: row.factory_parent.extended_schema_by_nsuid
            ),
        )
        metadata = factory.SubFactory(external_nintendo_reports_factory)

        @factory.lazy_attribute
        def raw_file(self):
            return self.input_raw_file.raw_zip_file

    return NintendoReportMetadataWithRawFileFactory


@pytest.fixture
def generate_raw_nintendo_sales_report(
    nintendo_sales_report_metadata_with_raw_file_factory, nintendo_raw_sales_factory
):
    def generate_raw_report(rows: int | None = None, custom_rows_data=None):
        return nintendo_sales_report_metadata_with_raw_file_factory(
            input_raw_file=nintendo_raw_sales_factory(
                rows=ListBetterSubFactory(
                    NintendoSalesRowFactory,
                    size=len(custom_rows_data)
                    if custom_rows_data is not None and rows is None
                    else rows or 1,
                    custom_rows_data=custom_rows_data,
                )
            )
        )

    return generate_raw_report


@pytest.fixture
def external_feature_flags_table_no_flags():
    return ExternalFeatureFlagsTable(
        df=pl.DataFrame({
            "name": [],
            "namespace": [],
        })
    )


@pytest.fixture
def external_feature_flags_table_deduplicate_nintendo_discounts():
    return ExternalFeatureFlagsTable(
        df=pl.DataFrame([
            FeatureFlag(
                name="deduplicate-nintendo-discounts", namespace="data_pipeline"
            )
        ])
    )
