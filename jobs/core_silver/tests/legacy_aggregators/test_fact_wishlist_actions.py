import pandas as pd
import polars as pl
import pytest
from pandera.errors import SchemaError

from core_silver.legacy_aggregators import FactWishlistActionsAggregator
from data_sdk.domain import TableName
from data_sdk.domain.tables import ObservationWishlistActionsTable
from tests.comparisons import compare_dataframes


@pytest.fixture
def wishlist_actions_input(
    converted_wishlist_actions_factory, silver_skus_sales_factory
):
    return {
        TableName.OBSERVATION_WISHLIST_ACTIONS: converted_wishlist_actions_factory.build_table(
            size=1
        ),
        TableName.SILVER_SKUS: silver_skus_sales_factory.build_table(size=1),
    }


@pytest.fixture
def wishlist_actions_input_empty_observations(
    wishlist_actions_input, converted_wishlist_actions_factory
):
    wishlist_actions_input[TableName.OBSERVATION_WISHLIST_ACTIONS] = (
        converted_wishlist_actions_factory.build_table(size=0)
    )
    return wishlist_actions_input


@pytest.fixture
def wishlist_actions_input_empty_skus(
    wishlist_actions_input, silver_skus_sales_factory
):
    wishlist_actions_input[TableName.SILVER_SKUS] = (
        silver_skus_sales_factory.build_table(size=0)
    )
    return wishlist_actions_input


EXPECTED_COLUMNS = [
    "portal_platform_region",
    "portal_platform_region_id",
    "product_id",
    "sku_studio",
    "date",
    "studio_id",
    "date_sku_studio",
    "date_product_studio",
    "adds",
    "deletes",
    "purchases_and_activations",
    "gifts",
    "country_code",
]


def test_fact_wishlist_actions_creates_all_required_columns(wishlist_actions_input):
    fact_wishlist_actions = (
        FactWishlistActionsAggregator(**wishlist_actions_input).aggregate().df
    )

    assert list(fact_wishlist_actions.columns) == EXPECTED_COLUMNS
    assert len(fact_wishlist_actions) == 1


def test_fact_wishlist_actions_handles_empty_observations(
    wishlist_actions_input_empty_observations,
):
    fact_wishlist_actions = (
        FactWishlistActionsAggregator(**wishlist_actions_input_empty_observations)
        .aggregate()
        .df
    )

    assert len(fact_wishlist_actions) == 0
    assert list(fact_wishlist_actions.columns) == EXPECTED_COLUMNS


def test_fact_wishlist_actions_handles_empty_skus(wishlist_actions_input_empty_skus):
    fact_wishlist_actions = (
        FactWishlistActionsAggregator(**wishlist_actions_input_empty_skus)
        .aggregate()
        .df
    )

    assert len(fact_wishlist_actions) == 1
    assert list(fact_wishlist_actions.columns) == EXPECTED_COLUMNS
    assert fact_wishlist_actions["product_id"][0] == "Unassigned:171010:1"


def test_fact_wishlist_actions_returns_expected_values(
    converted_wishlist_actions_factory,
    silver_skus_sales_factory,
):
    wishlist_fields = converted_wishlist_actions_factory()
    sku_fields = silver_skus_sales_factory(
        unique_sku_id=wishlist_fields["unique_sku_id"],
        product_name="Bobry z Nadodrza",
    )
    input_tables = {
        TableName.OBSERVATION_WISHLIST_ACTIONS: converted_wishlist_actions_factory.build_table(
            size=1, **wishlist_fields
        ),
        TableName.SILVER_SKUS: silver_skus_sales_factory.build_table(
            size=1, **sku_fields
        ),
    }

    fact_wishlist_actions = FactWishlistActionsAggregator(**input_tables).aggregate().df

    assert isinstance(fact_wishlist_actions, pl.DataFrame)
    assert len(fact_wishlist_actions) == 1

    FactWishlistActionsAggregator.schema(fact_wishlist_actions)
    actual_df = fact_wishlist_actions.to_pandas()
    actual_row = actual_df.iloc[0]

    copied_columns = [
        "adds",
        "date",
        "deletes",
        "gifts",
        "portal_platform_region",
        "purchases_and_activations",
        "studio_id",
        "country_code",
    ]
    compare_dataframes(
        actual_df[copied_columns],
        pd.DataFrame(
            data=[
                {
                    "studio_id": 1,
                    "date": "2000-01-01",
                    "adds": 369,
                    "deletes": 0,
                    "purchases_and_activations": 0,
                    "gifts": 0,
                    "portal_platform_region": "Steam:PC:Global",
                    "country_code": "YYY",
                }
            ]
        ),
    )
    assert actual_row["sku_studio"] == wishlist_fields["unique_sku_id"]
    assert actual_row["portal_platform_region_id"] == 171010
    assert (
        actual_row["product_id"]
        == f"{sku_fields['product_name']}:171010:{wishlist_fields['studio_id']}"
    )
    assert (
        actual_row["date_product_studio"]
        == f"{wishlist_fields['date']}:{sku_fields['product_name']}:{wishlist_fields['studio_id']}"
    )
    assert (
        actual_row["date_sku_studio"]
        == f"{wishlist_fields['date']}:{wishlist_fields['unique_sku_id']}"
    )


def test_fact_wishlist_actions_returns_expected_values_from_steam_and_ps(
    converted_wishlist_actions_factory,
    silver_skus_sales_factory,
):
    wishlist_fields = converted_wishlist_actions_factory()
    sku_fields = silver_skus_sales_factory(
        unique_sku_id=wishlist_fields["unique_sku_id"],
        product_name="Bobry z Nadodrza",
    )
    steam_wishlist = converted_wishlist_actions_factory.build_df_batch(
        size=1, **wishlist_fields
    )
    ps_wishlist = converted_wishlist_actions_factory.build_df_batch(
        size=1,
        report_id=5,
        platform="PS4",
        portal="PlayStation",
        region="PS Europe",
        human_name="Bobry z Nadodrza PS",
        store_id="CUSA08165_00",
        sku_id="EP2477-CUSA08165_00-BOBRYRGAME",
        store="PlayStation Europe",
        abbreviated_name="PS EU",
        country_code="POL",
    )
    ps_wishlist_not_applicable = converted_wishlist_actions_factory.build_df_batch(
        size=1,
        report_id=5,
        platform="Unknown",
        portal="PlayStation",
        region="PS Europe",
        human_name="Bobry z Nadodrza PS",
        store_id="CUSA08165_00",
        sku_id="EP2477-CUSA08165_00-BOBRYRGAME",
        store="PlayStation Europe",
        abbreviated_name="PS EU",
        country_code="POL",
    )
    wishlist_actions = pl.concat([
        steam_wishlist,
        ps_wishlist,
        ps_wishlist_not_applicable,
    ])

    input_tables = {
        TableName.OBSERVATION_WISHLIST_ACTIONS: ObservationWishlistActionsTable(
            df=wishlist_actions
        ),
        TableName.SILVER_SKUS: silver_skus_sales_factory.build_table(
            size=1, **sku_fields
        ),
    }

    fact_wishlist_actions = FactWishlistActionsAggregator(**input_tables).aggregate().df

    FactWishlistActionsAggregator.schema(fact_wishlist_actions)
    actual_df = fact_wishlist_actions.to_pandas()

    compare_dataframes(
        actual_df,
        pd.DataFrame(
            data=[
                {
                    "portal_platform_region": "Steam:PC:Global",
                    "portal_platform_region_id": 171010,
                    "product_id": "Bobry z Nadodrza:171010:1",
                    "sku_studio": "583710-store:1",
                    "date": "2000-01-01",
                    "studio_id": 1,
                    "date_sku_studio": "2000-01-01:583710-store:1",
                    "date_product_studio": "2000-01-01:Bobry z Nadodrza:1",
                    "adds": 369,
                    "deletes": 0,
                    "purchases_and_activations": 0,
                    "gifts": 0,
                    "country_code": "YYY",
                },
                {
                    "portal_platform_region": "PlayStation:PS4:PS Europe",
                    "portal_platform_region_id": 162216,
                    "product_id": "Unassigned:162216:1",
                    "sku_studio": "EP2477-CUSA08165_00-BOBRYRGAME-store:1",
                    "date": "2000-01-04",
                    "studio_id": 1,
                    "date_sku_studio": "2000-01-04:EP2477-CUSA08165_00-BOBRYRGAME-store:1",
                    "date_product_studio": "2000-01-04:UNASSIGNED:1",
                    "adds": 369,
                    "deletes": 0,
                    "purchases_and_activations": 0,
                    "gifts": 0,
                    "country_code": "POL",
                },
                {
                    "portal_platform_region": "PlayStation:Unknown:PS Europe",
                    "portal_platform_region_id": 169916,
                    "product_id": "Unassigned:169916:1",
                    "sku_studio": "EP2477-CUSA08165_00-BOBRYRGAME-store:1",
                    "date": "2000-01-05",
                    "studio_id": 1,
                    "date_sku_studio": "2000-01-05:EP2477-CUSA08165_00-BOBRYRGAME-store:1",
                    "date_product_studio": "2000-01-05:UNASSIGNED:1",
                    "adds": 369,
                    "deletes": 0,
                    "purchases_and_activations": 0,
                    "gifts": 0,
                    "country_code": "POL",
                },
            ]
        ),
    )


def test_fact_wishlist_actions_validates_output(
    converted_wishlist_actions_factory,
    silver_skus_sales_factory,
):
    wishlist_actions_table = converted_wishlist_actions_factory.build_table(size=1)

    # We can just pass invalid values to factory because we are too strict with validation
    wishlist_actions_table.df = wishlist_actions_table.df.with_columns(
        portal=pl.lit("PS"),
        portal_platform_region=pl.lit("PS:PC:Global"),
    )
    input_data = {
        TableName.OBSERVATION_WISHLIST_ACTIONS: wishlist_actions_table,
        TableName.SILVER_SKUS: silver_skus_sales_factory.build_table(size=1),
    }
    with pytest.raises(SchemaError):
        FactWishlistActionsAggregator(**input_data).aggregate().df
