from datetime import date, datetime

import polars as pl
import pytest

from core_silver.job import top_level_process_aggregators
from core_silver.legacy_aggregators import (
    FactSalesAggregator,
    FactVisibilityAggregator,
    FactWishlistActionsAggregator,
    FactWishlistCohortsAggregator,
)
from data_sdk.custom_partition.partitioner import get_table_partition
from data_sdk.custom_partition.writer import CustomPartitionsWriter
from data_sdk.domain import Portal
from data_sdk.domain.domain_types import StudioId
from data_sdk.domain.tables import (
    TableDefinition,
    TablePartitionedByPortal,
    TablePartitionedByStudio,
)
from data_sdk.segmentator import SegmentDefinition
from data_sdk.utils.date_utils import datetime_to_string


@pytest.fixture
def studio_id() -> StudioId:
    return StudioId(1)


@pytest.fixture
def portal() -> Portal:
    return Portal.STEAM


@pytest.fixture
def creation_datetime() -> datetime:
    return datetime(year=2020, month=1, day=1, hour=1, minute=0)


@pytest.fixture
def default_segments(creation_datetime):
    chunks_folder_name = datetime_to_string(creation_datetime)
    return [
        SegmentDefinition(
            path=f"{chunks_folder_name}.parquet",
            mask_expression=(True),
        )
    ]


@pytest.fixture
def saved_input_files(
    local_config,
    studio_id,
    portal,
    default_segments,
    converted_sales_factory,
    converted_visibility_factory,
    converted_wishlist_actions_factory,
    converted_wishlist_cohorts_factory,
    silver_skus_store_factory,
    country_codes,
) -> list[TableDefinition]:
    writer = CustomPartitionsWriter.get_writer(local_config.output_cfg)

    tables: list[TableDefinition] = [
        country_codes,
        converted_sales_factory.build_table(size=1),
        silver_skus_store_factory.build_table(size=1),
        converted_visibility_factory.build_table(size=1),
        converted_wishlist_actions_factory.build_table(1),
        converted_wishlist_cohorts_factory.build_table(1),
    ]
    for table in tables:
        if isinstance(table, TablePartitionedByStudio):
            partition = get_table_partition(table, studio_id=studio_id)
        elif isinstance(table, TablePartitionedByPortal):
            partition = get_table_partition(table, studio_id=studio_id, portal=portal)
        else:
            raise TypeError("Unsupported table in aggregator!")  # noqa: TRY003

        writer.save_table(table, partition, default_segments)

    return tables


def test_processes_legacy_fact_sales(
    tmp_path, local_config, saved_input_files, studio_id, portal, creation_datetime
):
    top_level_process_aggregators(
        aggregators=[FactSalesAggregator],
        config=local_config,
        studio_id=studio_id,
        portal=portal,
        creation_datetime=creation_datetime,
    )
    expected_output_path = (
        tmp_path
        / "result/fact_sales/studio_id=1/portal=steam/version=v1/20200101T010000Z.parquet"
    )
    assert expected_output_path.is_file()

    df = pl.read_parquet(
        expected_output_path,
        hive_partitioning=False,
    )
    assert df.to_dicts() == [
        {
            "country_code": "USA",
            "currency_code": "USD",
            "studio_id": 1,
            "sku_studio": "505511-steam:1",
            "bundle_name": "Direct Package Sale",
            "portal_platform_region": "Steam:PC:Global",
            "portal_platform_region_id": 171010,
            "product_id": "Unassigned:171010:1",
            "hash_acquisition_properties": "49f305a36112f91e640c608818304db5",
            "date": date(2000, 1, 1),
            "date_sku_studio": "2000-01-01:505511-steam:1",
            "source_file_id": 0,
            "retailer_tag": "France",
            "base_price_local": 10.0,
            "calculated_base_price_usd": 24.99,
            "net_sales": 113.6,
            "gross_returned": 0.0,
            "gross_sales": 113.6,
            "units_returned": 0,
            "units_sold": 5,
            "free_units": 0,
            "price_local": 22.72,
            "price_usd": 24.99,
            "net_sales_approx": 79.52,
            "category": "Sale",
            "calculated_base_price_local_v2": 10.0,
            "calculated_base_price_usd_v2": 10.99,
        }
    ]


def test_processes_legacy_fact_visibility(
    tmp_path, local_config, saved_input_files, studio_id, portal, creation_datetime
):
    top_level_process_aggregators(
        aggregators=[FactVisibilityAggregator],
        config=local_config,
        studio_id=studio_id,
        portal=portal,
        creation_datetime=creation_datetime,
    )
    expected_output_path = (
        tmp_path
        / "result/fact_visibility/studio_id=1/portal=steam/version=v1/20200101T010000Z.parquet"
    )
    assert expected_output_path.is_file()

    df = pl.read_parquet(
        expected_output_path,
        hive_partitioning=False,
    )
    assert df.to_dicts() == [
        {
            "portal_platform_region": "Steam:PC:Global",
            "portal_platform_region_id": 171010,
            "product_id": "Unassigned:171010:1",
            "sku_studio": "583710-store:1",
            "date": date(2000, 1, 3),
            "studio_id": 1,
            "date_sku_studio": "2000-01-03:583710-store:1",
            "hash_traffic_source": "8006adebacb4c27c389fd872110fcc7c",
            "date_product_studio": "2000-01-03:UNASSIGNED:1",
            "visits": 0,
            "owner_visits": 0,
            "impressions": 369,
            "owner_impressions": 0,
            "navigation": "Direct Navigation",
        }
    ]


def test_processes_legacy_fact_wishlist_actions(
    tmp_path, local_config, saved_input_files, studio_id, portal, creation_datetime
):
    top_level_process_aggregators(
        aggregators=[FactWishlistActionsAggregator],
        config=local_config,
        studio_id=studio_id,
        portal=portal,
        creation_datetime=creation_datetime,
    )
    expected_output_path = (
        tmp_path
        / "result/fact_wishlist_actions/studio_id=1/portal=steam/version=v1/20200101T010000Z.parquet"
    )
    assert expected_output_path.is_file()

    df = pl.read_parquet(
        expected_output_path,
        hive_partitioning=False,
    )
    assert df.to_dicts() == [
        {
            "portal_platform_region": "Steam:PC:Global",
            "portal_platform_region_id": 171010,
            "product_id": "Unassigned:171010:1",
            "sku_studio": "583710-store:1",
            "date": date(2000, 1, 4),
            "studio_id": 1,
            "date_sku_studio": "2000-01-04:583710-store:1",
            "date_product_studio": "2000-01-04:UNASSIGNED:1",
            "adds": 369,
            "deletes": 0,
            "purchases_and_activations": 0,
            "gifts": 0,
            "country_code": "YYY",
        }
    ]


def test_processes_legacy_fact_wishlist_cohorts(
    tmp_path, local_config, saved_input_files, studio_id, portal, creation_datetime
):
    top_level_process_aggregators(
        aggregators=[FactWishlistCohortsAggregator],
        config=local_config,
        studio_id=studio_id,
        portal=portal,
        creation_datetime=creation_datetime,
    )
    expected_output_path = (
        tmp_path
        / "result/fact_wishlist_cohorts/studio_id=1/portal=steam/version=v1/20200101T010000Z.parquet"
    )
    assert expected_output_path.is_file()

    df = pl.read_parquet(
        expected_output_path,
        hive_partitioning=False,
    )
    assert df.to_dicts() == [
        {
            "portal_platform_region": "Steam:PC:Global",
            "portal_platform_region_id": 171010,
            "product_id": "Unassigned:171010:1",
            "sku_studio": "583710-store:1",
            "date": date(2000, 1, 5),
            "studio_id": 1,
            "date_sku_studio": "2000-01-05:583710-store:1",
            "date_product_studio": "2000-01-05:UNASSIGNED:1",
            "month_cohort": "369",
            "total_conversions": 0,
            "purchases_and_activations": 0,
            "gifts": 0,
        }
    ]
