import pandas as pd
import polars as pl
import pytest
from pandera.errors import SchemaError

from core_silver.legacy_aggregators import FactWishlistCohortsAggregator
from data_sdk.domain import TableName
from tests.comparisons import compare_dataframes


@pytest.fixture
def wishlist_cohorts_input(
    converted_wishlist_cohorts_factory, silver_skus_sales_factory
):
    return {
        TableName.OBSERVATION_WISHLIST_COHORTS: converted_wishlist_cohorts_factory.build_table(
            size=1
        ),
        TableName.SILVER_SKUS: silver_skus_sales_factory.build_table(size=1),
    }


@pytest.fixture
def wishlist_cohorts_input_empty_observations(
    wishlist_cohorts_input, converted_wishlist_cohorts_factory
):
    wishlist_cohorts_input[TableName.OBSERVATION_WISHLIST_COHORTS] = (
        converted_wishlist_cohorts_factory.build_table(size=0)
    )
    return wishlist_cohorts_input


@pytest.fixture
def wishlist_cohorts_input_empty_skus(
    wishlist_cohorts_input, silver_skus_sales_factory
):
    wishlist_cohorts_input[TableName.SILVER_SKUS] = (
        silver_skus_sales_factory.build_table(size=0)
    )
    return wishlist_cohorts_input


EXPECTED_COLUMNS = [
    "portal_platform_region",
    "portal_platform_region_id",
    "product_id",
    "sku_studio",
    "date",
    "studio_id",
    "date_sku_studio",
    "date_product_studio",
    "month_cohort",
    "total_conversions",
    "purchases_and_activations",
    "gifts",
]


def test_fact_wishlist_cohorts_creates_all_required_columns(wishlist_cohorts_input):
    fact_wishlist_cohorts = (
        FactWishlistCohortsAggregator(**wishlist_cohorts_input).aggregate().df
    )

    assert list(fact_wishlist_cohorts.columns) == EXPECTED_COLUMNS
    assert len(fact_wishlist_cohorts) == 1


def test_fact_wishlist_cohorts_handles_empty_observations(
    wishlist_cohorts_input_empty_observations,
):
    fact_wishlist_cohorts = (
        FactWishlistCohortsAggregator(**wishlist_cohorts_input_empty_observations)
        .aggregate()
        .df
    )

    assert len(fact_wishlist_cohorts) == 0
    assert list(fact_wishlist_cohorts.columns) == EXPECTED_COLUMNS


def test_fact_wishlist_cohorts_handles_empty_skus(wishlist_cohorts_input_empty_skus):
    fact_wishlist_cohorts = (
        FactWishlistCohortsAggregator(**wishlist_cohorts_input_empty_skus)
        .aggregate()
        .df
    )

    assert len(fact_wishlist_cohorts) == 1
    assert list(fact_wishlist_cohorts.columns) == EXPECTED_COLUMNS
    assert fact_wishlist_cohorts["product_id"][0] == "Unassigned:171010:1"


def test_fact_wishlist_cohorts_returns_expected_values(
    converted_wishlist_cohorts_factory,
    silver_skus_sales_factory,
):
    wishlist_fields = converted_wishlist_cohorts_factory()
    sku_fields = silver_skus_sales_factory(
        unique_sku_id=wishlist_fields["unique_sku_id"],
        product_name="Bobry z Nadodrza",
    )
    input_tables = {
        TableName.OBSERVATION_WISHLIST_COHORTS: converted_wishlist_cohorts_factory.build_table(
            size=1, **wishlist_fields
        ),
        TableName.SILVER_SKUS: silver_skus_sales_factory.build_table(
            size=1, **sku_fields
        ),
    }

    fact_wishlist_cohorts = FactWishlistCohortsAggregator(**input_tables).aggregate().df

    assert isinstance(fact_wishlist_cohorts, pl.DataFrame)
    assert len(fact_wishlist_cohorts) == 1

    FactWishlistCohortsAggregator.schema(fact_wishlist_cohorts)
    actual_df = fact_wishlist_cohorts.to_pandas()
    actual_row = actual_df.iloc[0]

    copied_columns = [
        "gifts",
        "portal_platform_region",
        "purchases_and_activations",
        "studio_id",
        "total_conversions",
        "month_cohort",
    ]
    compare_dataframes(
        actual_df[copied_columns],
        pd.DataFrame([
            {k: v for k, v in wishlist_fields.items() if k in copied_columns}
        ]),
    )
    assert actual_row["sku_studio"] == wishlist_fields["unique_sku_id"]
    assert actual_row["portal_platform_region_id"] == 171010
    assert (
        actual_row["product_id"]
        == f"{sku_fields['product_name']}:171010:{wishlist_fields['studio_id']}"
    )
    assert (
        actual_row["date_product_studio"]
        == f"{wishlist_fields['date']}:{sku_fields['product_name']}:{wishlist_fields['studio_id']}"
    )
    assert (
        actual_row["date_sku_studio"]
        == f"{wishlist_fields['date']}:{wishlist_fields['unique_sku_id']}"
    )


def test_fact_wishlist_cohorts_validates_output(
    converted_wishlist_cohorts_factory,
    silver_skus_sales_factory,
):
    wishlist_table = converted_wishlist_cohorts_factory.build_table(size=1)

    # We can just pass invalid values to factory because we are too strict with validation
    wishlist_table.df = wishlist_table.df.with_columns(
        portal=pl.lit("PS"),
        portal_platform_region=pl.lit("PS:PC:Global"),
    )

    input_data = {
        TableName.OBSERVATION_WISHLIST_COHORTS: wishlist_table,
        TableName.SILVER_SKUS: silver_skus_sales_factory.build_table(size=1),
    }
    with pytest.raises(SchemaError):
        FactWishlistCohortsAggregator(**input_data).aggregate()
