from datetime import date

import polars as pl
import pytest
from pandera.errors import SchemaError
from polars.testing import assert_frame_equal

from core_silver.legacy_aggregators import FactVisibilityAggregator
from data_sdk.domain import TableName


@pytest.fixture
def visibility_input_data(converted_visibility_factory, silver_skus_sales_factory):
    return {
        TableName.OBSERVATION_VISIBILITY: converted_visibility_factory.build_table(
            size=1
        ),
        TableName.SILVER_SKUS: silver_skus_sales_factory.build_table(size=1),
    }


@pytest.fixture
def visibility_input_data_empty_observations(
    visibility_input_data, converted_visibility_factory
):
    visibility_input_data[TableName.OBSERVATION_VISIBILITY] = (
        converted_visibility_factory.build_table(size=0)
    )
    return visibility_input_data


@pytest.fixture
def visibility_input_data_empty_skus(visibility_input_data, silver_skus_sales_factory):
    visibility_input_data[TableName.SILVER_SKUS] = (
        silver_skus_sales_factory.build_table(size=0)
    )
    return visibility_input_data


EXPECTED_COLUMNS = [
    "portal_platform_region",
    "portal_platform_region_id",
    "product_id",
    "sku_studio",
    "date",
    "studio_id",
    "date_sku_studio",
    "hash_traffic_source",
    "date_product_studio",
    "visits",
    "owner_visits",
    "impressions",
    "owner_impressions",
    "navigation",
]


def test_fact_visibility_creates_all_required_columns(visibility_input_data):
    fact_visibility_data = (
        FactVisibilityAggregator(**visibility_input_data).aggregate().df
    )

    assert list(fact_visibility_data.columns) == EXPECTED_COLUMNS
    assert len(fact_visibility_data) == 1


def test_fact_visibility_handles_empty_observations(
    visibility_input_data_empty_observations,
):
    fact_visibility_dataa = (
        FactVisibilityAggregator(**visibility_input_data_empty_observations)
        .aggregate()
        .df
    )

    assert len(fact_visibility_dataa) == 0
    assert list(fact_visibility_dataa.columns) == EXPECTED_COLUMNS


def test_fact_visibility_handles_empty_skus(visibility_input_data_empty_skus):
    fact_visibility_dataa = (
        FactVisibilityAggregator(**visibility_input_data_empty_skus).aggregate().df
    )

    assert len(fact_visibility_dataa) == 1
    assert list(fact_visibility_dataa.columns) == EXPECTED_COLUMNS
    assert fact_visibility_dataa["product_id"][0] == "Unassigned:171010:1"


def test_fact_visibility_returns_expected_values(
    converted_visibility_factory,
    silver_skus_sales_factory,
):
    visibility_fields = converted_visibility_factory()
    sku_fields = silver_skus_sales_factory(
        unique_sku_id=visibility_fields["unique_sku_id"],
        product_name="Bobry z Nadodrza",
    )
    input_tables = {
        TableName.OBSERVATION_VISIBILITY: converted_visibility_factory.build_table(
            size=1, **visibility_fields
        ),
        TableName.SILVER_SKUS: silver_skus_sales_factory.build_table(
            size=1, **sku_fields
        ),
    }

    fact_visibility_data = FactVisibilityAggregator(**input_tables).aggregate().df

    assert isinstance(fact_visibility_data, pl.DataFrame)
    assert pl.Categorical not in fact_visibility_data.dtypes
    assert pl.Enum not in fact_visibility_data.dtypes
    assert len(fact_visibility_data) == 1

    FactVisibilityAggregator.schema.validate(fact_visibility_data)
    actual_df = fact_visibility_data.to_pandas()
    actual_row = actual_df.iloc[0]

    copied_columns = [
        "date",
        "hash_traffic_source",
        "impressions",
        "navigation",
        "owner_impressions",
        "owner_visits",
        "portal_platform_region",
        "studio_id",
        "visits",
    ]
    assert_frame_equal(
        fact_visibility_data[copied_columns],
        pl.DataFrame([
            {
                "date": date(2000, 1, 1),
                "hash_traffic_source": "8006adebacb4c27c389fd872110fcc7c",
                "impressions": 369,
                "navigation": "Direct Navigation",
                "owner_impressions": 0,
                "owner_visits": 0,
                "portal_platform_region": "Steam:PC:Global",
                "studio_id": 1,
                "visits": 0,
            }
        ]),
    )
    assert actual_row["sku_studio"] == visibility_fields["unique_sku_id"]
    assert actual_row["portal_platform_region_id"] == 171010
    assert (
        actual_row["product_id"]
        == f"{sku_fields['product_name']}:171010:{visibility_fields['studio_id']}"
    )
    assert (
        actual_row["date_product_studio"]
        == f"{visibility_fields['date']}:{sku_fields['product_name']}:{visibility_fields['studio_id']}"
    )
    assert (
        actual_row["date_sku_studio"]
        == f"{visibility_fields['date']}:{visibility_fields['unique_sku_id']}"
    )


def test_fact_visibility_validates_output(
    converted_visibility_factory,
    silver_skus_sales_factory,
):
    visibility_table = converted_visibility_factory.build_table(size=1)

    # We can just pass invalid values to factory because we are too strict with validation
    visibility_table.df = visibility_table.df.with_columns(
        portal=pl.lit("PS"),
        portal_platform_region=pl.lit("PS:PC:Global"),
    )
    input_data = {
        TableName.OBSERVATION_VISIBILITY: visibility_table,
        TableName.SILVER_SKUS: silver_skus_sales_factory.build_table(size=1),
    }
    with pytest.raises(SchemaError):
        FactVisibilityAggregator(**input_data).aggregate().df
