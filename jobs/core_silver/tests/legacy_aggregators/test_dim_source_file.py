import polars as pl
from polars.testing import assert_frame_equal

from core_silver.legacy_aggregators.dim_source_file import DimSourceFileAggregator
from data_sdk.domain import Portal, TableName


def test_dim_source_file_produces_correct_records(external_steam_reports_factory):
    input_data = {
        TableName.EXTERNAL_REPORTS: external_steam_reports_factory.build_table(size=1),
    }
    dim_source_file_data = (
        DimSourceFileAggregator(**input_data, portal=Portal.STEAM).aggregate().df
    )

    assert list(dim_source_file_data.columns) == [
        "source_file_id",
        "file_name",
        "upload_date",
        "studio_id",
    ]
    expected = pl.from_dict({
        "source_file_id": [0],
        "file_name": ["STEAM-2024-04-01_2024-04-01.zip"],
        "upload_date": ["2024-04-01 00:00:00+00:00"],
        "studio_id": [1],
    })
    assert_frame_equal(dim_source_file_data, expected)


def test_dim_source_file_filters_out_other_portals_records(
    external_steam_reports_factory,
    external_nintendo_reports_factory,
):
    input_data = {
        TableName.EXTERNAL_REPORTS: external_steam_reports_factory.build_table(size=1)
        + external_nintendo_reports_factory.build_table(size=1),
    }
    dim_source_file_data = (
        DimSourceFileAggregator(**input_data, portal=Portal.STEAM).aggregate().df
    )

    assert list(dim_source_file_data.columns) == [
        "source_file_id",
        "file_name",
        "upload_date",
        "studio_id",
    ]
    expected = pl.from_dict({
        "source_file_id": [0],
        "file_name": ["STEAM-2024-04-01_2024-04-01.zip"],
        "upload_date": ["2024-04-01 00:00:00+00:00"],
        "studio_id": [1],
    })
    assert dim_source_file_data.equals(expected)
