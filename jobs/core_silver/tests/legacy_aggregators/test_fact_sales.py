import pytest

from core_silver.legacy_aggregators.fact_sales.fact_sales import FactSalesAggregator
from data_sdk.domain import TableName


@pytest.fixture
def sales_input_data(converted_sales_factory, silver_skus_store_factory, country_codes):
    return {
        TableName.OBSERVATION_SALES: converted_sales_factory.build_table(size=1),
        TableName.SILVER_SKUS: silver_skus_store_factory.build_table(size=1),
        TableName.EXTERNAL_COUNTRY_CODES: country_codes,
    }


@pytest.fixture
def sales_input_data_empty_observations(sales_input_data, converted_sales_factory):
    sales_input_data[TableName.OBSERVATION_SALES] = converted_sales_factory.build_table(
        size=0
    )
    return sales_input_data


@pytest.fixture
def sales_input_data_empty_skus(sales_input_data, silver_skus_store_factory):
    sales_input_data[TableName.SILVER_SKUS] = silver_skus_store_factory.build_table(
        size=0
    )
    return sales_input_data


EXPECTED_COLUMNS = [
    "country_code",
    "currency_code",
    "studio_id",
    "sku_studio",
    "bundle_name",
    "portal_platform_region",
    "portal_platform_region_id",
    "product_id",
    "hash_acquisition_properties",
    "date",
    "date_sku_studio",
    "source_file_id",
    "retailer_tag",
    "base_price_local",
    "calculated_base_price_usd",
    "net_sales",
    "gross_returned",
    "gross_sales",
    "units_returned",
    "units_sold",
    "free_units",
    "price_local",
    "price_usd",
    "net_sales_approx",
    "category",
    "calculated_base_price_local_v2",
    "calculated_base_price_usd_v2",
]


def test_fact_sales_creates_all_required_columns(sales_input_data):
    fact_sales_data = FactSalesAggregator(**sales_input_data).aggregate().df
    assert list(fact_sales_data.columns) == EXPECTED_COLUMNS
    assert len(fact_sales_data) == 1


def test_fact_sales_handles_empty_observations(sales_input_data_empty_observations):
    fact_sales_data = (
        FactSalesAggregator(**sales_input_data_empty_observations).aggregate().df
    )

    assert len(fact_sales_data) == 0
    assert list(fact_sales_data.columns) == EXPECTED_COLUMNS


def test_fact_sales_handles_empty_skus(sales_input_data_empty_skus):
    fact_sales_data = FactSalesAggregator(**sales_input_data_empty_skus).aggregate().df

    assert len(fact_sales_data) == 1
    assert list(fact_sales_data.columns) == EXPECTED_COLUMNS
    assert fact_sales_data["product_id"][0] == "Unassigned:171010:1"
