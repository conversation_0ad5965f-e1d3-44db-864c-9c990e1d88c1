import pandas as pd

from core_silver.legacy_aggregators.common_aggregations import (
    compute_date_product_studio,
)
from tests.comparisons import compare_dataframes


def test_compute_date_product_studio():
    df = pd.DataFrame(
        data=[
            {"sku_studio": "SKU:1", "studio_id": 1, "date": "2022-01-01"},
            {"sku_studio": "DIFFERENT_SKU:1", "studio_id": 1, "date": "2022-01-01"},
        ],
    )
    sku = pd.DataFrame(
        data=[
            {"unique_sku_id": "SKU:1", "product_name": None},
            {"unique_sku_id": "DIFFERENT_SKU:1", "product_name": "Product 2"},
        ],
    )
    expected = pd.DataFrame(
        data=[
            ("SKU:1", 1, "2022-01-01", "2022-01-01:UNASSIGNED:1"),
            ("DIFFERENT_SKU:1", 1, "2022-01-01", "2022-01-01:Product 2:1"),
        ],
        columns=["sku_studio", "studio_id", "date", "date_product_studio"],
    )

    df["date_product_studio"] = compute_date_product_studio(df, sku)
    compare_dataframes(df, expected)
