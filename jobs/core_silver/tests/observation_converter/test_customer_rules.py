from datetime import date

import polars as pl
import pytest

from core_silver.observation_converter.customer_rules.filters import (
    CustomerRules,
    FilterType,
    RuleType,
    SKUFilterOptions,
    apply_customer_filters,
    load_customer_rules,
)
from data_sdk.domain.domain_types import StudioId


@pytest.fixture
def customer_rules() -> CustomerRules:
    return CustomerRules(
        studio_id=StudioId(1),
        filter_type=FilterType.SKU_FILTERS,
        value={
            RuleType.FILTERS: [
                SKUFilterOptions(sku_id="filter_this_id", date_to=date(2099, 1, 1)),
            ]
        },
    )


@pytest.mark.usefixtures("_mock_customer_rules_json")
def test_load_customer_rules():
    assert load_customer_rules(studio_id=StudioId(1)) is not None


@pytest.mark.usefixtures("_mock_customer_rules_json")
def test_load_customer_rules_when_no_studio_rules():
    assert load_customer_rules(studio_id=StudioId(0)) is None


@pytest.mark.usefixtures("_mock_customer_rules_json")
def test_load_customer_rules_content():
    customer_rules = load_customer_rules(studio_id=StudioId(1))
    expected = CustomerRules(
        studio_id=StudioId(1),
        filter_type=FilterType.SKU_FILTERS,
        value={
            RuleType.FILTERS: [
                SKUFilterOptions(sku_id="filter_this_id", date_to=date(2099, 1, 1)),
                SKUFilterOptions(sku_id="filter_this_id_too", date_to=date(2099, 1, 1)),
                SKUFilterOptions(sku_id="999999999999", date_to=date(2099, 1, 1)),
            ]
        },
    )

    assert customer_rules == expected


def test_apply_customer_filters_when_no_observations(
    converted_sales_factory, customer_rules
):
    observations: pl.DataFrame = converted_sales_factory.build_df_batch(
        size=0,
    )

    observations = apply_customer_filters(
        observations=observations, customer_rules=customer_rules
    )

    assert len(observations) == 0


def test_apply_customer_filters_when_only_filtered_rows(
    converted_sales_factory, customer_rules
):
    observations: pl.DataFrame = converted_sales_factory.build_df_batch(
        size=10, sku_id="filter_this_id"
    )

    observations = apply_customer_filters(
        observations=observations, customer_rules=customer_rules
    )

    assert len(observations) == 0


def test_apply_customer_filters(converted_sales_factory, customer_rules):
    observations_to_keep: pl.DataFrame = converted_sales_factory.build_df_batch(
        size=1, sku_id="filter_this_id"
    )
    observations_to_filter: pl.DataFrame = converted_sales_factory.build_df_batch(
        size=1, sku_id="keep_this_id"
    )

    observations = pl.concat([observations_to_keep, observations_to_filter])

    observations = apply_customer_filters(
        observations=observations, customer_rules=customer_rules
    )

    assert len(observations) == 1
    assert observations["sku_id"].unique().to_list() == ["keep_this_id"]


def test_apply_customer_filters_to_discounts_observation_type(
    converted_discounts_factory, customer_rules
):
    observations_to_keep: pl.DataFrame = converted_discounts_factory.build_df_batch(
        size=1, base_sku_id="filter_this_id"
    )
    observations_to_filter: pl.DataFrame = converted_discounts_factory.build_df_batch(
        size=1, base_sku_id="keep_this_id"
    )

    observations = pl.concat([observations_to_keep, observations_to_filter])

    observations = apply_customer_filters(
        observations=observations, customer_rules=customer_rules
    )

    assert len(observations) == 1
    assert observations["base_sku_id"].unique().to_list() == ["keep_this_id"]
