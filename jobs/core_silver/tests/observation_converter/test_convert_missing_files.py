from unittest.mock import MagicMock, patch

from core_silver.job import top_level_observation_converter
from core_silver.observation_converter.process_studio_files import (
    get_missing_files,
)
from data_sdk.custom_partition.reader import LocalCustomPartitionReader
from data_sdk.domain import Portal
from data_sdk.domain.domain_types import (
    ReportMetadata,
    ReportsMetadata,
    StudioId,
)
from data_sdk.domain.observations import ObservationType
from data_sdk.domain.tables import (
    ExternalCurrencyExchangeRatesTable,
    ExternalReportsTable,
)
from data_sdk.reports.reader import ConvertedReportsReader


@patch.object(
    LocalCustomPartitionReader,
    "read_table",
    autospec=True,
    wraps=LocalCustomPartitionReader.read_table,
)
def test_convert_missing_files_reads_dependency_table_once(
    read_table_spy,
    mock_tables,
    mock_reports,
    external_steam_reports_factory,
    external_nintendo_reports_factory,
    nintendo_raw_sales_factory,
    external_currency_exchange_rates_table,
    local_config,
    creation_datetime,
):
    mock_tables([external_currency_exchange_rates_table])

    mock_reports([
        {
            "external_reports_row": external_nintendo_reports_factory.build(),
            "report": nintendo_raw_sales_factory(),
        }
        for _ in range(3)
    ])

    top_level_observation_converter(
        config=local_config,
        portal=Portal.NINTENDO,
        studio_id=StudioId(1),
        observation_type=ObservationType.SALES,
        creation_datetime=creation_datetime,
    )
    assert read_table_spy.call_count == 2

    assert read_table_spy.call_args_list[0][1]["table_cls"] == ExternalReportsTable
    assert (
        read_table_spy.call_args_list[1][1]["table_cls"]
        == ExternalCurrencyExchangeRatesTable
    )


def test_get_missing_files_returns_no_missing_files():
    raw_file_list = MagicMock(spec=ReportsMetadata)
    raw_file_list.with_data.root = [
        MagicMock(spec=ReportMetadata, converted_filename="file1")
    ]
    converted_reader = MagicMock(spec=ConvertedReportsReader)
    converted_reader.get_file_list.return_value = ["file1"]

    result = get_missing_files(raw_file_list, StudioId(1), converted_reader)
    assert result == []


def test_get_missing_files_returns_all_missing_files():
    raw_file_list = MagicMock(spec=ReportsMetadata)
    raw_file_list.with_data.root = [
        MagicMock(spec=ReportMetadata, converted_filename="file1"),
        MagicMock(spec=ReportMetadata, converted_filename="file2"),
    ]
    converted_reader = MagicMock(spec=ConvertedReportsReader)
    converted_reader.get_file_list.return_value = []

    result = get_missing_files(raw_file_list, StudioId(1), converted_reader)
    assert len(result) == 2


def test_get_missing_files_returns_only_missing_files():
    raw_file_list = MagicMock(spec=ReportsMetadata)
    raw_file_list.with_data.root = [
        MagicMock(spec=ReportMetadata, converted_filename="file1"),
        MagicMock(spec=ReportMetadata, converted_filename="file2"),
    ]
    converted_reader = MagicMock(spec=ConvertedReportsReader)
    converted_reader.get_file_list.return_value = ["file1"]

    result = get_missing_files(raw_file_list, StudioId(1), converted_reader)
    assert len(result) == 1
    assert result[0].converted_filename == "file2"
