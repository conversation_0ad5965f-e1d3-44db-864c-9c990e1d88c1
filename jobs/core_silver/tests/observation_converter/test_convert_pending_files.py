from unittest.mock import MagicMock

from core_silver.observation_converter.process_studio_files import (
    get_pending_but_converted_files,
)
from data_sdk.domain.domain_types import (
    ReportMetadata,
    ReportsMetadata,
)


def test_get_pending_but_converted_files_returns_no_incorrectly_pending_files_when_converted():
    raw_file_list = MagicMock(spec=ReportsMetadata)
    raw_file_list.with_data.root = [
        MagicMock(spec=ReportMetadata, converted_filename="file1", state="CONVERTED")
    ]
    missing_parquet_files = []

    result = get_pending_but_converted_files(
        raw_file_list=raw_file_list,
        missing_files=missing_parquet_files,
    )
    assert result == []


def test_get_pending_but_converted_files_returns_no_incorrectly_pending_files_when_no_parquets():
    raw_file_list = MagicMock(spec=ReportsMetadata)
    raw_file_list.with_data.root = [
        MagicMock(spec=ReportMetadata, converted_filename="file1", state="PENDING"),
        MagicMock(spec=ReportMetadata, converted_filename="file2", state="PENDING"),
    ]
    missing_parquet_files: list[ReportMetadata] = [
        MagicMock(spec=ReportMetadata, converted_filename="file1", state="PENDING"),
        MagicMock(spec=ReportMetadata, converted_filename="file2", state="PENDING"),
    ]

    result = get_pending_but_converted_files(
        raw_file_list=raw_file_list,
        missing_files=missing_parquet_files,
    )
    assert result == []


def test_get_pending_but_converted_files_returns_no_incorrectly_pending_files_when_parquet_is_from_converted_file():
    raw_file_list = MagicMock(spec=ReportsMetadata)
    raw_file_list.with_data.root = [
        MagicMock(spec=ReportMetadata, converted_filename="file1", state="PENDING"),
        MagicMock(spec=ReportMetadata, converted_filename="file2", state="CONVERTED"),
    ]
    missing_parquet_files: list[ReportMetadata] = [
        MagicMock(spec=ReportMetadata, converted_filename="file1", state="PENDING"),
    ]

    result = get_pending_but_converted_files(
        raw_file_list, missing_files=missing_parquet_files
    )
    assert result == []


def test_get_pending_but_converted_files_recognizes_positive_case():
    raw_file_list = MagicMock(spec=ReportsMetadata)
    raw_file_list.with_data.root = [
        MagicMock(spec=ReportMetadata, converted_filename="file1", state="PENDING"),
        MagicMock(spec=ReportMetadata, converted_filename="file2", state="PENDING"),
        MagicMock(spec=ReportMetadata, converted_filename="file3", state="CONVERTED"),
    ]
    missing_parquet_files: list[ReportMetadata] = [
        MagicMock(spec=ReportMetadata, converted_filename="file1", state="PENDING"),
    ]

    result = get_pending_but_converted_files(
        raw_file_list, missing_files=missing_parquet_files
    )
    assert result == [raw_file_list.with_data.root[1]]
