from datetime import datetime
from pathlib import Path
from unittest.mock import patch

import polars as pl

from core_silver.job import top_level_observation_converter
from core_silver.observation_converter.converters.steam_sales import SteamSalesConverter
from data_sdk.domain import Portal
from data_sdk.domain.domain_types import (
    ReportState,
    StudioId,
)
from data_sdk.domain.observations import ObservationType

# Make reference to the original _convert method that is going to be patched
original_convert_method = SteamSalesConverter._convert


@patch.object(SteamSalesConverter, "_convert", autospec=True)
def test_silver_reports_contains_list_of_all_reports_with_their_updated_states(
    mocked_convert,
    tmp_path: Path,
    creation_datetime: datetime,
    external_steam_reports_factory,
    legacy_steam_raw_sales_factory,
    converted_sales_factory,
    mock_reports,
    local_config,
):
    rows = external_steam_reports_factory.build_batch(size=5)
    reports = legacy_steam_raw_sales_factory.build_batch(size=5)
    converted = converted_sales_factory.build_table().df

    rows[0]["state"] = ReportState.CONVERTED
    rows[1]["state"] = ReportState.PENDING
    rows[2]["state"] = ReportState.PENDING
    rows[3]["state"] = ReportState.FAILED
    rows[4]["state"] = ReportState.DELETED

    mock_reports([
        {
            "external_reports_row": row,
            "report": report,
            "converted_df": converted
            if row["state"] == ReportState.CONVERTED
            else None,
        }
        for row, report in zip(rows, reports)
    ])

    def convert_with_failing_on_report_id_2(self, *a, **kw):
        if self._raw_report.metadata.report_id == 2:
            raise ValueError("Corrupted report")  # noqa: TRY003
        return original_convert_method(self)

    mocked_convert.side_effect = convert_with_failing_on_report_id_2

    top_level_observation_converter(
        config=local_config,
        portal=Portal.STEAM,
        studio_id=StudioId(1),
        observation_type=ObservationType.SALES,
        creation_datetime=creation_datetime,
    )

    assert mocked_convert.call_count == 3

    silver_reports_df = pl.read_parquet(
        tmp_path
        / "result/silver_reports/studio_id=1/portal=steam/observation_type=sales/version=v1/20240401T120000Z.parquet",
        hive_partitioning=False,
    )

    assert silver_reports_df[["report_id", "state"]].to_dicts() == [
        {"report_id": 0, "state": "CONVERTED"},
        {"report_id": 1, "state": "CONVERTED"},
        {"report_id": 2, "state": "FAILED"},
        {"report_id": 3, "state": "CONVERTED"},
    ]
