from datetime import datetime
from pathlib import Path
from typing import Any, TypedDict

import polars as pl
import pytest

from data_sdk.custom_partition.partitioner import get_table_partition
from data_sdk.custom_partition.writer import CustomPartitionsWriter
from data_sdk.domain.tables import TableDefinition
from data_sdk.segmentator import BaseSegmentator, SingleSegmentator
from data_sdk.utils.date_utils import datetime_to_string


@pytest.fixture
def mock_tables(local_config, creation_datetime):
    writer = CustomPartitionsWriter.get_writer(local_config.output_cfg)
    segmentator: BaseSegmentator = SingleSegmentator()  # TODO: this should be dynamic

    def _mock_tables(tables: list[TableDefinition]):
        for table in tables:
            partition = get_table_partition(table, studio_id=1)
            segments = segmentator.create_segments(table, creation_datetime)
            writer.save_table(table, partition, segments)

    return _mock_tables


class MockedReport(TypedDict):
    external_reports_row: dict[str, Any]
    report: dict[str, Any] | None
    converted_df: pl.DataFrame | None


@pytest.fixture
def mock_reports(external_reports_path: Path, tmp_path: Path):
    def _mock_reports(mocked_reports: list[MockedReport]):
        external_report_rows = []

        for mocked_report in mocked_reports:
            row = mocked_report["external_reports_row"]
            external_report_rows.append(row)

            report = mocked_report.get("report")
            if report is not None:
                blob_name = row["blob_name"]
                zip_path: Path = tmp_path / "raw" / blob_name
                zip_path.parent.mkdir(exist_ok=True)
                zip_path.write_bytes(report.raw_zip_file)  # type: ignore[]

            converted_df = mocked_report.get("converted_df")
            if converted_df is not None:
                path = tmp_path / "converted" / f"studio_id={row['studio_id']}"
                path.mkdir(parents=True, exist_ok=True)
                filename = generate_expected_filename(row)
                converted_df.write_parquet(path / filename)

        reports_df = pl.DataFrame(data=external_report_rows)
        reports_df.write_parquet(external_reports_path)

    return _mock_reports


def generate_expected_filename(row) -> str:
    return f"{row['report_id']}_{row['date_from']}_{row['date_to']}.parquet"


@pytest.fixture
def external_reports_path(tmp_path, creation_datetime) -> Path:
    result = (
        tmp_path
        / "result/external_reports/studio_id=1/version=v1/"
        / f"{datetime_to_string(creation_datetime)}.parquet"
    )
    result.parent.mkdir(parents=True, exist_ok=True)
    return result


@pytest.fixture
def creation_datetime() -> datetime:
    return datetime(year=2024, month=4, day=1, hour=12, minute=0)
