from datetime import date, datetime
from pathlib import Path
from shutil import copy2

import polars as pl
import pytest

from core_silver import get_project_root
from core_silver.job import top_level_observation_converter
from data_sdk.domain import Portal
from data_sdk.domain.domain_types import StudioId
from data_sdk.domain.observations import ObservationType
from data_sdk.domain.source import Source
from tests.converters.steam_discounts.data import (
    steam_discounts_raw_report_no_data_content,
)

"""
Test input existing file, generate report metadata for that file and check what will happen
do that for all portals and obs

Test id deduplicator will take only data from metadata and warn if report will contain other dates
"""


def test_sales_conversion_missing_converted(
    external_steam_reports_factory,
    external_nintendo_reports_factory,
    legacy_steam_raw_sales_factory,
    tmp_path: Path,
    creation_datetime: datetime,
    get_tmp_path_files,
    mock_reports,
    local_config,
):
    steam_report: dict = external_steam_reports_factory.build(date_to=date(2024, 4, 2))
    nintendo_report: dict = external_nintendo_reports_factory.build()
    steam_raw_sales = legacy_steam_raw_sales_factory(
        end_date=date(2024, 4, 2),
        rows__gross_units_sold=2,
        rows__bundle_name="SUPER COLD",
    )

    mock_reports([
        {"external_reports_row": nintendo_report},
        {"external_reports_row": steam_report, "report": steam_raw_sales},
    ])

    top_level_observation_converter(
        config=local_config,
        portal=Portal.STEAM,
        studio_id=StudioId(1),
        observation_type=ObservationType.SALES,
        creation_datetime=creation_datetime,
    )
    assert get_tmp_path_files() == [
        "converted/studio_id=1/0_2024-04-01_2024-04-02.parquet",
        "raw/STEAM-2024-04-01_2024-04-02.zip",
        "result/external_reports/studio_id=1/version=v1/20240401T120000Z.parquet",
        "result/observation_sales/studio_id=1/portal=steam/version=v1/20240401T120000Z.parquet",
        "result/silver_reports/studio_id=1/portal=steam/observation_type=sales/version=v1/20240401T120000Z.parquet",
    ]

    converted_report_df = pl.read_parquet(
        tmp_path / "converted/studio_id=1/0_2024-04-01_2024-04-02.parquet",
        hive_partitioning=False,
    )

    assert converted_report_df.to_dicts() == [
        {
            "country_code": "USA",
            "currency_code": "USD",
            "sku_id": "1",
            "bundle_name": "SUPER COLD",
            "portal": "Steam",
            "platform": "PC",
            "transaction_type": "Game",
            "payment_instrument": "Unknown",
            "tax_type": "Unknown",
            "sale_modificator": "NOT_APPLICABLE",
            "acquisition_platform": "Steam",
            "iap_flag": "False",
            "date": date(2024, 4, 1),
            "retailer_tag": "VR",
            "human_name": "SUPERHOT VR",
            "store_id": "1",
            "region": "Global",
            "studio_id": 1,
            "price_local": 24.99,
            "base_price_local": 24.99,
            "units_returned": 0,
            "report_id": 0,
            "acquisition_origin": "RETAIL",
            "units_sold": 2,
            "free_units": 0,
            "price_usd": 24.99,
            "net_sales_approx": 34.99,
            "store": "Steam",
            "abbreviated_name": "Steam",
            "net_sales": 49.98,
            "gross_returned": 0.0,
            "gross_sales": 49.98,
            "unique_sku_id": "1-steam:1",
            "portal_platform_region": "Steam:PC:Global",
            "hash_acquisition_properties": "f13e608a5489fb90cf2f809a8e05d610",
            "category": "Sale",
        },
        {
            "country_code": "USA",
            "currency_code": "USD",
            "sku_id": "1",
            "bundle_name": "SUPER COLD",
            "portal": "Steam",
            "platform": "PC",
            "transaction_type": "Game",
            "payment_instrument": "Unknown",
            "tax_type": "Unknown",
            "sale_modificator": "NOT_APPLICABLE",
            "acquisition_platform": "Steam",
            "iap_flag": "False",
            "date": date(2024, 4, 2),
            "retailer_tag": "VR",
            "human_name": "SUPERHOT VR",
            "store_id": "1",
            "region": "Global",
            "studio_id": 1,
            "price_local": 24.99,
            "base_price_local": 24.99,
            "units_returned": 0,
            "report_id": 0,
            "acquisition_origin": "RETAIL",
            "units_sold": 2,
            "free_units": 0,
            "price_usd": 24.99,
            "net_sales_approx": 34.99,
            "store": "Steam",
            "abbreviated_name": "Steam",
            "net_sales": 49.98,
            "gross_returned": 0.0,
            "gross_sales": 49.98,
            "unique_sku_id": "1-steam:1",
            "portal_platform_region": "Steam:PC:Global",
            "hash_acquisition_properties": "f13e608a5489fb90cf2f809a8e05d610",
            "category": "Sale",
        },
    ]

    sales_schema_df = pl.read_parquet(
        tmp_path
        / "result/observation_sales/studio_id=1/portal=steam/version=v1/20240401T120000Z.parquet",
        hive_partitioning=False,
    ).sort(by="date", descending=True)

    assert sales_schema_df.to_dicts() == [
        {
            "report_id": 0,
            "date": date(2024, 4, 2),
            "country_code": "USA",
            "currency_code": "USD",
            "sku_id": "1",
            "bundle_name": "SUPER COLD",
            "portal": "Steam",
            "platform": "PC",
            "transaction_type": "Game",
            "payment_instrument": "Unknown",
            "tax_type": "Unknown",
            "sale_modificator": "NOT_APPLICABLE",
            "acquisition_platform": "Steam",
            "iap_flag": "False",
            "retailer_tag": "VR",
            "human_name": "SUPERHOT VR",
            "store_id": "1",
            "region": "Global",
            "studio_id": 1,
            "price_local": 24.99,
            "base_price_local": 24.99,
            "units_returned": 0,
            "acquisition_origin": "RETAIL",
            "units_sold": 2,
            "free_units": 0,
            "price_usd": 24.99,
            "net_sales_approx": 34.99,
            "store": "Steam",
            "abbreviated_name": "Steam",
            "net_sales": 49.98,
            "gross_returned": 0.0,
            "gross_sales": 49.98,
            "unique_sku_id": "1-steam:1",
            "portal_platform_region": "Steam:PC:Global",
            "hash_acquisition_properties": "f13e608a5489fb90cf2f809a8e05d610",
            "category": "Sale",
        },
        {
            "report_id": 0,
            "date": date(2024, 4, 1),
            "country_code": "USA",
            "currency_code": "USD",
            "sku_id": "1",
            "bundle_name": "SUPER COLD",
            "portal": "Steam",
            "platform": "PC",
            "transaction_type": "Game",
            "payment_instrument": "Unknown",
            "tax_type": "Unknown",
            "sale_modificator": "NOT_APPLICABLE",
            "acquisition_platform": "Steam",
            "iap_flag": "False",
            "retailer_tag": "VR",
            "human_name": "SUPERHOT VR",
            "store_id": "1",
            "region": "Global",
            "studio_id": 1,
            "price_local": 24.99,
            "base_price_local": 24.99,
            "units_returned": 0,
            "acquisition_origin": "RETAIL",
            "units_sold": 2,
            "free_units": 0,
            "price_usd": 24.99,
            "net_sales_approx": 34.99,
            "store": "Steam",
            "abbreviated_name": "Steam",
            "net_sales": 49.98,
            "gross_returned": 0.0,
            "gross_sales": 49.98,
            "unique_sku_id": "1-steam:1",
            "portal_platform_region": "Steam:PC:Global",
            "hash_acquisition_properties": "f13e608a5489fb90cf2f809a8e05d610",
            "category": "Sale",
        },
    ]


# TODO: thats a badly written test
# test_discounts_conversion_missing_converted


def test_discounts_conversion_all_no_data_reports(
    external_steam_reports_factory,
    tmp_path: Path,
    creation_datetime: datetime,
    external_reports_path: Path,
    mock_zip_file,
    get_tmp_path_files,
    local_config,
):
    reports_df: pl.DataFrame = external_steam_reports_factory.build_df_batch(
        source=Source.STEAM_DISCOUNTS, size=1, no_data=True
    )
    reports_df.write_parquet(external_reports_path)

    with mock_zip_file(steam_discounts_raw_report_no_data_content) as zip_file:
        steam_raw_discounts = zip_file.fp.getvalue()

    zip_path: Path = tmp_path / "raw" / reports_df["blob_name"][0]
    zip_path.parent.mkdir(exist_ok=True)
    zip_path.write_bytes(steam_raw_discounts)

    top_level_observation_converter(
        config=local_config,
        studio_id=StudioId(1),
        portal=Portal.STEAM,
        observation_type=ObservationType.DISCOUNTS,
        creation_datetime=creation_datetime,
    )
    assert get_tmp_path_files() == [
        "raw/STEAM-2024-04-01_2024-04-01.zip",
        "result/external_reports/studio_id=1/version=v1/20240401T120000Z.parquet",
        "result/observation_discounts/studio_id=1/portal=steam/version=v1/20240401T120000Z.parquet",
        "result/silver_reports/studio_id=1/portal=steam/observation_type=discounts/version=v1/20240401T120000Z.parquet",
    ]

    assert not (
        tmp_path / "converted/studio_id=1/0_2024-04-01_2024-04-01.parquet"
    ).exists()

    observation_discounts_df = pl.read_parquet(
        tmp_path
        / "result/observation_discounts/studio_id=1/portal=steam/version=v1/20240401T120000Z.parquet",
        hive_partitioning=False,
    )
    observation_discounts_results = observation_discounts_df.to_dicts()
    assert len(observation_discounts_results) == 0


def test_visibility_conversion_missing_converted(
    external_steam_reports_factory,
    tmp_path: Path,
    creation_datetime: datetime,
    external_reports_path: Path,
    mock_zip_file,
    get_tmp_path_files,
    local_config,
):
    reports_df: pl.DataFrame = external_steam_reports_factory.build_df_batch(
        source=Source.STEAM_IMPRESSIONS,
        date_from=date(2019, 2, 21),
        date_to=date(2019, 4, 13),
        size=1,
    )
    reports_df.write_parquet(external_reports_path)

    zip_path: Path = tmp_path / "raw" / reports_df["blob_name"][0]
    zip_path.parent.mkdir(exist_ok=True)
    copy2(
        src=get_project_root()
        / "tests"
        / "snapshot"
        / "input"
        / "STEAM_IMPRESSIONS-2019-02-21_2019-04-13.zip",
        dst=zip_path,
    )

    top_level_observation_converter(
        config=local_config,
        portal=Portal.STEAM,
        studio_id=StudioId(1),
        observation_type=ObservationType.VISIBILITY,
        creation_datetime=creation_datetime,
    )
    assert get_tmp_path_files() == [
        "converted/studio_id=1/0_2019-02-21_2019-04-13.parquet",
        "raw/STEAM-2019-02-21_2019-04-13.zip",
        "result/external_reports/studio_id=1/version=v1/20240401T120000Z.parquet",
        "result/observation_visibility/studio_id=1/portal=steam/version=v1/20240401T120000Z.parquet",
        "result/silver_reports/studio_id=1/portal=steam/observation_type=visibility/version=v1/20240401T120000Z.parquet",
    ]

    converted_report_df = pl.read_parquet(
        tmp_path / "converted/studio_id=1/0_2019-02-21_2019-04-13.parquet",
        hive_partitioning=False,
    )
    converted_results = converted_report_df.to_dicts()
    assert len(converted_results) == 72
    assert converted_results[0] == {
        "page_category": "Browse Search Results",
        "page_feature": "Browse Search Results",
        "impressions": 369,
        "visits": 0,
        "owner_impressions": 0,
        "owner_visits": 0,
        "sku_id": "583710",
        "date": date(2019, 2, 21),
        "human_name": "Dimension Hunter Demo",
        "studio_id": 1,
        "report_id": 0,
        "portal": "Steam",
        "platform": "PC",
        "region": "Global",
        "store_id": "583710",
        "hash_traffic_source": "8006adebacb4c27c389fd872110fcc7c",
        "store": "Steam",
        "abbreviated_name": "Steam",
        "navigation": "Visits from Impressions",
        "unique_sku_id": "583710-store:1",
        "portal_platform_region": "Steam:PC:Global",
    }

    observation_visibility_df = pl.read_parquet(
        tmp_path
        / "result/observation_visibility/studio_id=1/portal=steam/version=v1/20240401T120000Z.parquet",
        hive_partitioning=False,
    )
    observation_visibility_results = observation_visibility_df.sort(
        by=["date", "impressions"], descending=True
    ).to_dicts()
    assert len(observation_visibility_results) == 72
    assert observation_visibility_results[0] == {
        "report_id": 0,
        "date": date(2019, 3, 12),
        "page_category": "Direct Search Results",
        "page_feature": "Search Results",
        "impressions": 280,
        "visits": 0,
        "owner_impressions": 0,
        "owner_visits": 0,
        "sku_id": "583710",
        "human_name": "Dimension Hunter Demo",
        "studio_id": 1,
        "portal": "Steam",
        "platform": "PC",
        "region": "Global",
        "store_id": "583710",
        "hash_traffic_source": "8a842396a2dc9d4a2dad4cbe2da8d65b",
        "store": "Steam",
        "abbreviated_name": "Steam",
        "navigation": "Visits from Impressions",
        "unique_sku_id": "583710-store:1",
        "portal_platform_region": "Steam:PC:Global",
    }


@pytest.mark.usefixtures("_mock_customer_rules_json")
def test_sales_conversion_missing_converted_with_customer_rules_filtering(
    external_steam_reports_factory,
    external_nintendo_reports_factory,
    legacy_steam_raw_sales_factory,
    tmp_path: Path,
    creation_datetime: datetime,
    get_tmp_path_files,
    mock_reports,
    local_config,
):
    steam_report: dict = external_steam_reports_factory.build(date_to=date(2024, 4, 2))
    nintendo_report: dict = external_nintendo_reports_factory.build()

    steam_raw_sales = legacy_steam_raw_sales_factory(
        end_date=date(2024, 4, 2),
        rows__gross_units_sold=2,
        rows__bundle_name="SUPER COLD",
    )

    steam_raw_sales_2 = legacy_steam_raw_sales_factory(
        end_date=date(2024, 4, 2),
        rows__gross_units_sold=2,
        rows__bundle_name="MEDIUM COLD",
        rows__product_id="999999999999",
    )

    steam_raw_sales += steam_raw_sales_2

    mock_reports([
        {"external_reports_row": nintendo_report},
        {"external_reports_row": steam_report, "report": steam_raw_sales},
    ])

    top_level_observation_converter(
        config=local_config,
        portal=Portal.STEAM,
        studio_id=StudioId(1),
        observation_type=ObservationType.SALES,
        creation_datetime=creation_datetime,
    )
    assert get_tmp_path_files() == [
        "converted/studio_id=1/0_2024-04-01_2024-04-02.parquet",
        "raw/STEAM-2024-04-01_2024-04-02.zip",
        "result/external_reports/studio_id=1/version=v1/20240401T120000Z.parquet",
        "result/observation_sales/studio_id=1/portal=steam/version=v1/20240401T120000Z.parquet",
        "result/silver_reports/studio_id=1/portal=steam/observation_type=sales/version=v1/20240401T120000Z.parquet",
    ]

    converted_report_df = pl.read_parquet(
        tmp_path / "converted/studio_id=1/0_2024-04-01_2024-04-02.parquet",
        hive_partitioning=False,
    )

    assert converted_report_df.to_dicts() == [
        {
            "country_code": "USA",
            "currency_code": "USD",
            "sku_id": "1",
            "bundle_name": "SUPER COLD",
            "portal": "Steam",
            "platform": "PC",
            "transaction_type": "Game",
            "payment_instrument": "Unknown",
            "tax_type": "Unknown",
            "sale_modificator": "NOT_APPLICABLE",
            "acquisition_platform": "Steam",
            "iap_flag": "False",
            "date": date(2024, 4, 1),
            "retailer_tag": "VR",
            "human_name": "SUPERHOT VR",
            "store_id": "1",
            "region": "Global",
            "studio_id": 1,
            "price_local": 24.99,
            "base_price_local": 24.99,
            "units_returned": 0,
            "report_id": 0,
            "acquisition_origin": "RETAIL",
            "units_sold": 2,
            "free_units": 0,
            "price_usd": 24.99,
            "net_sales_approx": 34.99,
            "store": "Steam",
            "abbreviated_name": "Steam",
            "net_sales": 49.98,
            "gross_returned": 0.0,
            "gross_sales": 49.98,
            "unique_sku_id": "1-steam:1",
            "portal_platform_region": "Steam:PC:Global",
            "hash_acquisition_properties": "f13e608a5489fb90cf2f809a8e05d610",
            "category": "Sale",
        },
        {
            "country_code": "USA",
            "currency_code": "USD",
            "sku_id": "1",
            "bundle_name": "SUPER COLD",
            "portal": "Steam",
            "platform": "PC",
            "transaction_type": "Game",
            "payment_instrument": "Unknown",
            "tax_type": "Unknown",
            "sale_modificator": "NOT_APPLICABLE",
            "acquisition_platform": "Steam",
            "iap_flag": "False",
            "date": date(2024, 4, 2),
            "retailer_tag": "VR",
            "human_name": "SUPERHOT VR",
            "store_id": "1",
            "region": "Global",
            "studio_id": 1,
            "price_local": 24.99,
            "base_price_local": 24.99,
            "units_returned": 0,
            "report_id": 0,
            "acquisition_origin": "RETAIL",
            "units_sold": 2,
            "free_units": 0,
            "price_usd": 24.99,
            "net_sales_approx": 34.99,
            "store": "Steam",
            "abbreviated_name": "Steam",
            "net_sales": 49.98,
            "gross_returned": 0.0,
            "gross_sales": 49.98,
            "unique_sku_id": "1-steam:1",
            "portal_platform_region": "Steam:PC:Global",
            "hash_acquisition_properties": "f13e608a5489fb90cf2f809a8e05d610",
            "category": "Sale",
        },
        {
            "country_code": "USA",
            "currency_code": "USD",
            "sku_id": "999999999999",
            "bundle_name": "MEDIUM COLD",
            "portal": "Steam",
            "platform": "PC",
            "transaction_type": "Game",
            "payment_instrument": "Unknown",
            "tax_type": "Unknown",
            "sale_modificator": "NOT_APPLICABLE",
            "acquisition_platform": "Steam",
            "iap_flag": "False",
            "date": date(2024, 4, 1),
            "retailer_tag": "VR",
            "human_name": "SUPERHOT VR",
            "store_id": "999999999999",
            "region": "Global",
            "studio_id": 1,
            "price_local": 24.99,
            "base_price_local": 24.99,
            "units_returned": 0,
            "report_id": 0,
            "acquisition_origin": "RETAIL",
            "units_sold": 2,
            "free_units": 0,
            "price_usd": 24.99,
            "net_sales_approx": 34.99,
            "store": "Steam",
            "abbreviated_name": "Steam",
            "net_sales": 49.98,
            "gross_returned": 0.0,
            "gross_sales": 49.98,
            "unique_sku_id": "999999999999-steam:1",
            "portal_platform_region": "Steam:PC:Global",
            "hash_acquisition_properties": "f13e608a5489fb90cf2f809a8e05d610",
            "category": "Sale",
        },
        {
            "country_code": "USA",
            "currency_code": "USD",
            "sku_id": "999999999999",
            "bundle_name": "MEDIUM COLD",
            "portal": "Steam",
            "platform": "PC",
            "transaction_type": "Game",
            "payment_instrument": "Unknown",
            "tax_type": "Unknown",
            "sale_modificator": "NOT_APPLICABLE",
            "acquisition_platform": "Steam",
            "iap_flag": "False",
            "date": date(2024, 4, 2),
            "retailer_tag": "VR",
            "human_name": "SUPERHOT VR",
            "store_id": "999999999999",
            "region": "Global",
            "studio_id": 1,
            "price_local": 24.99,
            "base_price_local": 24.99,
            "units_returned": 0,
            "report_id": 0,
            "acquisition_origin": "RETAIL",
            "units_sold": 2,
            "free_units": 0,
            "price_usd": 24.99,
            "net_sales_approx": 34.99,
            "store": "Steam",
            "abbreviated_name": "Steam",
            "net_sales": 49.98,
            "gross_returned": 0.0,
            "gross_sales": 49.98,
            "unique_sku_id": "999999999999-steam:1",
            "portal_platform_region": "Steam:PC:Global",
            "hash_acquisition_properties": "f13e608a5489fb90cf2f809a8e05d610",
            "category": "Sale",
        },
    ]

    sales_schema_df = pl.read_parquet(
        tmp_path
        / "result/observation_sales/studio_id=1/portal=steam/version=v1/20240401T120000Z.parquet",
        hive_partitioning=False,
    ).sort(by="date", descending=True)

    assert sales_schema_df.to_dicts() == [
        {
            "report_id": 0,
            "date": date(2024, 4, 2),
            "country_code": "USA",
            "currency_code": "USD",
            "sku_id": "1",
            "bundle_name": "SUPER COLD",
            "portal": "Steam",
            "platform": "PC",
            "transaction_type": "Game",
            "payment_instrument": "Unknown",
            "tax_type": "Unknown",
            "sale_modificator": "NOT_APPLICABLE",
            "acquisition_platform": "Steam",
            "iap_flag": "False",
            "retailer_tag": "VR",
            "human_name": "SUPERHOT VR",
            "store_id": "1",
            "region": "Global",
            "studio_id": 1,
            "price_local": 24.99,
            "base_price_local": 24.99,
            "units_returned": 0,
            "acquisition_origin": "RETAIL",
            "units_sold": 2,
            "free_units": 0,
            "price_usd": 24.99,
            "net_sales_approx": 34.99,
            "store": "Steam",
            "abbreviated_name": "Steam",
            "net_sales": 49.98,
            "gross_returned": 0.0,
            "gross_sales": 49.98,
            "unique_sku_id": "1-steam:1",
            "portal_platform_region": "Steam:PC:Global",
            "hash_acquisition_properties": "f13e608a5489fb90cf2f809a8e05d610",
            "category": "Sale",
        },
        {
            "report_id": 0,
            "date": date(2024, 4, 1),
            "country_code": "USA",
            "currency_code": "USD",
            "sku_id": "1",
            "bundle_name": "SUPER COLD",
            "portal": "Steam",
            "platform": "PC",
            "transaction_type": "Game",
            "payment_instrument": "Unknown",
            "tax_type": "Unknown",
            "sale_modificator": "NOT_APPLICABLE",
            "acquisition_platform": "Steam",
            "iap_flag": "False",
            "retailer_tag": "VR",
            "human_name": "SUPERHOT VR",
            "store_id": "1",
            "region": "Global",
            "studio_id": 1,
            "price_local": 24.99,
            "base_price_local": 24.99,
            "units_returned": 0,
            "acquisition_origin": "RETAIL",
            "units_sold": 2,
            "free_units": 0,
            "price_usd": 24.99,
            "net_sales_approx": 34.99,
            "store": "Steam",
            "abbreviated_name": "Steam",
            "net_sales": 49.98,
            "gross_returned": 0.0,
            "gross_sales": 49.98,
            "unique_sku_id": "1-steam:1",
            "portal_platform_region": "Steam:PC:Global",
            "hash_acquisition_properties": "f13e608a5489fb90cf2f809a8e05d610",
            "category": "Sale",
        },
    ]


def test_simple_deduplication(
    external_steam_reports_factory,
    legacy_steam_raw_sales_factory,
    tmp_path: Path,
    creation_datetime: datetime,
    get_tmp_path_files,
    mock_reports,
    local_config,
):
    steam_report_older: dict = external_steam_reports_factory.build(
        upload_date=date(2024, 4, 1), blob_name="older.zip"
    )
    steam_report_newer: dict = external_steam_reports_factory.build(
        upload_date=date(2024, 5, 1), blob_name="newer.zip"
    )

    steam_raw_sales_older = legacy_steam_raw_sales_factory(
        rows__gross_units_sold=2,
        rows__bundle_name="SUPER OLDER",
    )

    steam_raw_sales_newer = legacy_steam_raw_sales_factory(
        rows__gross_units_sold=2,
        rows__bundle_name="SUPER NEWER",
    )

    mock_reports([
        {"external_reports_row": steam_report_older, "report": steam_raw_sales_older},
        {"external_reports_row": steam_report_newer, "report": steam_raw_sales_newer},
    ])

    top_level_observation_converter(
        config=local_config,
        studio_id=StudioId(1),
        portal=Portal.STEAM,
        observation_type=ObservationType.SALES,
        creation_datetime=creation_datetime,
    )
    assert get_tmp_path_files() == [
        "converted/studio_id=1/0_2024-04-01_2024-04-01.parquet",
        "converted/studio_id=1/1_2024-04-01_2024-04-01.parquet",
        "raw/newer.zip",
        "raw/older.zip",
        "result/external_reports/studio_id=1/version=v1/20240401T120000Z.parquet",
        "result/observation_sales/studio_id=1/portal=steam/version=v1/20240401T120000Z.parquet",
        "result/silver_reports/studio_id=1/portal=steam/observation_type=sales/version=v1/20240401T120000Z.parquet",
    ]

    sales_schema_df = pl.read_parquet(
        tmp_path
        / "result/observation_sales/studio_id=1/portal=steam/version=v1/20240401T120000Z.parquet",
        hive_partitioning=False,
    ).sort(by="date", descending=True)

    assert sales_schema_df["bundle_name"].to_list() == ["SUPER NEWER"]
    assert sales_schema_df["report_id"].to_list() == [1]
