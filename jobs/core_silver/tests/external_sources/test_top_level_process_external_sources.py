from datetime import date, datetime, timezone

import polars as pl

from core_silver.job import top_level_process_external_sources
from data_sdk.domain.domain_types import StudioId


def test_creates_external_reports_file(local_config, tmp_path):
    start_time = datetime(year=2020, month=1, day=1, hour=1, minute=0)

    top_level_process_external_sources(
        config=local_config, studio_id=StudioId(1), creation_datetime=start_time
    )

    assert (
        tmp_path
        / "result/external_reports/studio_id=1/version=v1/20200101T010000Z.parquet"
    ).is_file()


def test_external_reports_file_contains_proper_data(local_config, tmp_path):
    start_time = datetime(year=2020, month=1, day=1, hour=1, minute=0)

    top_level_process_external_sources(
        config=local_config, studio_id=StudioId(1), creation_datetime=start_time
    )

    df = pl.read_parquet(
        tmp_path
        / "result/external_reports/studio_id=1/version=v1/20200101T010000Z.parquet",
        hive_partitioning=False,
    )
    # Test just a couple of rows
    assert df.to_dicts()[:2] == [
        {
            "source": "app_store_sales",
            "studio_id": 1,
            "report_id": 1,
            "upload_date": datetime(2023, 2, 20, 0, 0, 1, tzinfo=timezone.utc),
            "blob_name": "app_store_sales.zip",
            "original_name": "app_store_sales.zip",
            "state": "PENDING",
            "no_data": False,
            "date_from": date(2023, 1, 1),
            "date_to": date(2023, 1, 4),
            "portal": "apple",
            "observation_type": "sales",
        },
        {
            "source": "epic_sales",
            "studio_id": 1,
            "report_id": 2,
            "upload_date": datetime(2023, 10, 3, 0, 0, 1, tzinfo=timezone.utc),
            "blob_name": "epic_with_positive_returns.zip",
            "original_name": "epic_with_positive_returns.zip",
            "state": "PENDING",
            "no_data": False,
            "date_from": date(2020, 1, 1),
            "date_to": date(2020, 1, 31),
            "portal": "epic",
            "observation_type": "sales",
        },
    ]


def test_creates_external_studios_file(local_config, tmp_path):
    start_time = datetime(year=2020, month=1, day=1, hour=1, minute=0)

    top_level_process_external_sources(
        config=local_config, studio_id=StudioId(1), creation_datetime=start_time
    )

    assert (
        tmp_path
        / "result/external_studios/studio_id=1/version=v1/20200101T010000Z.parquet"
    ).is_file()


def test_external_studios_file_contains_proper_data(local_config, tmp_path):
    start_time = datetime(year=2020, month=1, day=1, hour=1, minute=0)

    top_level_process_external_sources(
        config=local_config, studio_id=StudioId(1), creation_datetime=start_time
    )

    df = pl.read_parquet(
        tmp_path
        / "result/external_studios/studio_id=1/version=v1/20200101T010000Z.parquet",
        hive_partitioning=False,
    )

    assert df.to_dicts() == [
        {
            "studio_id": 1,
            "organization_id": "o-EGQfNf",
            "email": "<EMAIL>",
            "company_name": "IndieBI Test DEV",
            "is_test_account": True,
            "is_verified": True,
            "agreement_date": datetime(2022, 10, 18, 11, 53, 25, tzinfo=timezone.utc),
            "studio_parent_id": None,
        }
    ]


def test_creates_external_shared_file(local_config, tmp_path):
    start_time = datetime(year=2020, month=1, day=1, hour=1, minute=0)

    top_level_process_external_sources(
        config=local_config, studio_id=StudioId(1), creation_datetime=start_time
    )

    assert (
        tmp_path
        / "result/external_shared/studio_id=1/version=v1/20200101T010000Z.parquet"
    ).is_file()


def test_external_shared_file_contains_proper_data(local_config, tmp_path):
    start_time = datetime(year=2020, month=1, day=1, hour=1, minute=0)

    top_level_process_external_sources(
        config=local_config, studio_id=StudioId(1), creation_datetime=start_time
    )

    df = pl.read_parquet(
        tmp_path
        / "result/external_shared/studio_id=1/version=v1/20200101T010000Z.parquet",
        hive_partitioning=False,
    )

    assert df.to_dicts() == [
        {
            "id": 45098277,
            "shared_with_id": 2,
            "shared_by_id": 1,
            "product_name": None,
            "date_from": datetime(1900, 1, 1),
            "date_to": datetime(2099, 1, 31),
            "portal_id": None,
            "game_id": None,
        },
        {
            "id": 45100611,
            "shared_with_id": 1,
            "shared_by_id": 1,
            "product_name": None,
            "date_from": datetime(1900, 1, 1),
            "date_to": datetime(2099, 1, 31),
            "portal_id": None,
            "game_id": None,
        },
    ]


def test_creates_external_skus_file(local_config, tmp_path):
    start_time = datetime(year=2020, month=1, day=1, hour=1, minute=0)

    top_level_process_external_sources(
        config=local_config, studio_id=StudioId(1), creation_datetime=start_time
    )

    assert (
        tmp_path
        / "result/external_skus/studio_id=1/version=v1/20200101T010000Z.parquet"
    ).is_file()


def test_external_skus_file_contains_proper_data(local_config, tmp_path):
    start_time = datetime(year=2020, month=1, day=1, hour=1, minute=0)

    top_level_process_external_sources(
        config=local_config, studio_id=StudioId(1), creation_datetime=start_time
    )

    df = pl.read_parquet(
        tmp_path
        / "result/external_skus/studio_id=1/version=v1/20200101T010000Z.parquet",
        hive_partitioning=False,
    )
    assert df.to_dicts() == [
        {
            "store_id": "Unknown",
            "unique_sku_id": "115109-steam:1",
            "studio_id": 1,
            "product_name": None,
            "custom_group": None,
            "ratio": 1,
            "base_sku_id": "115109",
            "human_name": "Dimension Hunter for Beta Testing (retail)",
            "package_name": None,
            "product_type": None,
            "portal": "steam",
            "sku_type": "SALES",
            "human_name_indicator": "sales",
            "is_discountable": True,
        },
        {
            "store_id": "Unknown",
            "unique_sku_id": "115110-steam:1",
            "studio_id": 1,
            "product_name": None,
            "custom_group": None,
            "ratio": 1,
            "base_sku_id": "115110",
            "human_name": "Dimension Hunter",
            "package_name": None,
            "product_type": None,
            "portal": "steam",
            "sku_type": "SALES",
            "human_name_indicator": "sales",
            "is_discountable": False,
        },
        {
            "store_id": "1380126",
            "unique_sku_id": "999110-store:1",
            "studio_id": 1,
            "product_name": None,
            "custom_group": None,
            "ratio": 1,
            "base_sku_id": "999110",
            "human_name": "Dimension Hunter - Complete Edition",
            "package_name": None,
            "product_type": None,
            "portal": "steam",
            "sku_type": "STORE",
            "human_name_indicator": "visibility",
            "is_discountable": False,
        },
    ]


def test_creates_external_country_codes(local_config, tmp_path):
    start_time = datetime(year=2020, month=1, day=1, hour=1, minute=0)

    top_level_process_external_sources(
        config=local_config, studio_id=StudioId(1), creation_datetime=start_time
    )

    assert (
        tmp_path
        / "result/external_country_codes/studio_id=1/version=v1/20200101T010000Z.parquet"
    ).is_file()


def test_external_country_code_file_contains_proper_data(local_config, tmp_path):
    start_time = datetime(year=2020, month=1, day=1, hour=1, minute=0)

    top_level_process_external_sources(
        config=local_config, studio_id=StudioId(1), creation_datetime=start_time
    )

    df = pl.read_parquet(
        tmp_path
        / "result/external_country_codes/studio_id=1/version=v1/20200101T010000Z.parquet",
        hive_partitioning=False,
    )

    assert df.to_dicts()[0] == {
        "country": "Afghanistan",
        "alpha_2_code": "AF",
        "country_code": "AFG",
        "numeric": 4,
        "country_currency": "AFN",
        "region": "Southern Asia",
    }
    assert len(df.to_dicts()) == 249


def test_creates_external_steam_events(local_config, tmp_path):
    start_time = datetime(year=2020, month=1, day=1, hour=1, minute=0)

    top_level_process_external_sources(
        config=local_config, studio_id=StudioId(1), creation_datetime=start_time
    )

    assert (
        tmp_path
        / "result/external_steam_events/studio_id=1/version=v1/20200101T010000Z.parquet"
    ).is_file()


def test_external_steam_events_file_contains_proper_data(local_config, tmp_path):
    start_time = datetime(year=2020, month=1, day=1, hour=1, minute=0)

    top_level_process_external_sources(
        config=local_config, studio_id=StudioId(1), creation_datetime=start_time
    )

    df = pl.read_parquet(
        tmp_path
        / "result/external_steam_events/studio_id=1/version=v1/20200101T010000Z.parquet",
        hive_partitioning=False,
    )

    # Test a fixed event in the past (this one will not change)
    assert df.to_dicts()[-157] == {
        "end_date": datetime(2024, 7, 11).date(),
        "end_day": 11,
        "end_month": 7,
        "end_year": 2024,
        "major": True,
        "name": "Summer Sale 2024",
        "start_date": datetime(2024, 6, 27).date(),
        "start_day": 27,
        "start_month": 6,
        "start_year": 2024,
    }
