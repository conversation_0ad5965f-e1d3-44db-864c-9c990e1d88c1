import datetime

import pytest
import responses
from pydantic import HttpUrl
from responses import matchers

from core_silver.external_sources.connectors.report_service import (
    APIConfig,
    APIReportServiceClient,
)
from data_sdk.domain import Portal
from data_sdk.domain.domain_types import (
    ExternalSKU,
    ReportMetadata,
    ReportState,
    SKUType,
    StudioId,
)
from data_sdk.domain.source import Source


@pytest.fixture
def responses_mock():
    with responses.RequestsMock() as mock:
        yield mock


@pytest.fixture
def report_service_api_client():
    return APIReportServiceClient(
        report_service_config=APIConfig(
            url=HttpUrl("https://report-service.not_existing/"),
            api_key="123",
        )
    )


@pytest.fixture
def raw_report():
    return {
        "source": "steam_sales",
        "studio_id": 2,
        "id": 2,
        "upload_date": "2021-01-27T13:57:44.607000Z",
        "file_path_raw": "STEAM-2018-08-08_2019-07-25.zip",
        "original_name": "STEAM-2018-08-08_2019-07-25.zip",
        "date_from": "2018-08-08",
        "date_to": "2019-07-25",
        "state": ReportState.PENDING.value,
        "no_data": False,
    }


@pytest.fixture
def raw_report_unsupported_sources():
    return [
        {
            "source": "unsupported_source",
            "studio_id": 2,
            "id": 3,
            "upload_date": "2021-01-27T13:57:44.607000Z",
            "file_path_raw": "UNSUPPORTED-2018-08-08_2019-07-25.zip",
            "original_name": "UNSUPPORTED-2018-08-08_2019-07-25.zip",
            "date_from": "2018-08-08",
            "date_to": "2019-07-25",
            "state": ReportState.PENDING.value,
            "no_data": False,
        },
        {
            "source": "another_unsupported_source",
            "studio_id": 2,
            "id": 4,
            "upload_date": "2021-01-27T13:57:44.607000Z",
            "file_path_raw": "ANOTHER_UNSUPPORTED-2018-08-08_2019-07-25.zip",
            "original_name": "ANOTHER_UNSUPPORTED-2018-08-08_2019-07-25.zip",
            "date_from": "2018-08-08",
            "date_to": "2019-07-25",
            "state": ReportState.PENDING.value,
            "no_data": False,
        },
    ]


# TODO - implement better way to test x-api-key
def test_api_client_reports_sends_x_api_key_header(
    responses_mock, report_service_api_client, raw_report
):
    responses_mock.get(
        url="https://report-service.not_existing/reports?studio_id=2&states=PENDING&states=FAILED&states=CONVERTED&limit=100&offset=0",
        json={
            "data": [raw_report],
            "count": 1,
        },
        match=[
            matchers.header_matcher({
                "Content-Type": "application/json",
                "x-api-key": "123",
            })
        ],
        status=200,
    )

    report_service_api_client.get_report_metadata(studio_id=StudioId("2"))


def test_api_client_reports_return_report_metadata(
    responses_mock, report_service_api_client, raw_report
):
    responses_mock.get(
        url="https://report-service.not_existing/reports?studio_id=2&states=PENDING&states=FAILED&states=CONVERTED&limit=100&offset=0",
        json={
            "data": [raw_report],
            "count": 1,
        },
        status=200,
    )

    result = report_service_api_client.get_report_metadata(studio_id=StudioId("2"))

    assert result.root == [
        ReportMetadata(
            source=Source("steam_sales"),
            studio_id=StudioId(2),
            id=2,
            upload_date=datetime.datetime(
                2021, 1, 27, 13, 57, 44, 607000, tzinfo=datetime.timezone.utc
            ),
            file_path_raw="STEAM-2018-08-08_2019-07-25.zip",
            original_name="STEAM-2018-08-08_2019-07-25.zip",
            state=ReportState.PENDING,
            no_data=False,
            date_from=datetime.date(2018, 8, 8),
            date_to=datetime.date(2019, 7, 25),
        )
    ]


def test_reports_reports_pagination_gets_pages_by_100_items(
    responses_mock, report_service_api_client, raw_report
):
    responses_mock.get(
        url="https://report-service.not_existing/reports?studio_id=2&states=PENDING&states=FAILED&states=CONVERTED&limit=100&offset=0",
        json={
            "data": [raw_report] * 100,
            "count": 199,
        },
        status=200,
    )
    responses_mock.get(
        url="https://report-service.not_existing/reports?studio_id=2&states=PENDING&states=FAILED&states=CONVERTED&limit=100&offset=100",
        json={
            "data": [raw_report] * 99,
            "count": 199,
        },
        status=200,
    )

    result = report_service_api_client.get_report_metadata(studio_id=StudioId("2"))

    assert len(result.root) == 199


def test_api_client_skus_return_sku(responses_mock, report_service_api_client):
    responses_mock.get(
        url="https://report-service.not_existing/skus?studio_id=2&limit=100&offset=0",
        json={
            "data": [
                {
                    "store_id": "Unknown",
                    "sku_studio": "115110:2",
                    "studio_id": 2,
                    "product_name": None,
                    "custom_group": None,
                    "ratio": 1,
                    "base_sku_id": "115110",
                    "human_name": "Dimension Hunter",
                    "package_name": None,
                    "product_type": None,
                    "portal": "steam",
                    "sku_type": "SALES",
                    "human_name_indicator": "sales",
                    "is_discountable": False,
                }
            ],
            "count": 1,
        },
        status=200,
    )

    result = report_service_api_client.get_skus(studio_id=StudioId("2"))

    assert result == [
        ExternalSKU(
            store_id="Unknown",
            sku_studio="115110:2",
            studio_id=StudioId(2),
            product_name=None,
            custom_group=None,
            ratio=1,
            base_sku_id="115110",
            human_name="Dimension Hunter",
            package_name=None,
            product_type=None,
            portal=Portal.STEAM,
            sku_type=SKUType.SALES,
            human_name_indicator="sales",
            is_discountable=False,
        )
    ]


def test_unsupported_sources_are_filtered_out(
    responses_mock,
    report_service_api_client,
    raw_report,
    raw_report_unsupported_sources,
):
    responses_mock.get(
        url="https://report-service.not_existing/reports?studio_id=2&states=PENDING&states=FAILED&states=CONVERTED&limit=100&offset=0",
        json={
            "data": [raw_report] + raw_report_unsupported_sources,
            "count": 3,
        },
        status=200,
    )

    result = report_service_api_client.get_report_metadata(studio_id=StudioId("2"))

    assert len(result.root) == 1
    assert result.root[0].source == Source("steam_sales")
