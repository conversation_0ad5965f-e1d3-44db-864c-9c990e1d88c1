from datetime import datetime

import pytest
from dateutil.tz import tzutc

from core_silver.external_sources.connectors.user_service import (
    FeatureFlag,
    InsufficientStudioPermission,
)
from core_silver.external_sources.shared import SharedExternalProcessor
from core_silver.external_sources.studios import StudiosExternalProcessor
from data_sdk.domain.domain_types import OrganizationId, ProductName, StudioId, UserId
from data_sdk.domain.users import (
    BaseOrganization,
    BaseUser,
    Organization,
    Permission,
    PermissionName,
    Role,
    RoleAssignment,
    Shared,
    Studio,
    User,
)


# TODO - implement better way to test x-api-key
@pytest.mark.usefixtures("responses_mock", "_mock_get_user_by_legacy_studio_id")
def test_api_client_user_service_sends_x_api_key_header(user_service_api_client):
    user_service_api_client.get_user(studio_id=StudioId("1"))


@pytest.mark.usefixtures("responses_mock", "_mock_get_user_by_legacy_studio_id")
def test_return_correct_studio_metadata(user_service_api_client):
    result = user_service_api_client.get_user(studio_id=StudioId(1))

    assert result == User(
        email="<EMAIL>",
        first_name="R",
        last_name="Bi",
        company_name="IndieBI Test DEV",
        test_account=True,
        id=UserId("u-iVi2pB"),
        legacy_id=StudioId(1),
        verified=True,
        email_verification_token="123456",  # noqa: S106
        email_verification_deadline=datetime(
            2022, 10, 21, 11, 53, 25, 123000, tzinfo=tzutc()
        ),
        reset_password_token="sample_token",  # noqa: S106
        reset_password_deadline=datetime(2023, 2, 4, 13, 3, 19, 123000, tzinfo=tzutc()),
        agreement_date=datetime(2022, 10, 18, 11, 53, 25, 987000, tzinfo=tzutc()),
        unsuccessful_verification_attempts=0,
        force_password_change=False,
    )


@pytest.mark.usefixtures("responses_mock", "_mock_get_organization_by_legacy_studio_id")
def test_return_correct_organization(user_service_api_client):
    result = user_service_api_client.get_organization(studio_id=StudioId(1))

    assert result == Organization(
        name="IndieBI Test DEV",
        legacy_id=StudioId(1),
        id=OrganizationId("o-EGQfNf"),
    )


@pytest.mark.usefixtures(
    "responses_mock", "_mock_get_list_of_permissions_by_legacy_studio_id"
)
def test_return_correct_permissions_metadata(user_service_api_client):
    result = user_service_api_client.get_permissions(
        user_id=UserId("u-iVi2pB"), organization_id=OrganizationId("o-EGQfNf")
    )

    assert result == [
        Permission(
            organization_id=OrganizationId("o-EGQfNf"),
            name=PermissionName.DATASET_MANAGER_VIEW_DASHBOARDS,
            filters=[],
        ),
        Permission(
            organization_id=OrganizationId("o-EGQfNf"),
            name=PermissionName.REPORT_SERVICE_READ_REPORTS,
            filters=[],
        ),
        Permission(
            organization_id=OrganizationId("o-EGQfNf"),
            name=PermissionName.REPORT_SERVICE_READ_SKUS,
            filters=[],
        ),
        Permission(
            organization_id=OrganizationId("o-EGQfNf"),
            name=PermissionName.REPORT_SERVICE_MODIFY_REPORTS,
            filters=[],
        ),
        Permission(
            organization_id=OrganizationId("o-EGQfNf"),
            name=PermissionName.REPORT_SERVICE_MODIFY_SKUS,
            filters=[],
        ),
        Permission(
            organization_id=OrganizationId("o-EGQfNf"),
            name=PermissionName.REPORT_SERVICE_UPLOAD_REPORTS,
            filters=[],
        ),
        Permission(
            organization_id=OrganizationId("o-EGQfNf"),
            name=PermissionName.USER_SERVICE_V2_ASSIGN_PERMISSIONS,
            filters=[],
        ),
    ]


@pytest.mark.usefixtures(
    "responses_mock", "_mock_get_list_of_feature_flags_for_organization_id"
)
def test_return_feature_flags(user_service_api_client):
    result = user_service_api_client.get_feature_flags(
        organization_id=OrganizationId("o-EGQfNf")
    )

    assert result == [
        FeatureFlag(
            name="discountDeduplication",
            namespace="data_pipeline",
        ),
        FeatureFlag(
            name="payment",
            namespace="data_pipeline",
        ),
    ]


@pytest.mark.usefixtures(
    "responses_mock", "_mock_get_list_of_feature_flags_for_organization_id_when_empty"
)
def test_return_empty_array_of_feature_flags(user_service_api_client):
    result = user_service_api_client.get_feature_flags(
        organization_id=OrganizationId("o-EGQfNf")
    )

    assert result == []


@pytest.mark.usefixtures(
    "responses_mock",
    "_mock_get_user_by_legacy_studio_id",
    "_mock_get_organization_by_legacy_studio_id",
    "_mock_get_list_of_permissions_by_legacy_studio_id",
)
def test_return_correct_studio_metadata_for_studio_id(user_service_api_client):
    result = StudiosExternalProcessor(user_service_api_client).get_studio_metadata(
        studio_id=StudioId(1)
    )

    assert result == Studio(
        studio_id=StudioId(1),
        organization_id=OrganizationId("o-EGQfNf"),
        email="<EMAIL>",
        company_name="IndieBI Test DEV",
        is_test_account=True,
        is_verified=True,
        agreement_date=datetime(2022, 10, 18, 11, 53, 25, 987000, tzinfo=tzutc()),
        studio_parent_id=None,
    )


@pytest.mark.usefixtures(
    "responses_mock",
    "_mock_get_user_by_legacy_studio_id",
    "_mock_get_organization_by_legacy_studio_id",
    "_mock_get_limited_list_of_permissions_by_legacy_studio_id",
)
def test_raise_error_for_legacy_studio_id_with_insufficient_permission(
    user_service_api_client,
):
    with pytest.raises(InsufficientStudioPermission):
        StudiosExternalProcessor(user_service_api_client).get_studio_metadata(
            studio_id=StudioId(1)
        )


@pytest.mark.usefixtures(
    "responses_mock",
    "_mock_get_list_of_view_dashboard_role_assignments",
)
def test_return_list_of_view_dashboard_role_assignments(user_service_api_client):
    result = user_service_api_client.get_view_dashboard_role_assignments(
        organization_id=OrganizationId("o-EGQfNf")
    )

    assert result == [
        RoleAssignment(
            user_id=UserId("u-t8eA5o"),
            organization_id=OrganizationId("o-EQfNf"),
            role=Role.EXTERNAL_READER,
            filter=None,
            created_by_user_id=None,
            id=********,
            created_at=datetime(2024, 2, 7, 11, 50, 0, 863000, tzinfo=tzutc()),
            updated_at=datetime(2024, 2, 7, 11, 50, 0, 863000, tzinfo=tzutc()),
            user=BaseUser(legacy_id=StudioId(2), email="<EMAIL>"),
            organization=BaseOrganization(
                legacy_id=StudioId(1),
                name="IndieBI Test DEV",
            ),
        ),
        RoleAssignment(
            user_id=UserId("u-iVi2pB"),
            organization_id=OrganizationId("o-EGQfNf"),
            role=Role.OWNER,
            filter=None,
            created_by_user_id=None,
            id=********,
            created_at=datetime(2024, 2, 7, 13, 51, 24, 147000, tzinfo=tzutc()),
            updated_at=datetime(2024, 2, 7, 13, 51, 24, 147000, tzinfo=tzutc()),
            user=BaseUser(
                legacy_id=StudioId(1),
                email="<EMAIL>",
            ),
            organization=BaseOrganization(
                legacy_id=StudioId(1), name="IndieBI Test DEV"
            ),
        ),
    ]


@pytest.mark.usefixtures(
    "responses_mock",
    "_mock_get_organization_by_legacy_studio_id",
    "_mock_get_list_of_view_dashboard_role_assignments",
)
def test_return_list_of_shared_accounts(user_service_api_client):
    result = SharedExternalProcessor(user_service_api_client)._get_shared(
        studio_id=StudioId(1)
    )

    assert result == [
        Shared(
            id=********,
            shared_with_id=StudioId(2),
            shared_by_id=StudioId(1),
            product_name=None,
            date_from=datetime(1900, 1, 1, tzinfo=tzutc()),
            date_to=datetime(2099, 1, 31, tzinfo=tzutc()),
            portal_id=None,
            game_id=None,
        ),
        Shared(
            id=********,
            shared_with_id=StudioId(1),
            shared_by_id=StudioId(1),
            product_name=None,
            date_from=datetime(1900, 1, 1, tzinfo=tzutc()),
            date_to=datetime(2099, 1, 31, tzinfo=tzutc()),
            portal_id=None,
            game_id=None,
        ),
    ]


@pytest.mark.usefixtures(
    "responses_mock",
    "_mock_get_list_of_view_dashboard_role_assignments_with_shared_products",
)
def test_return_list_of_view_dashboard_role_assignments_with_role_assignment(
    user_service_api_client,
):
    result = user_service_api_client.get_view_dashboard_role_assignments(
        organization_id=OrganizationId("o-EGQfNf")
    )

    assert result == [
        RoleAssignment(
            user_id=UserId("u-111111"),
            organization_id=OrganizationId("o-EQfNf"),
            role=Role.READER,
            filter={"product": [ProductName("A")]},
            created_by_user_id=None,
            id=********,
            created_at=datetime(2024, 2, 7, 11, 50, 0, 863000, tzinfo=tzutc()),
            updated_at=datetime(2024, 2, 7, 11, 50, 0, 863000, tzinfo=tzutc()),
            user=BaseUser(legacy_id=StudioId(2), email="<EMAIL>"),
            organization=BaseOrganization(
                legacy_id=StudioId(1),
                name="IndieBI Test DEV",
            ),
        ),
        RoleAssignment(
            user_id=UserId("u-222222"),
            organization_id=OrganizationId("o-EQfNf"),
            role=Role.READER,
            filter={"product": [ProductName("X"), ProductName("Y")]},
            created_by_user_id=None,
            id=********,
            created_at=datetime(2024, 2, 7, 11, 50, 0, 863000, tzinfo=tzutc()),
            updated_at=datetime(2024, 2, 7, 11, 50, 0, 863000, tzinfo=tzutc()),
            user=BaseUser(legacy_id=StudioId(3), email="<EMAIL>"),
            organization=BaseOrganization(
                legacy_id=StudioId(1),
                name="IndieBI Test DEV",
            ),
        ),
        RoleAssignment(
            user_id=UserId("u-iVi2pB"),
            organization_id=OrganizationId("o-EGQfNf"),
            role=Role.OWNER,
            filter=None,
            created_by_user_id=None,
            id=********,
            created_at=datetime(2024, 2, 7, 13, 51, 24, 147000, tzinfo=tzutc()),
            updated_at=datetime(2024, 2, 7, 13, 51, 24, 147000, tzinfo=tzutc()),
            user=BaseUser(
                legacy_id=StudioId(1),
                email="<EMAIL>",
            ),
            organization=BaseOrganization(
                legacy_id=StudioId(1), name="IndieBI Test DEV"
            ),
        ),
    ]


@pytest.mark.usefixtures(
    "responses_mock",
    "_mock_get_organization_by_legacy_studio_id",
    "_mock_get_list_of_view_dashboard_role_assignments_with_shared_products",
)
def test_return_list_of_shared_accounts_with_shared_products(user_service_api_client):
    result = SharedExternalProcessor(user_service_api_client)._get_shared(
        studio_id=StudioId(1)
    )

    assert result == [
        Shared(
            id=********,
            shared_with_id=StudioId(2),
            shared_by_id=StudioId(1),
            product_name=ProductName("A"),
            date_from=datetime(1900, 1, 1, 0, 0, tzinfo=tzutc()),
            date_to=datetime(2099, 1, 31, 0, 0, tzinfo=tzutc()),
            portal_id=None,
            game_id=None,
        ),
        Shared(
            id=********,
            shared_with_id=StudioId(3),
            shared_by_id=StudioId(1),
            product_name=ProductName("X"),
            date_from=datetime(1900, 1, 1, 0, 0, tzinfo=tzutc()),
            date_to=datetime(2099, 1, 31, 0, 0, tzinfo=tzutc()),
            portal_id=None,
            game_id=None,
        ),
        Shared(
            id=********,
            shared_with_id=StudioId(3),
            shared_by_id=StudioId(1),
            product_name=ProductName("Y"),
            date_from=datetime(1900, 1, 1, 0, 0, tzinfo=tzutc()),
            date_to=datetime(2099, 1, 31, 0, 0, tzinfo=tzutc()),
            portal_id=None,
            game_id=None,
        ),
        Shared(
            id=********,
            shared_with_id=StudioId(1),
            shared_by_id=StudioId(1),
            product_name=None,
            date_from=datetime(1900, 1, 1, 0, 0, tzinfo=tzutc()),
            date_to=datetime(2099, 1, 31, 0, 0, tzinfo=tzutc()),
            portal_id=None,
            game_id=None,
        ),
    ]


@pytest.mark.usefixtures("responses_mock", "_known_and_unknown_permissions")
def test_returns_only_known_permissions_and_ignores_unknown_permissions(
    user_service_api_client,
):
    result = user_service_api_client.get_permissions(
        user_id=UserId("u-iVi2pB"), organization_id=OrganizationId("o-EGQfNf")
    )
    assert result == [
        Permission(
            organization_id=OrganizationId("o-EGQfNf"),
            name=PermissionName.DATASET_MANAGER_VIEW_DASHBOARDS,
            filters=[],
        ),
    ]
