import pytest
import responses
from pydantic import HttpUrl
from responses import matchers

from core_silver.external_sources.connectors.api import APIConfig
from core_silver.external_sources.connectors.user_service import APIUserServiceClient


@pytest.fixture
def responses_mock():
    with responses.RequestsMock() as mock:
        yield mock


@pytest.fixture
def user_service_api_client():
    return APIUserServiceClient(
        user_service_config=APIConfig(
            url=HttpUrl("https://user-service.not_existing/"),
            api_key="123",
        )
    )


@pytest.fixture
def _mock_get_user_by_legacy_studio_id(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        url="https://user-service.not_existing/user/legacy/1",
        json={
            "email": "<EMAIL>",
            "first_name": "R",
            "last_name": "Bi",
            "company_name": "IndieBI Test DEV",
            "test_account": True,
            "id": "u-iVi2pB",
            "legacy_id": 1,
            "verified": True,
            "email_verification_token": "123456",
            "email_verification_deadline": "2022-10-21T11:53:25.123Z",
            "reset_password_token": "sample_token",
            "reset_password_deadline": "2023-02-04T13:03:19.123Z",
            "agreement_date": "2022-10-18T11:53:25.987Z",
            "unsuccessful_verification_attempts": 0,
            "force_password_change": False,
        },
        match=[
            matchers.header_matcher(
                headers={
                    "Content-Type": "application/json",
                    "x-api-key": "123",
                }
            )
        ],
        status=200,
    )


@pytest.fixture
def _mock_get_organization_by_legacy_studio_id(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        url="https://user-service.not_existing/organization/legacy/1",
        json={"name": "IndieBI Test DEV", "legacy_id": 1, "id": "o-EGQfNf"},
        match=[
            matchers.header_matcher(
                headers={
                    "Content-Type": "application/json",
                    "x-api-key": "123",
                }
            )
        ],
        status=200,
    )


@pytest.fixture
def _mock_get_list_of_permissions_by_legacy_studio_id(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        url="https://user-service.not_existing/user/u-iVi2pB/permissions?organization_id=o-EGQfNf",
        json=[
            {
                "organization_id": "o-EGQfNf",
                "name": "DATASET_MANAGER_VIEW_DASHBOARDS",
                "filters": [],
            },
            {
                "organization_id": "o-EGQfNf",
                "name": "REPORT_SERVICE_READ_REPORTS",
                "filters": [],
            },
            {
                "organization_id": "o-EGQfNf",
                "name": "REPORT_SERVICE_READ_SKUS",
                "filters": [],
            },
            {
                "organization_id": "o-EGQfNf",
                "name": "REPORT_SERVICE_MODIFY_REPORTS",
                "filters": [],
            },
            {
                "organization_id": "o-EGQfNf",
                "name": "REPORT_SERVICE_MODIFY_SKUS",
                "filters": [],
            },
            {
                "organization_id": "o-EGQfNf",
                "name": "REPORT_SERVICE_UPLOAD_REPORTS",
                "filters": [],
            },
            {
                "organization_id": "o-EGQfNf",
                "name": "USER_SERVICE_V2_ASSIGN_PERMISSIONS",
                "filters": [],
            },
        ],
        match=[
            matchers.header_matcher(
                headers={
                    "Content-Type": "application/json",
                    "x-api-key": "123",
                }
            )
        ],
        status=200,
    )


@pytest.fixture
def _mock_get_list_of_feature_flags_for_organization_id(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        url="https://user-service.not_existing/organization-key-value/search",
        json={
            "data": [
                {
                    "key": "feature.discountDeduplication",
                    "namespace": "data_pipeline",
                    "organization_id": "o-EGQfNf",
                    "value": "True",
                    "version": 0,
                },
                {
                    "key": "feature.payment",
                    "namespace": "data_pipeline",
                    "organization_id": "o-EGQfNf",
                    "value": "True",
                    "version": 0,
                },
            ],
            "count": 2,
        },
        match=[
            matchers.header_matcher(
                headers={
                    "Content-Type": "application/json",
                    "x-api-key": "123",
                }
            ),
            matchers.query_param_matcher(
                params={
                    "namespace": "data_pipeline",
                    "organization_id": "o-EGQfNf",
                    "key": "feautres.*",
                    "value": "True",
                }
            ),
        ],
        status=200,
    )


@pytest.fixture
def _mock_get_list_of_feature_flags_for_organization_id_when_empty(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        url="https://user-service.not_existing/organization-key-value/search",
        json={
            "data": [],
            "count": 2,
        },
        match=[
            matchers.header_matcher(
                headers={
                    "Content-Type": "application/json",
                    "x-api-key": "123",
                }
            ),
            matchers.query_param_matcher(
                params={
                    "namespace": "data_pipeline",
                    "organization_id": "o-EGQfNf",
                    "key": "feautres.*",
                    "value": "True",
                }
            ),
        ],
        status=200,
    )


@pytest.fixture
def _mock_get_limited_list_of_permissions_by_legacy_studio_id(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        url="https://user-service.not_existing/user/u-iVi2pB/permissions?organization_id=o-EGQfNf",
        json=[
            {
                "organization_id": "o-EGQfNf",
                "name": "DATASET_MANAGER_VIEW_DASHBOARDS",
                "filters": [],
            },
            {
                "organization_id": "o-EGQfNf",
                "name": "REPORT_SERVICE_READ_REPORTS",
                "filters": [],
            },
            {
                "organization_id": "o-EGQfNf",
                "name": "REPORT_SERVICE_READ_SKUS",
                "filters": [],
            },
            {
                "organization_id": "o-EGQfNf",
                "name": "REPORT_SERVICE_MODIFY_REPORTS",
                "filters": [],
            },
            {
                "organization_id": "o-EGQfNf",
                "name": "REPORT_SERVICE_MODIFY_SKUS",
                "filters": [],
            },
            {
                "organization_id": "o-EGQfNf",
                "name": "USER_SERVICE_V2_ASSIGN_PERMISSIONS",
                "filters": [],
            },
        ],
        match=[
            matchers.header_matcher(
                headers={
                    "Content-Type": "application/json",
                    "x-api-key": "123",
                }
            )
        ],
        status=200,
    )


@pytest.fixture
def _known_and_unknown_permissions(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        url="https://user-service.not_existing/user/u-iVi2pB/permissions?organization_id=o-EGQfNf",
        json=[
            {
                "organization_id": "o-EGQfNf",
                "name": "DATASET_MANAGER_VIEW_DASHBOARDS",
                "filters": [],
            },
            {
                "organization_id": "o-EGQfNf",
                "name": "DESKTOP_APP_LEGACY_PARENT",
                "filters": [],
            },
        ],
        status=200,
    )


@pytest.fixture
def _mock_get_list_of_view_dashboard_role_assignments(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        url="https://user-service.not_existing/role-assignment",
        json={
            "data": [
                {
                    "user_id": "u-t8eA5o",
                    "organization_id": "o-EQfNf",
                    "role": "EXTERNAL_READER",
                    "filter": None,
                    "created_by_user_id": None,
                    "id": 45098277,
                    "created_at": "2024-02-07T11:50:00.863Z",
                    "updated_at": "2024-02-07T11:50:00.863Z",
                    "user": {"legacy_id": 2, "email": "<EMAIL>"},
                    "organization": {"legacy_id": 1, "name": "IndieBI Test DEV"},
                },
                {
                    "user_id": "u-iVi2pB",
                    "organization_id": "o-EGQfNf",
                    "role": "OWNER",
                    "filter": None,
                    "created_by_user_id": None,
                    "id": 45100611,
                    "created_at": "2024-02-07T13:51:24.147Z",
                    "updated_at": "2024-02-07T13:51:24.147Z",
                    "user": {"legacy_id": 1, "email": "<EMAIL>"},
                    "organization": {"legacy_id": 1, "name": "IndieBI Test DEV"},
                },
            ],
            "count": 2,
        },
        match=[
            matchers.header_matcher(
                headers={
                    "Content-Type": "application/json",
                    "x-api-key": "123",
                }
            ),
            matchers.query_param_matcher(
                params={
                    "organization_id": "o-EGQfNf",
                    "permission": "DATASET_MANAGER_VIEW_DASHBOARDS",
                    "limit": 100,
                    "offset": 0,
                }
            ),
        ],
        status=200,
    )


@pytest.fixture
def _mock_get_list_of_view_dashboard_role_assignments_with_shared_products(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        url="https://user-service.not_existing/role-assignment",
        json={
            "data": [
                {
                    "user_id": "u-111111",
                    "organization_id": "o-EQfNf",
                    "role": "READER",
                    "filter": {"product": "A"},
                    "created_by_user_id": None,
                    "id": 45098277,
                    "created_at": "2024-02-07T11:50:00.863Z",
                    "updated_at": "2024-02-07T11:50:00.863Z",
                    "user": {"legacy_id": 2, "email": "<EMAIL>"},
                    "organization": {"legacy_id": 1, "name": "IndieBI Test DEV"},
                },
                {
                    "user_id": "u-222222",
                    "organization_id": "o-EQfNf",
                    "role": "READER",
                    "filter": {"product": ["X", "Y"]},
                    "created_by_user_id": None,
                    "id": 45098288,
                    "created_at": "2024-02-07T11:50:00.863Z",
                    "updated_at": "2024-02-07T11:50:00.863Z",
                    "user": {"legacy_id": 3, "email": "<EMAIL>"},
                    "organization": {"legacy_id": 1, "name": "IndieBI Test DEV"},
                },
                {
                    "user_id": "u-iVi2pB",
                    "organization_id": "o-EGQfNf",
                    "role": "OWNER",
                    "filter": None,
                    "created_by_user_id": None,
                    "id": 45100611,
                    "created_at": "2024-02-07T13:51:24.147Z",
                    "updated_at": "2024-02-07T13:51:24.147Z",
                    "user": {"legacy_id": 1, "email": "<EMAIL>"},
                    "organization": {"legacy_id": 1, "name": "IndieBI Test DEV"},
                },
            ],
            "count": 3,
        },
        match=[
            matchers.header_matcher(
                headers={
                    "Content-Type": "application/json",
                    "x-api-key": "123",
                }
            ),
            matchers.query_param_matcher(
                params={
                    "organization_id": "o-EGQfNf",
                    "permission": "DATASET_MANAGER_VIEW_DASHBOARDS",
                    "limit": 100,
                    "offset": 0,
                }
            ),
        ],
        status=200,
    )
