import pandas as pd
import polars as pl


def compare_dataframes(actual: pd.DataFrame, expected: pd.DataFrame):
    actual = actual.sort_index(axis=1)
    expected = expected.sort_index(axis=1)
    actual_columns = set(actual.columns)
    expected_columns = set(expected.columns)

    missing_in_actual = expected_columns - actual_columns
    extra_in_actual = actual_columns - expected_columns

    if missing_in_actual or extra_in_actual:
        error_message = "Columns don't match.\n"
        if missing_in_actual:
            missing_columns_str = ", ".join(sorted(missing_in_actual))
            error_message += f"Missing columns in actual: {missing_columns_str}\n"
        if extra_in_actual:
            extra_columns_str = ", ".join(sorted(extra_in_actual))
            error_message += f"Extra columns in actual: {extra_columns_str}\n"
        raise AssertionError(error_message)

    actual_rows = len(actual)
    expected_rows = len(expected)

    assert (
        actual_rows == expected_rows
    ), f"Number of rows don't match: {actual_rows} != {expected_rows}"

    try:
        actual = actual.sort_values(by=list(actual.columns)).reset_index(drop=True)
        expected = expected.sort_values(by=list(actual.columns)).reset_index(drop=True)
        diff = actual.compare(expected, keep_equal=True)
    except ValueError as e:
        raise AssertionError(f"Error in comparing dataframes: {e}") from e  # noqa: TRY003

    if len(diff) != 0:
        print("Dataframes are different")  # noqa: T201
    assert (
        len(diff) == 0
    ), f"DataFrames are different, diff:\n{diff.to_string(max_rows=20)}"
    return True


def compare_polars_dataframes(actual: pl.DataFrame, expected: pl.DataFrame):
    # TODO change it to more native polars comparator
    compare_dataframes(
        convert_categorical_to_str(actual.to_pandas()),
        convert_categorical_to_str(expected.to_pandas()),
    )
    # to be sure that cenvertion to pandas don't break anything
    assert actual.equals(expected)
    return True


def convert_categorical_to_str(df: pd.DataFrame) -> pd.DataFrame:
    cat_cols = df.select_dtypes(include=["category"]).columns
    for col in cat_cols:
        df[col] = df[col].astype(str)
    return df
