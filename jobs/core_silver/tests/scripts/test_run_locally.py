import pytest

from data_sdk.domain import Portal
from data_sdk.domain.observations import ObservationType
from scripts.run_locally import InputEnv, OutputEnv, main


@pytest.mark.parametrize(
    ("input_env", "output_env", "studio_id", "observation_type", "portal"),
    [
        (InputEnv.LOCAL, OutputEnv.LOCAL, 1, ObservationType.SALES, Portal.STEAM),
        (InputEnv.LOCAL, OutputEnv.LOCAL, 1, ObservationType.SALES, Portal.NINTENDO),
        # Add more parameter sets here if needed
    ],
)
def test_run_locally(
    tmp_path, input_env, output_env, studio_id, observation_type, portal
):
    main(
        input_env=input_env,
        output_env=output_env,
        studio_id=studio_id,
        observation_type=observation_type,
        portal=portal,
        local_output_dir=str(tmp_path),
    )
