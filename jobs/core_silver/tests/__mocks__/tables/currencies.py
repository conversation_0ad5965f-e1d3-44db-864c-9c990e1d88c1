from datetime import datetime

import polars as pl

from data_sdk.domain.tables import ExternalCurrencyExchangeRatesTable

# Before Core Silver, converters used hardcoded values for currency exchange
# rates (yeah, really!). To not override outputs of all tests, we actually use those old
# values as values for 2000-01-01. Because algorithm in case of missing currency rate
# for a specific date should use the last available rate, this in practice means, that
# we can use old outputs for tests

_currency_exchanges_hardcoded_in_the_past = {
    "USD": 1,
    "ARS": 20.17,
    "EUR": 0.8819,
    "BRL": 3.406,
    "CAD": 1.257,
    "CHF": 0.9683,
    "CLP": 595.95,
    "CNY": 6.284,
    "COP": 3610.57,
    "GBP": 0.7038,
    "HKD": 7.849,
    "ILS": 3.518,
    "INR": 65.62,
    "JPY": 107.23,
    "KRW": 1106.91,
    "KZT": 328.03,
    "MXN": 17.99,
    "NOK": 7.771,
    "NZD": 1.367,
    "PHP": 52.05,
    "PLN": 3.372,
    "QAR": 3.64,
    "RUB": 61.61,
    "SAR": 3.75,
    "SGD": 1.31,
    "THB": 31.23,
    "TRY": 4.104,
    "TWD": 29.35,
    "AED": 3.672,
    "IDR": 14129.42,
    "KWD": 0.3,
    "MYR": 3.889,
    "UAH": 26.08,
    "ZAR": 11.98,
    "PEN": 3.22,
    "UYU": 28.2,
    "SEK": 9.06,
    "AUD": 1.38,
    "CZK": 22.889,
    "DKK": 6.57,
    "HUF": 283.527,
    "VND": 23720,
    "RON": 1.63,
    "PKR": 267.06,
    "EGP": 30.72,
    "BGN": 1.84,
    "NGN": 460.47,
    "TZS": 2339.5,
    "HRK": 7.03,
    "IQD": 1316.47,
    "CRC": 542.86,
    "BOB": 6.94,
    "MAD": 10,
    "RSD": 107.9,
    "PYG": 7308.92,
    "JOD": 0.71,
    "LKR": 308.66,
    "GHS": 11.40,
    "DZD": 135.85,
    "KES": 140.45,
    "BDT": 108.67,
    "GEL": 2.64,
    "LBP": 15009.95,
    "MMK": 2099.32,
    "MOP": 8.07,
}

external_currency_exchange_rates_table = ExternalCurrencyExchangeRatesTable(
    df=pl.DataFrame([
        {
            "date": datetime.strptime("2000-01-01", "%Y-%m-%d"),
            "currency_code": currency,
            "rate_to_usd": rate_to_usd,
        }
        for currency, rate_to_usd in _currency_exchanges_hardcoded_in_the_past.items()
    ])
)
