from unittest.mock import Mock, patch

import polars as pl

from core_silver.external_sources.connectors.report_service import ReportServiceClient
from core_silver.reporters.status_report_service_reporter import (
    StatusReportServiceReporter,
)
from data_sdk.domain.tables import ExternalReportsTable, SilverReportsTable


@patch("core_silver.reporters.REPORTING_ENABLED", new=False)
def test_status_report_service_reporter_should_not_report_if_reporting_is_disabled():
    report_service_client_mock = Mock(spec=ReportServiceClient)
    reporter = StatusReportServiceReporter(
        report_service_client=report_service_client_mock,
        external_reports=ExternalReportsTable.empty(),
        silver_reports=SilverReportsTable.empty(),
    )
    reporter.process()

    report_service_client_mock.send_reports.assert_not_called()


@patch("core_silver.reporters.REPORTING_ENABLED", new=True)
def test_status_report_service_reporter_should_report_nothing_when_no_reports():
    report_service_client_mock = Mock(spec=ReportServiceClient)
    reporter = StatusReportServiceReporter(
        report_service_client=report_service_client_mock,
        external_reports=ExternalReportsTable.empty(),
        silver_reports=SilverReportsTable.empty(),
    )
    reporter.process()

    report_service_client_mock.send_reports.assert_called_with([])


@patch("core_silver.reporters.REPORTING_ENABLED", new=True)
def test_status_report_service_reporter_should_report_nothing_if_nothing_changed(
    external_steam_reports_factory,
):
    report_service_client_mock = Mock(spec=ReportServiceClient)
    external_reports = external_steam_reports_factory.build_table(size=3)

    external_reports_table = ExternalReportsTable(
        df=pl.DataFrame(external_reports.df.to_dicts())
    )
    silver_reports_table = SilverReportsTable.empty()

    reporter = StatusReportServiceReporter(
        report_service_client=report_service_client_mock,
        external_reports=external_reports_table,
        silver_reports=silver_reports_table,
    )
    reporter.process()

    report_service_client_mock.send_reports.assert_called_with([])


@patch("core_silver.reporters.REPORTING_ENABLED", new=True)
def test_status_report_service_reporter_should_report_updated_reports(
    external_steam_reports_factory,
):
    report_service_client_mock = Mock(spec=ReportServiceClient)
    external_reports = external_steam_reports_factory.build_table(size=3).df.to_dicts()

    external_reports[0]["state"] = "CONVERTED"

    external_reports_table = ExternalReportsTable(df=pl.DataFrame(external_reports))

    silver_reports = external_reports
    silver_reports[1]["state"] = "CONVERTED"
    silver_reports[2]["state"] = "FAILED"
    silver_reports_table = SilverReportsTable(df=pl.DataFrame(silver_reports))

    reporter = StatusReportServiceReporter(
        report_service_client=report_service_client_mock,
        external_reports=external_reports_table,
        silver_reports=silver_reports_table,
    )
    reporter.process()

    expected_updated_report = [
        {"id": 1, "state": "CONVERTED"},
        {"id": 2, "state": "FAILED"},
    ]
    report_service_client_mock.send_reports.assert_called_with(expected_updated_report)
