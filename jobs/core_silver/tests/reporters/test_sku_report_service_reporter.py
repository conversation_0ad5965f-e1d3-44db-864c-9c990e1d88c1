from unittest.mock import Mock, patch

import polars as pl
import pytest

from core_silver.aggregators.skus import SilverSKUsTable
from core_silver.external_sources.connectors.report_service import (
    ReportServiceClient,
)
from core_silver.reporters.sku_report_service_reporter import (
    CreatedSkuModel,
    SkuReportServiceReporter,
    UpdatedSkuModel,
)
from data_sdk.domain import Portal
from data_sdk.domain.tables import ExternalSKUsTable


@patch("core_silver.reporters.REPORTING_ENABLED", new=False)
def test_sku_report_service_reporter_should_not_report_if_reporting_is_disabled():
    report_service_client_mock = Mock(spec=ReportServiceClient)
    reporter = SkuReportServiceReporter(
        portal=Portal.STEAM,
        external_skus=ExternalSKUsTable.empty(),
        silver_skus=SilverSKUsTable.empty(),
        report_service_client=report_service_client_mock,
    )
    reporter.process()

    report_service_client_mock.send_sku.assert_not_called()


@patch("core_silver.reporters.REPORTING_ENABLED", new=True)
def test_sku_report_service_reporter_should_report_nothing_when_no_skus():
    report_service_client_mock = Mock(spec=ReportServiceClient)
    reporter = SkuReportServiceReporter(
        portal=Portal.STEAM,
        external_skus=ExternalSKUsTable.empty(),
        silver_skus=SilverSKUsTable.empty(),
        report_service_client=report_service_client_mock,
    )
    reporter.process()

    report_service_client_mock.send_sku.assert_called_with([], [])


@patch("core_silver.reporters.REPORTING_ENABLED", new=True)
def test_sku_report_service_reporter_should_report_nothing_if_nothing_changed(
    silver_skus_store_factory, external_skus_sales_factory
):
    report_service_client_mock = Mock(spec=ReportServiceClient)
    external_skus_table = external_skus_sales_factory.build_table(size=3)
    default_silver_skus_values = silver_skus_store_factory.build_table(
        size=1
    ).df.to_dicts()[0]
    silver_skus_data = [
        {
            **default_silver_skus_values,
            **row,
        }
        for row in external_skus_table.df.to_dicts()
    ]

    silver_skus_table = SilverSKUsTable(df=pl.DataFrame(silver_skus_data))

    reporter = SkuReportServiceReporter(
        portal=Portal.STEAM,
        external_skus=external_skus_table,
        silver_skus=silver_skus_table,
        report_service_client=report_service_client_mock,
    )
    reporter.process()

    report_service_client_mock.send_sku.assert_called_with([], [])


@patch("core_silver.reporters.REPORTING_ENABLED", new=True)
def test_sku_report_service_reporter_should_report_one_new_sku(
    silver_skus_store_factory, external_skus_sales_factory
):
    report_service_client_mock = Mock(spec=ReportServiceClient)

    silver_skus_table = silver_skus_store_factory.build_table(size=1)

    reporter = SkuReportServiceReporter(
        portal=Portal.STEAM,
        external_skus=external_skus_sales_factory.build_table(size=0),
        silver_skus=silver_skus_table,
        report_service_client=report_service_client_mock,
    )
    reporter.process()

    expected_skus_to_report = CreatedSkuModel.validate(
        silver_skus_table.df.with_columns(
            pl.lit("steam").alias("portal"), pl.col("unique_sku_id").alias("sku_studio")
        ).select(*CreatedSkuModel.to_schema().columns)
    )

    report_service_client_mock.send_sku.assert_called_with(
        expected_skus_to_report.to_dicts(), []
    )


@patch("core_silver.reporters.REPORTING_ENABLED", new=True)
def test_sku_report_service_reporter_should_report_one_new_sku_when_external_skus_tables_contains_more_values(
    silver_skus_store_factory, external_skus_sales_factory
):
    report_service_client_mock = Mock(spec=ReportServiceClient)
    external_skus_table = external_skus_sales_factory.build_table(size=3)
    default_silver_skus_values = silver_skus_store_factory.build_table(
        size=1
    ).df.to_dicts()[0]

    silver_skus_data = [
        {
            **default_silver_skus_values,
            **row,
        }
        for row in external_skus_table.df.to_dicts()
    ]

    new_skus_to_report = silver_skus_store_factory.build_table(size=3).df
    silver_skus_data.extend(new_skus_to_report.to_dicts())

    reporter = SkuReportServiceReporter(
        portal=Portal.STEAM,
        external_skus=external_skus_table,
        silver_skus=SilverSKUsTable(df=pl.DataFrame(silver_skus_data)),
        report_service_client=report_service_client_mock,
    )
    reporter.process()

    expected_skus_to_report = CreatedSkuModel.validate(
        new_skus_to_report.with_columns(
            pl.lit("steam").alias("portal"), pl.col("unique_sku_id").alias("sku_studio")
        ).select(*CreatedSkuModel.to_schema().columns)
    )

    report_service_client_mock.send_sku.assert_called_with(
        expected_skus_to_report.to_dicts(), []
    )


@patch("core_silver.reporters.REPORTING_ENABLED", new=True)
def test_sku_report_service_reporter_should_report_one_new_sku_when_external_skus_tables_contains_case_similar_sku(
    silver_skus_store_factory, external_skus_sales_factory
):
    report_service_client_mock = Mock(spec=ReportServiceClient)
    external_skus_table = external_skus_sales_factory.build_table(
        size=1, unique_sku_id="sku-1"
    )
    silver_skus_table = silver_skus_store_factory.build_table(
        size=1, unique_sku_id="Sku-1"
    )

    reporter = SkuReportServiceReporter(
        portal=Portal.STEAM,
        external_skus=external_skus_table,
        silver_skus=silver_skus_table,
        report_service_client=report_service_client_mock,
    )
    reporter.process()

    expected_skus_to_report = CreatedSkuModel.validate(
        silver_skus_table.df.with_columns(
            pl.lit("steam").alias("portal"), pl.col("unique_sku_id").alias("sku_studio")
        ).select(*CreatedSkuModel.to_schema().columns)
    )

    report_service_client_mock.send_sku.assert_called_with(
        expected_skus_to_report.to_dicts(), []
    )


@pytest.mark.parametrize(
    ("field_to_update", "updated_value"),
    [
        ("human_name", "Updated Name"),
        ("human_name_indicator", "Updated Indicator"),
        ("store_id", "Updated Store ID"),
    ],
)
@patch("core_silver.reporters.REPORTING_ENABLED", new=True)
def test_sku_report_service_reporter_should_report_updated_sku_with_updated_field(
    silver_skus_store_factory,
    external_skus_sales_factory,
    field_to_update,
    updated_value,
):
    report_service_client_mock = Mock(spec=ReportServiceClient)

    external_skus_table = external_skus_sales_factory.build_table(size=1)
    external_skus_data = external_skus_table.df.to_dicts()[0]
    default_silver_sku = silver_skus_store_factory.build_table(size=1).df.to_dicts()[0]
    silver_sku = {
        **default_silver_sku,
        **external_skus_data,
        field_to_update: updated_value,
    }

    silver_skus_table = SilverSKUsTable(df=pl.DataFrame([silver_sku]))

    reporter = SkuReportServiceReporter(
        portal=Portal.STEAM,
        external_skus=external_skus_table,
        silver_skus=silver_skus_table,
        report_service_client=report_service_client_mock,
    )
    reporter.process()

    expected_updated_sku = silver_skus_table.df.rename({
        "unique_sku_id": "sku_studio",
    })[list(UpdatedSkuModel.to_schema().columns.keys())]

    report_service_client_mock.send_sku.assert_called_with(
        [], expected_updated_sku.to_dicts()
    )


@patch("core_silver.reporters.REPORTING_ENABLED", new=True)
def test_sku_report_service_reporter_should_report_updated_sku_with_updated_field_only_for_updated_row(
    silver_skus_store_factory,
    external_skus_sales_factory,
):
    report_service_client_mock = Mock(spec=ReportServiceClient)

    external_skus_table = external_skus_sales_factory.build_table(size=3)
    default_silver_sku = silver_skus_store_factory.build_table(size=1).df.to_dicts()[0]

    silver_skus_data = [
        {
            **default_silver_sku,
            **row,
        }
        for row in external_skus_table.df.to_dicts()
    ]

    row_number_to_update = 2
    silver_skus_data[row_number_to_update] = {
        **default_silver_sku,
        **external_skus_table.df.to_dicts()[row_number_to_update],
        "human_name": "Updated",
    }

    silver_skus_table = SilverSKUsTable(df=pl.DataFrame(silver_skus_data))

    reporter = SkuReportServiceReporter(
        portal=Portal.STEAM,
        external_skus=external_skus_table,
        silver_skus=silver_skus_table,
        report_service_client=report_service_client_mock,
    )
    reporter.process()

    expected_updated_skus = UpdatedSkuModel.validate(
        silver_skus_table.df.rename({"unique_sku_id": "sku_studio"})
        .select(UpdatedSkuModel.to_schema().columns.keys())
        .slice(row_number_to_update, 1)
    ).to_dicts()

    report_service_client_mock.send_sku.assert_called_with([], expected_updated_skus)
