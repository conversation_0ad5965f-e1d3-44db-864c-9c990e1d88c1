import pytest

from core_silver.aggregators.acquisition_properties import (
    AcquisitionPropertiesAggregator,
)
from data_sdk.domain import TableName


@pytest.fixture
def input_data(converted_sales_factory):
    return {
        TableName.OBSERVATION_SALES: converted_sales_factory.build_table(size=1),
    }


def test_acquisition_properties_aggregator_has_proper_schema_validation(input_data):
    acquisition_properites_data = AcquisitionPropertiesAggregator(
        **input_data
    ).aggregate()
    assert acquisition_properites_data.df.is_empty() is False
    AcquisitionPropertiesAggregator.table_cls.model.validate(
        acquisition_properites_data.df.lazy()
    )


def test_acquisition_properties_creates_all_required_columns(input_data):
    acquisition_properites_data = AcquisitionPropertiesAggregator(
        **input_data
    ).aggregate()

    assert acquisition_properites_data.df.columns == [
        "transaction_type",
        "tax_type",
        "sale_modificator",
        "acquisition_platform",
        "acquisition_origin",
        "iap_flag",
        "hash_acquisition_properties",
    ]


def test_acquisition_properties_sets_base_values(input_data):
    acquisition_properites_data = AcquisitionPropertiesAggregator(
        **input_data
    ).aggregate()

    assert acquisition_properites_data.df.to_dicts() == [
        {
            "transaction_type": "Retail",
            "tax_type": "Unknown",
            "sale_modificator": "NOT_APPLICABLE",
            "acquisition_platform": "Windows",
            "acquisition_origin": "MAIN_STORE",
            "iap_flag": "False",
            "hash_acquisition_properties": "49f305a36112f91e640c608818304db5",
        }
    ]


def test_acquisition_properties_with_different_value_should_create_return_hash(
    converted_sales_factory,
):
    input_data = {
        TableName.OBSERVATION_SALES: converted_sales_factory.build_table(
            size=1, acquisition_platform="Other_platform"
        ),
    }

    acquisition_properites_data = AcquisitionPropertiesAggregator(
        **input_data
    ).aggregate()

    assert acquisition_properites_data.df.to_dicts() == [
        {
            "transaction_type": "Retail",
            "tax_type": "Unknown",
            "sale_modificator": "NOT_APPLICABLE",
            "acquisition_platform": "Other_platform",
            "acquisition_origin": "MAIN_STORE",
            "iap_flag": "False",
            "hash_acquisition_properties": "e7d82322ae673bcf5e207da893902a2d",
        }
    ]
