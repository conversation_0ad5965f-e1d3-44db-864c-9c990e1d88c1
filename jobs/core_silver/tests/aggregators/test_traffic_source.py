import polars as pl
import pytest

from core_silver.aggregators.traffic_source.traffic_source import (
    SilverTrafficSourceAggregator,
)
from data_sdk.domain import TableName


@pytest.fixture
def input_data(converted_visibility_factory) -> dict[TableName, pl.DataFrame]:
    return {
        TableName.OBSERVATION_VISIBILITY: converted_visibility_factory.build_table(
            size=1
        ),
    }


def test_traffic_source_aggregator_has_proper_schema_validation(input_data):
    traffic_source_data = SilverTrafficSourceAggregator(**input_data).aggregate()
    assert traffic_source_data.df.is_empty() is False
    SilverTrafficSourceAggregator.table_cls.model.validate(
        traffic_source_data.df.lazy()
    )


def test_traffic_source_creates_all_required_columns(input_data):
    traffic_source_data = SilverTrafficSourceAggregator(**input_data).aggregate()

    assert traffic_source_data.df.columns == [
        "page_category",
        "page_category_group",
        "page_feature",
        "hash_traffic_source",
    ]


def test_traffic_source_sets_base_values(
    input_data,
):
    traffic_source_data = SilverTrafficSourceAggregator(**input_data).aggregate()

    assert traffic_source_data.df.to_dicts() == [
        {
            "page_category": "Browse Search Results",
            "page_category_group": "Search & Browse",
            "page_feature": "Browse Search Results",
            "hash_traffic_source": "8006adebacb4c27c389fd872110fcc7c",
        }
    ]


def test_acquisition_properties_with_different_value_should_create_return_hash(
    converted_visibility_factory,
):
    input_data = {
        TableName.OBSERVATION_VISIBILITY: converted_visibility_factory.build_table(
            size=1, page_category="Other category"
        ),
    }

    traffic_source_data = SilverTrafficSourceAggregator(**input_data).aggregate()

    assert traffic_source_data.df.to_dicts() == [
        {
            "page_category": "Other category",
            "page_category_group": "Other",
            "page_feature": "Browse Search Results",
            "hash_traffic_source": "8006adebacb4c27c389fd872110fcc7c",
        }
    ]
