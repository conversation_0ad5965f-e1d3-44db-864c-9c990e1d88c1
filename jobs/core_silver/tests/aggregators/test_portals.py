import polars as pl
import pytest

from core_silver.aggregators.portals import PortalsAggregator
from data_sdk.domain import DisplayPortal, TableName
from data_sdk.domain.platform import DisplayPlatform
from data_sdk.domain.regions import Region
from data_sdk.domain.stores import Store


@pytest.fixture
def input_data_with_sales_only(
    converted_sales_factory,
    converted_visibility_factory,
    converted_wishlist_actions_factory,
    converted_wishlist_cohorts_factory,
):
    return {
        TableName.OBSERVATION_SALES: converted_sales_factory.build_table(size=1),
        TableName.OBSERVATION_VISIBILITY: converted_visibility_factory.build_table(
            size=0
        ),
        TableName.OBSERVATION_WISHLIST_ACTIONS: converted_wishlist_actions_factory.build_table(
            size=0
        ),
        TableName.OBSERVATION_WISHLIST_COHORTS: converted_wishlist_cohorts_factory.build_table(
            size=0
        ),
    }


def test_empty_input_produces_empty_df(
    converted_sales_factory,
    converted_visibility_factory,
    converted_wishlist_actions_factory,
    converted_wishlist_cohorts_factory,
):
    input_data = {
        TableName.OBSERVATION_SALES: converted_sales_factory.build_table(size=0),
        TableName.OBSERVATION_VISIBILITY: converted_visibility_factory.build_table(
            size=0
        ),
        TableName.OBSERVATION_WISHLIST_ACTIONS: converted_wishlist_actions_factory.build_table(
            size=0
        ),
        TableName.OBSERVATION_WISHLIST_COHORTS: converted_wishlist_cohorts_factory.build_table(
            size=0
        ),
    }

    portals_data = PortalsAggregator(**input_data).aggregate()

    assert portals_data.df.to_dicts() == []


def test_input_with_only_sales(input_data_with_sales_only):
    portals_data = PortalsAggregator(
        **input_data_with_sales_only,
    ).aggregate()

    assert portals_data.df.to_dicts() == [
        {
            "portal": DisplayPortal.STEAM.value,
            "platform": DisplayPlatform.PC.value,
            "region": Region.GLOBAL.value,
            "store_name": Store.STEAM.value,
            "abbreviated_name": "Steam",
            "portal_platform_region": f"{DisplayPortal.STEAM.value}:{DisplayPlatform.PC.value}:{Region.GLOBAL.value}",
        }
    ]


def test_input_with_steam_sales_and_steam_visibility(
    converted_sales_factory,
    converted_visibility_factory,
    converted_wishlist_actions_factory,
    converted_wishlist_cohorts_factory,
):
    input_data = {
        TableName.OBSERVATION_SALES: converted_sales_factory.build_table(size=1),
        TableName.OBSERVATION_VISIBILITY: converted_visibility_factory.build_table(
            size=1
        ),
        TableName.OBSERVATION_WISHLIST_ACTIONS: converted_wishlist_actions_factory.build_table(
            size=0
        ),
        TableName.OBSERVATION_WISHLIST_COHORTS: converted_wishlist_cohorts_factory.build_table(
            size=0
        ),
    }

    portals_data = PortalsAggregator(**input_data).aggregate()

    assert portals_data.df.to_dicts() == [
        {
            "portal": DisplayPortal.STEAM.value,
            "platform": DisplayPlatform.PC.value,
            "region": Region.GLOBAL.value,
            "store_name": Store.STEAM.value,
            "abbreviated_name": "Steam",
            "portal_platform_region": f"{DisplayPortal.STEAM.value}:{DisplayPlatform.PC.value}:{Region.GLOBAL.value}",
        }
    ]


def test_input_with_all_steam_observation_types(
    converted_sales_factory,
    converted_visibility_factory,
    converted_wishlist_actions_factory,
    converted_wishlist_cohorts_factory,
):
    input_data = {
        TableName.OBSERVATION_SALES: converted_sales_factory.build_table(size=1),
        TableName.OBSERVATION_VISIBILITY: converted_visibility_factory.build_table(
            size=2
        ),
        TableName.OBSERVATION_WISHLIST_ACTIONS: converted_wishlist_actions_factory.build_table(
            size=3
        ),
        TableName.OBSERVATION_WISHLIST_COHORTS: converted_wishlist_cohorts_factory.build_table(
            size=4
        ),
    }

    portals_data = PortalsAggregator(**input_data).aggregate()

    assert portals_data.df.to_dicts() == [
        {
            "portal": DisplayPortal.STEAM.value,
            "platform": DisplayPlatform.PC.value,
            "region": Region.GLOBAL.value,
            "store_name": Store.STEAM.value,
            "abbreviated_name": "Steam",
            "portal_platform_region": f"{DisplayPortal.STEAM.value}:{DisplayPlatform.PC.value}:{Region.GLOBAL.value}",
        }
    ]


def test_input_with_different_portal_platform_region(
    converted_sales_factory,
    converted_visibility_factory,
    converted_wishlist_actions_factory,
    converted_wishlist_cohorts_factory,
):
    steam_sales = converted_sales_factory.build_df_batch(size=1)
    playstation_europe_sales = converted_sales_factory.build_df_batch(
        size=1,
        portal=DisplayPortal.PLAYSTATION,
        platform=DisplayPlatform.PS_4,
        region=Region.PLAYSTATION_EUROPE,
        store=Store.PLAYSTATION_EUROPE,
        abbreviated_name=Store.PLAYSTATION_EUROPE.abbreviate(),
    )
    playstation_americas_sales = converted_sales_factory.build_df_batch(
        size=1,
        portal=DisplayPortal.PLAYSTATION,
        platform=DisplayPlatform.PS_5,
        region=Region.PLAYSTATION_AMERICA,
        store=Store.PLAYSTATION_AMERICA,
        abbreviated_name=Store.PLAYSTATION_AMERICA.abbreviate(),
    )

    sales_data = pl.concat([
        steam_sales,
        playstation_europe_sales,
        playstation_americas_sales,
    ])

    input_data = {
        TableName.OBSERVATION_SALES: converted_sales_factory.converted_rows_to_table(
            sales_data.to_dicts()
        ),
        TableName.OBSERVATION_VISIBILITY: converted_visibility_factory.build_table(
            size=0
        ),
        TableName.OBSERVATION_WISHLIST_ACTIONS: converted_wishlist_actions_factory.build_table(
            size=0
        ),
        TableName.OBSERVATION_WISHLIST_COHORTS: converted_wishlist_cohorts_factory.build_table(
            size=0
        ),
    }

    portals_data = PortalsAggregator(**input_data).aggregate()

    expected_output = [
        {
            "portal": DisplayPortal.STEAM.value,
            "platform": DisplayPlatform.PC.value,
            "region": Region.GLOBAL.value,
            "store_name": Store.STEAM.value,
            "abbreviated_name": Store.STEAM.abbreviate(),
            "portal_platform_region": f"{DisplayPortal.STEAM.value}:{DisplayPlatform.PC.value}:{Region.GLOBAL.value}",
        },
        {
            "portal": DisplayPortal.PLAYSTATION.value,
            "platform": DisplayPlatform.PS_4.value,
            "region": Region.PLAYSTATION_EUROPE.value,
            "store_name": Store.PLAYSTATION_EUROPE.value,
            "abbreviated_name": Store.PLAYSTATION_EUROPE.abbreviate(),
            "portal_platform_region": f"{DisplayPortal.PLAYSTATION.value}:{DisplayPlatform.PS_4.value}:{Region.PLAYSTATION_EUROPE.value}",
        },
        {
            "portal": DisplayPortal.PLAYSTATION.value,
            "platform": DisplayPlatform.PS_5.value,
            "region": Region.PLAYSTATION_AMERICA.value,
            "store_name": Store.PLAYSTATION_AMERICA.value,
            "abbreviated_name": Store.PLAYSTATION_AMERICA.abbreviate(),
            "portal_platform_region": f"{DisplayPortal.PLAYSTATION.value}:{DisplayPlatform.PS_5.value}:{Region.PLAYSTATION_AMERICA.value}",
        },
    ]

    assert (
        pl.DataFrame(expected_output)
        .sort("portal_platform_region")
        .equals(portals_data.df)
    )
