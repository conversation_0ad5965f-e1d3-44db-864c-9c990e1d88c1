import datetime

import polars as pl
import pytest

from core_silver.aggregators.skus import SkusAggregator
from data_sdk.domain import TableName
from data_sdk.domain.observations import ObservationType
from data_sdk.domain.tables import ObservationSalesTable
from data_sdk.reports.schema import SalesConvertedReport, WishlistActionsConvertedReport

"""
TODO: tests that are missing
test empty rsv2 sku list
test SILVER_WISHLIST_ACTIONS input
test SILVER_WISHLIST_COHORTS input
"""


@pytest.fixture
def empty_input_kwargs(
    converted_sales_factory,
    converted_visibility_factory,
    converted_wishlist_actions_factory,
    converted_wishlist_cohorts_factory,
    converted_discounts_factory,
    external_skus_sales_factory,
):
    return {
        TableName.OBSERVATION_SALES: converted_sales_factory.build_table(size=0),
        TableName.OBSERVATION_VISIBILITY: converted_visibility_factory.build_table(
            size=0
        ),
        TableName.OBSERVATION_WISHLIST_ACTIONS: converted_wishlist_actions_factory.build_table(
            size=0
        ),
        TableName.OBSERVATION_WISHLIST_COHORTS: converted_wishlist_cohorts_factory.build_table(
            size=0
        ),
        TableName.EXTERNAL_SKUS: external_skus_sales_factory.build_table(size=0),
    }


@pytest.fixture
def input_kwargs(
    empty_input_kwargs,
    converted_sales_factory,
    external_skus_sales_factory,
):
    return {
        **empty_input_kwargs,
        TableName.OBSERVATION_SALES: converted_sales_factory.build_table(
            size=1, date=datetime.date(2000, 1, 3)
        ),
        TableName.EXTERNAL_SKUS: external_skus_sales_factory.build_table(size=1),
    }


@pytest.fixture
def input_kwargs_sku_with_product(
    empty_input_kwargs,
    converted_sales_factory,
    external_skus_sales_factory,
):
    external_skus = external_skus_sales_factory.build_table(
        size=1,
        product_name="Test Product Name",
        custom_group="Test Custom Group",
        product_type="Test Product Type",
        base_sku_id="505511",
    )
    return {
        **empty_input_kwargs,
        TableName.OBSERVATION_SALES: converted_sales_factory.build_table(size=1),
        TableName.EXTERNAL_SKUS: external_skus,
    }


def test_empty_input_produces_empty_df(empty_input_kwargs):
    skus_data = SkusAggregator(**empty_input_kwargs).aggregate()

    assert skus_data.df.to_dicts() == []


def test_sku_creates_all_required_columns(input_kwargs):
    skus_data = SkusAggregator(**input_kwargs).aggregate()
    assert skus_data.df.columns == [
        "unique_sku_id",
        "base_sku_id",
        "human_name",
        "store_id",
        "studio_id",
        "portal_platform_region",
        "human_name_indicator",
        "sku_type",
        "release_date",
        "product_name",
        "product_type",
    ]


def test_sku_sets_base_values_from_converted_sales_report_without_rsv2_data(
    input_kwargs,
):
    skus_data = SkusAggregator(**input_kwargs).aggregate()

    assert skus_data.df.to_dicts() == [
        {
            "base_sku_id": "505511",
            "human_name": "SUPERHOT",
            "store_id": "505511",
            "studio_id": 1,
            "portal_platform_region": "Steam:PC:Global",
            "human_name_indicator": ObservationType.SALES.value,
            "release_date": datetime.date(2000, 1, 3),
            "sku_type": "SALES",
            "product_name": None,
            "product_type": None,
            "unique_sku_id": "505511-steam:1",
        }
    ]


def test_sku_from_sales_report_is_merged_with_sku_from_rsv2(
    input_kwargs_sku_with_product,
):
    skus_data = SkusAggregator(**input_kwargs_sku_with_product).aggregate()

    merged_sku = skus_data.df.to_dicts()[0]

    assert merged_sku["product_name"] == "Test Product Name"
    assert merged_sku["product_type"] == "Test Product Type"


def test_sku_human_name_is_updated_with_newest_human_name_from_sales_report(
    empty_input_kwargs,
    converted_sales_factory,
    external_skus_sales_factory,
):
    silver_sales = pl.concat([
        converted_sales_factory.build_df_batch(
            size=1, human_name="older name from report"
        ),
        converted_sales_factory.build_df_batch(
            size=1, human_name="never name from report"
        ),
    ])

    external_skus = external_skus_sales_factory.build_table(
        size=1, human_name="Old RSv2 name"
    )

    init_kwargs = {
        **empty_input_kwargs,
        TableName.OBSERVATION_SALES: ObservationSalesTable(df=silver_sales),
        TableName.EXTERNAL_SKUS: external_skus,
    }

    skus_data = SkusAggregator(**init_kwargs).aggregate()

    sku_with_name_updated = skus_data.df.to_dicts()[0]

    assert sku_with_name_updated["human_name"] == "never name from report"


product_names_examples = [
    {"input": "Test case", "output": "Test case"},
    {"input": "🔥SUPER🔥HOT🔥", "output": "SUPERHOT"},
    {"input": "Test case😆😅😂", "output": "Test case"},
    {"input": "Test case☝🏻👍🏻👎🏻", "output": "Test case"},
    {"input": "Test case🍙🍚🍘🍥🥠🥮🍢", "output": "Test case"},
    {"input": "Test case鳥鸟馬马", "output": "Test case鳥鸟馬马"},
    {"input": "Test caseザバモミャ", "output": "Test caseザバモミャ"},
    {"input": "Test case기역쌍기역니은", "output": "Test case기역쌍기역니은"},
]


@pytest.mark.parametrize(
    "test_scenario", product_names_examples, ids=lambda x: x["input"]
)
def test_sku_human_name_is_stored_without_emojis(
    test_scenario: dict,
    empty_input_kwargs,
    converted_sales_factory,
    external_skus_sales_factory,
):
    init_kwargs = {
        **empty_input_kwargs,
        TableName.OBSERVATION_SALES: converted_sales_factory.build_table(
            size=1, human_name=test_scenario["input"]
        ),
        TableName.EXTERNAL_SKUS: external_skus_sales_factory.build_table(size=1),
    }
    skus_data = SkusAggregator(**init_kwargs).aggregate()

    assert skus_data.df["human_name"].to_list() == [test_scenario["output"]]


def test_sku_sets_base_values_from_converted_visibility_report_without_rsv2_data(
    empty_input_kwargs,
    converted_visibility_factory,
    external_skus_sales_factory,
):
    init_kwargs = {
        **empty_input_kwargs,
        TableName.OBSERVATION_VISIBILITY: converted_visibility_factory.build_table(
            size=1
        ),
        TableName.EXTERNAL_SKUS: external_skus_sales_factory.build_table(size=1),
    }

    skus_data = SkusAggregator(**init_kwargs).aggregate()

    assert skus_data.df.to_dicts() == [
        {
            "base_sku_id": "583710",
            "human_name": "Dimension Hunter Demo",
            "human_name_indicator": ObservationType.VISIBILITY.value,
            "product_name": None,
            "product_type": None,
            "release_date": None,
            "sku_type": "STORE",
            "store_id": "583710",
            "studio_id": 1,
            "portal_platform_region": "Steam:PC:Global",
            "unique_sku_id": "583710-store:1",
        },
    ]


def test_sku_human_name_from_visibility_overrides_human_name_from_cohorts(
    empty_input_kwargs,
    external_skus_store_factory,
    converted_visibility_factory,
):
    init_kwargs = {
        **empty_input_kwargs,
        TableName.OBSERVATION_VISIBILITY: converted_visibility_factory.build_table(
            size=1, unique_sku_id="123-store:1", human_name="Human Name from visibility"
        ),
        TableName.EXTERNAL_SKUS: external_skus_store_factory.build_table(
            size=1,
            unique_sku_id="123-store:1",
            human_name="Name that should be overidden",
            human_name_indicator=ObservationType.WISHLIST_COHORTS,
        ),
    }

    skus_data = SkusAggregator(**init_kwargs).aggregate()

    assert skus_data.df["human_name"].to_list() == ["Human Name from visibility"]


def test_skus_other_store_observation_types_are_concatenated(
    external_skus_store_factory,
    converted_sales_factory,
    converted_discounts_factory,
    converted_visibility_factory,
    converted_wishlist_actions_factory,
    converted_wishlist_cohorts_factory,
):
    init_kwargs = {
        TableName.OBSERVATION_SALES: converted_sales_factory.build_table(
            size=1,
            sku_id="12",
        ),
        TableName.OBSERVATION_VISIBILITY: converted_visibility_factory.build_table(
            size=1,
            sku_id="34",
        ),
        TableName.OBSERVATION_WISHLIST_ACTIONS: converted_wishlist_actions_factory.build_table(
            size=1,
            sku_id="56",
        ),
        TableName.OBSERVATION_WISHLIST_COHORTS: converted_wishlist_cohorts_factory.build_table(
            size=1,
            sku_id="78",
        ),
        TableName.EXTERNAL_SKUS: external_skus_store_factory.build_table(
            size=1,
            base_sku_id="AB",
        ),
    }
    skus_data = SkusAggregator(**init_kwargs).aggregate()

    assert skus_data.df["base_sku_id"].to_list() == ["12", "34", "56", "78"]


def test_should_calculate_proper_release_date_when_one_is_calculable(
    empty_input_kwargs,
    converted_sales_factory,
    external_skus_store_factory,
):
    init_kwargs = {
        **empty_input_kwargs,
        TableName.OBSERVATION_SALES: SalesConvertedReport(
            df=pl.concat([
                converted_sales_factory.build_df_batch(
                    size=1, gross_sales=1, date=datetime.date(2000, 1, 1)
                ),
                converted_sales_factory.build_df_batch(
                    size=1, gross_sales=1, date=datetime.date(2000, 1, 2)
                ),
            ])
        ),
        TableName.EXTERNAL_SKUS: external_skus_store_factory.build_table(
            size=1,
            base_sku_id="AB",
        ),
    }
    skus_data = SkusAggregator(**init_kwargs).aggregate()
    assert skus_data.df["release_date"].to_list() == [datetime.date(2000, 1, 1)]


def test_set_release_date_to_none(
    empty_input_kwargs,
    converted_wishlist_actions_factory,
    external_skus_store_factory,
):
    init_kwargs = {
        **empty_input_kwargs,
        TableName.OBSERVATION_WISHLIST_ACTIONS: converted_wishlist_actions_factory.build_table(
            size=1,
            sku_id="56",
        ),
        TableName.EXTERNAL_SKUS: external_skus_store_factory.build_table(
            size=1,
            base_sku_id="AB",
        ),
    }
    skus_data = SkusAggregator(**init_kwargs).aggregate()
    assert skus_data.df["release_date"].to_list() == [None]


def test_should_ignore_external_skus_which_do_not_appear_in_different_places(
    empty_input_kwargs,
    external_skus_store_factory,
):
    init_kwargs = {
        **empty_input_kwargs,
        TableName.EXTERNAL_SKUS: external_skus_store_factory.build_table(
            size=1,
            base_sku_id="AB",
        ),
    }
    skus_data = SkusAggregator(**init_kwargs).aggregate()
    assert skus_data.df.to_dicts() == []


def test_ps_wishlist_numeric_skus_from_two_regions_are_stored_separately(
    empty_input_kwargs,
    converted_wishlist_actions_factory,
):
    ps_wishlist_region_1 = converted_wishlist_actions_factory.build_df_batch(
        size=1,
        platform="Unknown",
        region="PS Europe",
        portal="PlayStation",
        human_name="Concept_1",
        sku_id="123456789",
        store_id="123456789",
        unique_sku_id="123456789-SIEE-playstation-store:1",
        country_code="ESP",
    )
    ps_wishlist_region_2 = converted_wishlist_actions_factory.build_df_batch(
        size=1,
        platform="Unknown",
        region="PS America",
        portal="PlayStation",
        human_name="Concept_1",
        sku_id="123456789",
        store_id="123456789",
        unique_sku_id="123456789-SIEA-playstation-store:1",
        country_code="USA",
    )
    obs_wishlist_actions = pl.concat([ps_wishlist_region_1, ps_wishlist_region_2])

    init_kwargs = {
        **empty_input_kwargs,
        TableName.OBSERVATION_WISHLIST_ACTIONS: WishlistActionsConvertedReport(
            df=obs_wishlist_actions
        ),
    }

    skus_data = SkusAggregator(**init_kwargs).aggregate()

    assert skus_data.df["unique_sku_id"].to_list() == [
        "123456789-SIEA-playstation-store:1",
        "123456789-SIEE-playstation-store:1",
    ]
