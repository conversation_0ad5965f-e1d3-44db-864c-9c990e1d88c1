from pathlib import Path

import pandas as pd
import polars as pl
import pytest

from core_silver.aggregators import aggregator_list as silver_aggregator_list
from core_silver.legacy_aggregators import aggregator_list as legacy_aggregator_list
from data_sdk.aggregator import BaseAggregator
from data_sdk.domain import Portal, TableName
from data_sdk.domain.domain_types import StudioId
from data_sdk.domain.tables import TableDefinition
from data_sdk.validators.schemas import NoCheckSchema
from tests.comparisons import compare_dataframes
from tests.snapshot.aggregators.processing_scenarios import (
    EMPTY_TABLE,
    ProcessingScenario,
    _id_from_scenario,
    dimension_scenarios,
    fact_scenarios,
)


@pytest.fixture
def input_table_factory(
    external_skus_sales_factory,
    country_codes,
    external_studios,
    silver_skus_sales_factory,
    converted_sales_factory,
    converted_visibility_factory,
    converted_wishlist_actions_factory,
    converted_wishlist_cohorts_factory,
    converted_discounts_factory,
    external_steam_reports_factory,
    external_shared,
    steam_events,
):
    def _input_table_factory(test_scenario: ProcessingScenario):
        base_tables = [
            external_skus_sales_factory.build_table(size=1),
            external_studios,
            country_codes,
            steam_events,
            silver_skus_sales_factory.build_table(size=0),
            converted_visibility_factory.build_table(size=0),
            converted_discounts_factory.build_table(size=0),
            converted_wishlist_actions_factory.build_table(size=0),
            converted_sales_factory.build_table(size=0),
            converted_wishlist_cohorts_factory.build_table(size=0),
            external_steam_reports_factory.build_table(size=0),
            external_shared,
        ]

        input_tables = {
            table: _get_input_table(table, path_or_df)
            for table, path_or_df in test_scenario.input_tables_paths.items()
            if not isinstance(path_or_df, pl.DataFrame)
        }

        return {**{table.__class__: table for table in base_tables}, **input_tables}

    def _get_input_table(table: type[TableDefinition], path: str | list[str]):
        if isinstance(path, list):
            return table(df=_read_files(path))
        return table(df=_read_files([path]))

    def _read_files(paths: list[str]):
        return pl.concat([_read_csv_or_parquet(path) for path in paths])

    def _read_csv_or_parquet(path: str):
        return (
            pl.read_csv(path) if Path(path).suffix == ".csv" else pl.read_parquet(path)
        )

    return _input_table_factory


@pytest.mark.parametrize(
    "test_scenario", dimension_scenarios + fact_scenarios, ids=_id_from_scenario
)
def test_snapshot_aggregators(
    test_scenario: ProcessingScenario,
    input_table_factory,
    time_machine,
):
    if test_scenario.move_time_to is not None:
        time_machine.move_to(test_scenario.move_time_to)
    input_tables = input_table_factory(test_scenario)
    core_output_tables = aggregate_tables(input_tables, silver_aggregator_list)

    legacy_input_tables = {**input_tables, **core_output_tables}
    legacy_output_tables = aggregate_tables(legacy_input_tables, legacy_aggregator_list)

    for table_name, table in legacy_output_tables.items():
        assert (
            pl.Categorical not in table.df.dtypes
        ), f"{table_name} has Categorical dtype"
        assert pl.Enum not in table.df.dtypes, f"{table_name} has Enum dtype"

    all_output_tables = {**core_output_tables, **legacy_output_tables}
    all_output_tables_by_name = {
        table_cls.table_name: table for table_cls, table in all_output_tables.items()
    }

    for table_name, path in test_scenario.output_tables_paths.items():
        output_schema = get_legacy_aggregator_schema(table_name)

        result = all_output_tables_by_name[table_name].df.to_pandas()

        if path == EMPTY_TABLE:
            assert result.empty, "Expected an empty dataframe."
            if hasattr(output_schema, "to_schema"):
                compare_dataframes(
                    result, pd.DataFrame(columns=output_schema.to_schema().columns)
                )
            else:
                # TODO drop handing of pure polars schema after all aggregators
                #  have been migrated to BaseStrictAggregator
                compare_dataframes(result, pd.DataFrame(columns=output_schema.columns))
        else:
            raw_expected_table = pd.read_csv(
                path,
                dtype={"base_event_id": str},
            )
            if "base_event_id" in raw_expected_table.columns:
                raw_expected_table["base_event_id"] = raw_expected_table[
                    "base_event_id"
                ].fillna("")
            if hasattr(output_schema, "to_schema"):
                expected_table = output_schema.to_schema()(
                    pl.DataFrame(raw_expected_table)
                ).to_pandas()
            else:
                # TODO drop handing of pure polars schema after all aggregators
                #  have been migrated to BaseStrictAggregator
                expected_table = output_schema(raw_expected_table)
            compare_dataframes(result, expected_table)


def aggregate_tables(input_tables, aggregator_list):
    result = {}
    for aggregator_class in aggregator_list:
        init_kwargs = {}
        for param_name, param_type in aggregator_class.__init__.__annotations__.items():
            if param_name == "return":
                continue  # Ignore return annotation

            elif issubclass(param_type, TableDefinition):
                init_kwargs[param_name] = input_tables[param_type]
            elif issubclass(param_type, Portal):
                init_kwargs[param_name] = Portal.STEAM
            elif issubclass(param_type, StudioId):
                init_kwargs[param_name] = StudioId(1)
            else:
                raise Exception(f"Unknown type {param_type}")  # noqa: TRY002, TRY003
        result[aggregator_class.table_cls] = aggregator_class(**init_kwargs).aggregate()
    return result


def get_legacy_aggregator_schema(table_name: TableName):
    for agg in legacy_aggregator_list:
        if (
            issubclass(agg, BaseAggregator)
            and agg.table_cls.table_name == table_name
            and agg.table_cls.model == NoCheckSchema
        ):
            return agg.schema
        if issubclass(agg, BaseAggregator) and agg.table_cls.table_name == table_name:
            return agg.table_cls.model
