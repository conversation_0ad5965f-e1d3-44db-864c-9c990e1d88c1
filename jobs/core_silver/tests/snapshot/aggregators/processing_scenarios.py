from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Optional

import polars as pl

from core_silver import get_project_root
from core_silver.external_sources.country_codes import CountryCodesExternalProcessor
from data_sdk.domain import TableName
from data_sdk.domain.domain_types import StudioId
from data_sdk.domain.tables import (
    ExternalCountryCodesModel,
    ExternalSKUsTable,
    ObservationDiscountsTable,
    ObservationSalesTable,
    ObservationVisibilityTable,
    ObservationWishlistActionsTable,
    ObservationWishlistCohortsTable,
    TableDefinition,
)

EMPTY_TABLE = "empty_table"


def aggregated_path(file_name: str) -> Path:
    return Path(
        get_project_root(), "tests", "snapshot", "aggregators_output", file_name
    )


def aux_path(file_name: str) -> Path:
    return Path(get_project_root(), "tests", "snapshot", "aux_data", file_name)


def converted_path(file_name: str) -> Path:
    return Path(get_project_root(), "tests", "snapshot", "output", file_name)


@dataclass
class ProcessingScenario:
    scenario_name: str
    input_tables_paths: dict[
        type[TableDefinition], pl.DataFrame | str | Path | list[str]
    ]
    output_tables_paths: dict[TableName, str | Path]
    move_time_to: Optional[datetime] = None


def _id_from_scenario(processing_scenario: ProcessingScenario) -> str:
    return processing_scenario.scenario_name


class ConvertedInputFiles(str, Enum):
    apple_app_store_empty_tsv_valid = converted_path(
        "app_store_sales_empty_tsv.parquet"
    )
    apple_app_store_valid = converted_path("app_store_sales.parquet")
    epic_free_units = converted_path("EPIC-free-units.parquet")
    epic_valid = converted_path("EPIC-2021-05-23_2021-11-03.parquet")
    epic_valid_with_positive_return_values = converted_path(
        "epic_with_positive_returns.parquet"
    )
    gog_json_valid = converted_path("gog_json_v.parquet")
    gog_json_empty = converted_path("gog_json_empty.parquet")
    gog_valid = converted_path("GOG-2021-12-29_2022-01-14.parquet")
    gog_valid_sku_in_manifest = converted_path(
        "GOG-2021-12-29_2022-01-14-sku-manifest.parquet"
    )
    google_less_columns_valid = converted_path("google_sales_less_cols_v.parquet")
    google_valid = converted_path("google_sales_v.parquet")
    google_valid_empty = converted_path("google_sales_v_e.parquet")
    humble_almost_free = converted_path("humble_almost_free.parquet")
    humble_filter_sku = converted_path("humble_filter_sku.parquet")
    humble_valid = converted_path("humble_sales_report_20200528_20200531.parquet")
    meta_quest_free_records = converted_path(
        "META_QUEST-2020-07-29_2021-07-29_free.parquet"
    )
    meta_quest_promo = converted_path("meta_quest_promo.parquet")
    meta_quest_valid = converted_path("META_QUEST-2021-07-14_2021-07-22.parquet")
    meta_rift_valid = converted_path("META_RIFT-2021-08-07_2021-08-18.parquet")
    meta_rift_valid_new_headers = converted_path("meta_rift_new_headers.parquet")
    microsoft_applicationname_sku = converted_path(
        "Microsoft_applicationName_sku.parquet"
    )
    microsoft_valid = converted_path("MICROSOFT_STORE-2021-05-03_2021-11-01.parquet")
    microsoft_valid_csv = converted_path("microsoft_csv.parquet")
    microsoft_valid_csv_no_purchase_tax_type = converted_path(
        "microsoft_csv_no_tax.parquet"
    )
    microsoft_valid_duplicated_preorders_excluded = converted_path(
        "microsoft_preorder_excluded.parquet"
    )
    microsoft_valid_inapp_sku = converted_path("Microsoft_inapp_sku.parquet")
    microsoft_valid_json_no_purchase_tax_type = converted_path(
        "microsoft_json_no_tax.parquet"
    )
    microsoft_valid_missing_in_product_name = converted_path(
        "MICROSOFT_STORE_missing_in_app_name.parquet"
    )
    microsoft_valid_raw_json = converted_path("ms_json.parquet")
    microsoft_valid_with_manifest_with_file_meta_data = converted_path(
        "Microsoft_new_manifest_test.parquet"
    )
    nintendo_base_price_with_bug = converted_path(
        "nintendo_sales-2018-12-28_2019-01-28.parquet"
    )
    nintendo_discounts_valid = converted_path("nintendo_discounts_v.parquet")
    nintendo_discounts_valid_2 = converted_path("nintendo_discounts_v.parquet")
    nintendo_discounts_valid_empty = converted_path("nintendo_discounts_empty.parquet")
    nintendo_discounts_valid_empty_2 = converted_path(
        "nintendo_discounts_empty.parquet"
    )
    nintendo_duplicate = converted_path("NINTENDO-2018-08-08_2019-07-25.parquet")
    ps_analytics_na_currency = converted_path("ps_analytics_na_currency.parquet")
    nintendo_empty = converted_path("NINTENDO-2010-01-01_2010-12-18.parquet")
    nintendo_empty_zip = converted_path("nintendo_empty.parquet")
    nintendo_local_price_only_promo = converted_path(
        "nintendo_sales-2022-05-20_2022-05-23.parquet"
    )
    nintendo_local_price_regular_promo = converted_path(
        "nintendo_sales-2022-05-01_2022-05-20.parquet"
    )
    nintendo_missing_product_names = converted_path(
        "NINTENDO-missing-product-names.parquet"
    )
    nintendo_promo = converted_path("nintendo_promo.parquet")
    nintendo_valid = converted_path("NINTENDO-2018-08-08_2019-07-25.parquet")
    nintendo_valid_different_title_names = converted_path(
        "NINTENDO-different_title_name.parquet"
    )
    nintendo_valid_discounts = converted_path(
        "nintendo_sales-2022-03-04_2022-04-04.parquet"
    )
    nintendo_valid_empty_currency = converted_path("nintendo_empty_currency.parquet")
    nintendo_valid_zip = converted_path("nintendo_valid.parquet")
    nintendo_valid_zip_no_sales_observation = converted_path(
        "nintendo_no_sales_observation_v.parquet"
    )
    nintendo_valid_zip_two_files = converted_path("nintendo_valid_two_files.parquet")
    nintendo_valid_zip_ymd_date = converted_path("nintendo_ymd_date_v.parquet")
    nintendo_wiiu_valid = converted_path("nintendo_wiiu_valid.parquet")
    ps_all_history = converted_path("PS_all_history.parquet")
    ps_analytics_changed_portal_platform_region = converted_path(
        "ps_analytics_changed_portal_platform_region.parquet"
    )
    ps_analytics_empty = converted_path("ps_analytics_empty.parquet")
    ps_analytics_missing_product_names = converted_path(
        "ps_analytics_missing_product_name.parquet"
    )
    ps_analytics_new_empty = converted_path("ps_analytics_empty_new.parquet")
    ps_analytics_new_missing_product_names = converted_path(
        "ps_analytics_missing_product_name_new.parquet"
    )
    ps_analytics_new_valid = converted_path("ps_analytics_new.parquet")
    ps_analytics_not_migrated_region = converted_path("ps_analytics_bad_region.parquet")
    ps_analytics_promo = converted_path("ps_analytics_promo.parquet")
    ps_analytics_valid = converted_path("ps_analytics.parquet")
    ps_analytics_valid2 = converted_path("ps_analytics2.parquet")
    ps_analytics_valid_with_emojis = converted_path("ps_analytics_emojis.parquet")
    ps_api_valid = converted_path("ps_api_json.parquet")
    ps_api_valid_no_sales = converted_path("ps_api_json_no_sales.parquet")
    ps_api_valid_one = converted_path("ps_api_psone.parquet")
    ps_api_valid_vr2 = converted_path("ps_api_vr2.parquet")
    steam_all_history = converted_path("steam_all.parquet")
    steam_discount_planner_error = converted_path(
        "steam_sales_2010-01-01_2023-03-15.parquet"
    )
    steam_discounts_valid = converted_path("steam_discounts_v.parquet")
    steam_discounts_valid_2 = converted_path("steam_discounts_v.parquet")
    steam_discounts_valid_empty = converted_path("steam_discounts_empty.parquet")
    steam_discounts_valid_empty_2 = converted_path("steam_discounts_empty.parquet")
    steam_discounts_valid_missing_optinname_column = converted_path(
        "steam_discounts_valid_missing_optinname_column.parquet"
    )
    steam_free_units_only_valid = converted_path(
        "steam_sales_free_units_only_v.parquet"
    )
    steam_full = converted_path("steam_all.parquet")
    steam_impressions_corrupted = converted_path("STEAM_IMPRESSIONS-corrupted.parquet")
    steam_impressions_empty = converted_path(
        "STEAM_IMPRESSIONS-2014-09-23_2014-11-13.parquet"
    )
    steam_impressions_empty_na_product_id = converted_path(
        "STEAM_IMPRESSIONS-empty-na-productId.parquet"
    )
    steam_impressions_missing_date = converted_path(
        "STEAM_IMPRESSIONS-missing-date.parquet"
    )
    steam_impressions_missing_not_required_cols = converted_path(
        "STEAM_IMPRESSIONS_missing_not_required_cols.parquet"
    )
    steam_impressions_missing_product_id = converted_path(
        "STEAM_IMPRESSIONS-missing-product-id.parquet"
    )
    steam_impressions_missing_product_name = converted_path(
        "STEAM_IMPRESSIONS-missing-product-name.parquet"
    )
    steam_impressions_no_data_flag = converted_path(
        "steam_impressions-2023-08-09_2023-08-15-empty.parquet"
    )
    steam_impressions_old_valid = converted_path("STEAM_IMPRESSIONS_OLD_FORMAT.parquet")
    steam_impressions_valid = converted_path(
        "STEAM_IMPRESSIONS-2019-02-21_2019-04-13.parquet"
    )
    steam_impressions_with_country = converted_path(
        "STEAM_IMPRESSIONS_with_country.parquet"
    )
    steam_missingevent_date = converted_path(
        "steam_sales-2022-06-30_2023-06-16-missingdate.parquet"
    )
    steam_new_sku = converted_path("steam_new_sku.parquet")
    steam_one_year = converted_path("steam-one-year.parquet")
    steam_promo = converted_path("STEAM-promo.parquet")
    steam_short_time_range = converted_path("steam_short_time_range.parquet")
    steam_valid = converted_path("STEAM-2018-08-08_2019-07-25.parquet")
    steam_valid_860 = converted_path(
        "STEAM-2018-08-08_2019-07-25.parquet"
    )  # TODO - remove whole scenario when RP parametrizing is no longer needed
    steam_valid_no_date_range = converted_path("STEAM-2018-08-08_2019-07-25.parquet")
    steam_wishlist_actions_bad_name = converted_path(
        "STEAM_WISHLISTS_BAD_NAMES.parquet"
    )
    steam_wishlist_actions_corrupted = converted_path(
        "STEAM_WISHLISTS-CORRUPTED.parquet"
    )
    steam_wishlist_actions_empty = converted_path(
        "STEAM_WISHLISTS-2021-08-26_2021-10-07.parquet"
    )
    steam_wishlist_actions_no_additional_data = converted_path(
        "STEAM_WISHLISTS-empty-no-additional-data.parquet"
    )
    steam_wishlist_actions_valid = converted_path(
        "STEAM_WISHLISTS-actions_valid.parquet"
    )
    ps_wishlist_actions_valid = converted_path("PS_WISHLISTS-actions_valid.parquet")
    steam_wishlist_actions_valid_with_empty_file = converted_path(
        "STEAM_WISHLISTS-actions_valid_one_csv_empty"
    )
    steam_wishlist_actions_valid_manifest_only = converted_path(
        "steam_wishlist_manifest_only.parquet"
    )
    steam_wishlist_cohorts_bad_name = converted_path(
        "STEAM_WISHLISTS_BAD_NAMES.parquet"
    )
    steam_wishlist_cohorts_corrupted = converted_path(
        "STEAM_WISHLISTS-CORRUPTED.parquet"
    )
    steam_wishlist_cohorts_empty = converted_path(
        "STEAM_WISHLISTS-2021-08-26_2021-10-07.parquet"
    )
    steam_wishlist_cohorts_empty_no_additional_data = converted_path(
        "STEAM_WISHLISTS-empty-no-additional-data.parquet"
    )
    steam_wishlist_cohorts_valid = converted_path(
        "STEAM_WISHLISTS-cohorts_valid.parquet"
    )
    steam_wishlist_cohorts_valid_with_empty_file = converted_path(
        "STEAM_WISHLISTS-cohorts_valid_one_csv_empty.parquet"
    )


nintendo_valid_detected_events = ProcessingScenario(
    scenario_name="Nintendo valid - detected events",
    input_tables_paths={
        ObservationSalesTable: ConvertedInputFiles.nintendo_valid_discounts,
    },
    output_tables_paths={
        TableName.FACT_DETECTED_EVENTS: aggregated_path(
            "fact_detected_event_nintento_valid.csv"
        ),
    },
    move_time_to=datetime(2023, 6, 23),
)

fact_discounts_scenarios: list[ProcessingScenario] = [
    ProcessingScenario(
        scenario_name="Steam Discounts Valid One file",
        input_tables_paths={
            ObservationDiscountsTable: ConvertedInputFiles.steam_discounts_valid,
        },
        output_tables_paths={
            TableName.FACT_DISCOUNTS: aggregated_path("fact_discounts_steam_valid.csv"),
        },
    ),
    ProcessingScenario(
        scenario_name="Nintendo Discounts Valid One file",
        input_tables_paths={
            ObservationDiscountsTable: ConvertedInputFiles.nintendo_discounts_valid,
        },
        output_tables_paths={
            TableName.FACT_DISCOUNTS: aggregated_path(
                "fact_discounts_nintendo_valid.csv"
            ),
        },
    ),
]

event_day_scenarios: list[ProcessingScenario] = [
    ProcessingScenario(
        scenario_name="Valid PS Analytics Event Day",
        input_tables_paths={
            ObservationSalesTable: ConvertedInputFiles.ps_analytics_promo,
            ExternalSKUsTable: aux_path("ps_analytics_sku_v.csv"),
            ExternalCountryCodesModel: CountryCodesExternalProcessor()._process(
                studio_id=StudioId(1)
            ),
        },
        output_tables_paths={
            TableName.FACT_EVENT_DAY: aggregated_path(
                "fact_event_day_ps_analytics_v.csv"
            )
        },
    ),
    ProcessingScenario(
        scenario_name="Valid Steam Event Day",
        input_tables_paths={
            ObservationSalesTable: ConvertedInputFiles.steam_valid,
        },
        output_tables_paths={
            TableName.FACT_EVENT_DAY: aggregated_path("fact_event_day_steam_v.csv"),
        },
    ),
    ProcessingScenario(
        scenario_name="Valid Steam Event Day",
        input_tables_paths={
            ObservationSalesTable: ConvertedInputFiles.meta_quest_promo,
        },
        output_tables_paths={
            TableName.FACT_EVENT_DAY: aggregated_path("fact_event_day_meta_v.csv"),
        },
    ),
]

baseline_scenarios: list[ProcessingScenario] = [
    ProcessingScenario(
        scenario_name="Steam short sales period - baseline",
        input_tables_paths={
            ObservationSalesTable: ConvertedInputFiles.steam_short_time_range,
        },
        output_tables_paths={
            TableName.FACT_BASELINE: aggregated_path(
                "fact_baseline_steam_short_time_range.csv"
            )
        },
    ),
    ProcessingScenario(
        scenario_name="Steam new sku - baseline",
        input_tables_paths={
            ObservationSalesTable: ConvertedInputFiles.steam_new_sku,
        },
        output_tables_paths={
            TableName.FACT_BASELINE: aggregated_path("fact_baseline_steam_new_sku.csv")
        },
    ),
    ProcessingScenario(
        scenario_name="Steam, one year - baseline",
        input_tables_paths={
            ObservationSalesTable: ConvertedInputFiles.steam_one_year,
        },
        output_tables_paths={
            TableName.FACT_BASELINE: aggregated_path("fact_baseline_steam_one_year.csv")
        },
    ),
]

_scenarios: list[ProcessingScenario] = [
    ProcessingScenario(
        scenario_name="Sales empty input",
        input_tables_paths={
            ObservationSalesTable: ObservationSalesTable.empty().df,
        },
        output_tables_paths={
            TableName.FACT_SALES: EMPTY_TABLE,
        },
    ),
    ProcessingScenario(
        scenario_name="Valid Steam file",
        input_tables_paths={
            ObservationSalesTable: ConvertedInputFiles.steam_valid,
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path("fact_sales_steam_v.csv"),
            TableName.DIM_PORTALS: aggregated_path("dim_portals_steam.csv"),
            TableName.FACT_DETECTED_EVENTS: aggregated_path(
                "fact_detected_event_steam_valid.csv"
            ),
        },
        move_time_to=datetime(2023, 6, 28),
    ),
    ProcessingScenario(
        scenario_name="Valid Nintendo CSV file",
        input_tables_paths={
            ObservationSalesTable: [
                ConvertedInputFiles.nintendo_valid,
            ],
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path("fact_sales_nintendo_v.csv"),
        },
    ),
    ProcessingScenario(
        scenario_name="Valid Nintendo file - empty currency",
        input_tables_paths={
            ObservationSalesTable: [
                ConvertedInputFiles.nintendo_valid_empty_currency,
            ],
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path("fact_sales_nintendo_e_c.csv"),
        },
    ),
    ProcessingScenario(
        scenario_name="Valid Nintendo ZIP file",
        input_tables_paths={
            ObservationSalesTable: [
                ConvertedInputFiles.nintendo_valid_zip,
            ],
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path("fact_sales_nintendo_zip_v.csv"),
        },
    ),
    ProcessingScenario(
        scenario_name="Valid Nintendo ZIP file - date format yy/mm/dd support",
        input_tables_paths={
            ObservationSalesTable: [
                ConvertedInputFiles.nintendo_valid_zip_ymd_date,
            ],
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path("fact_sales_nintendo_ymd_date_v.csv"),
        },
    ),
    ProcessingScenario(
        scenario_name="Valid Nintendo ZIP file with two reports",
        input_tables_paths={
            ObservationSalesTable: [
                ConvertedInputFiles.nintendo_valid_zip_two_files,
            ],
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path(
                "fact_sales_nintendo_zip_two_files_v.csv"
            ),
        },
    ),
    ProcessingScenario(
        scenario_name="Nintendo Wii U valid",
        input_tables_paths={
            ObservationSalesTable: [
                ConvertedInputFiles.nintendo_wiiu_valid,
            ],
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path("fact_sales_nintendo_wiiu_valid.csv"),
        },
    ),
    ProcessingScenario(
        scenario_name="Nintendo Base Price - 1",
        input_tables_paths={
            ObservationSalesTable: [
                ConvertedInputFiles.nintendo_base_price_with_bug,
            ],
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path(
                "fact_sales_nintendo_with_base_price.csv"
            ),
        },
    ),
    ProcessingScenario(
        scenario_name="Valid PS Analytics",
        input_tables_paths={
            ObservationSalesTable: [
                ConvertedInputFiles.ps_analytics_valid,
            ],
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path("fact_sales_ps_analytics_v.csv"),
        },
    ),
    ProcessingScenario(
        scenario_name="Valid PS Analytics New File",
        input_tables_paths={
            ObservationSalesTable: [ConvertedInputFiles.ps_analytics_new_valid],
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path("fact_sales_ps_analytics_new_v.csv")
        },
    ),
    ProcessingScenario(
        scenario_name="Valid MS File",
        input_tables_paths={
            ObservationSalesTable: [
                ConvertedInputFiles.microsoft_valid,
            ],
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path("fact_sales_microsoft_v.csv")
        },
    ),
    ProcessingScenario(
        scenario_name="Valid MS File - raw json",
        input_tables_paths={
            ObservationSalesTable: [
                ConvertedInputFiles.microsoft_valid_raw_json,
            ],
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path(
                "fact_sales_microsoft_v_from_json.csv"
            )
        },
    ),
    ProcessingScenario(
        scenario_name="Valid MS File - csv",
        input_tables_paths={
            ObservationSalesTable: [ConvertedInputFiles.microsoft_valid_csv],
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path("fact_sales_microsoft_csv_v.csv")
        },
    ),
    ProcessingScenario(
        scenario_name="Valid MS File no PurchaseTaxType downloaded - csv",
        input_tables_paths={
            ObservationSalesTable: [
                ConvertedInputFiles.microsoft_valid_csv_no_purchase_tax_type
            ],
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path("fact_sales_microsoft_csv_v.csv")
        },
    ),
    ProcessingScenario(
        scenario_name="Valid MS File no PurchaseTaxType downloaded - JSON",
        input_tables_paths={
            ObservationSalesTable: [
                ConvertedInputFiles.microsoft_valid_json_no_purchase_tax_type,
            ],
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path(
                "fact_sales_microsoft_with_manifest_with_file_meta_data_v.csv",
            )
        },
    ),
    ProcessingScenario(
        scenario_name="Valid MS File with manifest with file meta data",
        input_tables_paths={
            ObservationSalesTable: [
                ConvertedInputFiles.microsoft_valid_with_manifest_with_file_meta_data,
            ],
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path(
                "fact_sales_microsoft_with_manifest_with_file_meta_data_v.csv",
            )
        },
    ),
    ProcessingScenario(
        scenario_name="Valid MS File without in app product name",
        input_tables_paths={
            ObservationSalesTable: [
                ConvertedInputFiles.microsoft_valid_missing_in_product_name
            ],
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path(
                "fact_sales_microsoft_missing_in_app_name.csv"
            )
        },
    ),
    ProcessingScenario(
        scenario_name="Valid MS File - duplicated preorders excluded",
        input_tables_paths={
            ObservationSalesTable: [
                ConvertedInputFiles.microsoft_valid_duplicated_preorders_excluded
            ],
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path(
                "fact_sales_microsoft_preorder_excluded.csv"
            )
        },
    ),
    ProcessingScenario(
        scenario_name="Valid Meta Quest file",
        input_tables_paths={
            ObservationSalesTable: [ConvertedInputFiles.meta_quest_valid],
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path("fact_sales_meta_quest.csv")
        },
    ),
    ProcessingScenario(
        scenario_name="Valid Meta Rift file",
        input_tables_paths={
            ObservationSalesTable: [ConvertedInputFiles.meta_rift_valid],
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path("fact_sales_meta_rift.csv")
        },
    ),
    ProcessingScenario(
        scenario_name="Valid Meta Quest file with free sales",
        input_tables_paths={
            ObservationSalesTable: [ConvertedInputFiles.meta_quest_free_records],
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path("fact_sales_meta_quest_free.csv")
        },
    ),
    ProcessingScenario(
        scenario_name="Valid Meta Quest no sales",
        input_tables_paths={
            ObservationSalesTable: [
                ConvertedInputFiles.meta_quest_valid,
            ],
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path("fact_sales_meta_quest.csv")
        },
    ),
    ProcessingScenario(
        scenario_name="Valid EPIC",
        input_tables_paths={
            ObservationSalesTable: [ConvertedInputFiles.epic_valid],
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path("fact_sales_epic_v.csv")
        },
    ),
    ProcessingScenario(
        scenario_name="Valid EPIC: free units",
        input_tables_paths={
            ObservationSalesTable: [ConvertedInputFiles.epic_free_units],
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path("fact_sales_epic_free_units_v.csv")
        },
    ),
    ProcessingScenario(
        scenario_name="Valid EPIC - new format of returns/chargebacks - positive values",
        input_tables_paths={
            ObservationSalesTable: [
                ConvertedInputFiles.epic_valid_with_positive_return_values
            ],
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path(
                "fact_sales_epic_positive_returns_v.csv"
            )
        },
    ),
    ProcessingScenario(
        scenario_name="Valid GOG",
        input_tables_paths={
            ObservationSalesTable: [ConvertedInputFiles.gog_valid],
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path("fact_sales_gog_v.csv")
        },
    ),
    ProcessingScenario(
        scenario_name="Valid GOG with with sku_id in manifest",
        input_tables_paths={
            ObservationSalesTable: [ConvertedInputFiles.gog_valid_sku_in_manifest],
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path("fact_sales_gog_skus_v.csv")
        },
    ),
    ProcessingScenario(
        scenario_name="HUMBLE: valid",
        input_tables_paths={
            ObservationSalesTable: [
                ConvertedInputFiles.humble_valid,
                ConvertedInputFiles.humble_almost_free,
            ],
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path("fact_sales_humble_v.csv")
        },
    ),
    ProcessingScenario(
        scenario_name="HUMBLE: valid, empty",
        input_tables_paths={
            ObservationSalesTable: [
                ConvertedInputFiles.humble_valid,
                ConvertedInputFiles.humble_almost_free,
            ],
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path("fact_sales_humble_v.csv")
        },
    ),
    ProcessingScenario(
        scenario_name="Valid Steam Impressions old format",
        input_tables_paths={
            ObservationVisibilityTable: ConvertedInputFiles.steam_impressions_old_valid,
            ExternalSKUsTable: aux_path("steam_impressions_sku_v.csv"),
        },
        output_tables_paths={
            TableName.FACT_VISIBILITY: aggregated_path("fact_visibility_old_v.csv")
        },
    ),
    ProcessingScenario(
        scenario_name="Valid Steam Impressions",
        input_tables_paths={
            ObservationVisibilityTable: ConvertedInputFiles.steam_impressions_valid,
            ExternalSKUsTable: aux_path("steam_impressions_sku_v.csv"),
        },
        output_tables_paths={
            TableName.FACT_VISIBILITY: aggregated_path("fact_visibility_v.csv")
        },
    ),
    ProcessingScenario(
        scenario_name="Empty impressions",
        input_tables_paths={
            ObservationVisibilityTable: ObservationVisibilityTable.empty().df,
        },
        output_tables_paths={
            TableName.FACT_VISIBILITY: EMPTY_TABLE,
        },
    ),
    ProcessingScenario(
        scenario_name="Valid Steam Impressions with missing not required columns",
        input_tables_paths={
            ObservationVisibilityTable: [
                ConvertedInputFiles.steam_impressions_missing_not_required_cols,
            ],
        },
        output_tables_paths={
            TableName.FACT_VISIBILITY: aggregated_path(
                "fact_visibility_missing_not_required_cols.csv"
            )
        },
    ),
    ProcessingScenario(
        scenario_name="Valid Steam Impressions Country as Source of Traffic filtered out",
        input_tables_paths={
            ObservationVisibilityTable: [
                ConvertedInputFiles.steam_impressions_with_country,
            ],
        },
        output_tables_paths={
            TableName.FACT_VISIBILITY: aggregated_path(
                "fact_visibility_with_country.csv"
            )
        },
    ),
    ProcessingScenario(
        scenario_name="Valid Steam Wishlist actions",
        input_tables_paths={
            ObservationWishlistActionsTable: [
                ConvertedInputFiles.steam_wishlist_actions_valid,
            ],
        },
        output_tables_paths={
            TableName.FACT_WISHLIST_ACTIONS: aggregated_path(
                "fact_wishlist_actions_v.csv"
            )
        },
    ),
    ProcessingScenario(
        scenario_name="Valid PS Wishlist actions",
        input_tables_paths={
            ObservationWishlistActionsTable: [
                ConvertedInputFiles.ps_wishlist_actions_valid,
            ],
        },
        output_tables_paths={
            TableName.FACT_WISHLIST_ACTIONS: aggregated_path(
                "fact_ps_wishlist_actions_v.csv"
            )
        },
    ),
    ProcessingScenario(
        scenario_name="Valid Steam Wishlist actions - with empty file inside",
        input_tables_paths={
            ObservationWishlistActionsTable: ObservationWishlistActionsTable.empty().df,
        },
        output_tables_paths={
            TableName.FACT_WISHLIST_ACTIONS: EMPTY_TABLE,
        },
    ),
    ProcessingScenario(
        scenario_name="Valid Steam Wishlist cohorts",
        input_tables_paths={
            ObservationWishlistCohortsTable: [
                ConvertedInputFiles.steam_wishlist_cohorts_valid,
            ],
        },
        output_tables_paths={
            TableName.FACT_WISHLIST_COHORTS: aggregated_path(
                "fact_wishlist_cohorts_v.csv"
            )
        },
    ),
    ProcessingScenario(
        scenario_name="Valid empty processing for observation_type: WISHLIST_COHORTS",
        input_tables_paths={
            ObservationWishlistCohortsTable: ObservationWishlistCohortsTable.empty().df,
        },
        output_tables_paths={
            TableName.FACT_WISHLIST_COHORTS: EMPTY_TABLE,
        },
    ),
    ProcessingScenario(
        scenario_name="HUMBLE: filter sku",
        input_tables_paths={
            ObservationSalesTable: [ConvertedInputFiles.humble_filter_sku],
        },
        output_tables_paths={
            TableName.FACT_SALES: aggregated_path("fact_sales_humble_filtered.csv")
        },
    ),
]
fact_scenarios: list[ProcessingScenario] = (
    _scenarios
    + event_day_scenarios
    + baseline_scenarios
    + [nintendo_valid_detected_events]
    + fact_discounts_scenarios
)


valid_portal_aggregation_steam = ProcessingScenario(
    scenario_name="Valid Portal Aggregation: STEAM",
    input_tables_paths={
        ObservationSalesTable: [
            ConvertedInputFiles.steam_valid,
        ],
    },
    output_tables_paths={
        TableName.DIM_PORTALS: aggregated_path("dim_portals_steam.csv"),
        TableName.FACT_SALES: aggregated_path("fact_sales_steam_v.csv"),
    },
)
valid_portal_aggregation_nintendo = ProcessingScenario(
    scenario_name="Valid Portal Aggregation: Nintendo",
    input_tables_paths={
        ObservationSalesTable: [
            ConvertedInputFiles.nintendo_valid,
            ConvertedInputFiles.nintendo_wiiu_valid,
        ],
    },
    output_tables_paths={
        TableName.DIM_PORTALS: aggregated_path("dim_portals_nintendo.csv"),
    },
)

valid_portal_aggregation_playstation = ProcessingScenario(
    scenario_name="Valid Portal Aggregation: Playstation",
    input_tables_paths={
        ObservationSalesTable: [
            ConvertedInputFiles.ps_analytics_valid,
            ConvertedInputFiles.ps_analytics_new_valid,
        ],
    },
    output_tables_paths={
        TableName.DIM_PORTALS: aggregated_path("dim_portals_playstation.csv"),
    },
)
valid_ps_analytics_changed_portal_platform_region = ProcessingScenario(
    scenario_name="Valid PS Analytics: changed portal_platform_region",
    input_tables_paths={
        ObservationSalesTable: [
            ConvertedInputFiles.ps_analytics_changed_portal_platform_region,
        ],
    },
    output_tables_paths={
        TableName.DIM_PORTALS: aggregated_path(
            "dim_portals_ps_analytics_changed_portal_platform_region.csv"
        ),
    },
)
valid_portal_aggregation_steam_visibility = ProcessingScenario(
    scenario_name="Valid Portal Aggregation: Steam Visibility",
    input_tables_paths={
        ObservationVisibilityTable: [
            ConvertedInputFiles.steam_impressions_valid,
        ],
    },
    output_tables_paths={
        TableName.DIM_PORTALS: aggregated_path("dim_portals_steam.csv"),
    },
)
valid_portal_aggregation_steam_wishlist_actions = ProcessingScenario(
    scenario_name="Valid Portal Aggregation: Steam Wishlist Actions",
    input_tables_paths={
        ObservationWishlistActionsTable: [
            ConvertedInputFiles.steam_wishlist_actions_valid,
        ],
    },
    output_tables_paths={
        TableName.DIM_PORTALS: aggregated_path("dim_portals_steam.csv"),
    },
)
dim_portal_scenarios = [
    valid_portal_aggregation_steam,
    valid_portal_aggregation_nintendo,
    valid_portal_aggregation_playstation,
    valid_ps_analytics_changed_portal_platform_region,
    valid_portal_aggregation_steam_visibility,
    valid_portal_aggregation_steam_wishlist_actions,
]

dim_sku_scenarios = [
    ProcessingScenario(
        scenario_name="Valid Steam SKU Aggregation",
        input_tables_paths={
            ObservationSalesTable: ConvertedInputFiles.steam_valid,
            ExternalSKUsTable: aux_path("steam_sku_v.csv"),
        },
        output_tables_paths={
            TableName.DIM_SKU: aggregated_path("dim_sku_steam.csv"),
        },
    ),
    ProcessingScenario(
        scenario_name="Microsoft SKU Aggregation with InAppProduct sku",
        input_tables_paths={
            ObservationSalesTable: ConvertedInputFiles.microsoft_valid_inapp_sku,
        },
        output_tables_paths={
            TableName.DIM_SKU: aggregated_path("dim_sku_micrososft_inapp.csv"),
        },
    ),
    ProcessingScenario(
        scenario_name="Valid Steam SKU Aggregation with applicationName sku",
        input_tables_paths={
            ObservationSalesTable: ConvertedInputFiles.microsoft_applicationname_sku,
        },
        output_tables_paths={
            TableName.DIM_SKU: aggregated_path(
                "dim_sku_micrososft_applicationname.csv"
            ),
        },
    ),
    ProcessingScenario(
        scenario_name="Valid Steam SKU Aggregation, new SKUs from report",
        input_tables_paths={
            ObservationSalesTable: ConvertedInputFiles.steam_valid,
            ExternalSKUsTable: aux_path("steam_sku_v_e.csv"),
        },
        output_tables_paths={
            TableName.DIM_SKU: aggregated_path("dim_sku_steam.csv"),
        },
    ),
    ProcessingScenario(
        scenario_name="Valid Steam SKU Aggregation, new SKUs from RS",
        input_tables_paths={
            ObservationSalesTable: ConvertedInputFiles.steam_valid,
            ExternalSKUsTable: aux_path("steam_sku_v_aux.csv"),
        },
        output_tables_paths={
            TableName.DIM_SKU: aggregated_path("dim_sku_steam_aux.csv"),
        },
    ),
    ProcessingScenario(
        scenario_name="Valid Nintendo CSV file with different title names SKU",
        input_tables_paths={
            ObservationSalesTable: ConvertedInputFiles.nintendo_valid_different_title_names,
        },
        output_tables_paths={
            TableName.DIM_SKU: aggregated_path(
                "dim_sku_nintendo_different_title_name.csv"
            ),
        },
    ),
    ProcessingScenario(
        scenario_name="Valid Steam Impressions SKU Aggregation",
        input_tables_paths={
            ObservationVisibilityTable: ConvertedInputFiles.steam_impressions_valid,
            ExternalSKUsTable: aux_path("steam_impressions_sku_v.csv"),
        },
        output_tables_paths={
            # OLEK: CS has new behavior, it should not return skus that are not there anymore :)
            TableName.DIM_SKU: aggregated_path("dim_sku_steam_impressions_cs.csv"),
        },
    ),
    ProcessingScenario(
        scenario_name="Valid SKU Aggregation with emojis",
        input_tables_paths={
            ObservationSalesTable: ConvertedInputFiles.ps_analytics_valid_with_emojis,
        },
        output_tables_paths={
            TableName.DIM_SKU: aggregated_path("dim_sku_emojis.csv"),
        },
    ),
    ProcessingScenario(
        scenario_name="Changed sku human_name in incoming report",
        input_tables_paths={
            ObservationSalesTable: ConvertedInputFiles.humble_valid,
            ExternalSKUsTable: aux_path("humble_sku.csv"),
        },
        output_tables_paths={
            # OLEK: package_name is not supported anymore :)
            TableName.DIM_SKU: aggregated_path("dim_sku_humble_new_human_name_cs.csv"),
        },
    ),
    ProcessingScenario(
        scenario_name="Steam Wishlist actions: overrides corrupted name from cohorts",
        input_tables_paths={
            ObservationSalesTable: ConvertedInputFiles.humble_valid,
            ExternalSKUsTable: aux_path("humble_sku.csv"),
        },
        output_tables_paths={
            # OLEK: package_name is not supported anymore :)
            TableName.DIM_SKU: aggregated_path("dim_sku_humble_new_human_name_cs.csv"),
        },
    ),
]

dimension_scenarios: list[ProcessingScenario] = (
    [
        ProcessingScenario(
            scenario_name="Valid Portal Aggregation: PS One",
            input_tables_paths={
                ObservationSalesTable: ConvertedInputFiles.ps_api_valid_one,
            },
            output_tables_paths={
                TableName.FACT_SALES: aggregated_path("fact_sales_ps_api_one.csv"),
                TableName.DIM_PORTALS: aggregated_path("dim_portals_ps_api_one.csv"),
            },
        ),
        ProcessingScenario(
            scenario_name="Valid Portal Aggregation: PS VR 2",
            input_tables_paths={
                ObservationSalesTable: ConvertedInputFiles.ps_api_valid_vr2,
            },
            output_tables_paths={
                TableName.FACT_SALES: aggregated_path("fact_sales_ps_api_vr2.csv"),
                TableName.DIM_PORTALS: aggregated_path("dim_portals_ps_api_vr2.csv"),
            },
        ),
    ]
    + dim_portal_scenarios
    + dim_sku_scenarios
)
