from pathlib import Path
from typing import Any

import pandas as pd
import pytest

from core_silver import get_project_root
from core_silver.observation_converter.converters.converters_factory import (
    get_converter_class_using_source,
)
from core_silver.observation_converter.exceptions import ProcessorError
from data_sdk.domain.domain_types import ReportMetadata, ReportMetadataWithRawFile
from data_sdk.domain.source import Source
from data_sdk.domain.tables import TableDefinition
from tests.comparisons import compare_dataframes
from tests.snapshot.metadata import (
    all_test_metadata,
    empty_test_metadata,
    invalid_test_metadata,
)


def read_input_test_file(file_name: str) -> bytes:
    file_path = get_project_root() / "tests" / "snapshot" / "input" / file_name
    with Path.open(file_path, "rb") as file:
        return file.read()


def read_output_test_file(file_name: str) -> pd.DataFrame:
    file_path = get_project_root() / "tests" / "snapshot" / "output" / file_name
    return pd.read_parquet(file_path)


def generate_init_kwargs(
    cls: Any,
    mocked_tables: dict[str, TableDefinition],
) -> dict[str, Any]:
    init_kwargs: dict[str, Any] = {}
    for param_name, param_type in cls.__init__.__annotations__.items():
        if param_name == "return":
            continue  # Ignore return annotation

        if issubclass(param_type, TableDefinition):
            init_kwargs[param_name] = mocked_tables[param_name]
        else:
            if param_name == "raw_report" and param_type is ReportMetadataWithRawFile:
                continue  # Ignore raw_report parameter

            raise TypeError(  # noqa: TRY003
                f"Unsupported type for parameter {param_name}: {param_type}"
            )

    return init_kwargs


def convert_new_file(metadata, zip_content, mocked_tables):
    report_metadata = ReportMetadataWithRawFile(metadata=metadata, raw_file=zip_content)
    converter_class = get_converter_class_using_source(Source(metadata.source))
    init_kwargs = generate_init_kwargs(cls=converter_class, mocked_tables=mocked_tables)
    converter = converter_class(report_metadata, **init_kwargs)
    return converter.convert()


@pytest.fixture
def mocked_tables(
    external_currency_exchange_rates_table, external_feature_flags_table_no_flags
):
    return {
        "external_currency_exchange_rates_table": external_currency_exchange_rates_table,
        "external_feature_flags_table": external_feature_flags_table_no_flags,
    }


@pytest.mark.parametrize(
    "metadata",
    all_test_metadata,
    ids=lambda x: x.original_name,
)
def test_valid_file(metadata: ReportMetadata, mocked_tables, to_utf8_for_parquet):
    zip_content = read_input_test_file(metadata.original_name)

    filename_without_extension = metadata.original_name.split(".")[0]
    expected_result_file = f"{filename_without_extension}.parquet"
    expected_result = read_output_test_file(expected_result_file)
    result = convert_new_file(metadata, zip_content, mocked_tables)

    simplified_result = to_utf8_for_parquet(result.df)

    compare_dataframes(
        simplified_result.to_pandas(), expected_result
    )  # TODO: remove to_pandas and compare using polars


@pytest.mark.parametrize(
    ("metadata", "expected_error_message"),
    invalid_test_metadata,
    ids=lambda x: x.original_name if isinstance(x, ReportMetadata) else None,
)
def test_invalid_file(
    metadata: ReportMetadata, expected_error_message: str, mocked_tables
):
    zip_content = read_input_test_file(metadata.original_name)

    with pytest.raises((ProcessorError, Exception)) as ex_info:
        convert_new_file(metadata, zip_content, mocked_tables)

    message = getattr(ex_info.value, "message", None) or str(ex_info.value)
    assert message == expected_error_message


@pytest.mark.parametrize(
    "metadata",
    empty_test_metadata,
    ids=lambda x: x.original_name,
)
def test_empty_file(metadata: ReportMetadata, mocked_tables):
    zip_content = read_input_test_file(metadata.original_name)
    result = convert_new_file(metadata, zip_content, mocked_tables)

    assert result.empty
