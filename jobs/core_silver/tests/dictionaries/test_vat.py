import pandas as pd
import pytest

from core_silver.dictionaries.vat import (
    convert_gross_to_net,
    convert_net_to_gross,
    get_vat_rate,
)
from core_silver.utils.math_tools import js_round


def test_get_vat_rate():
    assert get_vat_rate("POL") == 0.23
    assert get_vat_rate("USA") == 0.07


def test_convert_net_to_gross():
    assert convert_net_to_gross(100, "POL") == 123
    assert convert_net_to_gross(100, "USA") == 107
    assert convert_net_to_gross(100, "GBR") == 120


def test_convert_gross_to_net():
    assert convert_gross_to_net(123, "POL") == 100
    assert convert_gross_to_net(107, "USA") == 100
    assert convert_gross_to_net(120, "GBR") == 100


def test_convert_net_to_gross_rounding():
    assert convert_net_to_gross(100.01, "POL") == 123.01
    assert convert_net_to_gross(200.12, "USA") == 214.13
    assert convert_net_to_gross(400.54, "GBR") == 480.65


def test_convert_net_to_gross_to_net():
    for cents in range(1, 10000):
        net = js_round(cents / 100, 2)
        for currency in ["POL", "USA", "HUN"]:
            assert (
                convert_gross_to_net(convert_net_to_gross(net, currency), currency)
                == net
            )


def test_convert_gross_to_net_to_gross():
    for currency in ["POL", "USA", "HUN"]:
        for gross in range(1, 10000):
            assert convert_net_to_gross(
                convert_gross_to_net(gross, currency), currency
            ) == pytest.approx(gross, 0.01)  # we loose precision due to double rounding


def test_vat_value_set_to_0_for_not_listed_vat_rates():
    assert get_vat_rate("NON_EXISTING") == 0
    assert convert_net_to_gross(100, "NON_EXISTING") == 100
    assert convert_gross_to_net(100, "NON_EXISTING") == 100


def test_convert_net_to_gross_works_with_series():
    result1 = convert_net_to_gross(pd.Series([100, 200]), pd.Series(["POL", "USA"]))
    result2 = convert_net_to_gross(pd.Series([100, 200]), "POL")

    assert list(result1.values) == [123, 214]
    assert list(result2.values) == [123, 246]


def test_convert_gross_to_net_works_with_series():
    result1 = convert_gross_to_net(pd.Series([123, 214]), pd.Series(["POL", "USA"]))
    result2 = convert_gross_to_net(pd.Series([123, 246]), "POL")

    assert list(result1.values) == [100, 200]
    assert list(result2.values) == [100, 200]
