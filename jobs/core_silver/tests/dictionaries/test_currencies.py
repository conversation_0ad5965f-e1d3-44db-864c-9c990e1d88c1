from datetime import datetime
from unittest.mock import patch

import pandas as pd
import polars as pl

from core_silver.dictionaries.currencies import (
    DEFAULT_CURRENCY_EXCHANGE_RATE,
    generate_currency_exchange_rates,
)
from data_sdk.domain.tables import ExternalCurrencyExchangeRatesTable
from tests.comparisons import compare_dataframes

USD_2020_01_01_RATE = 1.00
PLN_2019_01_01_RATE = 3.01
PLN_2020_01_02_RATE = 4.02
PLN_2020_01_03_RATE = 4.03
GBP_2022_01_01_RATE = 0.75

table = ExternalCurrencyExchangeRatesTable(
    df=pl.DataFrame([
        {
            "date": datetime(2020, 1, 1),
            "currency_code": "USD",
            "rate_to_usd": USD_2020_01_01_RATE,
        },
        {
            "date": datetime(2019, 1, 1),
            "currency_code": "PLN",
            "rate_to_usd": PLN_2019_01_01_RATE,
        },
        {
            "date": datetime(2020, 1, 2),
            "currency_code": "PLN",
            "rate_to_usd": PLN_2020_01_02_RATE,
        },
        {
            "date": datetime(2020, 1, 3),
            "currency_code": "PLN",
            "rate_to_usd": PLN_2020_01_03_RATE,
        },
        {
            "date": datetime(2022, 1, 1),
            "currency_code": "GBP",
            "rate_to_usd": GBP_2022_01_01_RATE,
        },
    ])
)


def test_empty_input():
    df = pd.DataFrame(columns=["date", "currency_code"])
    result = generate_currency_exchange_rates(df, table)
    assert list(result) == []


def test_date_before_earliest_record():
    df = pd.DataFrame([
        {"date": datetime(2019, 1, 1), "currency_code": "EUR"},
    ])
    result = generate_currency_exchange_rates(df, table)
    assert list(result) == [DEFAULT_CURRENCY_EXCHANGE_RATE]


def test_single_currency_and_date():
    df = pd.DataFrame([
        {"date": datetime(2022, 1, 1), "currency_code": "GBP"},
    ])
    result = generate_currency_exchange_rates(df, table)
    assert list(result) == [GBP_2022_01_01_RATE]


def test_multiple_currencies_and_dates():
    df = pd.DataFrame([
        {"date": datetime(2019, 1, 1), "currency_code": "PLN"},
        {"date": datetime(2020, 1, 1), "currency_code": "PLN"},
        {"date": datetime(2020, 1, 2), "currency_code": "PLN"},
        {"date": datetime(2020, 1, 3), "currency_code": "PLN"},
    ])
    result = generate_currency_exchange_rates(df, table)
    assert list(result) == [
        PLN_2019_01_01_RATE,
        PLN_2019_01_01_RATE,
        PLN_2020_01_02_RATE,
        PLN_2020_01_03_RATE,
    ]


def test_missing_currency():
    df = pd.DataFrame({"date": [datetime(2020, 1, 1)], "currency_code": ["XYZ"]})
    result = generate_currency_exchange_rates(df, table)
    assert list(result) == [DEFAULT_CURRENCY_EXCHANGE_RATE]


def test_future_dates_using_the_last_known_rate():
    df = pd.DataFrame({"date": [datetime(2100, 1, 1)], "currency_code": ["GBP"]})
    result = generate_currency_exchange_rates(df, table)
    assert list(result) == [GBP_2022_01_01_RATE]


def test_rates_are_returned_valid_for_different_currencies():
    df = pd.DataFrame([
        {"date": datetime(2020, 1, 1), "currency_code": "USD"},
        {"date": datetime(2020, 1, 2), "currency_code": "PLN"},
        {"date": datetime(2022, 1, 1), "currency_code": "GBP"},
    ])

    result = generate_currency_exchange_rates(df, table)

    assert list(result) == [
        USD_2020_01_01_RATE,
        PLN_2020_01_02_RATE,
        GBP_2022_01_01_RATE,
    ]


def test_rates_are_assigned_correctly_regardles_if_order_of_rows_changed():
    df = pd.DataFrame([
        {"date": datetime(2022, 1, 1), "currency_code": "GBP"},
        {"date": datetime(2020, 1, 2), "currency_code": "PLN"},
        {"date": datetime(2020, 1, 1), "currency_code": "USD"},
    ])

    df["rate_to_usd"] = generate_currency_exchange_rates(df, table)

    assert list(df["rate_to_usd"]) == [
        GBP_2022_01_01_RATE,
        PLN_2020_01_02_RATE,
        USD_2020_01_01_RATE,
    ]


def test_source_df_is_not_sorted_as_a_side_effect_after_passing_to_generate_currency_exchange_rates():
    df = pd.DataFrame([
        {"date": datetime(2020, 1, 2), "currency_code": "PLN"},
        {"date": datetime(2022, 1, 1), "currency_code": "GBP"},
        {"date": datetime(2020, 1, 1), "currency_code": "USD"},
    ])

    df_copy = df.copy()
    generate_currency_exchange_rates(df, table)

    compare_dataframes(df, df_copy)
    assert df.equals(df_copy)


def test_date_column_can_be_type_string():
    df = pd.DataFrame([
        {"date": "2020-01-01", "currency_code": "USD"},
        {"date": "2020-01-02", "currency_code": "PLN"},
        {"date": "2022-01-01", "currency_code": "GBP"},
    ])

    result = generate_currency_exchange_rates(df, table)

    assert list(result) == [
        USD_2020_01_01_RATE,
        PLN_2020_01_02_RATE,
        GBP_2022_01_01_RATE,
    ]


@patch("core_silver.dictionaries.currencies.log.warning")
def test_generate_currency_exchange_rates_logs_warning_only_on_unsupported_currencies(
    mock_log_warning,
):
    source_df = pd.DataFrame([
        {"date": "2023-01-01", "currency_code": "USD"},
        {"date": "2023-01-02", "currency_code": "XYZ"},
    ])

    rates_df = pl.DataFrame([
        {"date": "2023-01-01", "currency_code": "USD", "rate_to_usd": 1.0},
    ])

    generate_currency_exchange_rates(
        source_df, ExternalCurrencyExchangeRatesTable(df=rates_df)
    )

    mock_log_warning.assert_called_once_with("Missing exchange rates for ['XYZ']")

    mock_log_warning.reset_mock()

    rates_df = pl.DataFrame([
        {"date": "2023-01-01", "currency_code": "USD", "rate_to_usd": 1.0},
        {"date": "2023-01-02", "currency_code": "XYZ", "rate_to_usd": 2.0},
    ])

    generate_currency_exchange_rates(
        source_df, ExternalCurrencyExchangeRatesTable(df=rates_df)
    )

    mock_log_warning.assert_not_called()
