import csv
import io
import json
from zipfile import ZIP_DEFLATED, ZipFile

import polars as pl


def _mock_zip_file(files_dict: dict[str, str | dict]) -> ZipFile:
    """
    Create a zip file in memory from the provided file contents.

    :param files_dict: A dictionary containing filename-content pairs.
    :return: bytes object representing a zip file.
    """
    zip_buffer = io.BytesIO()

    with ZipFile(zip_buffer, "a", ZIP_DEFLATED) as zip_file:
        for name, content in files_dict.items():
            content = content if isinstance(content, str) else json.dumps(content)
            byte_content = (
                content if isinstance(content, bytes) else content.encode("utf-8")
            )
            zip_file.writestr(name, byte_content)

    zip_buffer.seek(0)  # rewind to the beginning, so it can be read by ZipFile
    return ZipFile(zip_buffer)


def _raw_zip_file(zip_content: dict[str, str | dict]) -> bytes:
    with _mock_zip_file(zip_content) as zip_file:
        return zip_file.fp.getvalue()


def _mock_csv_file(
    headers: list[str],
    content: list[dict[str, str | int]],
    lineterminator: str = "\n",
) -> str:
    csv_buffer = io.StringIO()

    writer = csv.DictWriter(
        csv_buffer, fieldnames=headers, lineterminator=lineterminator
    )

    writer.writeheader()
    for row in content:
        writer.writerow(row)

    return csv_buffer.getvalue()


def _csv_file_factory(data_idx: int, headers: list[str] = None, rows: int = 1) -> str:
    """
    Returns csv file content with header ["a", "b", "c"] and data:
    [
        {"a": 1, "b": 2, "c": 3},
        {"a": 4, "b": 5, "c": 6},
        ...
    ]
    with a specified number of `rows`.

    :param data_idx: An integer to manipulate data values.
    :param headers: A list of strings specifying the CSV headers.
    :param rows: An integer specifying how many rows of data to produce.
    :return: A string representing the CSV file content.
    """
    headers = headers or ["a", "b", "c"]
    base = len(headers)

    data: list[dict[str, str | int]] = [
        {
            header: (data_idx - 1) * base + index + 1 + row * base
            for index, header in enumerate(headers)
        }
        for row in range(rows)
    ]

    return _mock_csv_file(headers, data)


def _to_utf8_for_parquet(df: pl.DataFrame):
    """
    This function simplifies the dataframe by casting all categorical and enum columns to Utf8.
    This is needed because the expected result is stored as parquet and polars does not support categorical and enum types.
    """
    return df.with_columns([
        pl.col(col).cast(pl.Utf8)
        if df[col].dtype in [pl.Categorical, pl.Enum]
        else pl.col(col)
        for col in df.columns
    ])
