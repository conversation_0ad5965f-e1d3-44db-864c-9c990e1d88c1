# Steam Discounts - Knowlage base


There are different types of discount events:


| Event type     | examples of name | discount type | triggers cooldown | major | notes |
| -------------- | ---------------- | ------------- | ----------------- |------ |------ |
| Seasonal sale  | Steam Winter Sale 2023, Steam Autumn Sale 2023 | STORE | No | Yes | |
| Special sales  | Steam Pirates vs. Ninjas Fest, Steam FPS Fest | STORE | Yes | No | |
| Weeklong deal  | Weeklong deal for Nov 13, Weeklong deal for Dec 04 | STORE | Yes | No | |
| Weekend sale  | Weekend Deal - 10/12/2023 - THE COMPLETE SUPERHOT BUNDLE | STORE | Yes | No | In the past Superhot (and possibly others) were able to got extra Weekend sales from Steam, which were not triggering a cooldown | |
| Custom event  | SUPERHOT SALE, Daily Deal - SUPERHOT | CUSTOM | Yes | No | |

**THERE ARE EXCEPTIONS FROM THE ABOVE RULES** in terms of what was considered a 'major', and what was triggering a cooldown. We have a manually created list of "steam_events_history" here: https://docs.google.com/spreadsheets/d/1nF_GuTbE9smkWd16t7xaWSRL-kVhVHM_d-MVY-P84E4/edit?usp=sharing, created and maintained by: Vasil Bratanov, Mohamad Ali Nesser and Łukasz Iwanowski

## Other Notes

Facts about **major** events:

- an event which do not triggers a cooldown
- only major events not triggers cooldown
- major events are always store events
- major events has CollisionType == "unique"

---

When a discount (in `discountsEvents.csv`) has in `collisionType` column has value:
- `proximity` - it means that this discount **triggers a cooldown**
- `unique` - it means that this discount **does not trigger a cooldown**

If a discount (in `discountHistory.csv`) contains `false` in a `group` columns, that means it is a `custom` event, otherwise it is a `store` event.

---

Event service will be able to determine if an Event (aka "Słonik"):
- `Fact` by checking the date of the event (everything in the past)
- `Planned` - event in the future, to which a studio joined (this could be determined by checking if a discount has `discount_depth` > 0)
- `Opportunity` - event in the future, to which a studio did not joined. We distinguish between:
    - `Store` - opportunities suggested by Steam
    - `Calculated` - opportunities calculated by IndieBI, in Event Service, by magic models.

We should not calculate and assign the type (for example `Planned`/`Fact`) to the event data, because this will change as the time goes by. Instead this should be calculated on the fly, by the Event Service, based on provided data and above rules.

---

At the moment of 2023-12-22, it looks like `discountsEvents.csv`` contains only store events
