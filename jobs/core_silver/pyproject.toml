[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poe]
include = "../../common/poe_shared_tasks.toml"

[tool.poe.tasks]
hooks = { "shell" = "../../.hooks/install.sh 'jobs/core_silver'" }
generate-snapshots = "poetry run python -m scripts.generate_snapshots"

[tool.poetry]
name = "core_silver"
version = "0.1.0"
description = "core silver job"
authors = ["Your Name <<EMAIL>>"]

[tool.poetry.dependencies]
python = "~3.12"
pandas = "^2.2.2"
azure-common = "^1.1.27"
azure-core = "^1.19.0"
azure-identity = "^1.13.0"
azure-servicebus = "^7.3.3"
pandera = { extras = ["mypy", "polars"], version = "^0.20.3" }
azure-storage-blob = "^12.9.0"
azure-storage-file-datalake = "^12.5.0"
debugpy = "^1.8.5"
logging-json = "^0.2.0"
pydantic = "^2.7.1"
types-python-dateutil = "^2.9.0.20240316"
polars = "^0.20.22"
sentry-sdk = "^2.0.0"
pipeline-sdk = {version = "2.5.2", source = "indiebi"}
numpy = "^1.26.4"
data-sdk = { path = "../../libs/data-sdk", develop = true }
typer = "^0.12.5"
emoji = "^2.0.0"
time-machine = "^2.14.1"
elastic-apm = "^6.22.3"
deltalake = "^1.1.4"

[tool.poetry.group.dev.dependencies]
debugpy = "^1.8.1"
responses = "^0.25.0"
factory-boy = "^3.3.0"
ipykernel = "^6.29.4"
ruff = "^0.4.5"
types-emoji = "^*******"
pre-commit = "^3.7.1"
poethepoet = "^0.26.1"
azure-keyvault-secrets = "^4.8.0"
azure-identity = "^1.17.1"

[tool.poetry.group.test.dependencies]
pytest = "^8.2.2"
pytest-deadfixtures = "^2.2.1"
pytest-xdist = "^3.6.1"
pytest-random-order = "^1.1.1"

[tool.poetry.group.profiling]
optional = true

[tool.poetry.group.profiling.dependencies]
py-spy = "^0.3.14"
memory-profiler = "^0.61.0"
matplotlib = "^3.9.0"

[[tool.poetry.source]]
name = "indiebi"
url = "https://gitlab.com/api/v4/groups/4449864/-/packages/pypi/simple"
priority = "explicit"

[tool.pytest.ini_options]
testpaths = ["tests"]
filterwarnings = [
    # Q: should we promote every warning to error? Right now we don't have any
    # warnings after filtering Future and Deprecation warnings.
    # "error",
    "ignore::DeprecationWarning",
    "ignore::FutureWarning",
]

[tool.ruff]
preview = true

[tool.ruff.lint]
select = [
    "F",     # Pyflakes
    "E",     # Pycodestyle errors
    "W",     # Pycodestyle warnings
    "I",     # isort
    "ASYNC", # flake8-async (asyncio)
    "S",     # flake8-bandit (security)
    "ERA",   # flake8-eradicate (remove commented out code)
    "FLY",   # flynt (f-string)
    "PTH",   # flake8-use-pathlib
    "A",     # flake8-builtins
    "C4",    # flake8-comprehensions
    "EXE",   # flake8-executable
    "LOG",   # flake8-logging
    "INP",   # flake8-no-pep420 (no implicit namespace packages)
    "T20",   # flake8-print (print statements)
    "PT",    # flake8-pytest-style (pytest specific style issues)
    "RSE",   # flake8-raise (issues with raise statements)
    "SIM",   # flake8-simplify (simplifiable constructs)
    "C90",   # mccabe (complexity checker)
    "N",     # pep8-naming (naming conventions)
    "UP",    # pyupgrade (Python syntax upgrades)
    "TRY",   # tryceratops (anti-patterns in try/except blocks)
    "PGH",   # pygrep-hooks
    "FBT",   # flake8-boolean-trap
    # "ISC",   # flake8-implicit-str-concat
    # "B",     # flake8-bugbear
    # "DTZ",   # flake8-datetimez
]
ignore = [
    "E501", # Line too long
    "E712", # Comparison to False should be 'if cond is False:' or 'if not cond:'
    "N818", # Exception name {name} should be named with an Error suffix
]

[tool.ruff.lint.per-file-ignores]
"tests/*" = [
    "S101",   # Bandit: Use of assert detected
    "INP001", # Implicit namespace package
]
"core_silver/main.py" = [
    "E402", # Module level import not at top of file
]
"**/metadata.py" = [
    "PGH003", # pygrep-hooks: Use specific rule codes when ignoring type issues
]
"core_silver/external_sources/connectors/report_service.py" = [
    "PGH003", # pygrep-hooks: Use specific rule codes when ignoring type issues
]
"scripts/*" = [
    "T201", # flake8-print: print found
]

[tool.ruff.lint.flake8-pytest-style]
fixture-parentheses = false

[tool.ruff.lint.isort]
known-first-party = ["core_silver", "data_sdk"]
