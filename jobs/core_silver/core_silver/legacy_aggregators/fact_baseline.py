import pandas as pd
import pandera as pa
import polars as pl

from core_silver.dictionaries.constants import Constant
from core_silver.legacy_aggregators.fact_event_day import generate_fact_event_day
from core_silver.legacy_aggregators.fact_sales.fact_sales import (
    run_aggregate_legacy_fact_sales,
)
from core_silver.observation_converter.pandas_utils import enforce_schema
from core_silver.validators.columns import (
    ExactLengthString,
    FloatWithTwoDecimalPlaces,
    Int,
    IntInRange,
    MediumString,
    NonNegativeInt,
    TinyString,
)
from data_sdk.aggregator import BaseAggregator
from data_sdk.domain import DisplayPortal, Portal
from data_sdk.domain.tables import (
    ExternalCountryCodesTable,
    LegacyFactBaselineTable,
    ObservationSalesTable,
    SilverPortalsTable,
    SilverSKUsTable,
)


class FactBaselineAggregator(BaseAggregator):
    table_cls = LegacyFactBaselineTable

    def __init__(
        self,
        silver_skus: SilverSKUsTable,
        observation_sales: ObservationSalesTable,
        external_country_codes: ExternalCountryCodesTable,
        silver_portals: SilverPortalsTable,
        portal: Portal,
    ) -> None:
        self._silver_skus = silver_skus
        self._observations_df = observation_sales.df.to_pandas()
        self._external_country_codes = external_country_codes
        self._silver_portals = silver_portals
        self._portal = portal

    int_measures = [
        "free_units",
        "units_returned",
        "units_sold_directly",
        "units_sold_non_billable",
        "units_sold_retail",
    ]
    float_measures = [
        "gross_returned",
        "gross_sales",
        "net_sales_approx",
    ]
    baseline_int_measures = [f"baseline_{measure}" for measure in int_measures]
    uplift_int_measures = [f"uplift_{measure}" for measure in int_measures]
    baseline_float_measures = [f"baseline_{measure}" for measure in float_measures]
    uplift_float_measures = [f"uplift_{measure}" for measure in float_measures]

    measures = int_measures + float_measures
    baseline_measures = baseline_int_measures + baseline_float_measures

    groupby_columns = ["unique_sku_id", "studio_id", "country_code", "weekday"]

    columns_needed_for_calc = [
        "date",
        "studio_id",
        "units_returned",
        "units_sold",
        "free_units",
        "gross_returned",
        "gross_sales",
        "net_sales",
        "net_sales_approx",
        "country_code",
        "sku_studio",
        "category",
        "product_id",
    ]

    schema = pa.DataFrameSchema(
        columns={
            "country_code": ExactLengthString(3),
            "studio_id": NonNegativeInt(),
            "sku_studio": MediumString(),  # temporary
            "unique_sku_id": MediumString(),
            "date": TinyString(nullable=True),
            "date_sku_studio": MediumString(),
            "portal_platform_region": MediumString(),
            "portal_platform_region_id": IntInRange(101000, 999999),
            "product_id": MediumString(),
            "portal": MediumString(),
            "baseline_units_sold_directly": Int(),
            "baseline_units_sold_non_billable": Int(),
            "baseline_units_sold_retail": Int(),
            "baseline_units_returned": Int(),
            "baseline_gross_sales": FloatWithTwoDecimalPlaces(),
            "baseline_gross_returned": FloatWithTwoDecimalPlaces(),
            "baseline_free_units": Int(),
            "baseline_net_sales_approx": FloatWithTwoDecimalPlaces(),
            "uplift_units_sold_directly": Int(),
            "uplift_units_sold_non_billable": Int(),
            "uplift_units_sold_retail": Int(),
            "uplift_units_returned": Int(),
            "uplift_gross_sales": FloatWithTwoDecimalPlaces(),
            "uplift_gross_returned": FloatWithTwoDecimalPlaces(),
            "uplift_free_units": Int(),
            "uplift_net_sales_approx": FloatWithTwoDecimalPlaces(),
        }
    )

    def _aggregate(self) -> pl.DataFrame:
        sku_df = self._silver_skus.df.to_pandas()
        silver_portals_df = self._silver_portals.df.to_pandas()
        country_codes_df = self._external_country_codes.df.to_pandas()

        pd_fact_sales_df = run_aggregate_legacy_fact_sales(
            sku_df, self._observations_df, country_codes_df=country_codes_df
        )
        self._observations_df = None  # Free some RAM

        if pd_fact_sales_df.empty:
            return pl.DataFrame(
                pd.DataFrame(columns=[str(column) for column in self.schema.columns])
            )

        fact_event_day = generate_fact_event_day(
            fact_sales_df=pd_fact_sales_df,
            sku_df=sku_df,
            silver_portals_df=silver_portals_df,
            country_codes_df=country_codes_df,
        )

        fact_event_day_lf = self._prepare_fact_event_day(fact_event_day)

        fact_sales_df = pl.DataFrame(pd_fact_sales_df)
        pd_fact_sales_df = None  # Free some RAM

        fact_sales_lf = fact_sales_df.select(self.columns_needed_for_calc).lazy()

        ppr = fact_sales_df.select(pl.first("portal_platform_region")).get_column(
            "portal_platform_region"
        )[0]
        portal = DisplayPortal(ppr.split(":")[0])
        fact_sales_lf = self._prepare_fact_sales(fact_sales_lf, portal)

        grouped_fact_sales_lf = self._group_fact_sales(fact_sales_lf)

        fact_sales_lf = self._filter_low_sales(fact_sales_lf)
        filtered_df_is_empty = fact_sales_lf.first().collect().is_empty()
        if filtered_df_is_empty:
            return pl.DataFrame(
                pd.DataFrame(columns=[str(column) for column in self.schema.columns])
            )
        fact_baseline_lf = self._group_fact_sales(fact_sales_lf)
        fact_baseline_lf = self._resample_grouped_sales(fact_baseline_lf.collect())

        fact_baseline_lf = self._reset_early_sales(fact_baseline_lf)
        fact_baseline_lf = self._join_event_days(fact_baseline_lf, fact_event_day_lf)
        fact_baseline_lf = self._forward_backward_fill(fact_baseline_lf)
        fact_baseline_lf = self._baseline_fill(fact_baseline_lf)
        fact_baseline_lf = self._join_with_input(
            fact_baseline_lf, grouped_fact_sales_lf
        )
        fact_baseline_lf = self._round_int_measures(fact_baseline_lf)
        fact_baseline_lf = self._calculate_uplift(fact_baseline_lf)

        fact_baseline_lf = fact_baseline_lf.with_columns(
            pl.col("unique_sku_id").alias("sku_studio")
        )  # TODO: remove sku_studio when all conflicts are resolved # copied from report processon

        fact_baseline_lf = (
            fact_baseline_lf.with_columns([
                pl.lit("temp").alias("date_sku_studio"),
                pl.lit("temp").alias("portal_platform_region"),
                pl.lit(101001).alias("portal_platform_region_id"),
                pl.lit("temp").alias("product_id"),
            ])
        )  # TODO: remove when all conflicts are resolved # copied from report processon

        # Types conversion for validator
        fact_baseline_lf = fact_baseline_lf.with_columns(
            pl.col("date").dt.strftime("%Y-%m-%d")
        )
        float_columns = self.baseline_float_measures + self.uplift_float_measures
        # function round in polars works differently than in Python 3.x and pandas
        fact_baseline_lf = fact_baseline_lf.with_columns(pl.col(float_columns).round(2))

        fact_baseline_lf = fact_baseline_lf.with_columns(
            pl.lit(self._portal.value).alias("portal")
        )
        # Conversion to Pandas
        columns = [str(column) for column in self.schema.columns]
        fact_baseline_lf = fact_baseline_lf.select(columns)
        fact_baseline_pandas_df = (
            fact_baseline_lf.sort(["unique_sku_id", "date"], descending=True)
            .collect()
            .to_pandas()
        )

        return pl.DataFrame(
            enforce_schema(
                (
                    fact_baseline_pandas_df
                    if not fact_baseline_pandas_df.empty
                    else pd.DataFrame(columns=columns)
                ),
                self.schema,
            )
        )

    def _prepare_fact_sales(
        self, fact_sales_lf: pl.LazyFrame, portal: DisplayPortal
    ) -> pl.LazyFrame:
        sold_directly_categories = pl.Series([
            "Free & Sale",
            "Free & Return",
            "Sale",
            "Invalid Sale",
            "Return",
            "Invalid Return",
        ])

        return fact_sales_lf.with_columns([
            pl.col("date").str.strptime(pl.Date, format="%Y-%m-%d"),
            pl.when(pl.col("category").is_in(sold_directly_categories))
            .then(pl.col("units_sold"))
            .otherwise(pl.lit(0))
            .alias("units_sold_directly"),
            pl.when(pl.col("category").str.starts_with("Retail"))
            .then(pl.col("units_sold"))
            .otherwise(pl.lit(0))
            .alias("units_sold_retail"),
            pl.when(pl.col("category").str.starts_with("Non-billable"))
            .then(pl.col("units_sold"))
            .otherwise(pl.lit(0))
            .alias("units_sold_non_billable"),
            pl.when(portal == DisplayPortal.EPIC)
            .then(pl.col("net_sales") * 0.88)
            .otherwise(pl.col("net_sales") * 0.7)
            .alias("net_sales_approx"),
        ]).rename({"sku_studio": "unique_sku_id"})

    def _prepare_fact_event_day(
        self, fact_event_day_pandas_df: pl.DataFrame
    ) -> pl.LazyFrame:
        if not fact_event_day_pandas_df.is_empty():
            return (
                fact_event_day_pandas_df.lazy()
                .with_columns(pl.col("date").str.strptime(pl.Date, format="%Y-%m-%d"))
                .groupby(["date", "unique_sku_id"])
                .agg([])
                .with_columns(pl.lit("promo").alias("promo_regular"))
            )
        else:
            return pl.DataFrame({
                "unique_sku_id": [],
                "date": [],
                "studio_id": [],
                "promo_regular": [],
            }).lazy()

    def _filter_low_sales(self, fact_sales: pl.LazyFrame) -> pl.LazyFrame:
        return fact_sales.filter(
            (
                (pl.col("gross_sales").sum() > Constant.BIG_SKU_SALES_THRESHOLD.value)
                | (
                    # Enable baseline calc for free skus with big units volume
                    # getting its monetization from separate in_app purchases
                    (pl.col("units_sold").sum() == 0)
                    & (
                        pl.col("free_units").sum()
                        > Constant.BIG_SKU_FREE_UNITS_THRESHOLD.value
                    )
                )
            ).over("unique_sku_id")
        )

    def _group_fact_sales(self, fact_sales: pl.LazyFrame) -> pl.LazyFrame:
        return (
            fact_sales.groupby([
                "unique_sku_id",
                "date",
                "studio_id",
                "country_code",
            ])
            .agg(pl.col(self.measures).sum())
            .with_columns([
                pl.col("date").dt.weekday().alias("weekday"),
                (pl.col("free_units") + pl.col("units_sold_non_billable")).alias(
                    "free_units"
                ),
            ])
            .sort("date")
        )

    def _resample_grouped_sales(self, grouped_sales: pl.DataFrame) -> pl.LazyFrame:
        return grouped_sales.upsample(
            "date", every="7d", by=self.groupby_columns
        ).lazy()

    def _reset_early_sales(
        self, resampled_sales: pl.LazyFrame, threshold: int = 30
    ) -> pl.LazyFrame:
        lf = resampled_sales
        for measure in self.measures:
            lf = lf.with_columns(
                pl.when(
                    (
                        pl.col("date")
                        < pl.col("date").min().dt.offset_by(f"{threshold}d")
                    ).over(["unique_sku_id", "country_code"])
                )
                .then(pl.lit(None))
                .otherwise(pl.col(measure))
                .alias(measure)
            )
        return lf

    def _join_event_days(
        self, resampled_fact_sales: pl.LazyFrame, event_days: pl.LazyFrame
    ) -> pl.LazyFrame:
        lf = resampled_fact_sales
        lf = lf.with_columns([
            pl.col(self.groupby_columns).forward_fill(),
            pl.col(self.measures).fill_null(strategy="zero"),
        ])

        if event_days.first().collect().height:
            lf = lf.join(
                event_days, on=["date", "unique_sku_id"], how="left"
            ).with_columns([
                pl.col("promo_regular").fill_null("regular"),
                pl.col("date").dt.weekday().alias("weekday"),
            ])
        else:
            lf = lf.with_columns(pl.lit("regular").alias("promo_regular"))

        for measure in self.measures:
            lf = lf.with_columns(
                pl.when(pl.col("promo_regular") == "promo")
                .then(pl.lit(None))
                .otherwise(pl.col(measure))
                .alias(measure)
            )
        return lf

    def _forward_backward_fill(
        self, resampled_fact_sales: pl.LazyFrame
    ) -> pl.LazyFrame:
        return (
            resampled_fact_sales.sort("date")
            .with_columns(
                pl.col(self.measures).forward_fill().over(self.groupby_columns)
            )
            .with_columns(
                pl.col(self.measures).backward_fill().over(self.groupby_columns)
            )
            .with_columns(pl.col(self.measures).fill_null(strategy="zero"))
            .with_columns(
                pl.col(self.measures)
                .rolling_median(5)
                .over(self.groupby_columns)
                .prefix("baseline_")
            )
        )

    def _baseline_fill(self, baseline_lf: pl.LazyFrame) -> pl.LazyFrame:
        return (
            baseline_lf.with_columns(
                pl.col(self.baseline_measures).forward_fill().over(self.groupby_columns)
            )
            .with_columns(
                pl.col(self.baseline_measures)
                .backward_fill()
                .over(self.groupby_columns),
            )
            .with_columns(pl.col(self.baseline_measures).fill_null(strategy="zero"))
        )

    def _join_with_input(
        self, baseline_lf: pl.LazyFrame, grouped_input_sales: pl.LazyFrame
    ) -> pl.LazyFrame:
        lf = baseline_lf.select(
            ["date", "unique_sku_id", "studio_id", "country_code"]
            + self.baseline_measures
        )
        lf_input = grouped_input_sales.select(
            ["date", "unique_sku_id", "country_code"] + self.measures
        )

        lf = lf.join(
            lf_input, on=["date", "unique_sku_id", "country_code"], how="left"
        ).fill_null(strategy="zero")
        return lf

    def _round_int_measures(self, baseline_lf: pl.LazyFrame) -> pl.LazyFrame:
        return baseline_lf.with_columns(
            pl.col(self.baseline_int_measures).round(0).cast(pl.Int64)
        )

    def _calculate_uplift(self, baseline_lf: pl.LazyFrame) -> pl.LazyFrame:
        lf = baseline_lf
        for i in range(len(self.measures)):
            lf = lf.with_columns(
                (
                    pl.col(self.measures[i]) - pl.col(f"baseline_{self.measures[i]}")
                ).alias(f"uplift_{self.measures[i]}")
            )
        return lf
