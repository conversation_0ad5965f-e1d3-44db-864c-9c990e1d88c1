import pandera as pa
import polars as pl

from core_silver.validators.columns import (
    Bool,
    EnumString,
    MediumString,
    NonNegativeInt,
    TinyString,
)
from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.discounts import DiscountType
from data_sdk.domain.ppr import get_portal_platform_region_id
from data_sdk.domain.tables import LegacyFactDiscountsTable, ObservationDiscountsTable


class FactDiscountsAggregator(BaseAggregator):
    table_cls = LegacyFactDiscountsTable

    def __init__(self, observation_discounts: ObservationDiscountsTable):
        self._observation_discounts = observation_discounts

    schema = pa.DataFrameSchema(
        columns={
            "report_id": NonNegativeInt(),
            "create_time": TinyString(),
            "update_time": TinyString(),
            "base_sku_id": MediumString(),
            "source_specific_discount_sku_id": MediumString(),
            "unique_sku_id": MediumString(),
            "studio_id": NonNegativeInt(),
            "discount_depth": NonNegativeInt(),
            "discount_type": EnumString(DiscountType),
            "datetime_from": TinyString(),
            "datetime_to": TinyString(),
            "is_event_joined": Bool(),
            "triggers_cooldown": Bool(),
            "major": Bool(),
            "event_name": MediumString(),
            "base_event_id": MediumString(nullable=True),
            "unique_event_id": MediumString(),
            "promo_length": NonNegativeInt(),
            "max_discount_percentage": NonNegativeInt(),  # TODO: In the future we want to move it to dim_sku
            "price_increase_time": TinyString(),  # TODO: In the future we want to move it to dim_sku
            "portal_platform_region_id": NonNegativeInt(),
        }
    )

    def _aggregate(self) -> pl.DataFrame:
        observations_df = self._observation_discounts.df.to_pandas()

        if observations_df.empty:
            return pl.DataFrame()

        observations_df["portal_platform_region_id"] = (
            observations_df["portal_platform_region"]
            .astype(str)
            .map(get_portal_platform_region_id)
        )
        observations_df = observations_df.drop(
            columns=[
                "portal_platform_region",
                "platform",
                "region",
                "portal",
                "group_id",
            ]
        )
        observations_df["create_time"] = observations_df["create_time"].dt.strftime(
            "%Y%m%dT%H%M%SZ"
        )
        observations_df["update_time"] = observations_df["update_time"].dt.strftime(
            "%Y%m%dT%H%M%SZ"
        )
        observations_df["datetime_from"] = observations_df["datetime_from"].dt.strftime(
            "%Y%m%dT%H%M%SZ"
        )
        observations_df["datetime_to"] = observations_df["datetime_to"].dt.strftime(
            "%Y%m%dT%H%M%SZ"
        )
        observations_df["price_increase_time"] = observations_df[
            "price_increase_time"
        ].dt.strftime("%Y%m%dT%H%M%SZ")
        observations_df["unique_sku_id"] = observations_df["unique_sku_id"].astype(str)
        observations_df["discount_type"] = observations_df["discount_type"].astype(str)
        return pl.DataFrame(observations_df)
