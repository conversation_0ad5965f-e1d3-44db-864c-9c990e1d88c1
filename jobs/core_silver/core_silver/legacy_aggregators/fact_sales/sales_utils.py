import pandas as pd

from core_silver.utils.math_tools import js_round

country_currency_filter_tuples: list[tuple[str, str]] = [
    ("CHN", "CNY"),
    ("CHN", "USD"),
    ("FRA", "EUR"),
    ("FRA", "USD"),
    ("DEU", "USD"),
    ("DEU", "EUR"),
    ("HKG", "USD"),
    ("HKG", "HKD"),
    ("JPN", "JPY"),
    ("JPN", "USD"),
    ("KOR", "KRW"),
    ("KOR", "USD"),
    ("TWN", "TWD"),
    ("TWN", "USD"),
    ("THA", "THB"),
    ("THA", "USD"),
    ("USA", "USD"),
]


def calculate_base_price(partial_fact_sales_df: pd.DataFrame) -> pd.DataFrame:
    """
    Calculates the calculated_base_price for key countries and currencies,
        so we can calculate discount in the biggest countries, even when the portal is
        not informing us about the base_price

    Args:
        partial_fact_sales_df (DataFrame): fact_sales dataframe, which does not have
        additional calculated values yet

    Returns:
        DataFrame: DataFrame that contains a list of calculated_base_price for each sku_studio,
        calculated for the most important currency_code, country_code sets
    """

    # We are filtering records with actual sales and cleaning the records where
    # we have returned units, so the calculations are not impacted by weird
    # situations where sale is very low or negative because of the return
    # (e.g. sold 10$, returned 9$, gross sales 1$)
    # We are also filtering only the countries and currencies from the list
    # because low sales in small countries provide random numbers, so we are
    # depending on the most important countries to calculate

    filtered_records = partial_fact_sales_df[
        (
            partial_fact_sales_df[["country_code", "currency_code"]]
            .apply(tuple, axis=1)
            .isin(country_currency_filter_tuples)
        )
        & (partial_fact_sales_df["units_returned"] == 0)
        & (partial_fact_sales_df["units_sold"] > 0)
        & (partial_fact_sales_df["gross_sales"] > 0)
    ]

    # The price is the value of the price in which the sku was sold,
    # that includes lower prices on sales. The gut feeling would say
    # that the best way to find the base price is to find max of price
    # but the practice shows that we sometimes have price with HIGHER
    # price than the base price in case of returns and random data noise.
    # Average or median is on the other hand too small, because there are so
    # many prices during the product lifetime, that those values are irrelevant.
    # During many hours of our data scientists research it came out that
    # 75 percentile(meaning that 25% of prices are equal or higher than this number)
    # is a sweet spot. However this still has the major problem with the change of the
    # base price to lower, since it would be detected as a constant discount.
    # This should be resolved in the future after some DS research
    calculated_base_price_df = (
        filtered_records.groupby(
            by=[
                "sku_studio",
                "country_code",
                "currency_code",
            ],
            as_index=False,
            observed=False,
        )["price_usd"]
        .quantile(0.75)
        .rename(columns={"price_usd": "calculated_base_price_usd"})
        # TODO: why was reset index needed here?
        # .reset_index(drop=True)
    )

    calculated_base_price_df["calculated_base_price_usd"] = js_round(
        calculated_base_price_df["calculated_base_price_usd"], 2
    )
    return calculated_base_price_df
