import numpy as np
import pandas as pd
import pandera as pa

from core_silver.validators.columns import (
    ExactLengthString,
    FloatWithTwoDecimalPlaces,
    HashString,
    Int,
    IntInRange,
    MediumString,
    NonNegativeInt,
    NullableFloatWithTwoDecimalPlaces,
    SmallString,
    TinyString,
)


def create_release_date_column(df: pd.DataFrame):
    gross_received_mask = (
        (df["gross_received"] > 0)
        & (df["units_gross"] > 0)
        & ((df["category"] == "Sale") | (df["category"] == "Free & Sale"))
    )

    sku_rel_date = (
        df[gross_received_mask][["sku_studio", "date"]]
        .groupby(["sku_studio"], observed=False)
        .min()
    )
    sku_rel_date.columns = ["release_date"]

    return sku_rel_date.reset_index()


def _get_country_region(fact_sales_df: pd.DataFrame, country_codes: pd.DataFrame):
    fact_sales_df = fact_sales_df.merge(
        country_codes[["country_code", "region"]],
        on="country_code",
        how="left",
    )

    return fact_sales_df


def _merge_release_date_qsales(fact_sales_df: pd.DataFrame, sku_rel_date: pd.DataFrame):
    fact_sales_df = pd.merge(
        fact_sales_df,
        sku_rel_date[["sku_studio", "release_date"]],
        on="sku_studio",
        how="left",
    )
    fact_sales_df["date"] = pd.to_datetime(fact_sales_df["date"])
    fact_sales_df["release_date"] = pd.to_datetime(fact_sales_df["release_date"])

    return fact_sales_df


def _get_first_fx(fact_sales_df: pd.DataFrame):
    fact_sales_df["exchange_rate_temp"] = np.where(
        fact_sales_df["price_local"] != 0,
        fact_sales_df["price_usd"] / fact_sales_df["price_local"],
        1,
    )  #

    exchange_rate_first = (
        fact_sales_df[
            (fact_sales_df["units_returned"] == 0)
            & (fact_sales_df["gross_returned"] == 0)
            & (fact_sales_df["category"] == "Sale")
            & (fact_sales_df["price_local"] > 0)
            & (
                (fact_sales_df["gross_received"] > 0)
                | (fact_sales_df["gross_sales"] > 0)
            )
            & ((fact_sales_df["units_gross"] > 0) | (fact_sales_df["units_sold"] > 0))
        ][
            [
                "year",
                "quarter",
                "month",
                "currency_code",
                "country_currency",
                "exchange_rate_temp",
            ]
        ]
        .groupby(["year", "quarter", "month", "currency_code"], observed=False)
        .first()
    )
    exchange_rate_first.columns = ["country_currency", "exchange_rate_first"]

    return exchange_rate_first.reset_index()


def _create_date_values(fact_sales_df: pd.DataFrame):
    fact_sales_df["release_date_quarter"] = fact_sales_df["release_date"].dt.quarter
    fact_sales_df["release_date_year"] = fact_sales_df["release_date"].dt.year
    fact_sales_df["release_date_month"] = fact_sales_df["release_date"].dt.month
    fact_sales_df["quarter"] = fact_sales_df["date"].dt.quarter
    fact_sales_df["year"] = fact_sales_df["date"].dt.year
    fact_sales_df["month"] = fact_sales_df["date"].dt.month

    return fact_sales_df


def _get_initial_sku_price(fact_sales_df: pd.DataFrame):
    fact_sales_df_filtered = fact_sales_df[
        (fact_sales_df["date"] >= fact_sales_df["release_date"])
        & (
            (fact_sales_df["date"] - fact_sales_df["release_date"])
            > pd.Timedelta("5 days")
        )
        & (
            (fact_sales_df["date"] - fact_sales_df["release_date"])
            < pd.Timedelta("33 days")
        )
        & (fact_sales_df["category"] == "Sale")
    ]
    sku_prices = (
        fact_sales_df_filtered[
            (fact_sales_df_filtered["units_returned"] == 0)
            & (fact_sales_df_filtered["gross_returned"] == 0)
            & (fact_sales_df_filtered["category"] == "Sale")
            & (
                (fact_sales_df_filtered["gross_received"] > 0)
                | (fact_sales_df_filtered["gross_sales"] > 0)
            )
            & (
                (fact_sales_df_filtered["units_gross"] > 0)
                | (fact_sales_df_filtered["units_sold"] > 0)
            )
        ][["sku_studio", "country_code", "currency_code", "price_local"]]
        .groupby(["sku_studio", "country_code", "currency_code"], observed=False)
        .max()
    )
    sku_prices.columns = ["new_cal_price_local"]

    return sku_prices.reset_index()


def _merge_prices_fact(
    fact_sales_df: pd.DataFrame,
    sku_prices: pd.DataFrame,
    exchange_rate_first: pd.DataFrame,
):
    fact_sales_df = fact_sales_df.merge(
        sku_prices[
            ["sku_studio", "new_cal_price_local", "country_code", "currency_code"]
        ],
        on=["sku_studio", "country_code", "currency_code"],
        how="left",
    )
    # In an effrot ot catch all exchange rates and to fill missing ones, we merge the rate on month, then quarter then year and then fill missing values respectively
    fact_sales_df = fact_sales_df.merge(
        exchange_rate_first[
            ["year", "quarter", "month", "exchange_rate_first", "currency_code"]
        ],
        left_on=[
            "release_date_year",
            "release_date_quarter",
            "release_date_month",
            "country_currency",
        ],
        right_on=["year", "quarter", "month", "currency_code"],
        how="left",
        suffixes=("", "_main"),
    )
    fact_sales_df = fact_sales_df.merge(
        exchange_rate_first[["year", "quarter", "exchange_rate_first", "currency_code"]]
        .groupby(["year", "quarter", "currency_code"], observed=False)
        .mean()
        .reset_index(),
        left_on=["release_date_year", "release_date_quarter", "country_currency"],
        right_on=["year", "quarter", "currency_code"],
        how="left",
        suffixes=("", "_quarter"),
    )
    fact_sales_df = fact_sales_df.merge(
        exchange_rate_first[["year", "exchange_rate_first", "currency_code"]]
        .groupby(["year", "currency_code"], observed=False)
        .mean()
        .reset_index(),
        left_on=["release_date_year", "country_currency"],
        right_on=["year", "currency_code"],
        how="left",
        suffixes=("", "_year"),
    )

    fact_sales_df["exchange_rate_first"] = fact_sales_df["exchange_rate_first"].fillna(
        fact_sales_df["exchange_rate_first_quarter"].fillna(
            fact_sales_df["exchange_rate_first_year"]
        )
    )

    return fact_sales_df


def _fill_with_base_price(
    fact_sales_df: pd.DataFrame,
):
    # Get prices that already exist (i.e Steam..)
    base_price = fact_sales_df[fact_sales_df["base_price_local"] > 0]

    fact_sales_df.loc[(fact_sales_df["base_price_local"] == 0), "base_price_local"] = (
        fact_sales_df.loc[(fact_sales_df["base_price_local"] == 0)].merge(
            base_price[
                [
                    "sku_studio",
                    "year",
                    "quarter",
                    "country_code",
                    "currency_code",
                    "base_price_local",
                ]
            ].drop_duplicates(),
            on=["sku_studio", "year", "quarter", "country_code", "currency_code"],
            how="left",
            suffixes=("", "_nofree"),
        )["base_price_local_nofree"]
    )

    fact_sales_df["new_cal_price_local"] = np.where(
        ~fact_sales_df["base_price_local"].isna(),
        fact_sales_df["base_price_local"],
        fact_sales_df["new_cal_price_local"],
    )

    return fact_sales_df


def _get_regional_sku_price(fact_sales_df: pd.DataFrame):
    sku_prices_region = (
        fact_sales_df[
            (fact_sales_df["units_returned"] == 0)
            & (fact_sales_df["gross_returned"] == 0)
            & (fact_sales_df["category"] == "Sale")
            & (
                (fact_sales_df["gross_received"] > 0)
                | (fact_sales_df["gross_sales"] > 0)
            )
            & ((fact_sales_df["units_gross"] > 0) | (fact_sales_df["units_sold"] > 0))
        ][["sku_studio", "region", "currency_code", "new_cal_price_local"]]
        .groupby(["sku_studio", "region", "currency_code"], observed=False)
        .max()
    )

    sku_prices_region.columns = ["new_cal_price_local_region"]
    sku_prices_region = sku_prices_region.reset_index()
    fact_sales_df = fact_sales_df.merge(
        sku_prices_region[
            ["sku_studio", "new_cal_price_local_region", "region", "currency_code"]
        ],
        on=["sku_studio", "region", "currency_code"],
        how="left",
    )
    fact_sales_df["new_cal_price_local"] = np.where(
        fact_sales_df["new_cal_price_local"].isna(),
        fact_sales_df["new_cal_price_local_region"],
        fact_sales_df["new_cal_price_local"],
    )

    return fact_sales_df


def _get_max_sku_price(fact_sales_df: pd.DataFrame):
    sku_prices_all = (
        fact_sales_df[
            (fact_sales_df["units_returned"] == 0)
            & (fact_sales_df["gross_returned"] == 0)
            & (fact_sales_df["category"] == "Sale")
            & (
                (fact_sales_df["gross_received"] > 0)
                | (fact_sales_df["gross_sales"] > 0)
            )
            & ((fact_sales_df["units_gross"] > 0) | (fact_sales_df["units_sold"] > 0))
        ][["sku_studio", "country_code", "currency_code", "price_local"]]
        .groupby(["sku_studio", "country_code", "currency_code"], observed=False)
        .max()
    )
    sku_prices_all.columns = ["new_cal_price_local_all"]
    sku_prices_all = sku_prices_all.reset_index()
    sku_prices_all = sku_prices_all.drop_duplicates()
    fact_sales_df = fact_sales_df.merge(
        sku_prices_all[
            ["sku_studio", "new_cal_price_local_all", "country_code", "currency_code"]
        ],
        on=["sku_studio", "country_code", "currency_code"],
        how="left",
    )
    fact_sales_df["new_cal_price_local"] = np.where(
        fact_sales_df["new_cal_price_local"].isna(),
        fact_sales_df["new_cal_price_local_all"],
        fact_sales_df["new_cal_price_local"],
    )
    fact_sales_df = fact_sales_df

    fact_sales_df = fact_sales_df.merge(
        fact_sales_df[~fact_sales_df["new_cal_price_local"].isna()][
            ["sku_studio", "country_code", "currency_code", "new_cal_price_local"]
        ].drop_duplicates(subset=["sku_studio", "country_code", "currency_code"]),
        on=["sku_studio", "country_code", "currency_code"],
        how="left",
    )
    fact_sales_df["new_cal_price_local"] = np.where(
        fact_sales_df["new_cal_price_local_x"].isna(),
        fact_sales_df["new_cal_price_local_y"],
        fact_sales_df["new_cal_price_local_x"],
    )
    return fact_sales_df


def _get_periodical_exchange_rates(
    fact_sales_df: pd.DataFrame,
    country_codes: pd.DataFrame,
    cols: list,
    group_cols: list,
):
    # TODO: refactor
    fact_sales_df = fact_sales_df.copy()
    fact_sales_df["exchange_rate"] = (
        fact_sales_df["price_usd"] / fact_sales_df["price_local"]
    )
    fact_sales_df = fact_sales_df.dropna(subset=["exchange_rate"])
    return fact_sales_df[cols].groupby(group_cols, observed=False).mean().reset_index()


def _aggregate_periodical_exchange_rates(
    fact_sales_df: pd.DataFrame, country_codes: pd.DataFrame
):
    exchange_df_month = _get_periodical_exchange_rates(
        fact_sales_df,
        country_codes,
        ["year", "quarter", "month", "currency_code", "exchange_rate"],
        ["year", "quarter", "month", "currency_code"],
    )
    exchange_df_quarter = _get_periodical_exchange_rates(
        fact_sales_df,
        country_codes,
        ["year", "quarter", "currency_code", "exchange_rate"],
        ["year", "quarter", "currency_code"],
    )
    exchange_df_year = _get_periodical_exchange_rates(
        fact_sales_df,
        country_codes,
        ["year", "currency_code", "exchange_rate"],
        ["year", "currency_code"],
    )

    exchange_df = exchange_df_year[["year", "currency_code", "exchange_rate"]].merge(
        exchange_df_quarter[["year", "quarter", "currency_code", "exchange_rate"]],
        on=["year", "currency_code"],
        how="left",
        suffixes=("_year", "_quarter"),
    )
    exchange_df = exchange_df[
        [
            "year",
            "quarter",
            "currency_code",
            "exchange_rate_year",
            "exchange_rate_quarter",
        ]
    ].merge(
        exchange_df_month[
            ["year", "quarter", "month", "currency_code", "exchange_rate"]
        ],
        on=["year", "quarter", "currency_code"],
        how="left",
        suffixes=("", ""),
    )

    exchange_df = exchange_df.rename(columns={"currency_code": "country_currency"})

    fact_sales_df = (
        fact_sales_df.merge(
            exchange_df[
                ["year", "quarter", "month", "country_currency", "exchange_rate"]
            ],
            on=["year", "quarter", "month", "country_currency"],
            how="left",
        )
        .merge(
            exchange_df[
                ["year", "quarter", "country_currency", "exchange_rate_quarter"]
            ].drop_duplicates(subset=["year", "quarter", "country_currency"]),
            on=["year", "quarter", "country_currency"],
            how="left",
        )
        .merge(
            exchange_df[["year", "country_currency", "exchange_rate_year"]]
            .drop_duplicates(subset=["year", "country_currency"])
            .drop_duplicates(),
            on=["year", "country_currency"],
            how="left",
        )
    )
    fact_sales_df["exchange_rate"] = fact_sales_df["exchange_rate"].fillna(
        fact_sales_df["exchange_rate_quarter"].fillna(
            fact_sales_df["exchange_rate_year"]
        )
    )

    return fact_sales_df


def _adjust_exchange_prices(fact_sales_df: pd.DataFrame, current_exchange: pd.Series):
    exchange_change = (
        fact_sales_df["exchange_rate"] / fact_sales_df["exchange_rate_first"]
    )

    fact_sales_df["new_cal_price_local"] = np.where(
        (
            (fact_sales_df["currency_code"] == "USD")
            & (fact_sales_df["country_currency"] != fact_sales_df["currency_code"])
        ),
        np.where(
            (~fact_sales_df["exchange_rate"].isna())
            & (~fact_sales_df["exchange_rate_first"].isna()),
            fact_sales_df["new_cal_price_local"] * exchange_change,
            fact_sales_df["new_cal_price_local"],
        ),
        fact_sales_df["new_cal_price_local"],
    )
    fact_sales_df["new_cal_price_usd"] = (
        fact_sales_df["new_cal_price_local"] * current_exchange
    )
    return fact_sales_df


def _identify_price_and_price_changes(fact_sales_df: pd.DataFrame):
    # Streak identificaiton - we try to identify the number of consuctive days (streak) a game had a price higher than previously recorded one.
    higher_price_mask = (
        fact_sales_df["price_local"] > fact_sales_df["new_cal_price_local"]
    )
    fact_sales_df["higher_price_mask"] = ~higher_price_mask
    streaks = (
        fact_sales_df[
            ["sku_studio", "country_code", "currency_code", "higher_price_mask"]
        ]
        .groupby(["sku_studio", "country_code", "currency_code"], observed=False)[
            "higher_price_mask"
        ]
        .cumsum()
    )  # continuous streaks
    fact_sales_df["streaks"] = streaks

    # This merge on Categorical columns exploaded on memory usage
    fact_sales_df["country_code"] = fact_sales_df["country_code"].astype(str)
    fact_sales_df["currency_code"] = fact_sales_df["currency_code"].astype(str)
    fact_sales_df["sku_studio"] = fact_sales_df["sku_studio"].astype(str)

    fact_sales_df = fact_sales_df.merge(
        (
            fact_sales_df[
                [
                    "sku_studio",
                    "country_code",
                    "currency_code",
                    "streaks",
                    "release_date",
                ]
            ]
            .groupby(
                ["sku_studio", "country_code", "currency_code", "streaks"],
                observed=False,
            )["release_date"]
            .agg(days_above_price="count")
            - 1
        ).reset_index(),
        on=["sku_studio", "country_code", "currency_code", "streaks"],
        how="left",
    )

    fact_sales_df["calculated_base_price_local_v2"] = fact_sales_df[
        "new_cal_price_local"
    ].copy()
    fact_sales_df.loc[
        (fact_sales_df["higher_price_mask"] == True)
        | (
            (fact_sales_df["higher_price_mask"] == False)
            & (fact_sales_df["days_above_price"] < 5)
        ),
        "calculated_base_price_local_v2",
    ] = np.nan
    fact_sales_df.loc[
        (
            (fact_sales_df["higher_price_mask"] == False)
            & (fact_sales_df["days_above_price"] > 4)
        ),
        "calculated_base_price_local_v2",
    ] = fact_sales_df.loc[
        (
            (fact_sales_df["higher_price_mask"] == False)
            & (fact_sales_df["days_above_price"] > 4)
        ),
        "price_local",
    ]

    fact_sales_df["calculated_base_price_local_v2"] = (
        fact_sales_df[
            [
                "sku_studio",
                "country_code",
                "currency_code",
                "calculated_base_price_local_v2",
            ]
        ]
        .groupby(["sku_studio", "country_code", "currency_code"])[
            "calculated_base_price_local_v2"
        ]
        .transform(lambda x: x.ffill())
    )
    fact_sales_df["calculated_base_price_local_v2"] = fact_sales_df[
        "calculated_base_price_local_v2"
    ].fillna(fact_sales_df["new_cal_price_local"])
    fact_sales_df["calculated_base_price_local_v2"] = np.where(
        ~fact_sales_df["base_price_local"].isna(),
        fact_sales_df["base_price_local"],
        fact_sales_df["calculated_base_price_local_v2"],
    )

    return fact_sales_df


def add_country_currency_column(
    fact_sales_df: pd.DataFrame, country_codes: pd.DataFrame
):
    fact_sales_df = fact_sales_df.merge(
        country_codes[["country_code", "country_currency"]],
        on="country_code",
        how="left",
    )

    return fact_sales_df


def schema():
    return pa.DataFrameSchema(
        columns={
            "country_code": ExactLengthString(3),
            "currency_code": ExactLengthString(3),
            "studio_id": NonNegativeInt(),
            "sku_studio": MediumString(),
            "bundle_name": SmallString(),
            "portal_platform_region": MediumString(),
            "portal_platform_region_id": IntInRange(101000, 999999),
            "product_id": MediumString(),
            "hash_acquisition_properties": HashString(),
            "date": TinyString(),
            "date_sku_studio": MediumString(),
            "source_file_id": NonNegativeInt(),
            "retailer_tag": MediumString(),
            "base_price_local": NullableFloatWithTwoDecimalPlaces(),
            "calculated_base_price_usd": NullableFloatWithTwoDecimalPlaces(),
            "net_sales": FloatWithTwoDecimalPlaces(),
            "gross_returned": FloatWithTwoDecimalPlaces(),
            "gross_sales": FloatWithTwoDecimalPlaces(),
            "units_returned": Int(),
            "units_sold": Int(),
            "free_units": Int(),
            "price_local": FloatWithTwoDecimalPlaces(),
            "price_usd": FloatWithTwoDecimalPlaces(),
            "net_sales_approx": FloatWithTwoDecimalPlaces(),
            "category": SmallString(),
            "calculated_base_price_local_v2": NullableFloatWithTwoDecimalPlaces(),
            "calculated_base_price_usd_v2": NullableFloatWithTwoDecimalPlaces(),
        },
    )


def assign_new_price(
    df_table: pd.DataFrame,
    country_codes_df: pd.DataFrame,
    get_currency_table=0,
) -> pd.DataFrame:
    fact_sales_df = df_table.sort_values(
        by=["sku_studio", "date"], ascending=True
    ).copy()
    if fact_sales_df.empty:
        return pd.DataFrame(columns=schema().columns.keys())

    fact_sales_df["units_gross"] = (
        fact_sales_df["units_sold"] + fact_sales_df["units_returned"]
    )
    fact_sales_df["gross_received"] = (
        fact_sales_df["gross_sales"] + fact_sales_df["gross_returned"]
    )

    sku_rel_date = create_release_date_column(fact_sales_df)
    fact_sales_df = _merge_release_date_qsales(fact_sales_df, sku_rel_date)

    fact_sales_df = add_country_currency_column(fact_sales_df, country_codes_df)

    fact_sales_df = _get_country_region(fact_sales_df, country_codes_df)

    fact_sales_df = _create_date_values(fact_sales_df)

    exchange_rate_first = _get_first_fx(fact_sales_df)
    sku_prices = _get_initial_sku_price(fact_sales_df)

    fact_sales_df = _merge_prices_fact(fact_sales_df, sku_prices, exchange_rate_first)
    fact_sales_df = _fill_with_base_price(fact_sales_df)

    fact_sales_df = _get_regional_sku_price(fact_sales_df)
    fact_sales_df = _get_max_sku_price(fact_sales_df)

    fact_sales_df = _aggregate_periodical_exchange_rates(
        fact_sales_df, country_codes_df
    )

    current_exchange = (
        fact_sales_df["price_usd"] / fact_sales_df["price_local"]
    ).where(fact_sales_df["price_local"] != 0, np.nan)

    fact_sales_df = _adjust_exchange_prices(fact_sales_df, current_exchange)

    fact_sales_df = _identify_price_and_price_changes(fact_sales_df)

    fact_sales_df["calculated_base_price_usd_v2"] = (
        current_exchange * fact_sales_df["calculated_base_price_local_v2"]
    )

    fact_sales_df.loc[
        ~(fact_sales_df["calculated_base_price_usd_v2"].isna()),
        "calculated_base_price_usd_v2",
    ] = (
        fact_sales_df.loc[
            ~(fact_sales_df["calculated_base_price_usd_v2"].isna()),
            "calculated_base_price_usd_v2",
        ]
        * 100
    ).astype(int) / 100

    fact_sales_df.loc[
        ~(fact_sales_df["calculated_base_price_local_v2"].isna()),
        "calculated_base_price_local_v2",
    ] = (
        fact_sales_df.loc[
            ~(fact_sales_df["calculated_base_price_local_v2"].isna()),
            "calculated_base_price_local_v2",
        ]
        * 100
    ).astype(int) / 100

    fact_sales_df["date"] = fact_sales_df["date"].dt.date

    fact_sales_df = fact_sales_df[schema().columns.keys()]

    return fact_sales_df
