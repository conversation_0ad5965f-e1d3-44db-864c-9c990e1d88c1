import pandas as pd
import polars as pl

from core_silver.legacy_aggregators.common_aggregations import (
    compute_date_product_studio,
    compute_product_id_in_fact_aggregator,
)
from core_silver.observation_converter.pandas_utils import enforce_schema
from core_silver.validators.fields import <PERSON>Field, MediumStringField, TinyStringField
from data_sdk.aggregator import (
    BaseAggregator,
    StrictBaseSchema,
)
from data_sdk.domain.ppr import get_portal_platform_region_id
from data_sdk.domain.tables import (
    LegacyFactWishlistCohortsTable,
    ObservationWishlistCohortsTable,
    SilverSKUsTable,
)


class FactWishlistCohortsSchema(StrictBaseSchema):
    portal_platform_region: str = MediumStringField()
    portal_platform_region_id: int = BasicField(
        in_range={"min_value": 101000, "max_value": 999999}
    )
    product_id: str = MediumStringField()
    sku_studio: str = MediumStringField()
    date: str = TinyStringField()
    studio_id: int = BasicField()
    date_sku_studio: str = MediumStringField()
    date_product_studio: str = MediumStringField()
    month_cohort: str = TinyStringField()
    total_conversions: int = BasicField()
    purchases_and_activations: int = BasicField()
    gifts: int = BasicField()


class FactWishlistCohortsAggregator(BaseAggregator):
    schema = FactWishlistCohortsSchema
    table_cls = LegacyFactWishlistCohortsTable

    def __init__(
        self,
        silver_skus: SilverSKUsTable,
        observation_wishlist_cohorts: ObservationWishlistCohortsTable,
    ):
        self._silver_skus = silver_skus
        self._observation_wishlist_cohorts = observation_wishlist_cohorts

    def _aggregate(self) -> pl.DataFrame:
        observations_df = self._observation_wishlist_cohorts.df.to_pandas()
        skus_df = self._silver_skus.df.to_pandas()

        if observations_df.empty:
            return self._generate_empty_output()

        observations_df["portal_platform_region"] = observations_df[
            "portal_platform_region"
        ].astype(str)

        observations_df["unique_sku_id"] = observations_df["unique_sku_id"].astype(str)

        fact_wishlist_cohorts_df = pd.DataFrame()
        fact_wishlist_cohorts_df = fact_wishlist_cohorts_df.assign(
            portal_platform_region=observations_df["portal_platform_region"],
            sku_studio=observations_df["unique_sku_id"],
            date=observations_df["date"].astype(str),
            studio_id=observations_df["studio_id"],
            date_sku_studio=observations_df["date"].astype(str)
            + ":"
            + observations_df["unique_sku_id"],
            month_cohort=observations_df["month_cohort"],
            total_conversions=observations_df["total_conversions"],
            purchases_and_activations=observations_df["purchases_and_activations"],
            gifts=observations_df["gifts"],
            portal_platform_region_id=observations_df["portal_platform_region"].map(
                get_portal_platform_region_id
            ),
        )
        fact_wishlist_cohorts_df["date_product_studio"] = compute_date_product_studio(
            fact_wishlist_cohorts_df, skus_df
        )
        fact_wishlist_cohorts_df["product_id"] = compute_product_id_in_fact_aggregator(
            fact_wishlist_cohorts_df, skus_df
        )
        fact_wishlist_cohorts_df = pl.DataFrame(
            fact_wishlist_cohorts_df.reindex(
                columns=self.schema.to_schema().columns
            ).sort_values(
                by=["sku_studio", "date"],
                ascending=False,
            )
        )
        self.schema.to_schema().validate(fact_wishlist_cohorts_df)

        return fact_wishlist_cohorts_df

    def _generate_empty_output(self) -> pl.DataFrame:
        return pl.DataFrame(
            enforce_schema(
                pd.DataFrame(columns=list(self.schema.to_schema().columns)),
                self.schema.to_schema(),
            )
        )
