import pandera as pa
import polars as pl

from core_silver.dictionaries.constants import Constant
from core_silver.observation_converter.pandas_utils import enforce_schema
from core_silver.validators.columns import IntInRange, MediumString, SmallString
from data_sdk.aggregator import BaseAggregator
from data_sdk.domain import DisplayPortal
from data_sdk.domain.platform import DisplayPlatform
from data_sdk.domain.ppr import get_portal_platform_region_id
from data_sdk.domain.regions import Region
from data_sdk.domain.tables import LegacyDimPortalsTable, SilverPortalsTable

_platform_sorting_order_map = {
    # global
    DisplayPlatform.PC.value: 0,
    DisplayPlatform.KEY.value: 0,
    # meta
    DisplayPlatform.RIFT.value: 1,
    DisplayPlatform.QUEST.value: 2,
    # nintendo
    DisplayPlatform.SWITCH.value: 1,
    DisplayPlatform.WII_U.value: 2,
    # playstation
    DisplayPlatform.PS_1.value: 0,
    DisplayPlatform.PS_VR.value: 1,
    DisplayPlatform.PS_5.value: 2,
    DisplayPlatform.PS_4.value: 3,
    DisplayPlatform.PS_3.value: 4,
    DisplayPlatform.PS_2.value: 5,
    DisplayPlatform.PSP.value: 6,
    DisplayPlatform.PS_VITA.value: 7,
    DisplayPlatform.PS_HOME.value: 8,
    DisplayPlatform.PS_VR2.value: 9,
    # TODO: for now it is not possible to add more sorting values and we will use default one
    # DisplayPlatform.PS_MINIS.value: 10,
    # microsoft
    DisplayPlatform.MICROSOFT.value: 0,
    # app_store
    DisplayPlatform.IPHONE.value: 1,
    DisplayPlatform.IPAD.value: 2,
    DisplayPlatform.DESKTOP.value: 3,
    DisplayPlatform.IPOD_TOUCH.value: 4,
    # google play store,
    DisplayPlatform.GOOGLE.value: 1,
    # other
    DisplayPlatform.UNKNOWN.value: 9,
}

# TODO Verify why do we need it and if this is correct
_region_sorting_order_map = {
    # global
    Region.GLOBAL.value: 0,
    # nintendo
    Region.NINTENDO_CHINA.value: 1,
    Region.NINTENDO_AMERICAS.value: 2,
    Region.NINTENDO_EUROPE_AUSTRALIA.value: 3,
    Region.NINTENDO_JAPAN.value: 4,
    Region.NINTENDO_KOREA.value: 5,
    Region.NINTENDO_TAIWAN_HONG_KONG.value: 6,
    # playstation
    Region.PLAYSTATION_AMERICA.value: 1,
    Region.PLAYSTATION_EUROPE.value: 2,
    Region.PLAYSTATION_JAPAN.value: 3,
    Region.PLAYSTATION_ASIA.value: 4,
}

_portal_sorting_order_map = {
    DisplayPortal.STEAM.value: 1,
    DisplayPortal.META.value: 2,
    DisplayPortal.MICROSOFT.value: 3,
    DisplayPortal.PLAYSTATION.value: 4,
    DisplayPortal.NINTENDO.value: 5,
    DisplayPortal.HUMBLE.value: 6,
    DisplayPortal.GOG.value: 7,
    DisplayPortal.EPIC.value: 8,
    DisplayPortal.APPLE.value: 9,
    DisplayPortal.GOOGLE.value: 10,
}


def get_sorting_order_for_platform(display_platform) -> int:
    """Return sorting priority for a given platform (1-highest)

    Args:
        platform: name of the platform

    Returns:
        Number representing platform sorting order. Defaults to 9 if platform unrecognized.

    >>> get_sorting_order_for_platform('PC')
    0
    >>> get_sorting_order_for_platform('nonexistent')
    9
    """
    try:
        return _platform_sorting_order_map[display_platform]
    except KeyError:
        return Constant.DEFAULT_SORTING_ORDER.value


def get_sorting_order_for_portal(portal) -> int:
    """Return sorting priority for a given portal (1-highest)

    Args:
        portal: name of the portal

    Returns:
        Number representing portal sorting order

    >>> get_sorting_order_for_portal('Steam')
    1
    """

    return _portal_sorting_order_map[portal]


def get_sorting_order_for_region(region) -> int:
    """Return sorting priority for a given region (1-highest)

    Args:
        region: name of the region

    Returns:
        Number representing region sorting order. Defaults to 9 if region unrecognized.

    >>> get_sorting_order_for_region('Nintendo Japan')
    4
    >>> get_sorting_order_for_region('nonexistent')
    9
    """
    try:
        return _region_sorting_order_map[region]
    except KeyError:
        return Constant.DEFAULT_SORTING_ORDER.value


class DimPortalsAggregator(BaseAggregator):
    table_cls = LegacyDimPortalsTable
    schema = pa.DataFrameSchema(
        columns={
            "portal": SmallString(),
            "platform": SmallString(),
            "region": SmallString(),
            "store": SmallString(),
            "abbreviated_name": SmallString(),
            "portal_platform_region": MediumString(),
            "portal_platform_region_id": IntInRange(101000, 999999),
            "so_portal": IntInRange(1, 99),
            "so_platform": IntInRange(0, 9),
            "so_region": IntInRange(0, 9),
            "pso": IntInRange(100, 9999),
        }
    )

    def __init__(self, silver_portals: SilverPortalsTable) -> None:
        self._silver_portals = silver_portals

    def _aggregate(self) -> pl.DataFrame:
        dim_portals_df = self._silver_portals.df.to_pandas().rename(
            columns={"store_name": "store"}
        )
        if dim_portals_df.empty:
            return pl.DataFrame()

        dim_portals_df["portal_platform_region_id"] = dim_portals_df[
            "portal_platform_region"
        ].map(get_portal_platform_region_id)
        dim_portals_df["so_portal"] = dim_portals_df["portal"].map(
            get_sorting_order_for_portal
        )
        dim_portals_df["so_platform"] = dim_portals_df["platform"].map(
            get_sorting_order_for_platform
        )
        dim_portals_df["so_region"] = dim_portals_df["region"].map(
            get_sorting_order_for_region
        )
        dim_portals_df["pso"] = (
            100 * dim_portals_df["so_portal"]
            + 10 * dim_portals_df["so_platform"]
            + dim_portals_df["so_region"]
        )
        return pl.DataFrame(enforce_schema(dim_portals_df, self.schema))
