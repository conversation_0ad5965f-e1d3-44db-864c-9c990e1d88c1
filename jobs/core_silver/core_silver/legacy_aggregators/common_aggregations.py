import pandas as pd

from core_silver.dictionaries.constants import Constant


def compute_product_id_in_fact_aggregator(
    observations_df: pd.DataFrame, skus: pd.DataFrame
) -> pd.Series:
    if skus.empty:
        observations_df["product_name"] = None
    else:
        observations_df = observations_df.merge(
            skus[["product_name", "unique_sku_id"]],
            left_on="sku_studio",
            right_on="unique_sku_id",
            how="left",
        )
    return concatenate_product_id(observations_df)


def concatenate_product_id(df: pd.DataFrame) -> pd.Series:
    portal_platform_region_id = df.portal_platform_region_id.astype(str)
    studio_id = df.studio_id.astype(int).astype(str)

    df["product_id"] = "Unassigned:" + portal_platform_region_id + ":" + studio_id

    df.loc[df.product_name.notna(), "product_id"] = (
        df["product_name"] + ":" + portal_platform_region_id + ":" + studio_id
    )
    return df["product_id"]


def compute_date_product_studio(df: pd.DataFrame, skus: pd.DataFrame) -> pd.Series:
    """Compute date_product_studio column values based on observation and SKUs.
    If `product_name` is not defined it defaults to value `UNASSIGNED`.

    Args:
        df: data frame with observations
        skus: data frame with SKUs fetched from Report Service

    Returns:
        Observations with computed date_product_studio column

    """
    if skus.empty:
        df["product_name"] = None
    else:
        df = df.merge(
            skus[["product_name", "unique_sku_id"]],
            left_on="sku_studio",
            right_on="unique_sku_id",
            how="left",
        )
    df["product_name_normalized"] = Constant.UNASSIGNED.value
    df.loc[df["product_name"].notnull(), "product_name_normalized"] = df[
        "product_name"
    ].astype(str)
    return (
        df["date"].astype(str)
        + ":"
        + df["product_name_normalized"]
        + ":"
        + df["studio_id"].astype(str)
    )
