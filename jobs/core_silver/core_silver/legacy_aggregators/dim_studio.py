import pandas as pd
import pandera as pa
import polars as pl

from core_silver.observation_converter.pandas_utils import enforce_schema
from core_silver.validators.columns import (
    Bool,
    NonNegativeInt,
    SmallString,
    TinyString,
)
from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.tables import ExternalStudiosTable, LegacyDimStudioTable


class DimStudioAggregator(BaseAggregator):
    table_cls = LegacyDimStudioTable

    def __init__(self, external_studios: ExternalStudiosTable) -> None:
        self._external_studios = external_studios

    schema = pa.DataFrameSchema(
        columns={
            "studio_id": NonNegativeInt(),
            "company_name": SmallString(),
            "email": SmallString(),
            "is_verified": Bool(nullable=True),
            "agreement_date": TinyString(nullable=True),
            "is_test_account": Bool(nullable=True),
            # pd.Int64Dtype() allows to have null values in series with type: Int64
            "studio_parent_id": pa.Column(
                pd.Int64Dtype(),
                nullable=True,
                coerce=True,
                checks=pa.Check.greater_than_or_equal_to(0),
            ),
        }
    )

    def _aggregate(self) -> pl.DataFrame:
        external_studios_df = self._external_studios.df.to_pandas()

        external_studios_df["agreement_date"] = external_studios_df[
            "agreement_date"
        ].apply(lambda x: f"{x.strftime('%Y-%m-%dT%H:%M:%S')}.{int(x.microsecond):06}Z")
        return pl.DataFrame(enforce_schema(external_studios_df, self.schema))
