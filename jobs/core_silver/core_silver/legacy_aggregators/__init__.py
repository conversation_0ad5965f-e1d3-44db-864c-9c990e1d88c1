from core_silver.legacy_aggregators.dim_portals import DimPortalsAggregator
from core_silver.legacy_aggregators.dim_sku import DimSkuAggregator
from core_silver.legacy_aggregators.dim_source_file import DimSourceFileAggregator
from core_silver.legacy_aggregators.dim_studio import DimStudioAggregator
from core_silver.legacy_aggregators.fact_baseline import FactBaselineAggregator
from core_silver.legacy_aggregators.fact_detected_events import (
    FactDetectedEventsAggregator,
)
from core_silver.legacy_aggregators.fact_discounts import FactDiscountsAggregator
from core_silver.legacy_aggregators.fact_event_day import EventDayAggregator
from core_silver.legacy_aggregators.fact_sales.fact_sales import (
    FactSalesAggregator,
)
from core_silver.legacy_aggregators.fact_visibility import FactVisibilityAggregator
from core_silver.legacy_aggregators.fact_wishlist_actions import (
    FactWishlistActionsAggregator,
)
from core_silver.legacy_aggregators.fact_wishlist_cohorts import (
    FactWishlistCohortsAggregator,
)
from data_sdk.aggregator import BaseAggregator

aggregator_list: list[type[BaseAggregator]] = [
    FactDiscountsAggregator,
    FactSalesAggregator,
    FactVisibilityAggregator,
    FactWishlistActionsAggregator,
    FactWishlistCohortsAggregator,
    EventDayAggregator,
    DimSourceFileAggregator,
    DimPortalsAggregator,
    FactBaselineAggregator,
    FactDetectedEventsAggregator,
    DimSkuAggregator,
    DimStudioAggregator,
]
