import pandas as pd
import polars as pl

from core_silver.legacy_aggregators.common_aggregations import (
    compute_date_product_studio,
    compute_product_id_in_fact_aggregator,
)
from core_silver.observation_converter.pandas_utils import enforce_schema
from core_silver.validators.fields import <PERSON><PERSON>ield, MediumStringField, TinyStringField
from data_sdk.aggregator import BaseAggregator, StrictBaseSchema
from data_sdk.domain.ppr import get_portal_platform_region_id
from data_sdk.domain.tables import (
    LegacyFactWishlistActionsTable,
    ObservationWishlistActionsTable,
    SilverSKUsTable,
)


class FactWishlistActionsSchema(StrictBaseSchema):
    portal_platform_region: str = MediumStringField()
    portal_platform_region_id: int = BasicField(ge=101000, le=999999)
    product_id: str = MediumStringField()
    sku_studio: str = MediumStringField()
    # Legacy column name, observations use unique_sku_id
    date: str = TinyStringField()
    studio_id: int = BasicField()
    date_sku_studio: str = MediumStringField()
    date_product_studio: str = MediumStringField()
    adds: int = BasicField()
    deletes: int = BasicField()
    purchases_and_activations: int = BasicField()
    gifts: int = BasicField()
    country_code: str = TinyStringField()


class FactWishlistActionsAggregator(BaseAggregator):
    table_cls = LegacyFactWishlistActionsTable
    schema = FactWishlistActionsSchema

    def __init__(
        self,
        silver_skus: SilverSKUsTable,
        observation_wishlist_actions: ObservationWishlistActionsTable,
    ):
        self._silver_skus = silver_skus
        self._observation_wishlist_actions = observation_wishlist_actions

    def _aggregate(self) -> pl.DataFrame:
        observations_df = self._observation_wishlist_actions.df.to_pandas()
        skus_df = self._silver_skus.df.to_pandas()

        if observations_df.empty:
            return self._generate_empty_output()

        fact_wishlist_actions_df = pd.DataFrame()
        fact_wishlist_actions_df = fact_wishlist_actions_df.assign(
            portal_platform_region=observations_df["portal_platform_region"].astype(
                str
            ),
            sku_studio=observations_df["unique_sku_id"].astype(str),
            date=observations_df["date"].astype(str),
            studio_id=observations_df["studio_id"],
            date_sku_studio=observations_df["date"].astype(str)
            + ":"
            + observations_df["unique_sku_id"].astype(str),
            adds=observations_df["adds"],
            deletes=observations_df["deletes"],
            purchases_and_activations=observations_df["purchases_and_activations"],
            gifts=observations_df["gifts"],
            portal_platform_region_id=observations_df["portal_platform_region"]
            .astype(str)
            .map(get_portal_platform_region_id),
            country_code=observations_df["country_code"].astype(str),
        )
        fact_wishlist_actions_df["date_product_studio"] = compute_date_product_studio(
            fact_wishlist_actions_df, skus_df
        )
        fact_wishlist_actions_df["product_id"] = compute_product_id_in_fact_aggregator(
            fact_wishlist_actions_df, skus_df
        )
        self.schema.to_schema().validate(pl.DataFrame(fact_wishlist_actions_df))
        return pl.DataFrame(
            fact_wishlist_actions_df.reindex(
                columns=self.schema.to_schema().columns
            ).sort_values(
                by=["sku_studio", "date"],
                ascending=False,
            )
        )

    def _generate_empty_output(self) -> pl.DataFrame:
        return pl.DataFrame(
            enforce_schema(
                pd.DataFrame(columns=list(self.schema.to_schema().columns)),
                self.schema.to_schema(),
            )
        )
