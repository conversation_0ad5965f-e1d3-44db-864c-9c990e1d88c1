import pandera as pa
import polars as pl

from core_silver.observation_converter.pandas_utils import enforce_schema
from core_silver.validators.columns import NonNegativeInt, SmallString, TinyString
from data_sdk.aggregator import BaseAggregator
from data_sdk.domain import Portal
from data_sdk.domain.tables import ExternalReportsTable, LegacyDimSourceFileTable


class DimSourceFileAggregator(BaseAggregator):
    table_cls = LegacyDimSourceFileTable

    schema = pa.DataFrameSchema(
        columns={
            "source_file_id": NonNegativeInt(),
            "file_name": SmallString(),
            "upload_date": TinyString(),
            "studio_id": NonNegativeInt(),
        }
    )

    def __init__(self, external_reports: ExternalReportsTable, portal: Portal) -> None:
        self._external_reports = external_reports
        self._portal = portal

    def _aggregate(self) -> pl.DataFrame:
        external_reports_df = self._external_reports.df
        external_reports_df = external_reports_df.filter(
            external_reports_df["portal"] == self._portal
        )
        source_file_df = pl.DataFrame().with_columns([
            external_reports_df["report_id"].alias("source_file_id"),
            external_reports_df["original_name"].alias("file_name"),
            external_reports_df["upload_date"].alias("upload_date"),
            external_reports_df["studio_id"].alias("studio_id"),
        ])
        return pl.DataFrame(enforce_schema(source_file_df.to_pandas(), self.schema))
