import pandas as pd
import polars as pl

from core_silver.observation_converter.deduplicators.base_deduplicator import (
    BaseDeduplicator,
)
from data_sdk.domain import get_display_portal_from_portal
from data_sdk.domain.domain_types import ReportMetadata, StudioId
from data_sdk.domain.source import get_portal_by_source
from data_sdk.reports.reader import ConvertedReportsReader


# TODO it is now for portal and platform as it used to be, I want to rewrite it to ppr once we change schema
class PortalPlatformRegionSalesDeduplicator(BaseDeduplicator):
    def deduplicate(
        self,
        metadata_list: list[ReportMetadata],
        converted_filename_list: list[str],
        converted_reader: ConvertedReportsReader,
    ) -> pl.DataFrame:
        if not metadata_list or not converted_filename_list:
            return pl.DataFrame()
        sales_obs = self._load_files(
            metadata_list[0].studio_id, converted_filename_list, converted_reader
        )
        coverage = self._get_coverage(sales_obs, metadata_list)

        merged = pd.merge(
            coverage,
            sales_obs,
            on=["date", "report_id", "portal", "platform"],
            how="inner",
        )

        return pl.from_pandas(merged)

    def _get_coverage(
        self, observations_df: pd.DataFrame, reports_metadata: list[ReportMetadata]
    ) -> pd.DataFrame:
        if observations_df.empty:
            result = pd.DataFrame(columns=["report_id", "portal", "platform", "date"])
            result["date"] = pd.to_datetime(result["date"])
            return result

        report_coverages = []
        for manifest in reports_metadata:
            report_df = observations_df.loc[
                observations_df["report_id"] == manifest.report_id
            ]
            report_data = get_report_data(manifest, report_df)
            coverage_days_df = get_coverage_from_report_data(report_data)
            report_coverages.append(coverage_days_df)

        coverage_df = pd.concat(report_coverages)
        groupby_cols = ["date", "portal", "platform"]
        max_time_scraped = coverage_df.groupby(groupby_cols)["upload_date"].transform(
            max
        )
        dim_date_coverage_df = coverage_df.loc[
            coverage_df["upload_date"] == max_time_scraped
        ].sort_values(by=["date", "report_id"])
        col_subset = list(dim_date_coverage_df.columns)
        col_subset.remove("report_id")
        dim_date_coverage_df = dim_date_coverage_df.drop_duplicates(
            subset=col_subset, keep="last"
        )
        return dim_date_coverage_df.drop(columns=["upload_date"])

    def _load_files(
        self,
        studio_id: StudioId,
        filenames: list[str],
        converted_reader: ConvertedReportsReader,
    ) -> pd.DataFrame:
        return pd.concat([
            converted_reader.read(f"studio_id={studio_id}/{filename}").to_pandas()
            for filename in filenames
        ])


def get_report_data(manifest: ReportMetadata, report_df: pd.DataFrame) -> dict:
    file_date_from = (
        report_df["date"].min().to_pydatetime().date()
        if not report_df.empty and "date" in report_df.columns
        else None
    )
    file_date_to = (
        report_df["date"].max().to_pydatetime().date()
        if not report_df.empty and "date" in report_df.columns
        else None
    )
    date_from = (
        min(manifest.date_from, file_date_from)
        if manifest.date_from and file_date_from
        else manifest.date_from or file_date_from
    )
    date_to = (
        max(manifest.date_to, file_date_to)
        if manifest.date_to and file_date_to
        else manifest.date_to or file_date_to
    )
    return {
        "report_id": manifest.report_id,
        "upload_date": manifest.upload_date,
        "date_from": date_from,
        "date_to": date_to,
        "portal": get_display_portal_from_portal(
            get_portal_by_source(manifest.source)
        ).value,
        "platforms": list(set(report_df.get("platform", []))),
    }


def get_coverage_from_report_data(report_data: dict) -> pd.DataFrame:
    """The function is generating a list of days with a specified portals and platforms per day in the file.

    Args:
        report_data: report data containing coverage information

    Returns:
        pd.DataFrame: DataFrame with report_id, upload_date, portal, platform, date columns
        Example: date_from: 2010-01-01 date_to: 2010-01-02 portal: Playstation platforms: PS4, PS5

        Output:
        report_id | upload_date | portal    | platform | date
        1         | 2021-05-05  | Playstation | PS4    | 2010-01-01
        1         | 2021-05-05  | Playstation | PS5    | 2010-01-02
        1         | 2021-05-05  | Playstation | PS4    | 2010-01-01
        1         | 2021-05-05  | Playstation | PS5    | 2010-01-02
    """
    dfs: list[pd.DataFrame] = [
        pd.DataFrame(
            {
                "report_id": report_data["report_id"],
                "upload_date": report_data["upload_date"],
                "portal": report_data["portal"],
                "platform": platform,
                "date": pd.date_range(
                    start=report_data["date_from"], end=report_data["date_to"]
                ),
            },
            index=None,
        )
        for platform in report_data["platforms"]
    ]
    return pd.concat(dfs) if len(dfs) > 0 else pd.DataFrame()
