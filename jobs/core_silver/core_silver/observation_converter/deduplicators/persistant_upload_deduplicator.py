import polars as pl

from core_silver.observation_converter.deduplicators.base_deduplicator import (
    BaseDeduplicator,
)
from data_sdk.domain.domain_types import ReportMetadata, StudioId
from data_sdk.reports.reader import ConvertedReportsReader


class PersistantUploadDeduplicator(BaseDeduplicator):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def deduplicate(
        self,
        metadata_list: list[ReportMetadata],
        converted_filename_list: list[str],
        converted_reader: ConvertedReportsReader,
    ) -> pl.DataFrame:
        if not metadata_list or not converted_filename_list:
            return pl.DataFrame()

        reports_data = self._load_files(
            metadata_list[0].studio_id, converted_filename_list, converted_reader
        )
        if reports_data.is_empty():
            return pl.DataFrame()
        return self._deduplicate_by_keeping_the_newest_report_per_day(reports_data)

    def _load_files(
        self,
        studio_id: StudioId,
        filenames: list[str],
        converted_reader: ConvertedReportsReader,
    ) -> pl.DataFrame:
        all_files = [
            converted_reader.read(f"studio_id={studio_id}/{filename}")
            for filename in filenames
        ]
        # Polars saves Enum column in empty df as Categorical
        # and we can't concat dfs with different columns types
        all_non_empty_files = [file for file in all_files if not file.is_empty()]
        if len(all_non_empty_files) == 0:
            return pl.DataFrame()
        return pl.concat([file for file in all_files if not file.is_empty()])

    def _deduplicate_by_keeping_the_newest_report_per_day(
        self, df: pl.DataFrame
    ) -> pl.DataFrame:
        sorted_df = df.sort(
            by=["date", "report_id", "unique_sku_id", "store_id"],
            descending=[True, True, True, True],
        )
        return sorted_df.unique(
            subset=["date", "unique_sku_id", "store_id"],
            keep="first",
            maintain_order=True,
        )
