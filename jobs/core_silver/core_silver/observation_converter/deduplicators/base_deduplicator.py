import abc

import polars as pl

from data_sdk.domain.domain_types import ReportMetadata
from data_sdk.reports.reader import ConvertedReportsReader


class BaseDeduplicator(abc.ABC):
    @abc.abstractmethod
    def deduplicate(
        self,
        metadata_list: list[ReportMetadata],
        converted_filename_list: list[str],
        converted_reader: ConvertedReportsReader,
    ) -> pl.DataFrame:
        pass
