import pandas as pd
import polars as pl

from core_silver.observation_converter.deduplicators.base_deduplicator import (
    BaseDeduplicator,
)
from core_silver.utils.string_format import get_report_id_from_filename
from data_sdk.domain.domain_types import ReportMetadata, ReportState, StudioId
from data_sdk.reports.reader import ConvertedReportsReader


class LastFileDeduplicator(BaseDeduplicator):
    def deduplicate(
        self,
        metadata_list: list[ReportMetadata],
        converted_filename_list: list[str],
        converted_reader: ConvertedReportsReader,
    ) -> pl.DataFrame:
        if not metadata_list or not converted_filename_list:
            return pl.DataFrame()

        filepath = self._find_filepath(metadata_list, converted_filename_list)
        return self._load_file(metadata_list[0].studio_id, filepath, converted_reader)

    def _find_filepath(
        self, metadata_list: list[ReportMetadata], converted_filename_list: list[str]
    ) -> str:
        metadata_df = pd.DataFrame([
            vars(obj) for obj in metadata_list if obj.state != ReportState.FAILED
        ])
        # find newest report_id by upload date
        newest_report_id = metadata_df.sort_values(
            by="upload_date", ascending=False
        ).iloc[0]["report_id"]

        return next(
            filepath
            for filepath in converted_filename_list
            if get_report_id_from_filename(filepath) == newest_report_id
        )

    def _load_file(
        self,
        studio_id: StudioId,
        filename: str,
        converted_reader: ConvertedReportsReader,
    ) -> pl.DataFrame:
        return converted_reader.read(f"studio_id={studio_id}/{filename}")

    def _load_files(
        self,
        studio_id: StudioId,
        filenames: list[str],
        converted_reader: ConvertedReportsReader,
    ) -> pl.DataFrame:
        all_files = [
            converted_reader.read(f"studio_id={studio_id}/{filename}")
            for filename in filenames
        ]
        # Polars saves Enum column in empty df as Categorical
        # and we can't concat dfs with different columns types
        return pl.concat([file for file in all_files if not file.is_empty()])
