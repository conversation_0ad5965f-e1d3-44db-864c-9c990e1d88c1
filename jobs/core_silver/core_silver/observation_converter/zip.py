import io
import json
from typing import I<PERSON>, Callable, Optional, Sequence
from zipfile import ZipFile

import pandas as pd
import pandera as pa

from core_silver.observation_converter.exceptions import FileNotFoundInZip
from core_silver.observation_converter.pandas_utils import enforce_schema

MANIFEST_NAME = "manifest.json"


def get_reports_from_zip(
    zip_file: ZipFile, extension: str
) -> list[tuple[str, IO[bytes]]]:
    """
    Read the zip stored in `buffer` and return an array of uncompressed data with assigned
    file names

    """
    return [
        (name, zip_file.open(name))
        for name in _get_non_empty_files(zip_file)
        if name.endswith(extension)
    ]


def get_additional_data_from_zip(zip_file: ZipFile) -> Optional[dict]:
    """
    Read the zip stored in `buffer` and return an additional data extracted from file.
    If additional data is unavailable returns None. Make sure to check for it wherever
    you use this function. This is because we don't need the additional data if all
    reports are empty.

    Args:
        zip: report zip file

    Returns:
        parsed additional data or None if file was not found

    """
    additional_data = [
        zip_file.read(name) for name in zip_file.namelist() if "additionalData" in name
    ]
    if not additional_data:
        return None
    return json.loads(additional_data[0])


def is_zip(buffer: bytes) -> bool:
    """
    Returns True if `buffer` contains a zipfile.
    """
    return buffer[0:4] == b"PK\x03\x04"


def get_streams_from_zip(zip_file: ZipFile, extension: str) -> list[IO[bytes]]:
    return [
        zip_file.open(name)
        for name in zip_file.namelist()
        if name.endswith(extension) and MANIFEST_NAME not in name
    ]


def read_manifest_from_zip(zip_file: ZipFile) -> dict:
    with zip_file.open(MANIFEST_NAME) as f:
        return json.load(f)


def get_report_names(zip_file: ZipFile, extension: str = "") -> list[str]:
    return [
        name
        for name in _get_non_empty_files(zip_file)
        if name.endswith(extension) and MANIFEST_NAME not in name
    ]


def _get_non_empty_files(zip_file: ZipFile) -> list[str]:
    return [f.orig_filename for f in zip_file.filelist if f.file_size > 0]


def extract_validated_dfs(
    zip_file: ZipFile,
    required_csv_files: Sequence[str],
    get_schema: Callable[[str], pa.DataFrameSchema],
) -> dict[str, pd.DataFrame]:
    dfs: dict[str, pd.DataFrame] = {}

    for filename in required_csv_files:
        if filename not in zip_file.namelist():
            raise FileNotFoundInZip(filename)

        if zip_file.getinfo(filename).file_size != 0:
            df = pd.read_csv(
                io.BytesIO(zip_file.read(filename)),
                sep=",",
                header=0,
                quotechar='"',
                keep_default_na=False,
                na_values=["", "N/A"],
            )

        else:
            df = pd.DataFrame(columns=list(get_schema(filename).columns.keys()))

        dfs[filename] = enforce_schema(df, get_schema(filename))

    return dfs
