import pandas as pd

from data_sdk.domain import Portal


def generate_portal_platform_region(
    portal: pd.Series, platform: pd.Series, region: pd.Series
) -> pd.Series:
    return portal + ":" + platform + ":" + region


def generate_unique_sku_id(df: pd.DataFrame, portal: Portal) -> pd.Series:
    return (
        df["sku_id"].astype(str)
        + "-"
        + portal.value
        + ":"
        + df["studio_id"].astype(str)
    )
