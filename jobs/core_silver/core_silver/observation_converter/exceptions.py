from data_sdk.domain.domain_error import DomainError


class ProcessorError(DomainError):
    def __init__(self, message: str | None = None):
        self.message = message

    def __repr__(self) -> str:
        s = self.__class__.__name__
        return s if not self.message else f'{s}: "{self.message}"'

    __str__ = __repr__


class FileNotFoundInZip(FileNotFoundError):
    def __init__(self, filename: str):
        super().__init__(f"File {filename} not found in zip file")
