import pandas as pd
import pandera as pa


def _message_from_pandera_exception(e: pa.errors.SchemaError) -> str:
    raw_exception_msg = str(e)
    search_msg_config = [
        ("not in dataframe", True),
        ("contains null values", True),
        (": Could not coerce", False),
    ]
    for search_msg, include_search_msg in search_msg_config:
        common_msg_index = raw_exception_msg.find(search_msg)
        search_msg_length = len(search_msg)
        extra_msg_length = search_msg_length if include_search_msg else 0
        if common_msg_index >= 0:
            return (
                raw_exception_msg[0].upper()
                + raw_exception_msg[1 : common_msg_index + extra_msg_length]
            )

    return raw_exception_msg


class InvalidSchema(pa.errors.SchemaError):
    pass


def enforce_schema(df: pd.DataFrame, schema: pa.DataFrameSchema) -> pd.DataFrame:
    """
    Validates a dataframe with a given schema.
    """
    if not df.empty:
        try:
            return schema.validate(df)
        except pa.errors.SchemaError as e:
            raise InvalidSchema(
                schema=e.schema,
                data=e.data,
                message=_message_from_pandera_exception(e),
                failure_cases=e.failure_cases,
                check=e.check,
                check_index=e.check_index,
                check_output=e.check_output,
            )
    return df
