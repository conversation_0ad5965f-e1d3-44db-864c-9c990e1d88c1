from datetime import datetime
from zipfile import BadZipFile

import numpy as np
import pandas as pd
import pandera as pa
from pandera import Column

from core_silver.dictionaries.constants import <PERSON><PERSON><PERSON>, Constant, RevenueFactor
from core_silver.dictionaries.currencies import generate_currency_exchange_rates
from core_silver.dictionaries.vat import convert_gross_to_net
from core_silver.observation_converter.common_columns import (
    generate_portal_platform_region,
)
from core_silver.observation_converter.converters.base_converter import (
    BaseSalesConverter,
    extract_csvs_from_raw_file,
)
from core_silver.observation_converter.converters.exceptions import (
    FileExtractionError,
    FileSchemaError,
)
from core_silver.observation_converter.converters.nintendo_utils import (
    assign_nintendo_store_information,
    generate_nintendo_sales_unique_sku_id,
)
from core_silver.observation_converter.pandas_utils import InvalidSchema, enforce_schema
from core_silver.utils.math_tools import js_round
from core_silver.utils.string_format import translate_column, unescape_html
from data_sdk.domain import DisplayPortal, regions
from data_sdk.domain.countries import CountryCodeAlpha3, get_country_alpha_3
from data_sdk.domain.domain_types import ReportMetadataWithRawFile
from data_sdk.domain.platform import get_display_platform_from_platform
from data_sdk.domain.tables import ExternalCurrencyExchangeRatesTable


def _transform(
    parsed_df: pd.DataFrame, *, is_extended_schema_by_nsuid: bool
) -> pd.DataFrame:
    if parsed_df.empty:
        return pd.DataFrame()

    """
    Nintendo in their reports has complicated format of data.
    Unit sales are kept in matrix with days, f.e.:

    |DATA----|07/14/21|07/15/21|07/16/21|07/17/21|07/18/21|07/19/21|07/20/21|07/21/21|07/22/21|
    |--------|--------|--------|--------|--------|--------|--------|--------|--------|--------|
    |raw-row-|1       |0       |0       |1       |1       |0       |0       |0       |0       |

    Purpose of this is to extract units sold per day, and duplicate rows in such a way to have
    row with data for particular units sold.
    For given example the output should by like this:

    |DATA----|Day-----|Units---|
    |--------|--------|--------|
    |raw-row-|07/14/21|1       |
    |raw-row-|07/17/21|1       |
    |raw-row-|07/18/21|1       |

    We do not care about days without units sold, our output should contain only
    row with units sold higher than 0
    """

    # TODO this code modifies valid currency for rows with 0 Total Sales, for example CAD to USD which I believe is incorrect
    # We can get rid of this assignment and filter it out in next line directly with:
    #   parsed_df["base_price_local"] = parsed_df.loc[parsed_df["Total Sales"] > 0].groupby...
    parsed_df.loc[parsed_df["Total Sales"] == 0, "Currency"] = None

    parsed_df["base_price_local"] = parsed_df.groupby([
        "TitleCode",
        "Country",
        "Currency",
        "StartTime",
    ])["Points/Cost"].transform(max)

    # Columns 18-.. contain dates that we want to unpivot
    # ex. 07/14/21 | 07/15/21 | 07/16/21 | 07/17/21
    unpivot_column_numbers = 19 if is_extended_schema_by_nsuid else 18
    day_columns = list(parsed_df.columns[unpivot_column_numbers:])
    day_columns.remove("base_price_local")

    # Columns 0-17 contain information that we don't want to unpivot
    # ex. TitleCode | TitleName | ItemCode | ItemName | Region
    other_columns = list(parsed_df.columns[:unpivot_column_numbers]) + [
        "base_price_local"
    ]

    # Assign Day and Units to the all report rows
    parsed_df = pd.melt(
        parsed_df,
        id_vars=other_columns,
        value_vars=day_columns,
        var_name="Day",
        value_name="Units",
    )
    parsed_df = parsed_df.loc[parsed_df["Units"] > 0]
    if parsed_df.empty:
        return pd.DataFrame()

    parsed_df["Day"] = parsed_df.apply(_parse_date, axis=1)
    parsed_df["Units"] = parsed_df["Units"].astype(int)

    # fix PIN and empty currency error
    parsed_df.loc[
        (parsed_df["eShop/PIN/POSA"] == "PIN") & parsed_df["Currency"].isna(),
        "Currency",
    ] = "USD"
    # fix PIN and GUNIT currency for Wii U
    parsed_df.loc[parsed_df["Currency"] == "GUNIT", "Points/Cost"] = "0"
    parsed_df.loc[parsed_df["Currency"] == "GUNIT", "Currency"] = "USD"

    # add support for Wii U sku_ids stored only in TitleCode
    parsed_df.loc[parsed_df["ItemCode"].isna(), "ItemCode"] = parsed_df["TitleCode"]

    # fill missing currency with USD
    parsed_df.loc[parsed_df["Currency"].isna(), "Currency"] = "USD"

    return parsed_df


def _parse_date(row):
    if str(row["StartTime"]).find("/") in [1, 2]:
        return datetime.strptime(row["Day"], "%m/%d/%y")
    else:
        return datetime.strptime(row["Day"], "%y/%m/%d")


class NintendoSalesConverter(BaseSalesConverter):
    _schema = pa.DataFrameSchema(
        columns={
            "TitleCode": Column(pa.String, coerce=True),
            "TitleName": Column(pa.String, coerce=True),
            "ItemCode": Column(pa.String, coerce=True, nullable=True),
            "ItemName": Column(pa.String, coerce=True, nullable=True),
            "Region": Column(pa.String, coerce=True),
            "Country": Column(pa.String, coerce=True),
            "CountryName": Column(pa.String, coerce=True),
            "Platform": Column(pa.String, coerce=True),
            "ContentType": Column(pa.String, coerce=True),
            "Publisher": Column(pa.String, coerce=True),
            "StartTime": Column(pa.DateTime, coerce=True),
            "Points/Cost": Column(pa.Float, coerce=True),
            "Currency": Column(pa.String, coerce=True),
            "First Week": Column(pa.Int, coerce=True),
            "Total Sales": Column(pa.Int, coerce=True),
            "eShop/PIN/POSA": Column(pa.String, coerce=True),
            "Card Type": Column(pa.String, coerce=True, nullable=True),
            "Period Total": Column(pa.Int, coerce=True),
            "Day": Column(pa.DateTime, coerce=True),
            "Units": Column(pa.Int, coerce=True),
        }
    )

    _extended_schema_by_nsuid = pa.DataFrameSchema(
        columns={
            **_schema.columns,
            "NsUid": Column(pa.String, coerce=True),
        }
    )

    def __init__(
        self,
        raw_report: ReportMetadataWithRawFile,
        external_currency_exchange_rates_table: ExternalCurrencyExchangeRatesTable,
    ):
        self._external_currency_exchange_rates_table = (
            external_currency_exchange_rates_table
        )
        super().__init__(raw_report)

    def _parse(self, raw_file: bytes) -> pd.DataFrame:
        df = self._extract_file(raw_file)
        self.is_extended_schema_by_nsuid = "NsUid" in df.columns
        schema = (
            self._extended_schema_by_nsuid
            if self.is_extended_schema_by_nsuid
            else self._schema
        )
        df = _transform(
            df, is_extended_schema_by_nsuid=self.is_extended_schema_by_nsuid
        )
        df.sort_index(ascending=True, inplace=True)
        try:
            return enforce_schema(df, schema)
        except InvalidSchema as ex:
            raise FileSchemaError(str(ex))

    def _convert(self) -> pd.DataFrame:
        parsed_df = self._parse(self._raw_report.raw_file)
        manifest = self._raw_report.metadata

        if parsed_df.empty:
            return pd.DataFrame()
        title_name_df = _build_title_name_dataframe(parsed_df)
        converted_df = pd.DataFrame()
        converted_df = converted_df.assign(
            country_code=parsed_df["CountryName"].map(get_country_alpha_3),
            currency_code=parsed_df["Currency"],
            sku_id=parsed_df["ItemCode"],
            portal=DisplayPortal.NINTENDO,
            platform=parsed_df["Platform"].map(get_display_platform_from_platform),
            transaction_type=Constant.UNKNOWN.value,
            payment_instrument=Constant.UNKNOWN.value,
            tax_type=Constant.UNKNOWN.value,
            sale_modificator=Constant.NOT_APPLICABLE.value,
            acquisition_platform=Constant.UNKNOWN.value,
            acquisition_origin=parsed_df["eShop/PIN/POSA"],
            iap_flag=Boolean.FALSE.value,
            date=parsed_df["Day"].map(lambda x: x.strftime("%Y-%m-%d")),
            region=parsed_df["Region"].map(
                regions.get_nintendo_region_by_reported_region
            ),
            retailer_tag=Constant.NOT_APPLICABLE.value,
            report_id=manifest.report_id,
            studio_id=manifest.studio_id,
            units_sold=0,
            bundle_name=Constant.NOT_APPLICABLE.value,
            price_local=js_round(parsed_df["Points/Cost"], 2),
            base_price_local=js_round(parsed_df["base_price_local"], 2),
            free_units=0,
            units_returned=0,
            gross_returned=0,
            price_usd=0,
            net_sales_approx=0.0,
        )
        converted_df["human_name"] = translate_column(
            parsed_df.apply(
                lambda row: title_name_df[
                    title_name_df["ItemCode"] == row["ItemCode"]
                ].iloc[0]["TitleName"],
                axis=1,
            ),
            unescape_html,
        )
        converted_df["store_id"] = (
            parsed_df["NsUid"]
            if self.is_extended_schema_by_nsuid
            else (converted_df["human_name"] + "-" + converted_df["platform"])
        )

        converted_df.loc[converted_df["price_local"] > 0, "units_sold"] = parsed_df.loc[
            parsed_df["Points/Cost"] > 0, "Units"
        ]
        converted_df.loc[converted_df["price_local"] == 0, "free_units"] = (
            parsed_df.loc[parsed_df["Points/Cost"] == 0, "Units"]
        )

        converted_df["currency_exchange_rate"] = generate_currency_exchange_rates(
            converted_df,
            self._external_currency_exchange_rates_table,
        )

        converted_df["gross_sales"] = js_round(
            converted_df["price_local"]
            * converted_df["units_sold"]
            / converted_df["currency_exchange_rate"],
            2,
        )

        converted_df["net_sales"] = converted_df.apply(
            lambda row: self.convert_gross_to_net_nintendo_way(
                row["gross_sales"], row["country_code"]
            ),
            axis=1,
        )

        converted_df.loc[converted_df["units_sold"] > 0, "price_usd"] = js_round(
            converted_df.loc[converted_df["units_sold"] > 0].apply(
                lambda row: row["gross_sales"] / row["units_sold"], axis=1
            ),
            2,
        )

        converted_df = generate_nintendo_sales_unique_sku_id("sku_id", converted_df)
        converted_df = assign_nintendo_store_information(converted_df)

        converted_df["net_sales_approx"] = js_round(
            converted_df["net_sales"] * RevenueFactor.STD.value, 2
        )
        converted_df["portal_platform_region"] = generate_portal_platform_region(
            converted_df["portal"], converted_df["platform"], converted_df["region"]
        )
        return converted_df

    def convert_gross_to_net_nintendo_way(
        self, gross: float, country_code: CountryCodeAlpha3
    ) -> float:
        if country_code in ("USA", "JPN"):
            # The analysis revealed that VAT deductions caused discrepancies
            # in Nintendo's net revenue values for the USA and Japan,
            # suggesting these deductions may be unnecessary.
            return gross

        return convert_gross_to_net(gross, country_code)

    def _extract_file(self, raw_file: bytes) -> pd.DataFrame:
        try:
            df = extract_csvs_from_raw_file(
                raw_file,
                sep=",",
                header=0,
                quotechar='"',
                skipfooter=1,
                engine="python",
                keep_default_na=False,
                na_values=[""],
                dtype={
                    "TitleCode": str,
                    "TitleName": str,
                    "ItemCode": str,
                    "ItemName": str,
                    "Region": str,
                    "Country": str,
                    "CountryName": str,
                    "Platform": str,
                    "ContentType": str,
                    "Publisher": str,
                    "StartTime": str,
                    "Points/Cost": np.float64,
                    "Currency": str,
                    "First Week": np.int64,
                    "Total Sales": np.int64,
                    "eShop/PIN/POSA": str,
                    "Card Type": str,
                    "NsUid": str,
                    "Period Total": np.int64,
                },
            )
        except (ValueError, BadZipFile) as ex:
            raise FileExtractionError(str(ex))
        return df


def _build_title_name_dataframe(parsed_df: pd.DataFrame) -> pd.DataFrame:
    countries_df = parsed_df.copy()
    countries_df["weight"] = parsed_df["Country"].map(
        lambda country: _country_weights.get(country, 0)
    )
    countries_df = countries_df.sort_values(
        ["weight", "Country"], ascending=[False, True]
    )
    return countries_df.groupby("ItemCode").head(1)


_country_weights = {"US": 3, "GB": 2, "AU": 1}
