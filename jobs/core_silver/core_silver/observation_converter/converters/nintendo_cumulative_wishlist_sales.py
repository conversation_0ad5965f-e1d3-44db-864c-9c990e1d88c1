from zipfile import BadZipFile

import numpy as np
import pandas as pd
import pandera as pa
from pandera import Column

from core_silver.observation_converter.common_columns import (
    generate_portal_platform_region,
)
from core_silver.observation_converter.converters.base_converter import (
    BaseCumulativeWishlistSalesConverter,
    extract_csvs_from_raw_file,
)
from core_silver.observation_converter.converters.exceptions import (
    FileExtractionError,
    FileSchemaError,
)
from core_silver.observation_converter.converters.nintendo_utils import (
    assign_nintendo_store_information,
    generate_nintendo_sales_unique_sku_id,
)
from core_silver.observation_converter.pandas_utils import InvalidSchema, enforce_schema
from core_silver.utils.string_format import translate_column, unescape_html
from data_sdk.domain import DisplayPortal, regions
from data_sdk.domain.platform import get_display_platform_from_platform


class NintendoCumulativeWishlistSalesConverter(BaseCumulativeWishlistSalesConverter):
    _schema = pa.DataFrameSchema(
        columns={
            "NsUid": Column(pa.String, coerce=True),
            "Code": Column(pa.String, coerce=True),
            "Name": Column(pa.String, coerce=True),
            "Region": Column(pa.String, coerce=True),
            "Platform": Column(pa.String, coerce=True),
            "Publisher": Column(pa.String, coerce=True),
            "Sales Total": Column(pa.Float, coerce=True),
            "Sales Conversion Rate": Column(pa.String, coerce=True),
            "Total": Column(pa.Int, coerce=True),
        }
    )

    def parse(self, raw_file: bytes) -> pd.DataFrame:
        df = self._extract_file(raw_file)
        df.sort_index(ascending=True, inplace=True)
        try:
            return enforce_schema(df, self._schema)
        except InvalidSchema as ex:
            raise FileSchemaError(str(ex))

    def _convert(self) -> pd.DataFrame:
        parsed_df = self.parse(self._raw_report.raw_file)
        manifest = self._raw_report.metadata

        if parsed_df.empty:
            return pd.DataFrame()

        converted_df = pd.DataFrame()

        converted_df = converted_df.assign(
            cumulative_wishlist_sales=parsed_df["Sales Total"],
            date=manifest.upload_date.strftime(
                "%Y-%m-%d"
            ),  # this needs to be second to properly populate the empty df
            source_based_conversion_rate=parsed_df["Sales Conversion Rate"],
            source_based_total_downloads=parsed_df["Total"],
            store_id=parsed_df["NsUid"],
            sku_id=parsed_df["Code"],
            human_name=translate_column(parsed_df["Name"], unescape_html),
            report_id=manifest.report_id,
            studio_id=manifest.studio_id,
            platform=parsed_df["Platform"].map(get_display_platform_from_platform),
            portal=DisplayPortal.NINTENDO,
            region=parsed_df["Region"].map(
                regions.get_nintendo_region_by_reported_region
            ),
        )

        converted_df = generate_nintendo_sales_unique_sku_id("sku_id", converted_df)

        converted_df["portal_platform_region"] = generate_portal_platform_region(
            converted_df["portal"], converted_df["platform"], converted_df["region"]
        )

        converted_df = assign_nintendo_store_information(converted_df)

        return converted_df

    def _extract_file(self, raw_file: bytes) -> pd.DataFrame:
        try:
            raw_df = extract_csvs_from_raw_file(
                raw_file,
                sep=",",
                header=0,
                quotechar='"',
                skipfooter=1,
                engine="python",
                keep_default_na=False,
                na_values=[""],
                dtype={
                    "NsUid": str,
                    "Code": str,
                    "Name": str,
                    "Region": str,
                    "Platform": str,
                    "Publisher": str,
                    "Sales Total": np.float64,
                    "Sales Conversion Rate": str,
                    "Total": np.int64,
                },
            )
        except (ValueError, BadZipFile) as ex:
            raise FileExtractionError(str(ex))
        return raw_df
