import pandas as pd

from core_silver.dictionaries.constants import Constant
from data_sdk.domain import Portal
from data_sdk.domain.platform import DisplayPlatform
from data_sdk.domain.regions import Region
from data_sdk.domain.stores import Store


def assign_nintendo_store_information(converted_df: pd.DataFrame):
    _nintendo_store_mapping = {
        # SWITCH
        (
            DisplayPlatform.SWITCH.value,
            Region.NINTENDO_AMERICAS.value,
        ): Store.NINTENDO_SWITCH_AMERICAS.value,
        (
            DisplayPlatform.SWITCH.value,
            Region.NINTENDO_EUROPE_AUSTRALIA.value,
        ): Store.NINTENDO_SWITCH_EUROPE_AUSTRALIA.value,
        (
            DisplayPlatform.SWITCH.value,
            Region.NINTENDO_JAPAN.value,
        ): Store.NINTENDO_SWITCH_JAPAN.value,
        (
            DisplayPlatform.SWITCH.value,
            Region.NINTENDO_KOREA.value,
        ): Store.NINTENDO_SWITCH_KOREA.value,
        (
            DisplayPlatform.SWITCH.value,
            Region.NINTENDO_TAIWAN_HONG_KONG.value,
        ): Store.NINTENDO_SWITCH_TAIWAN_HONG_KONG.value,
        (
            DisplayPlatform.SWITCH.value,
            Region.NINTENDO_CHINA.value,
        ): Store.NINTENDO_SWITCH_CHINA.value,
        # WII_U
        (
            DisplayPlatform.WII_U.value,
            Region.NINTENDO_AMERICAS.value,
        ): Store.NINTENDO_OTHER_AMERICAS.value,
        (
            DisplayPlatform.WII_U.value,
            Region.NINTENDO_EUROPE_AUSTRALIA.value,
        ): Store.NINTENDO_OTHER_EUROPE_AUSTRALIA.value,
        (
            DisplayPlatform.WII_U.value,
            Region.NINTENDO_JAPAN.value,
        ): Store.NINTENDO_OTHER_JAPAN.value,
        (
            DisplayPlatform.WII_U.value,
            Region.NINTENDO_KOREA.value,
        ): Store.NINTENDO_OTHER_KOREA.value,
        (
            DisplayPlatform.WII_U.value,
            Region.NINTENDO_TAIWAN_HONG_KONG.value,
        ): Store.NINTENDO_OTHER_TAIWAN_HONG_KONG.value,
        (
            DisplayPlatform.WII_U.value,
            Region.NINTENDO_CHINA.value,
        ): Store.NINTENDO_OTHER_CHINA.value,
        # SWITCH_2
        (
            DisplayPlatform.SWITCH_2.value,
            Region.NINTENDO_AMERICAS.value,
        ): Store.NINTENDO_SWITCH_2_AMERICAS.value,
        (
            DisplayPlatform.SWITCH_2.value,
            Region.NINTENDO_EUROPE_AUSTRALIA.value,
        ): Store.NINTENDO_SWITCH_2_EUROPE_AUSTRALIA.value,
        (
            DisplayPlatform.SWITCH_2.value,
            Region.NINTENDO_JAPAN.value,
        ): Store.NINTENDO_SWITCH_2_JAPAN.value,
        (
            DisplayPlatform.SWITCH_2.value,
            Region.NINTENDO_KOREA.value,
        ): Store.NINTENDO_SWITCH_2_KOREA.value,
        (
            DisplayPlatform.SWITCH_2.value,
            Region.NINTENDO_TAIWAN_HONG_KONG.value,
        ): Store.NINTENDO_SWITCH_2_TAIWAN_HONG_KONG.value,
        (
            DisplayPlatform.SWITCH_2.value,
            Region.NINTENDO_CHINA.value,
        ): Store.NINTENDO_SWITCH_2_CHINA.value,
    }

    # Assign store based on mapping
    mask = converted_df[["platform", "region"]].apply(tuple, axis=1)
    converted_df["store"] = mask.map(_nintendo_store_mapping).fillna(
        Constant.UNKNOWN.value
    )

    # Assign unknowns for each platform if still unknown
    unknown_store_map = {
        DisplayPlatform.SWITCH.value: Store.NINTENDO_SWITCH_UNKNOWN.value,
        DisplayPlatform.WII_U.value: Store.NINTENDO_OTHER_UNKNOWN.value,
        DisplayPlatform.SWITCH_2.value: Store.NINTENDO_SWITCH_2_UNKNOWN.value,
    }
    for platform, unknown_store in unknown_store_map.items():
        idx = (converted_df["platform"] == platform) & (
            converted_df["store"] == Constant.UNKNOWN.value
        )
        converted_df.loc[idx, "store"] = unknown_store

    converted_df["abbreviated_name"] = converted_df["store"].apply(
        lambda s: Store(s).abbreviate()
    )
    return converted_df


def generate_nintendo_sales_unique_sku_id(sku_id_column_name: str, converted_df):
    region_name = (
        converted_df["region"]
        .astype(str)
        .str.strip()
        .str.replace(r"^Nintendo\s+", "", regex=True)  # remove leading 'Nintendo '
        .str.replace(r"[ /]", "_", regex=True)  # replace spaces/slashes
        .str.lower()
    )

    converted_df["unique_sku_id"] = (
        converted_df[sku_id_column_name].astype(str)
        + "-"
        + region_name
        + "-"
        + Portal.NINTENDO.value
        + ":"
        + converted_df["studio_id"].astype(str)
    )

    return converted_df
