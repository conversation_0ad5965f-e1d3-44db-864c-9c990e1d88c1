import polars as pl

non_date_duplication_determinants = [
    "discount_depth",
    "major",
    "unique_sku_id",
]


def filter_out_data_older_than_2016(
    input_df: pl.DataFrame,
) -> pl.DataFrame:
    """
    Filter out rows with datetime_from before 2016
    """
    return input_df.filter(pl.col("datetime_from").dt.year() >= 2016)


def deduplicate_items_and_combine_name(input_df: pl.DataFrame) -> pl.DataFrame:
    """
    If two or more rows have the same discount_depth, major, datetime_form and datetime_to values
    they should be deduplicated and only a single row with the combined name of all the rows should be returned
    """
    name_normalized_events = (
        input_df.groupby([
            *non_date_duplication_determinants,
            "datetime_from",
            "datetime_to",
        ])
        .agg(pl.col("event_name").apply(lambda x: " / ".join(x)))
        .with_columns(pl.col("*"))
    )
    work_df = input_df.unique(
        subset=[*non_date_duplication_determinants, "datetime_from", "datetime_to"],
        keep="first",
    )
    result = work_df.join(
        name_normalized_events,
        on=[*non_date_duplication_determinants, "datetime_from", "datetime_to"],
        suffix="_combined",
    )
    result = result.with_columns(pl.col("event_name_combined").alias("event_name"))
    result = result.drop("event_name_combined")
    return result


def deduplicate_items_with_fully_overlapping_time_but_different_discount_depth(
    input_df: pl.DataFrame,
) -> pl.DataFrame:
    """
    If two or more rows have overlapping datetime_from, datetime_to and major values but different discount_depth values,
    then we should only keep the discount with the highest discount depth
    """
    df = input_df.sort(
        by=[
            "datetime_from",
            "datetime_to",
            "is_event_joined",
            *non_date_duplication_determinants,
        ],
        descending=True,
    )
    return df.unique(
        subset=["datetime_from", "datetime_to", "major", "unique_sku_id"], keep="first"
    )


def round_datetimes_to_nearest_30_minutes(input_df: pl.DataFrame):
    """
    Round datetime_from and datetime_to to closest 30 minutes
    """
    work_df = input_df.with_columns(pl.col("datetime_from").dt.round("30m"))
    return work_df.with_columns(pl.col("datetime_to").dt.round("30m"))


def sanitize(df: pl.DataFrame) -> pl.DataFrame:
    df = filter_out_data_older_than_2016(df)
    return round_datetimes_to_nearest_30_minutes(df)


def deduplicate(df: pl.DataFrame) -> pl.DataFrame:
    df = deduplicate_items_with_fully_overlapping_time_but_different_discount_depth(df)
    return deduplicate_items_and_combine_name(df)
