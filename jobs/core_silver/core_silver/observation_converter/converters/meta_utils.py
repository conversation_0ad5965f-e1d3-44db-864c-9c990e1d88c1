import numpy as np
import pandas as pd

from core_silver.observation_converter.common_columns import (
    generate_portal_platform_region,
)
from data_sdk.domain import Portal
from data_sdk.domain.platform import DisplayPlatform
from data_sdk.domain.stores import Store


def assign_common_meta_fields(converted_df: pd.DataFrame) -> pd.DataFrame:
    converted_df["unique_sku_id"] = (
        converted_df["sku_id"].astype(str)
        + "-"
        + converted_df["platform"].astype(str).map(lambda p: p.lower())
        + "-"
        + Portal.META.value
        + ":"
        + converted_df["studio_id"].astype(str)
    )

    converted_df["store"] = np.where(
        converted_df["platform"] == DisplayPlatform.RIFT.value,
        Store.META_RIFT.value,
        Store.META_QUEST.value,
    )

    converted_df["abbreviated_name"] = converted_df[["store"]].apply(
        lambda c: Store(c["store"]).abbreviate(), axis=1
    )

    converted_df["portal_platform_region"] = generate_portal_platform_region(
        converted_df["portal"], converted_df["platform"], converted_df["region"]
    )

    return converted_df
