import functools
import io
from typing import Union
from zipfile import ZipFile

import numpy as np
import pandas as pd
import pandera as pa
from pandera import Column

from core_silver.dictionaries.constants import <PERSON><PERSON><PERSON>, Constant, RevenueFactor
from core_silver.dictionaries.currencies import NO_CURRENCY_TRANSACTION_ISO_4217_CODE
from core_silver.observation_converter.common_columns import (
    generate_portal_platform_region,
    generate_unique_sku_id,
)
from core_silver.observation_converter.converters.base_converter import (
    BaseSalesConverter,
    extract_csvs_from_raw_file,
    extract_json_from_raw_zip_file,
    extract_json_from_unzipped_raw_file,
    get_manifest_data_from_raw_file,
)
from core_silver.observation_converter.converters.exceptions import (
    FileExtractionError,
    FileSchemaError,
)
from core_silver.observation_converter.pandas_utils import InvalidSchema, enforce_schema
from core_silver.observation_converter.zip import get_report_names, is_zip
from core_silver.utils.math_tools import js_round
from core_silver.utils.string_format import translate_column, unescape_html
from data_sdk.domain import DisplayPortal, Portal, countries
from data_sdk.domain.platform import DisplayPlatform
from data_sdk.domain.regions import Region
from data_sdk.domain.stores import Store

duplicated_preorder_acquisition_types_to_exclude = [
    "Cancelled Pre Order",
    "Failed Pre Order",
    "Pre Order",
]

# TODO rename manifest to - metadata in a inteligent way!


class MicrosoftConverter(BaseSalesConverter):
    _schema = pa.DataFrameSchema(
        columns={
            "market": Column(pa.String, coerce=True),
            "applicationName": Column(pa.String, coerce=True),
            "acquisitionType": Column(pa.String, coerce=True),
            "osVersion": Column(pa.String, coerce=True),
            "age": Column(pa.String, coerce=True),
            "deviceType": Column(pa.String, coerce=True),
            "gender": Column(pa.String, coerce=True),
            "paymentInstrumentType": Column(pa.String, coerce=True),
            "sandboxId": Column(pa.String, coerce=True),
            "storeClient": Column(pa.String, coerce=True),
            "xboxTitleId": Column(pa.String, coerce=True),
            "localCurrencyCode": Column(pa.String, coerce=True),
            "xboxProductId": Column(pa.String, coerce=True),
            "availabilityId": Column(pa.String, coerce=True),
            "skuId": Column(pa.String, coerce=True),
            "skuDisplayName": Column(pa.String, coerce=True),
            "xboxParentProductId": Column(pa.String, coerce=True),
            "parentProductName": Column(pa.String, coerce=True),
            "applicationId": Column(pa.String, coerce=True),
            "date": Column(pa.String, coerce=True),
            "acquisitionQuantity": Column(pa.Int, coerce=True),
            "purchasePriceUSDAmount": Column(pa.Float, coerce=True),
            "purchasePriceLocalAmount": Column(pa.Float, coerce=True),
            "purchaseTaxUSDAmount": Column(pa.Float, coerce=True),
            "purchaseTaxLocalAmount": Column(pa.Float, coerce=True),
            "inAppProductName": Column(pa.String, coerce=True, required=False),
        }
    )

    _schema_csv = pa.DataFrameSchema(
        columns={
            "titleId": Column(pa.String, coerce=True, nullable=True),
            "applicationId": Column(pa.String, coerce=True),
            "xboxProductId": Column(pa.String, coerce=True, nullable=True),
            "productTypeName": Column(pa.String, coerce=True),
            "applicationName": Column(pa.String, coerce=True),
            "catalogId": Column(pa.Int, coerce=True),
            "sandboxId": Column(pa.String, coerce=True),
            "skuId": Column(pa.String, coerce=True),
            "skuTypeName": Column(pa.String, coerce=True),
            "skuDisplayName": Column(pa.String, coerce=True),
            "availabilityId": Column(pa.String, coerce=True, nullable=True),
            "regionName": Column(pa.String, coerce=True),
            "countryName": Column(pa.String, coerce=True),
            "market": Column(pa.String, coerce=True),
            "paymentInstrumentType": Column(pa.String, coerce=True),
            "storeClientName": Column(pa.String, coerce=True, nullable=True),
            "storeClient": Column(pa.String, coerce=True),
            "parentProductName": Column(pa.String, coerce=True),
            "parentProductId": Column(pa.String, coerce=True),
            "xboxParentProductId": Column(pa.String, coerce=True, nullable=True),
            "acquisitionType": Column(pa.String, coerce=True),
            "localCurrencyCode": Column(pa.String, coerce=True),
            "supportedPlatform": Column(pa.String, coerce=True),
            "age": Column(pa.String, coerce=True),
            "gender": Column(pa.String, coerce=True),
            "osVersion": Column(pa.String, coerce=True),
            "deviceType": Column(pa.String, coerce=True),
            "date": Column(pa.String, coerce=True),
            "acquisitionQuantity": Column(pa.Int, coerce=True),
            "purchasePriceUSDAmount": Column(pa.Float, coerce=True),
            "purchaseTaxUSDAmount": Column(pa.Float, coerce=True),
            "purchasePriceLocalAmount": Column(pa.Float, coerce=True),
            "purchaseTaxLocalAmount": Column(pa.Float, coerce=True),
        }
    )

    def _parse(self, raw_file: bytes) -> pd.DataFrame:
        df, is_csv = self._extract_file(raw_file)
        if df.empty:
            return pd.DataFrame()

        # for new CSV format
        if is_csv:
            df = self._adjust_csv_to_common_columns_schema(df)

        df = df[
            ~df["acquisitionType"].isin(
                duplicated_preorder_acquisition_types_to_exclude
            )
        ].reset_index()
        self._transform(df)
        df = df.sort_index(ascending=True)
        try:
            if is_csv:
                return enforce_schema(df, self._schema_csv)
            return enforce_schema(df, self._schema)
        except InvalidSchema as ex:
            raise FileSchemaError(str(ex))

    def _convert(self) -> pd.DataFrame:
        parsed_df = self._parse(self._raw_report.raw_file)
        metadata = self._raw_report.metadata

        if parsed_df.empty:
            return pd.DataFrame()

        converted_df = pd.DataFrame()
        converted_df = converted_df.assign(
            country_code=parsed_df["market"].map(countries.get_country_alpha_3),
            currency_code=parsed_df["localCurrencyCode"],
            studio_id=metadata.studio_id,
            portal=DisplayPortal.MICROSOFT,
            platform=DisplayPlatform.MICROSOFT,
            region=Region.GLOBAL.value,
            bundle_name=Constant.NOT_APPLICABLE.value,
            transaction_type=parsed_df["acquisitionType"],
            payment_instrument=parsed_df["paymentInstrumentType"],
            tax_type=Constant.UNKNOWN.value,
            sale_modificator=Constant.NOT_APPLICABLE.value,
            acquisition_platform=parsed_df["deviceType"],
            acquisition_origin=parsed_df["storeClient"],
            iap_flag=Boolean.FALSE.value,
            date=parsed_df["date"].map(lambda x: x[:10]),
            report_id=metadata.report_id,
            retailer_tag=Constant.NOT_APPLICABLE.value,
            base_price_local=None,
            net_sales=0,
            gross_returned=0,
            gross_sales=0,
            units_returned=0,
            units_sold=0,
            free_units=0,
            price_local=parsed_df["purchasePriceLocalAmount"],
            price_usd=parsed_df["purchasePriceUSDAmount"],
            net_sales_approx=0.0,
            store=Store.MICROSOFT.value,
            abbreviated_name=Store.MICROSOFT.abbreviate(),
        )

        if "titleId" in parsed_df.columns:
            converted_df.loc[parsed_df["productTypeName"] == "Add-On", "iap_flag"] = (
                Boolean.TRUE.value
            )
        else:
            converted_df["iap_flag"] = self._get_iap_flag_series(parsed_df)

        if "human_name_from_manifest" in parsed_df.columns:
            # Assign sku_id and name using mapping from manifest.json (3rd gen)
            self._assign_values_from_manifest(parsed_df, converted_df)
        elif "titleId" in parsed_df.columns:
            # Assign sku_id and name using parsed_df directly from csv (4th gen)
            self._assign_values_from_the_same_data(parsed_df, converted_df)
        else:
            # Assign sku_id and name using mapping from sales jsons (1st and 2nd gen)
            self._assign_values_from_parsed_data(parsed_df, converted_df)

        # fill empty currency codes with "USD" for free purchases
        # since we still have raw data we have strings here
        # we can use casting to bool to quickly find empty ones

        converted_df.loc[
            (~converted_df["currency_code"].astype(bool))
            & (converted_df["transaction_type"] == "Free"),
            "currency_code",
        ] = "USD"

        # ISO 4217: The code XXX is used to denote a "transaction" involving no currency.
        converted_df.loc[
            (
                ~converted_df["currency_code"].astype(bool)
                & (converted_df["transaction_type"] != "Free")
            ),
            "currency_code",
        ] = NO_CURRENCY_TRANSACTION_ISO_4217_CODE

        # units_sold
        converted_df.loc[converted_df["transaction_type"] != "Free", "units_sold"] = (
            parsed_df.loc[parsed_df["acquisitionType"] != "Free", "acquisitionQuantity"]
        )

        # price_local
        price_local = converted_df.loc[converted_df["units_sold"] != 0, "price_local"]
        units_sold = converted_df.loc[converted_df["units_sold"] != 0, "units_sold"]
        converted_df.loc[converted_df["units_sold"] != 0, "price_local"] = js_round(
            price_local / units_sold, 2
        )

        # units returned
        converted_df.loc[converted_df["units_sold"] < 0, "units_returned"] = (
            converted_df.loc[converted_df["units_sold"] < 0, "units_sold"]
        )

        # free units
        converted_df.loc[converted_df["transaction_type"] == "Free", "free_units"] = (
            parsed_df.loc[parsed_df["acquisitionType"] == "Free", "acquisitionQuantity"]
        )

        # price_usd
        price_usd = converted_df.loc[converted_df["units_sold"] != 0, "price_usd"]
        units_sold = converted_df.loc[converted_df["units_sold"] != 0, "units_sold"]
        converted_df.loc[converted_df["units_sold"] != 0, "price_usd"] = js_round(
            price_usd / units_sold, 2
        )

        # gross sales
        converted_df["gross_sales"] = parsed_df["purchasePriceUSDAmount"]

        # gross returned
        converted_df.loc[converted_df["gross_sales"] <= 0, "gross_returned"] = (
            converted_df.loc[converted_df["gross_sales"] <= 0, "gross_sales"]
        )
        converted_df["gross_returned"] = converted_df["gross_returned"].apply(
            lambda x: -abs(x)
        )

        # net sales
        converted_df["net_sales"] = js_round(
            converted_df["gross_sales"] - parsed_df["purchaseTaxUSDAmount"], 2
        )

        converted_df["unique_sku_id"] = generate_unique_sku_id(
            converted_df, Portal.MICROSOFT
        )
        converted_df["portal_platform_region"] = generate_portal_platform_region(
            converted_df["portal"], converted_df["platform"], converted_df["region"]
        )

        converted_df["net_sales_approx"] = js_round(
            converted_df["net_sales"] * RevenueFactor.STD.value, 2
        )
        # reset index before returning so pandera won't freak out on validation
        return converted_df.reset_index()

    def _extract_file(self, raw_file: bytes) -> tuple[pd.DataFrame, bool]:
        with io.BytesIO(raw_file) as stream:
            try:
                is_csv: bool = False
                if is_zip(raw_file):
                    zip_file = ZipFile(stream)
                    report_names = get_report_names(zip_file)

                    if all(name.endswith(".json") for name in report_names):
                        # zip with JSON only - old flow
                        file_meta_data = self._get_file_meta_data_if_exists(raw_file)
                        if not file_meta_data:
                            # support 2nd gen MS Scraper - zipped raw JSON
                            # sales data, but no manifest
                            return (extract_json_from_raw_zip_file(raw_file), is_csv)
                        # support 3nd gen MS Scraper - zipped raw JSON with
                        # sku-name assignment in manifest
                        return (
                            extract_json_from_raw_zip_file(
                                raw_file,
                                functools.partial(
                                    _assign_human_name_and_sku_ids,
                                    file_meta_data=file_meta_data,
                                ),
                            ),
                            is_csv,
                        )
                    else:
                        # support 4rd gen MS Scraper - zipped CSVs with
                        # manifests with organizational CSV assignment
                        is_csv = True
                        return (self._load_csvs_to_df(raw_file), is_csv)
                else:
                    # support 1st gen MS Scraper - unzipped raw json
                    return extract_json_from_unzipped_raw_file(raw_file), is_csv
            except Exception:
                raise FileExtractionError(message="Incorrect file format")

    @staticmethod
    def _get_file_meta_data_if_exists(raw_file: bytes) -> dict | None:
        manifest = None
        try:
            manifest = get_manifest_data_from_raw_file(raw_file)
            if "fileMetaData" in manifest:  # python scraper version
                return manifest["fileMetaData"]
            return manifest["metadata"].get("fileMetaData", None)
        except Exception:
            return None

    @staticmethod
    def _transform(parsed_df: pd.DataFrame) -> None:
        for col in (
            "purchasePriceUSDAmount",
            "purchasePriceLocalAmount",
            "purchaseTaxUSDAmount",
            "purchaseTaxLocalAmount",
        ):
            parsed_df[col] = parsed_df[col].apply(
                lambda x: 0 if x == "" else js_round(x, 2)
            )

    @staticmethod
    def _adjust_csv_to_common_columns_schema(parsed_df: pd.DataFrame) -> pd.DataFrame:
        parsed_df = parsed_df.rename(columns=lambda x: x[0].lower() + x[1:])
        parsed_df = parsed_df.rename(
            columns={
                "paymentType": "paymentInstrumentType",
                "storeClientCategory": "storeClient",
                "dateStamp": "date",
                "productId": "applicationId",
                "titleName": "applicationName",
                "purchaseQuantity": "acquisitionQuantity",
            }
        )
        parsed_df[
            [
                "purchasePriceUSDAmount",
                "purchaseTaxUSDAmount",
                "purchasePriceLocalAmount",
                "purchaseTaxLocalAmount",
            ]
        ] = parsed_df[
            [
                "purchasePriceUSDAmount",
                "purchaseTaxUSDAmount",
                "purchasePriceLocalAmount",
                "purchaseTaxLocalAmount",
            ]
        ].fillna(0)

        parsed_df.loc[parsed_df["acquisitionType"] == "Disk", "skuId"] = 10
        parsed_df.loc[parsed_df["acquisitionType"] == "Disk", "purchaseTaxType"] = (
            Constant.UNKNOWN.value
        )
        parsed_df.loc[parsed_df["acquisitionType"] == "Disk", "localCurrencyCode"] = (
            "USD"
        )
        return parsed_df

    @staticmethod
    def _get_iap_flag_series(parsed_df: pd.DataFrame) -> Union[pd.Series, str]:
        if "inAppProductName" in parsed_df.columns:
            return np.where(
                parsed_df["inAppProductName"], Boolean.TRUE.value, Boolean.FALSE.value
            )
        return Boolean.FALSE.value

    @staticmethod
    def _assign_values_from_manifest(
        parsed_df: pd.DataFrame, converted_df: pd.DataFrame
    ):
        converted_df["sku_id"] = parsed_df["sku_id_from_manifest"]
        converted_df["store_id"] = parsed_df["sku_id_from_manifest"]
        converted_df["human_name"] = translate_column(
            parsed_df["human_name_from_manifest"], unescape_html
        )

    @staticmethod
    def _assign_values_from_parsed_data(
        parsed_df: pd.DataFrame, converted_df: pd.DataFrame
    ):
        converted_df["sku_id"] = parsed_df["applicationId"] + parsed_df["skuId"]
        converted_df["store_id"] = parsed_df["applicationId"]
        converted_df["human_name"] = translate_column(
            _get_human_name_series(parsed_df), unescape_html
        )

    @staticmethod
    def _assign_values_from_the_same_data(
        parsed_df: pd.DataFrame, converted_df: pd.DataFrame
    ):
        converted_df["sku_id"] = parsed_df["applicationId"]
        converted_df["store_id"] = parsed_df["applicationId"]
        converted_df["human_name"] = translate_column(
            _get_human_name_series(parsed_df), unescape_html
        )

    @staticmethod
    def _load_csvs_to_df(raw_file: bytes) -> pd.DataFrame:
        try:
            df = extract_csvs_from_raw_file(
                raw_file,
                sep=",",
                header=0,
                skiprows=0,
                parse_dates=["DateStamp"],
                date_parser=lambda x: pd.to_datetime(x, format="%d-%m-%Y %H:%M:%S"),
                quotechar='"',
                keep_default_na=False,
                na_values=[""],
                dtype={
                    "TitleId": str,
                    "ProductId": str,
                    "XboxProductId": str,
                    "ProductTypeName": str,
                    "TitleName": str,
                    "CatalogId": np.int64,
                    "SandboxId": str,
                    "SkuId": str,
                    "SkuTypeName": str,
                    "SkuDisplayName": str,
                    "AvailabilityId": str,
                    "RegionName": str,
                    "CountryName": str,
                    "Market": str,
                    "PaymentType": str,
                    "StoreClientName": str,
                    "StoreClientCategory": str,
                    "ParentProductName": str,
                    "ParentProductId": str,
                    "XboxParentProductId": str,
                    "AcquisitionType": str,
                    "LocalCurrencyCode": str,
                    "SupportedPlatform": str,
                    "Age": str,
                    "Gender": str,
                    "OsVersion": str,
                    "DeviceType": str,
                    "DateStamp": str,
                    "PurchaseQuantity": np.int64,
                    "PurchasePriceUSDAmount": np.float64,
                    "PurchaseTaxUSDAmount": np.float64,
                    "PurchasePriceLocalAmount": np.float64,
                    "PurchaseTaxLocalAmount": np.float64,
                },
            )
        except ValueError as ex:
            raise FileExtractionError(str(ex))
        return df


def _assign_human_name_and_sku_ids(
    data_frame: pd.DataFrame, name: str, file_meta_data: dict
) -> pd.DataFrame:
    file_data = file_meta_data[name]
    data_frame["human_name_from_manifest"] = file_data["humanName"]
    data_frame["sku_id_from_manifest"] = file_data["skuId"]
    data_frame["parent_sku_id_from_manifest"] = file_data["parentSkuId"]
    return data_frame


def _get_human_name_series(parsed_df: pd.DataFrame) -> Union[pd.Series, str]:
    if "inAppProductName" in parsed_df.columns:
        return pd.Series(
            np.where(
                parsed_df["inAppProductName"],
                parsed_df["inAppProductName"],
                parsed_df["applicationName"],
            )
        )
    return parsed_df["applicationName"]
