import io

import numpy as np
import pandas as pd
import pandera as pa

from core_silver.dictionaries.constants import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, RevenueFactor
from core_silver.observation_converter.common_columns import (
    generate_portal_platform_region,
    generate_unique_sku_id,
)
from core_silver.observation_converter.converters.base_converter import (
    BaseSalesConverter,
)
from core_silver.observation_converter.converters.exceptions import (
    FileExtractionError,
    FileSchemaError,
)
from core_silver.observation_converter.pandas_utils import InvalidSchema, enforce_schema
from core_silver.utils.math_tools import js_round
from core_silver.utils.string_format import (
    filter_nonalphanumeric,
    translate_column,
    unescape_html,
)
from data_sdk.domain import DisplayPortal, Portal, countries
from data_sdk.domain.platform import DisplayPlatform
from data_sdk.domain.regions import Region
from data_sdk.domain.stores import Store


class HumbleConverter(BaseSalesConverter):
    _schema = pa.DataFrameSchema(
        columns={
            "human_name": pa.Column(pa.String, coerce=True),
            "base_human_name": pa.Column(pa.String, nullable=True, coerce=True),
            "coupon_human_name": pa.Column(pa.String, nullable=True, coerce=True),
            "machine_name": pa.Column(pa.String, coerce=True),
            "publisher_id": pa.Column(pa.String, nullable=True, coerce=True),
            "category": pa.Column(pa.String, coerce=True),
            "country": pa.Column(pa.String, coerce=True),
            "transaction_date": pa.Column(pa.DateTime, coerce=True),
            "price": pa.Column(pa.Float, coerce=True),
            "currency": pa.Column(pa.String, coerce=True),
            "platform": pa.Column(pa.String, coerce=True),
            "event_type": pa.Column(pa.String, coerce=True),
            "count": pa.Column(pa.Int, coerce=True),
            "gross": pa.Column(pa.Float, coerce=True),
            "tax": pa.Column(pa.Float, coerce=True),
            "charity": pa.Column(pa.Float, coerce=True),
            "hb_fee": pa.Column(pa.Float, coerce=True),
            "other_dev": pa.Column(pa.Float, coerce=True),
            "paymentfee": pa.Column(pa.Float, coerce=True),
            "revenue_total": pa.Column(pa.Float, coerce=True),
            "revenue_usa": pa.Column(pa.Float, coerce=True),
            "revenue_international": pa.Column(pa.Float, coerce=True),
        }
    )

    def _parse(self, raw_file: bytes) -> pd.DataFrame:
        df = self._extract_file(raw_file)
        df.sort_index(ascending=True, inplace=True)
        try:
            return enforce_schema(df, self._schema)
        except InvalidSchema as ex:
            raise FileSchemaError(str(ex))

    def _convert(self) -> pd.DataFrame:
        parsed_df = self._parse(self._raw_report.raw_file)
        metadata = self._raw_report.metadata

        if parsed_df.empty:
            return pd.DataFrame()
        converted_df = pd.DataFrame()

        converted_df = converted_df.assign(
            country_code=parsed_df["country"].map(countries.get_country_alpha_3),
            currency_code=parsed_df["currency"],
            studio_id=metadata.studio_id,
            sku_id=parsed_df["machine_name"].map(filter_nonalphanumeric),
            portal=DisplayPortal.HUMBLE,
            platform=DisplayPlatform.KEY,
            region=Region.GLOBAL.value,
            transaction_type=parsed_df["event_type"],
            payment_instrument=Constant.UNKNOWN.value,
            bundle_name=Constant.NOT_APPLICABLE.value,
            tax_type=Constant.UNKNOWN.value,
            sale_modificator=Constant.NOT_APPLICABLE.value,
            acquisition_platform=parsed_df["platform"],
            acquisition_origin=Constant.UNKNOWN.value,
            iap_flag=Boolean.FALSE.value,
            date=parsed_df["transaction_date"].map(lambda x: x.strftime("%Y-%m-%d")),
            report_id=metadata.report_id,
            retailer_tag=Constant.NOT_APPLICABLE.value,
            human_name=translate_column(parsed_df["human_name"], unescape_html),
            store_id=parsed_df["machine_name"],
            base_price_local=np.nan,
            net_sales=js_round(parsed_df["gross"] - parsed_df["tax"], 2),
            gross_sales=js_round(parsed_df["gross"], 2),
            units_sold=0,
            free_units=0,
            gross_returned=0.0,
            units_returned=0,
            price_usd=0.0,
            price_local=0.0,
            net_sales_approx=0.0,
            store=Store.HUMBLE.value,
            abbreviated_name=Store.HUMBLE.abbreviate(),
        )

        converted_df.loc[converted_df["gross_sales"] != 0, "units_sold"] = parsed_df[
            "count"
        ]
        converted_df.loc[converted_df["gross_sales"] == 0, "free_units"] = parsed_df[
            "count"
        ]

        converted_df.loc[
            converted_df["gross_sales"] < 0, "gross_returned"
        ] = -converted_df.loc[converted_df["gross_sales"] < 0, "gross_sales"]
        converted_df.loc[
            converted_df["units_sold"] < 0, "units_returned"
        ] = -converted_df.loc[converted_df["units_sold"] < 0, "units_sold"]
        converted_df.loc[converted_df["units_sold"] != 0, "price_usd"] = js_round(
            converted_df.loc[converted_df["units_sold"] != 0, "gross_sales"]
            / converted_df.loc[converted_df["units_sold"] != 0, "units_sold"],
            2,
        )
        converted_df.loc[converted_df["units_sold"] != 0, "price_local"] = js_round(
            parsed_df["price"], 2
        )
        converted_df["unique_sku_id"] = generate_unique_sku_id(
            converted_df, Portal.HUMBLE
        )
        converted_df["portal_platform_region"] = generate_portal_platform_region(
            converted_df["portal"], converted_df["platform"], converted_df["region"]
        )

        converted_df["net_sales_approx"] = js_round(
            converted_df["net_sales"] * RevenueFactor.STD.value, 2
        )

        return converted_df

    @staticmethod
    def _extract_file(raw_file: bytes) -> pd.DataFrame:
        try:
            with io.BytesIO(raw_file) as stream:
                df = pd.read_csv(
                    stream,
                    sep=",",
                    header=0,
                    parse_dates=True,
                    quotechar='"',
                    engine="python",
                    keep_default_na=False,
                    na_values=[""],
                    dtype={
                        "human_name": str,
                        "base_human_name": str,
                        "coupon_human_name": str,
                        "machine_name": str,
                        "publisher_id": str,
                        "category": str,
                        "country": str,
                        "transaction_date": str,
                        "price": np.float64,
                        "currency": str,
                        "platform": str,
                        "event_type": str,
                        "count": np.int64,
                        "gross": np.float64,
                        "tax": np.float64,
                        "charity": np.float64,
                        "hb_fee": np.float64,
                        "other_dev": np.float64,
                        "paymentfee": np.float64,
                        "revenue_total": np.float64,
                        "revenue_usa": np.float64,
                        "revenue_international": np.float64,
                    },
                )
        except ValueError as ex:
            raise FileExtractionError(str(ex))
        return df
