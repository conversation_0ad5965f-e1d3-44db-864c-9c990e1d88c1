import io
from datetime import datetime
from enum import Enum
from typing import IO
from zipfile import ZipFile

import numpy as np
import pandas as pd
import pandera as pa
import polars as pl

from core_silver.observation_converter.common_columns import (
    generate_portal_platform_region,
)
from core_silver.observation_converter.converters.base_converter import (
    BaseDiscountsConverter,
)
from core_silver.observation_converter.converters.discount_sanitize_functions import (
    deduplicate,
    sanitize,
)
from core_silver.observation_converter.pandas_utils import enforce_schema
from core_silver.observation_converter.zip import extract_validated_dfs
from core_silver.utils.string_format import (
    datetime_to_string,
    filter_nonalphanumeric,
    timestamp_to_datetime_str,
)
from core_silver.validators.columns import EnumString
from data_sdk.domain import DisplayPortal, Portal
from data_sdk.domain.discounts import DiscountType
from data_sdk.domain.domain_types import ReportMetadataWithRawFile
from data_sdk.domain.platform import DisplayPlatform
from data_sdk.domain.regions import Region


class SteamCollisionType(str, Enum):
    PROXIMITY = "proximity"
    UNIQUE = "unique"


class SteamDiscountClass(str, Enum):
    RELEASED = "released"
    UNAVAILABLE = "unavailable"


class SteamDiscountsConverter(BaseDiscountsConverter):
    _raw_csvs_schemas = {
        "priceIncreaseTimes.csv": pa.DataFrameSchema(
            columns={
                "publisherId": pa.Column(pa.Int, coerce=True),
                "packageId": pa.Column(pa.Int, coerce=True),
                "priceIncreaseTime": pa.Column(pa.Int, coerce=True),
            },
            strict=True,
        ),
        "userinfo.csv": pa.DataFrameSchema(
            columns={
                "publisherId": pa.Column(pa.Int, coerce=True),
                "loggedIn": pa.Column(pa.String, coerce=True),
                "steamid": pa.Column(pa.Int, coerce=True),
                "accountid": pa.Column(pa.Int, coerce=True),
                "accountName": pa.Column(pa.String, coerce=True),
                "isSupport": pa.Column(pa.String, coerce=True),
                "isLimited": pa.Column(pa.String, coerce=True),
                "isPartnerMember": pa.Column(pa.String, coerce=True),
                "countryCode": pa.Column(pa.String, coerce=True),
                "excludedContentDescriptors": pa.Column(
                    pa.String, coerce=True, required=False
                ),
            },
            strict=True,
        ),
        "discountEvents.csv": pa.DataFrameSchema(
            columns={
                "publisherId": pa.Column(pa.Int, coerce=True),
                "name": pa.Column(pa.String, coerce=True),
                "startDate": pa.Column(pa.Int, coerce=True),
                "endDate": pa.Column(pa.Int, coerce=True),
                "description": pa.Column(pa.String, coerce=True, nullable=True),
                "collisionType": EnumString(SteamCollisionType),
                "event": pa.Column(pa.String, coerce=True, nullable=True),
                "header": pa.Column(pa.String, coerce=True, nullable=True),
                "tooltip": pa.Column(pa.String, coerce=True, nullable=True),
                "type": pa.Column(pa.String, coerce=True, nullable=True),
                "preventWeeklong": pa.Column(pa.String, coerce=True, nullable=True),
                "appids": pa.Column(pa.String, coerce=True, nullable=True),
                "optInName": pa.Column(
                    pa.String, coerce=True, nullable=True, required=False
                ),
                "id": pa.Column(pa.String, coerce=True),
            },
            strict=True,
        ),
        "discountHistory.csv": pa.DataFrameSchema(
            columns={
                "name": pa.Column(pa.String, coerce=True),
                "description": pa.Column(pa.String, coerce=True, nullable=True),
                "startDate": pa.Column(pa.Int, coerce=True),
                "dateStr": pa.Column(pa.String, coerce=True),
                "endDate": pa.Column(pa.Int, coerce=True),
                "endDateStr": pa.Column(pa.String, coerce=True),
                "percent": pa.Column(pa.Int, coerce=True),
                "quantity": pa.Column(pa.Int, coerce=True),
                "amount": pa.Column(pa.Int, coerce=True),
                "id": pa.Column(pa.Int, coerce=True),
                "packageid": pa.Column(
                    pa.Int, coerce=True, nullable=True, required=False
                ),
                "group": pa.Column(pa.String, coerce=True),
                "grossUnitsSold": pa.Column(
                    pa.String, coerce=True, nullable=True, required=False
                ),
                "grossSalesUsdx100": pa.Column(
                    pa.String, coerce=True, nullable=True, required=False
                ),
                "discountOccurrence": pa.Column(
                    pa.String, coerce=True, nullable=True, required=False
                ),
                "multidiscount": pa.Column(
                    pa.String, coerce=True, nullable=True, required=False
                ),
                "hasViewGrant": pa.Column(
                    pa.Bool, coerce=True, nullable=True, required=False
                ),
                "olddiscount": pa.Column(  # was replaced by hasViewGrant field on 30.07.2025
                    pa.String, coerce=True, nullable=True, required=False
                ),
                "grossSalesPerDay": pa.Column(
                    pa.String, coerce=True, nullable=True, required=False
                ),
                "class": pa.Column(pa.String, coerce=True),
                "productId": pa.Column(pa.Int, coerce=True),
                "publisherId": pa.Column(pa.Int, coerce=True),
            },
            strict=False,
        ),
    }

    _raw_discount_all_csv_schema = pa.DataFrameSchema(
        columns={
            "Package Name": pa.Column(pa.String, coerce=True),
            "ID": pa.Column(pa.Int, coerce=True),
            "Base Price": pa.Column(pa.String, coerce=True),
        },
        strict=False,  # important as this file may contain different columns over time
    )

    _intermidiate_facts_and_planned_events_schema = pa.DataFrameSchema(
        columns={
            "event_name": pa.Column(pa.String, coerce=True),
            "datetime_from": pa.Column(pa.DateTime, coerce=True),
            "datetime_to": pa.Column(pa.DateTime, coerce=True),
            "is_event_joined": pa.Column(pa.Bool, coerce=True),
            "discount_depth": pa.Column(pa.Int, coerce=True),
            "unique_event_id": pa.Column(pa.String, coerce=True),
            "base_event_id": pa.Column(pa.String, coerce=True),
            "group_id": pa.Column(pa.String, coerce=True),
            "triggers_cooldown": pa.Column(pa.Bool, coerce=True),
            "major": pa.Column(pa.Bool, coerce=True),
            "discount_type": EnumString(DiscountType),
            "base_sku_id": pa.Column(pa.Int, coerce=True),
        },
        strict=True,
    )

    _intermidiate_discount_events_schema = pa.DataFrameSchema(
        columns={
            "datetime_from": pa.Column(pa.DateTime, coerce=True),
            "datetime_to": pa.Column(pa.DateTime, coerce=True),
            "is_event_joined": pa.Column(pa.Bool, coerce=True),
            "group_id": pa.Column(pa.String, coerce=True),
            "base_event_id": pa.Column(pa.String, coerce=True),
            "triggers_cooldown": pa.Column(pa.Bool, coerce=True),
            "major": pa.Column(pa.Bool, coerce=True),
            "join_id": pa.Column(pa.String, coerce=True),
        },
        strict=True,
    )

    _static_required_filenames: tuple[str, ...] = (
        "priceIncreaseTimes.csv",
        "userinfo.csv",
        "discountEvents.csv",
        "discountHistory.csv",
    )

    def __init__(self, raw_report: ReportMetadataWithRawFile):
        super().__init__(raw_report)
        self._dynamic_required_filenames: list[str] = []

    def _convert(self) -> pd.DataFrame:
        raw_dfs = self._parse(self._raw_report.raw_file)

        no_user_info_data = raw_dfs["userinfo.csv"].empty
        no_data_in_other_files = all(
            df.empty for filename, df in raw_dfs.items() if filename != "userinfo.csv"
        )
        no_past_and_future_discounts_data = (
            raw_dfs["discountEvents.csv"].empty and raw_dfs["discountHistory.csv"].empty
        )

        if (
            no_user_info_data
            or no_data_in_other_files
            or no_past_and_future_discounts_data
        ):
            return pd.DataFrame()
        all_discounts_df = self._get_combined_discounts(raw_dfs)
        all_discounts_df["collision_id"] = None
        all_discounts_df["source_specific_discount_sku_id"] = all_discounts_df[
            "base_sku_id"
        ]
        all_discounts_df["sales_unique_sku_id"] = all_discounts_df["base_sku_id"]

        all_discounts_df = sanitize(pl.DataFrame(all_discounts_df))
        all_discounts_df = (
            deduplicate(all_discounts_df)
            .sort(by=["datetime_from", "datetime_to", "unique_sku_id"])
            .to_pandas()
        )

        return all_discounts_df[
            self.converted_report_cls.model.to_schema().columns.keys()
        ]

    def _get_combined_discounts(self, raw_dfs: dict[str, pd.DataFrame]) -> pd.DataFrame:
        df = pd.concat(
            [
                self._get_facts_and_planned_events_aka_joined_events(raw_dfs),
                self._get_store_opportunities(raw_dfs),
            ],
            ignore_index=True,
        ).drop_duplicates(subset="unique_event_id")

        df["max_discount_percentage"] = df.groupby("base_sku_id")[
            "discount_depth"
        ].transform("max")

        df["price_increase_time"] = df["base_sku_id"].map(
            raw_dfs["priceIncreaseTimes.csv"]
            .set_index("packageId")["priceIncreaseTime"]
            .to_dict()
        )
        df["price_increase_time"] = df["price_increase_time"].fillna(0)
        df["price_increase_time"] = df["price_increase_time"].map(
            datetime.utcfromtimestamp
        )

        df["report_id"] = self._raw_report.metadata.report_id
        df[["create_time", "update_time"]] = self._raw_report.metadata.upload_date

        df["studio_id"] = self._raw_report.metadata.studio_id

        df["platform"] = DisplayPlatform.PC
        df["region"] = Region.GLOBAL
        df["portal"] = DisplayPortal.STEAM

        df["unique_sku_id"] = (
            df["base_sku_id"].astype(str)
            + "-"
            + Portal.STEAM.value
            + ":"
            + df["studio_id"].astype(str)
        )
        df["portal_platform_region"] = generate_portal_platform_region(
            df["portal"], df["platform"], df["region"]
        )
        df["promo_length"] = (df["datetime_to"] - df["datetime_from"]) / 1000000000

        return df

    def _get_facts_and_planned_events_aka_joined_events(
        self, raw_dfs: dict[str, pd.DataFrame]
    ) -> pd.DataFrame:
        def get_trigger_cooldown(df: pd.DataFrame) -> pd.Series:
            return (
                df["collisionType"].map(collision_type_to_triggers_cooldown)
                if df["collisionType"].notnull().all()
                else df["name"].map(name_to_triggers_cooldown)
            )

        def collision_type_to_triggers_cooldown(value: str) -> bool:
            return value != SteamCollisionType.UNIQUE

        def name_to_triggers_cooldown(value: str) -> bool:
            return all(name not in value for name in hardcoded_major_events_names)

        def get_major(df: pd.DataFrame) -> pd.Series:
            return (
                df["collisionType"].map(collision_type_to_major)
                if df["collisionType"].notnull().all()
                else df["name"].map(name_to_major)
            )

        def collision_type_to_major(value: str) -> bool:
            return value == SteamCollisionType.UNIQUE

        def name_to_major(value: str) -> bool:
            return any(name in value for name in hardcoded_major_events_names)

        hardcoded_major_events_names = [
            "Summer Sale",
            "Winter Sale",
            "Autumn Sale",
            "Spring Sale",
        ]

        def generate_event_id(df: pd.DataFrame) -> pd.Series:
            return (
                df["startDate"].map(timestamp_to_datetime_str)
                + ":"
                + df["name"].map(filter_nonalphanumeric)
                + ":"
                + df["productId"].astype(str)
            )

        def group_to_discount_type(group: str):
            return DiscountType.CUSTOM if group == "false" else DiscountType.STORE

        # Merge with discountEvents to get collisionType from which we can get triggers_cooldown and major as only major events has `unique` collisionType

        discount_events = raw_dfs["discountEvents.csv"].rename(columns={"id": "group"})
        df = raw_dfs["discountHistory.csv"].merge(
            discount_events[["group", "collisionType"]], on="group", how="left"
        )

        return enforce_schema(
            pd.DataFrame({
                "event_name": df["name"],
                "datetime_from": df["startDate"].map(datetime.utcfromtimestamp),
                "datetime_to": df["endDate"].map(datetime.utcfromtimestamp),
                "is_event_joined": True,
                "discount_depth": df["percent"],
                "unique_event_id": generate_event_id(df),
                "base_event_id": df["id"].map(self._id_to_base_event_id),
                "group_id": df["group"].map(self._group_to_group_id),
                "base_sku_id": df["productId"],
                "triggers_cooldown": get_trigger_cooldown(df),
                "major": get_major(df),
                "discount_type": df["group"].map(group_to_discount_type),
            }),
            schema=self._intermidiate_facts_and_planned_events_schema,
        )

    def _get_store_opportunities(
        self, raw_dfs: dict[str, pd.DataFrame]
    ) -> pd.DataFrame:
        def collision_type_to_triggers_cooldown(value: str) -> bool:
            return value != SteamCollisionType.UNIQUE

        def collision_type_to_major(value: str) -> bool:
            return value == SteamCollisionType.UNIQUE

        def get_event_name_from_header_or_name(row: pd.Series):
            return row["header"] if pd.notnull(row["header"]) else row["name"]

        dynamic_dfs = [
            raw_dfs[filename] for filename in self._dynamic_required_filenames
        ]
        all_transposed_dfs = [self._transpose_dynamic_columns(df) for df in dynamic_dfs]

        unapproved_events_df = pd.concat(all_transposed_dfs, ignore_index=True).rename(
            columns={
                "Package Name": "package_name",
                "ID": "base_sku_id",
                "Base Price": "base_price",
            }
        )
        unapproved_events_df = self._remove_unavailable_events(unapproved_events_df)

        unapproved_events_df["join_id"] = unapproved_events_df["event_name"].map(
            filter_nonalphanumeric
        )

        df = raw_dfs["discountEvents.csv"]

        discount_events_df = enforce_schema(
            pd.DataFrame({
                "datetime_from": df["startDate"].map(datetime.utcfromtimestamp),
                "datetime_to": df["endDate"].map(datetime.utcfromtimestamp),
                "is_event_joined": False,
                "group_id": df["id"].map(self._group_to_group_id),
                "base_event_id": self._id_to_base_event_id(np.nan),
                "triggers_cooldown": df["collisionType"].map(
                    collision_type_to_triggers_cooldown
                ),
                "major": df["collisionType"].map(collision_type_to_major),
                "join_id": df.apply(get_event_name_from_header_or_name, axis=1).map(
                    filter_nonalphanumeric
                ),
            }),
            schema=self._intermidiate_discount_events_schema,
        )

        unapproved_events_df = unapproved_events_df.merge(
            discount_events_df,
            on="join_id",
            how="inner",
        ).drop_duplicates(ignore_index=True)

        # Because we merged (with inner join) with discountEvents (which only contains store events), we can set discount_type to STORE
        unapproved_events_df["discount_type"] = DiscountType.STORE

        if unapproved_events_df.empty:
            unapproved_events_df["unique_event_id"] = ""
        else:
            unapproved_events_df["unique_event_id"] = unapproved_events_df.apply(
                lambda row: (
                    f"{datetime_to_string(row['datetime_from'])}:{row['join_id']}:{row['base_sku_id']}"
                    if not row.empty
                    else ""
                ),
                axis="columns",
            )

        return enforce_schema(
            unapproved_events_df[
                self._intermidiate_facts_and_planned_events_schema.columns.keys()
            ]
            .sort_values(by=["base_sku_id", "datetime_from"], ascending=[False, False])
            .reset_index(drop=True),
            schema=self._intermidiate_facts_and_planned_events_schema,
        )

    def _remove_unavailable_events(self, df: pd.DataFrame) -> pd.DataFrame:
        df = df.dropna(subset="discount_depth").assign(
            discount_depth=lambda x: x["discount_depth"].astype(int)
        )
        rows_to_drop = df[
            (df["event_name"].str.contains("Weeklong")) & (df["discount_depth"] == 0)
        ]
        return df.drop(rows_to_drop.index)

    def _transpose_dynamic_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        static_headers = list(self._raw_discount_all_csv_schema.columns.keys())

        return df.melt(
            id_vars=static_headers,
            value_vars=tuple(df.columns[len(static_headers) :]),
            var_name="event_name",
            value_name="discount_depth",
        ).sort_values(by=["ID"])

    def _parse(self, raw_file: bytes) -> dict[str, pd.DataFrame]:
        with io.BytesIO(raw_file) as stream:
            zip_file = ZipFile(stream)

            self._dynamic_required_filenames = [
                f"discounts_all_{publisher_id}.csv"
                for publisher_id in self._get_publisher_ids(zip_file)
            ]

            return {
                **extract_validated_dfs(
                    zip_file,
                    self._static_required_filenames,
                    lambda filename: self._raw_csvs_schemas[filename],
                ),
                **extract_validated_dfs(
                    zip_file,
                    self._dynamic_required_filenames,
                    lambda _: self._raw_discount_all_csv_schema,
                ),
            }

    def _get_publisher_ids(self, zip_file: ZipFile) -> list[int]:
        with zip_file.open("userinfo.csv") as stream:
            df = self._get_df_from_streamed_csv(stream)
            return df["publisherId"].unique().tolist()

    def _get_df_from_streamed_csv(self, stream: IO[bytes]) -> pd.DataFrame:
        return pd.read_csv(
            stream,
            sep=",",
            header=0,
            quotechar='"',
            keep_default_na=False,
            na_values=[""],
        )

    def _group_to_group_id(self, group: str):
        return "" if group == "false" else group

    def _id_to_base_event_id(self, _id: float):
        return "" if _id is np.nan else _id
