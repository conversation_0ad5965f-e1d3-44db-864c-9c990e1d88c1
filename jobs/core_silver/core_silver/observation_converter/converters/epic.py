from zipfile import BadZipFile

import numpy as np
import pandas as pd
import pandera as pa

from core_silver.dictionaries.constants import <PERSON><PERSON><PERSON>, Constant, RevenueFactor
from core_silver.dictionaries.currencies import generate_currency_exchange_rates
from core_silver.dictionaries.vat import convert_net_to_gross
from core_silver.observation_converter.common_columns import (
    generate_portal_platform_region,
    generate_unique_sku_id,
)
from core_silver.observation_converter.converters.base_converter import (
    BaseSalesConverter,
    extract_csvs_from_raw_file,
)
from core_silver.observation_converter.converters.exceptions import (
    FileExtractionError,
    FileSchemaError,
)
from core_silver.observation_converter.pandas_utils import InvalidSchema, enforce_schema
from core_silver.utils.math_tools import js_round
from core_silver.utils.string_format import translate_column, unescape_html
from data_sdk.domain import DisplayPortal, Portal, countries
from data_sdk.domain.domain_types import ReportMetadataWithRawFile
from data_sdk.domain.platform import DisplayPlatform
from data_sdk.domain.regions import Region
from data_sdk.domain.stores import Store
from data_sdk.domain.tables import ExternalCurrencyExchangeRatesTable


class EpicConverter(BaseSalesConverter):
    _schema = pa.DataFrameSchema(
        columns={
            "Id": pa.Column(pa.String, coerce=True),
            "Developer": pa.Column(pa.String, coerce=True),
            "Product Id": pa.Column(pa.String, coerce=True),
            "Offer Id": pa.Column(pa.String, coerce=True),
            "Offer Title": pa.Column(pa.String, coerce=True),
            "Country": pa.Column(pa.String, coerce=True),
            "Sale Day": pa.Column(pa.DateTime, coerce=True),
            "Currency Code": pa.Column(pa.String, coerce=True),
            "Net Units": pa.Column(pa.Int, coerce=True),
            "Total Purchase Number": pa.Column(pa.Int, coerce=True),
            "Total Refund Number": pa.Column(pa.Int, coerce=True),
            "Total Chargeback Number": pa.Column(pa.Int, coerce=True),
            "Adjusted Base Revenue(USD)": pa.Column(pa.Float, coerce=True),
            "Total Purchase Amount(USD)": pa.Column(pa.Float, coerce=True),
            "Total Refund Amount(USD)": pa.Column(pa.Float, coerce=True),
            "Total Chargeback Amount(USD)": pa.Column(pa.Float, coerce=True),
        }
    )

    def __init__(
        self,
        raw_report: ReportMetadataWithRawFile,
        external_currency_exchange_rates_table: ExternalCurrencyExchangeRatesTable,
    ):
        self._external_currency_exchange_rates_table = (
            external_currency_exchange_rates_table
        )
        super().__init__(raw_report)

    def _parse(self, raw_file: bytes) -> pd.DataFrame:
        df = self._extract_file(raw_file)
        df.sort_index(ascending=True, inplace=True)
        try:
            return enforce_schema(df, self._schema)
        except InvalidSchema as ex:
            raise FileSchemaError(str(ex))

    def _convert(self) -> pd.DataFrame:
        parsed_df = self._parse(self._raw_report.raw_file)
        manifest = self._raw_report.metadata

        if parsed_df.empty:
            return pd.DataFrame()
        converted_df = pd.DataFrame()

        converted_df = converted_df.assign(
            country_code=parsed_df["Country"].map(countries.get_country_alpha_3),
            currency_code=parsed_df["Currency Code"],
            studio_id=manifest.studio_id,
            sku_id=parsed_df["Offer Id"],
            portal=DisplayPortal.EPIC,
            platform=DisplayPlatform.PC,
            region=Region.GLOBAL.value,
            transaction_type=Constant.UNKNOWN.value,
            payment_instrument=Constant.UNKNOWN.value,
            bundle_name=Constant.NOT_APPLICABLE.value,
            tax_type=Constant.UNKNOWN.value,
            sale_modificator=Constant.NOT_APPLICABLE.value,
            acquisition_platform=Constant.UNKNOWN.value,
            acquisition_origin=Constant.UNKNOWN.value,
            iap_flag=Boolean.FALSE.value,
            date=parsed_df["Sale Day"].map(lambda x: x.strftime("%Y-%m-%d")),
            report_id=manifest.report_id,
            retailer_tag=Constant.NOT_APPLICABLE.value,
            human_name=translate_column(parsed_df["Offer Title"], unescape_html),
            store_id=parsed_df["Product Id"],
            base_price_local=np.nan,
            net_sales=js_round(parsed_df["Adjusted Base Revenue(USD)"], 2),
            gross_returned=0,
            gross_sales=0,
            units_returned=0,
            units_sold=parsed_df["Net Units"],
            free_units=0,
            price_local=0,
            price_usd=0.0,
            net_sales_approx=0.0,
            store=Store.EPIC.value,
            abbreviated_name=Store.EPIC.abbreviate(),
        )
        converted_df["portal_platform_region"] = generate_portal_platform_region(
            converted_df["portal"], converted_df["platform"], converted_df["region"]
        )

        converted_df["units_returned"] = abs(parsed_df["Total Refund Number"]) + abs(
            parsed_df["Total Chargeback Number"]
        )

        converted_df["gross_returned"] = self._compute_gross_returned(
            parsed_df["Total Refund Amount(USD)"],
            parsed_df["Total Chargeback Amount(USD)"],
            countries.get_country_alpha_3(parsed_df["Country"]),
        )
        converted_df["gross_sales"] = convert_net_to_gross(
            converted_df["net_sales"], converted_df["country_code"]
        )

        converted_df.loc[converted_df["units_sold"] != 0, "price_usd"] = js_round(
            converted_df.loc[converted_df["units_sold"] != 0].apply(
                lambda r: self._compute_price_usd(
                    r["gross_sales"],
                    r["gross_returned"],
                    r["units_sold"],
                    r["units_returned"],
                ),
                axis=1,
            ),
            2,
        )

        converted_df["currency_exchange_rate"] = generate_currency_exchange_rates(
            converted_df, self._external_currency_exchange_rates_table
        )

        converted_df["price_local"] = convert_net_to_gross(
            converted_df["price_usd"] * converted_df["currency_exchange_rate"],
            converted_df["country_code"],
        )

        # if price_local is equal to zero we reassign sold units to free units
        converted_df.loc[
            (converted_df["price_local"] == 0) & (converted_df["units_sold"] > 0),
            "free_units",
        ] = converted_df.loc[
            (converted_df["price_local"] == 0) & (converted_df["units_sold"] > 0),
            "units_sold",
        ]
        converted_df.loc[
            (converted_df["price_local"] == 0) & (converted_df["units_sold"] > 0),
            "units_sold",
        ] = 0

        converted_df["unique_sku_id"] = generate_unique_sku_id(
            converted_df, Portal.EPIC
        )

        converted_df["net_sales_approx"] = js_round(
            converted_df["net_sales"] * RevenueFactor.EPIC.value, 2
        )

        return converted_df

    def _compute_gross_returned(self, refund, chargeback, country_code):
        net_value_of_returned_products = -js_round(refund + chargeback, 2)
        return convert_net_to_gross(net_value_of_returned_products, country_code)

    def _compute_price_usd(
        self, gross_sales, gross_returned, units_sold, units_returned
    ):
        return (gross_sales + gross_returned + gross_returned) / (
            units_sold + units_returned + units_returned
        )

    def _extract_file(self, raw_file: bytes) -> pd.DataFrame:
        try:
            df = extract_csvs_from_raw_file(
                raw_file,
                sep=",",
                header=0,
                parse_dates=True,
                quotechar='"',
                engine="python",
                keep_default_na=False,
                na_values=[""],
                dtype={
                    "Id": str,
                    "Developer": str,
                    "Product Id": str,
                    "Offer Id": str,
                    "Offer Title": str,
                    "Country": str,
                    "Sale Day": str,
                    "Currency Code": str,
                    "Net Units": np.int64,
                    "Total Purchase Number": np.int64,
                    "Total Refund Number": np.int64,
                    "Total Chargeback Number": np.int64,
                    "Adjusted Base Revenue(USD)": np.float64,
                    "Total Purchase Amount(USD)": np.float64,
                    "Total Refund Amount(USD)": np.float64,
                    "Total Chargeback Amount(USD)": np.float64,
                },
            )
        except (BadZipFile, ValueError) as ex:
            raise FileExtractionError(str(ex))
        return df
