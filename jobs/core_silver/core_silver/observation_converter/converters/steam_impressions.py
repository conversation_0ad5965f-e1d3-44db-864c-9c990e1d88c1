import hashlib
import io
from zipfile import BadZipFile, ZipFile

import numpy as np
import pandas as pd
import pandera as pa

from core_silver.observation_converter.common_columns import (
    generate_portal_platform_region,
)
from core_silver.observation_converter.converters.base_converter import (
    BaseVisibilityConverter,
)
from core_silver.observation_converter.converters.exceptions import (
    AdditionalDataFileNotFound,
    FileExtractionError,
    FileSchemaError,
    InvalidAdditionalData,
)
from core_silver.observation_converter.pandas_utils import InvalidSchema, enforce_schema
from core_silver.observation_converter.zip import (
    get_additional_data_from_zip,
    get_reports_from_zip,
)
from core_silver.utils.string_format import translate_column, unescape_html
from data_sdk.domain import DisplayPortal, Navigation
from data_sdk.domain.platform import DisplayPlatform
from data_sdk.domain.regions import Region
from data_sdk.domain.stores import Store


class SteamImpressionsConverter(BaseVisibilityConverter):
    _schema = pa.DataFrameSchema({
        "Page / Category": pa.Column(pa.String, coerce=True),
        "Page / Feature": pa.Column(pa.String, coerce=True),
        "Impressions": pa.Column(pa.Int, coerce=True),
        "Visits": pa.Column(pa.Int, coerce=True),
        "Owner Impressions": pa.Column(pa.Int, coerce=True, required=False),
        "Owner Visits": pa.Column(pa.Int, coerce=True, required=False),
        "Product ID": pa.Column(pa.Int, coerce=True),
        "Date": pa.Column(pa.DateTime, coerce=True),
        "Product Name": pa.Column(pa.String, coerce=True),
    })

    def _parse(self, raw_file: bytes) -> pd.DataFrame:
        try:
            with io.BytesIO(raw_file) as stream, ZipFile(stream) as zip_file:
                reports = get_reports_from_zip(zip_file, ".csv")
                additional_data = get_additional_data_from_zip(zip_file)
                parsed_df = (
                    pd.concat([
                        _extract_additional_data(
                            pd.read_csv(
                                data,
                                sep=",",
                                header=0,
                                dtype={
                                    "Page / Category": str,
                                    "Page / Feature": str,
                                    "Impressions": np.int64,
                                    "Visits": np.int64,
                                    "Owner Impressions": np.int64,
                                    "Owner Visits": np.int64,
                                    "Product ID": np.int64,
                                    "Date": str,
                                    "Product Name": str,
                                },
                            ),
                            name,
                            additional_data,
                        )
                        for name, data in reports
                    ])
                    .fillna(0)
                    .reset_index()
                )
        except (ValueError, BadZipFile) as ex:
            raise FileExtractionError(str(ex)) from ex

        try:
            return enforce_schema(parsed_df, self._schema)
        except InvalidSchema as ex:
            raise FileSchemaError(str(ex))

    def _convert(self) -> pd.DataFrame:
        parsed_df = self._parse(self._raw_report.raw_file)
        manifest = self._raw_report.metadata

        if parsed_df.empty:
            return pd.DataFrame()

        parsed_df = parsed_df[parsed_df["Page / Category"] != "Country"]
        if parsed_df.empty:
            return pd.DataFrame()

        converted_df = parsed_df.rename(
            columns={
                "Page / Category": "page_category",
                "Page / Feature": "page_feature",
                "Impressions": "impressions",
                "Visits": "visits",
                "Owner Impressions": "owner_impressions",
                "Owner Visits": "owner_visits",
                "Product ID": "sku_id",
                "Date": "date",
                "Product Name": "human_name",
            }
        )
        converted_df = converted_df.assign(
            human_name=translate_column(converted_df["human_name"], unescape_html),
            studio_id=manifest.studio_id,
            date=converted_df["date"].map(lambda x: x.strftime("%Y-%m-%d")),
            report_id=manifest.report_id,
            portal=DisplayPortal.STEAM.value,
            platform=DisplayPlatform.PC.value,
            region=Region.GLOBAL.value,
            store_id=converted_df["sku_id"].astype(str),
            hash_traffic_source=converted_df.apply(
                lambda row: hashlib.md5(  # noqa: S324
                    "".join([row["page_category"], row["page_feature"]]).encode("utf-8")
                ).hexdigest(),
                axis=1,
            ),
            store=Store.STEAM.value,
            abbreviated_name=Store.STEAM.abbreviate(),
        ).reset_index()
        converted_df["navigation"] = Navigation.IMPRESSIONS
        converted_df.loc[
            (converted_df["impressions"] == 0) & (converted_df["visits"] > 0),
            "navigation",
        ] = Navigation.DIRECT
        if "owner_impressions" not in converted_df.columns:
            converted_df["owner_impressions"] = 0
        if "owner_visits" not in converted_df.columns:
            converted_df["owner_visits"] = 0
        converted_df["sku_id"] = converted_df["sku_id"].astype(str)
        converted_df["unique_sku_id"] = (
            converted_df["sku_id"].astype(str)
            + "-store:"
            + converted_df["studio_id"].astype(str)
        )
        converted_df["portal_platform_region"] = generate_portal_platform_region(
            converted_df["portal"], converted_df["platform"], converted_df["region"]
        )

        return converted_df


def _extract_additional_data(df, file_name, additional_data) -> pd.DataFrame:
    if df.empty:
        return df
    if not additional_data:
        raise AdditionalDataFileNotFound
    try:
        df["Product ID"] = int(
            additional_data.get("fileMetaData").get(file_name)["productId"]
        )
        df["Date"] = additional_data.get("fileMetaData").get(file_name)["date"][:-1]
        df["Product Name"] = additional_data.get("fileMetaData").get(file_name)[
            "productName"
        ]
    except (AttributeError, TypeError, KeyError):
        raise InvalidAdditionalData

    return df
