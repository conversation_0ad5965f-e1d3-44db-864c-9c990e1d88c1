from core_silver.observation_converter.converters.base_monthly_active_users_converter import (
    BaseMonthlyActiveUsersConverter,
)
from data_sdk.domain import DisplayPortal, Portal
from data_sdk.domain.stores import Store


class MicrosoftMonthlyActiveUsersConverter(BaseMonthlyActiveUsersConverter):
    PORTAL = Portal.MICROSOFT
    DISPLAY_PORTAL = DisplayPortal.MICROSOFT
    STORE = Store.MICROSOFT
