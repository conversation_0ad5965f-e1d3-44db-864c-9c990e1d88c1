import pandas as pd
import pandera as pa

from core_silver.observation_converter.converters.base_daily_active_users_converter import (
    BaseDailyActiveUsersConverter,
)
from core_silver.observation_converter.converters.meta_utils import (
    assign_common_meta_fields,
)
from core_silver.utils.string_format import translate_column
from data_sdk.domain import DisplayPortal
from data_sdk.domain.countries import get_country_alpha_3
from data_sdk.domain.domain_types import ReportMetadata
from data_sdk.domain.platform import get_display_platform_from_platform
from data_sdk.domain.regions import Region


class MetaDailyActiveUsersConverter(BaseDailyActiveUsersConverter):
    _schema = pa.DataFrameSchema({
        **BaseDailyActiveUsersConverter._schema.columns,
        "platform": pa.Column(pa.String, coerce=True),
    })

    def _convert(self) -> pd.DataFrame:
        manifest: ReportMetadata = self._raw_report.metadata
        parsed_df = self.parse(self._raw_report.raw_file)

        if parsed_df.empty:
            return pd.DataFrame()

        converted_df = pd.DataFrame()
        converted_df = self._build_converted_dataframe(parsed_df, manifest)
        converted_df = assign_common_meta_fields(converted_df)

        return converted_df

    def _get_common_columns(
        self, parsed_df: pd.DataFrame, metadata: ReportMetadata
    ) -> dict:
        return {
            "sku_id": parsed_df["product"].replace({r"\s": "_", r"\W": ""}, regex=True),
            "human_name": parsed_df["product"],
            "store_id": parsed_df["product_id"],
            "platform": translate_column(
                parsed_df["platform"], get_display_platform_from_platform
            ),
            "region": Region.GLOBAL.value,
            "portal": DisplayPortal.META.value,
            "country_code": get_country_alpha_3("unknown"),
            "report_id": metadata.report_id,
            "studio_id": metadata.studio_id,
        }
