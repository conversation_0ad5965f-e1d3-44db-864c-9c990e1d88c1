from typing import Callable, Type<PERSON><PERSON>s, Union

from core_silver.observation_converter.converters.app_store import AppStoreConverter
from core_silver.observation_converter.converters.base_converter import BaseConverter
from core_silver.observation_converter.converters.epic import EpicConverter
from core_silver.observation_converter.converters.exceptions import ConverterNotFound
from core_silver.observation_converter.converters.gog import GOGConverter
from core_silver.observation_converter.converters.google import GoogleConverter
from core_silver.observation_converter.converters.humble import HumbleConverter
from core_silver.observation_converter.converters.meta import MetaConverter
from core_silver.observation_converter.converters.meta_daily_active_users import (
    MetaDailyActiveUsersConverter,
)
from core_silver.observation_converter.converters.meta_monthly_active_users import (
    MetaMonthlyActiveUsersConverter,
)
from core_silver.observation_converter.converters.microsoft import MicrosoftConverter
from core_silver.observation_converter.converters.microsoft_daily_active_users import (
    MicrosoftDailyActiveUsersConverter,
)
from core_silver.observation_converter.converters.microsoft_monthly_active_users import (
    MicrosoftMonthlyActiveUsersConverter,
)
from core_silver.observation_converter.converters.nintendo_cumulative_wishlist_sales import (
    NintendoCumulativeWishlistSalesConverter,
)
from core_silver.observation_converter.converters.nintendo_discounts import (
    NintendoDiscountsConverter,
)
from core_silver.observation_converter.converters.nintendo_sales import (
    NintendoSalesConverter,
)
from core_silver.observation_converter.converters.nintendo_wishlist_actions import (
    NintendoWishlistActionsConverter,
)
from core_silver.observation_converter.converters.playstation import (
    PlaystationConverter,
)
from core_silver.observation_converter.converters.playstation_daily_active_users import (
    PlaystationDailyActiveUsersConverter,
)
from core_silver.observation_converter.converters.playstation_monthly_active_users import (
    PlaystationMonthlyActiveUsersConverter,
)
from core_silver.observation_converter.converters.ps_wishlist_actions import (
    PsWishlistActionsConverter,
)
from core_silver.observation_converter.converters.steam_daily_active_users import (
    SteamDailyActiveUsersConverter,
)
from core_silver.observation_converter.converters.steam_discounts import (
    SteamDiscountsConverter,
)
from core_silver.observation_converter.converters.steam_impressions import (
    SteamImpressionsConverter,
)
from core_silver.observation_converter.converters.steam_sales import SteamSalesConverter
from core_silver.observation_converter.converters.steam_wishlist_actions import (
    SteamWishlistActionsConverter,
)
from core_silver.observation_converter.converters.steam_wishlist_balance import (
    SteamWishlistBalanceConverter,
)
from core_silver.observation_converter.converters.steam_wishlist_cohorts import (
    SteamWishlistCohortsConverter,
)
from data_sdk.domain.domain_types import ReportMetadataWithRawFile
from data_sdk.domain.source import Source
from data_sdk.domain.tables import ExternalCurrencyExchangeRatesTable

ConverterType: TypeAlias = Union[
    Callable[[ReportMetadataWithRawFile, ...], BaseConverter],  # type: ignore[ReportInvalidTypeForm]
    Callable[
        [ReportMetadataWithRawFile, ExternalCurrencyExchangeRatesTable], BaseConverter
    ],  # TODO: make it better, so we would not have to add a new type for each converter
]

source_to_converter: dict[Source, ConverterType] = {
    Source.APP_STORE_SALES: AppStoreConverter,
    Source.EPIC_SALES: EpicConverter,
    Source.GOG_SALES: GOGConverter,
    Source.GOOGLE_SALES: GoogleConverter,
    Source.HUMBLE_SALES: HumbleConverter,
    Source.META_QUEST_SALES: MetaConverter,
    Source.META_RIFT_SALES: MetaConverter,
    Source.META_DAILY_ACTIVE_USERS: MetaDailyActiveUsersConverter,
    Source.META_MONTHLY_ACTIVE_USERS: MetaMonthlyActiveUsersConverter,
    Source.MICROSOFT_DAILY_ACTIVE_USERS: MicrosoftDailyActiveUsersConverter,
    Source.MICROSOFT_MONTHLY_ACTIVE_USERS: MicrosoftMonthlyActiveUsersConverter,
    Source.MICROSOFT_SALES: MicrosoftConverter,
    Source.NINTENDO_CUMULATIVE_WISHLIST_SALES: NintendoCumulativeWishlistSalesConverter,
    Source.NINTENDO_DISCOUNTS: NintendoDiscountsConverter,
    Source.NINTENDO_SALES: NintendoSalesConverter,
    Source.NINTENDO_WISHLIST_ACTIONS: NintendoWishlistActionsConverter,
    Source.PLAYSTATION_SALES: PlaystationConverter,
    Source.PLAYSTATION_WISHLIST_ACTIONS: PsWishlistActionsConverter,
    Source.PLAYSTATION_DAILY_ACTIVE_USERS: PlaystationDailyActiveUsersConverter,
    Source.PLAYSTATION_MONTHLY_ACTIVE_USERS: PlaystationMonthlyActiveUsersConverter,
    Source.STEAM_DAILY_ACTIVE_USERS: SteamDailyActiveUsersConverter,
    Source.STEAM_DISCOUNTS: SteamDiscountsConverter,
    Source.STEAM_IMPRESSIONS: SteamImpressionsConverter,
    Source.STEAM_SALES: SteamSalesConverter,
    Source.STEAM_WISHLIST_ACTIONS: SteamWishlistActionsConverter,
    Source.STEAM_WISHLIST_BALANCE: SteamWishlistBalanceConverter,
    Source.STEAM_WISHLIST_COHORTS: SteamWishlistCohortsConverter,
}


def get_converter_class_using_source(source: Source) -> ConverterType:
    try:
        return source_to_converter[source]
    except KeyError:
        raise ConverterNotFound(source)
