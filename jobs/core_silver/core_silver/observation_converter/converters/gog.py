import io
from typing import Callable
from zipfile import BadZipFile, ZipFile

import numpy as np
import pandas as pd
import pandera as pa

from core_silver.dictionaries.constants import <PERSON><PERSON><PERSON>, Constant, RevenueFactor
from core_silver.observation_converter.common_columns import (
    generate_portal_platform_region,
    generate_unique_sku_id,
)
from core_silver.observation_converter.converters.base_converter import (
    BaseSalesConverter,
    extract_csvs_from_raw_file,
    extract_json_from_raw_zip_file,
    get_manifest_data_from_raw_file,
)
from core_silver.observation_converter.converters.exceptions import (
    FileExtractionError,
    FileMissingError,
    FileSchemaError,
    InvalidManifest,
)
from core_silver.observation_converter.pandas_utils import InvalidSchema, enforce_schema
from core_silver.observation_converter.zip import (
    MANIFEST_NAME,
    get_report_names,
    is_zip,
)
from core_silver.utils.math_tools import js_round
from core_silver.utils.string_format import (
    filter_nonalphanumeric,
    translate_column,
    unescape_html,
)
from data_sdk.domain import DisplayPortal, Portal, countries
from data_sdk.domain.platform import DisplayPlatform
from data_sdk.domain.regions import Region
from data_sdk.domain.stores import Store


class GOGConverter(BaseSalesConverter):
    _schema = pa.DataFrameSchema(
        columns={
            "Date": pa.Column(pa.String, coerce=True),
            "SRP Revenue": pa.Column(pa.Float, coerce=True),
            "Promo Deductions": pa.Column(pa.Float, coerce=True),
            "Gross Revenue": pa.Column(pa.Float, coerce=True),
            "Gross Share": pa.Column(pa.Float, coerce=True),
            "VAT": pa.Column(pa.Float, coerce=True),
            "Net Sales": pa.Column(pa.Float, coerce=True),
            "Net Share": pa.Column(pa.Float, coerce=True),
            "Affiliates": pa.Column(pa.Float, coerce=True),
            "Free Units": pa.Column(pa.Int, coerce=True),
            "Free Units Returned": pa.Column(pa.Int, coerce=True),
            "Paid Units": pa.Column(pa.Int, coerce=True),
            "Paid Units Returned": pa.Column(pa.Int, coerce=True),
            "Units": pa.Column(pa.Int, coerce=True),
            "Units Share": pa.Column(pa.Float, coerce=True),
        }
    )

    def _parse(self, raw_file: bytes) -> pd.DataFrame:
        df = self._extract_zip_file(raw_file)
        df.sort_index(ascending=True, inplace=True)
        try:
            return enforce_schema(df, self._schema)
        except InvalidSchema as ex:
            raise FileSchemaError(str(ex))

    def _convert(self) -> pd.DataFrame:
        parsed_df = self._parse(self._raw_report.raw_file)
        manifest = self._raw_report.metadata

        if parsed_df.empty:
            return pd.DataFrame()

        converted_df = pd.DataFrame()

        converted_df = converted_df.assign(
            date=parsed_df["Date"].str[:10],
            human_name=translate_column(parsed_df["__product_name"], unescape_html),
            country_code=countries.get_country_alpha_3("unknown"),
            currency_code="USD",
            studio_id=manifest.studio_id,
            sku_id=parsed_df["__sku_id"].map(filter_nonalphanumeric),
            portal=DisplayPortal.GOG,
            platform=DisplayPlatform.PC,
            region=Region.GLOBAL.value,
            transaction_type=Constant.UNKNOWN.value,
            payment_instrument=Constant.UNKNOWN.value,
            bundle_name=Constant.NOT_APPLICABLE.value,
            tax_type=Constant.UNKNOWN.value,
            sale_modificator=Constant.NOT_APPLICABLE.value,
            acquisition_platform=Constant.UNKNOWN.value,
            acquisition_origin=Constant.UNKNOWN.value,
            iap_flag=Boolean.FALSE.value,
            report_id=manifest.report_id,
            retailer_tag=Constant.NOT_APPLICABLE.value,
            store_id=Constant.UNKNOWN.value,
            base_price_local=np.nan,
            net_sales=js_round(parsed_df["Net Sales"], 2),
            gross_returned=0,
            gross_sales=js_round(parsed_df["Gross Revenue"], 2),
            units_returned=-parsed_df["Paid Units Returned"],
            units_sold=parsed_df["Paid Units"] + parsed_df["Paid Units Returned"],
            free_units=parsed_df["Free Units"] + parsed_df["Free Units Returned"],
            price_local=0,
            price_usd=0.0,
            net_sales_approx=0.0,
            store=Store.GOG.value,
            abbreviated_name=Store.GOG.abbreviate(),
        )
        converted_df.loc[
            converted_df["gross_sales"] < 0, "gross_returned"
        ] = -converted_df.loc[converted_df["gross_sales"] < 0, "gross_sales"]

        converted_df.loc[converted_df["units_sold"] != 0, "price_usd"] = js_round(
            converted_df.loc[converted_df["units_sold"] != 0, "gross_sales"]
            / converted_df.loc[converted_df["units_sold"] != 0, "units_sold"],
            2,
        )

        converted_df["price_local"] = converted_df["price_usd"]

        converted_df["unique_sku_id"] = generate_unique_sku_id(converted_df, Portal.GOG)

        converted_df["net_sales_approx"] = js_round(
            converted_df["net_sales"] * RevenueFactor.STD.value, 2
        )
        converted_df["portal_platform_region"] = generate_portal_platform_region(
            converted_df["portal"], converted_df["platform"], converted_df["region"]
        )

        return _filter_blank_records(converted_df)

    def _extract_zip_file(self, raw_file: bytes) -> pd.DataFrame:
        try:
            manifest = get_manifest_data_from_raw_file(raw_file)
            file_name_product_map = _build_file_name_product_map(manifest)
            _validate_manifest_data(raw_file, manifest, file_name_product_map)
            with io.BytesIO(raw_file) as stream:
                if is_zip(raw_file):
                    zip_file = ZipFile(stream)
                    report_names = get_report_names(zip_file)
                    if _is_manifest_v3(manifest):
                        # zip with CSVs v3 (metabase)
                        return self._load_v3_csvs_to_df(raw_file, file_name_product_map)
                    if all(name.endswith(".json") for name in report_names):
                        # zip with JSONs
                        return self._load_jsons_to_df(raw_file, file_name_product_map)
                    # zip with CSVs
                    return self._load_csvs_to_df(raw_file, file_name_product_map)
                else:
                    return pd.DataFrame()
        except BadZipFile as ex:
            raise FileExtractionError(str(ex))

    def _load_csvs_to_df(
        self, raw_file: bytes, file_name_product_map: dict
    ) -> pd.DataFrame:
        try:
            df = extract_csvs_from_raw_file(
                raw_file,
                df_transform=_assign_file_name_to_df(file_name_product_map),
                sep=";",
                header=0,
                skipfooter=1,
                quotechar='"',
                keep_default_na=False,
                na_values=[""],
                parse_dates=True,
                engine="python",
                dtype={
                    "Date": str,
                    "SRP Revenue": np.float64,
                    "Promo Deductions": np.float64,
                    "Gross Revenue": np.float64,
                    "Gross Share": np.float64,
                    "VAT": np.float64,
                    "Net Sales": np.float64,
                    "Net Share": np.float64,
                    "Affiliates": np.float64,
                    "Free Units": np.int64,
                    "Free Units Returned": np.int64,
                    "Paid Units": np.int64,
                    "Paid Units Returned": np.int64,
                    "Units": np.int64,
                    "Units Share": np.float64,
                },
            )
        except ValueError as ex:
            raise FileExtractionError(str(ex))
        return df

    def _load_jsons_to_df(self, raw_file: bytes, file_name_product_map) -> pd.DataFrame:
        _json_schema = pa.DataFrameSchema(
            columns={
                "key": pa.Column(pa.String, coerce=True),
                "key_as_string": pa.Column(pa.DateTime, coerce=True),
                "srp_value": pa.Column(pa.Float, coerce=True),
                "md_value": pa.Column(pa.Float, coerce=True),
                "gross_value": pa.Column(pa.Float, coerce=True),
                "gross_share": pa.Column(pa.Float, coerce=True),
                "vat_value": pa.Column(pa.Float, coerce=True),
                "net_value": pa.Column(pa.Float, coerce=True),
                "net_share": pa.Column(pa.Float, coerce=True),
                "affiliate_share_value": pa.Column(pa.Float, coerce=True),
                "units": pa.Column(pa.Int, coerce=True),
                "units_share": pa.Column(pa.Float, coerce=True),
                "free_units": pa.Column(pa.Int, coerce=True),
                "returned_free_units": pa.Column(pa.Int, coerce=True),
                "paid_units": pa.Column(pa.Int, coerce=True),
                "returned_paid_units": pa.Column(pa.Int, coerce=True),
                "for_verification": pa.Column(pa.Int, coerce=True),
            }
        )
        try:
            df = extract_json_from_raw_zip_file(
                raw_file, df_transform=_assign_file_name_to_df(file_name_product_map)
            )
            if df.empty:
                return pd.DataFrame()
            try:
                df = enforce_schema(df, _json_schema)
            except InvalidSchema as ex:
                raise FileSchemaError(str(ex))
            df = df.drop(columns=["key", "for_verification"], axis=1)
            df.rename(
                columns={
                    "key_as_string": "Date",
                    "srp_value": "SRP Revenue",
                    "md_value": "Promo Deductions",
                    "gross_value": "Gross Revenue",
                    "gross_share": "Gross Share",
                    "vat_value": "VAT",
                    "net_value": "Net Sales",
                    "net_share": "Net Share",
                    "affiliate_share_value": "Affiliates",
                    "units": "Units",
                    "units_share": "Units Share",
                    "free_units": "Free Units",
                    "returned_free_units": "Free Units Returned",
                    "paid_units": "Paid Units",
                    "returned_paid_units": "Paid Units Returned",
                },
                inplace=True,
            )
        except BadZipFile as ex:
            raise FileExtractionError(str(ex))
        return df.reset_index()

    def _load_v3_csvs_to_df(
        self, raw_file: bytes, file_name_products_map: dict
    ) -> pd.DataFrame:
        try:
            df = extract_csvs_from_raw_file(
                raw_file,
                df_transform=_assign_file_name_to_df(file_name_products_map),
                sep=";",
                header=0,
                skipfooter=0,
                quotechar='"',
                keep_default_na=False,
                na_values=[""],
                parse_dates=True,
                engine="python",
            )
            df = df.rename(
                columns={
                    "date": "Date",
                    "SRP_Revenue": "SRP Revenue",
                    "Promo_Deductions": "Promo Deductions",
                    "Gross_Revenue": "Gross Revenue",
                    "Gross_Share": "Gross Share",
                    "Net_Sales": "Net Sales",
                    "Net_Share": "Net Share",
                    "Units_Share": "Units Share",
                    "Free_Units": "Free Units",
                    "Paid_Units": "Paid Units",
                    "Paid_Units_Returned": "Paid Units Returned",
                }
            )
            df["Free Units Returned"] = (
                0  # v3 does not contain this columns so we assume it is 0
            )
            df = df.fillna({
                "Units": 0,
                "Gross Share": 0,
                "Net Share": 0,
                "Units Share": 0,
                "Free Units": 0,
                "Paid Units": 0,
                "Paid Units Returned": 0,
            })
        except ValueError as ex:
            raise FileExtractionError(str(ex))
        return df


def _validate_manifest_data(
    raw_file: bytes, manifest: dict, file_name_product_map: dict
):
    zip_file = ZipFile(io.BytesIO(raw_file))
    zip_content_filenams = zip_file.namelist()

    # v3 manifest contains unused, raw json files (just for debugging purposes) so we only check if all specified files are present in zip
    if _is_manifest_v3(manifest) and not all(
        file_name in zip_content_filenams for file_name in file_name_product_map
    ):
        raise FileMissingError(
            message=f"Files listed in manifest not found in zip. "
            f"Manifest: {' '.join(file_name_product_map.keys())}, "
            f"actual: {' '.join([n for n in zip_content_filenams if n != MANIFEST_NAME])}"
        )

    if not _is_manifest_v3(manifest) and len(zip_content_filenams) - 1 != len(
        file_name_product_map
    ):
        raise InvalidManifest(
            message=f"Mismatch in files listed in manifest. "
            f"Manifest: {' '.join(file_name_product_map.keys())}, "
            f"actual: {' '.join([n for n in zip_content_filenams if n != MANIFEST_NAME])}"
        )


def _is_manifest_v3(manifest: dict) -> bool:
    return "version" in manifest and manifest["version"] == "v3"


def _build_file_name_product_map(manifest) -> dict:
    if _is_manifest_v3(manifest):
        file_name_product_map = {
            file_name: manifest["metadata"][file_name]["product"]
            for file_name in manifest["metadata"]
            if not manifest["metadata"][file_name]["rawData"]
        }
    elif "fileMetaData" in manifest["metadata"]:
        file_name_product_map = {
            file_name: {
                "human_name": manifest["metadata"]["fileMetaData"][file_name][
                    "humanName"
                ],
                "sku_id": manifest["metadata"]["fileMetaData"][file_name]["skuId"],
            }
            for file_name in manifest["metadata"]["fileMetaData"]
        }
    else:
        file_name_product_map = {
            file_name: product_name
            for product_name in manifest["metadata"]["productNames"]
            for file_name in manifest["metadata"]["productNames"][product_name]
        }
    return file_name_product_map


def _assign_file_name_to_df(
    file_name_product_map: dict,
) -> Callable[[pd.DataFrame, str], pd.DataFrame]:
    def _assign_file_name(data_frame: pd.DataFrame, file_name: str) -> pd.DataFrame:
        data_frame["__product_name"] = (
            file_name_product_map[file_name]["human_name"]
            if "human_name" in file_name_product_map[file_name]
            else file_name_product_map[file_name]
        )
        data_frame["__sku_id"] = (
            file_name_product_map[file_name]["sku_id"]
            if "sku_id" in file_name_product_map[file_name]
            else file_name_product_map[file_name]
        )
        data_frame["__file_name"] = file_name
        return data_frame

    return _assign_file_name


def _filter_blank_records(df: pd.DataFrame) -> pd.DataFrame:
    """Filter out blank records from the data frame.
    A record is considered blank when ALL of the follwing
    columns values are equal to 0:

    - gross_sales,
    - units_sold,
    - units_returned,
    - free_units.

    Args:
        df: data frame to filter

    Returns:
        filtered data frame

    """
    return df.drop(
        df[
            (df["gross_sales"] == 0)
            & (df["units_sold"] == 0)
            & (df["units_returned"] == 0)
            & (df["free_units"] == 0)
        ].index
    )
