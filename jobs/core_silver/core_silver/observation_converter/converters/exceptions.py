from core_silver.observation_converter.exceptions import ProcessorError


class ConverterError(ProcessorError):
    pass


class FileSchemaError(ConverterError):
    """Errors caused by invalid CSV structure, like missing columns, wrongs delimiter,
    schema error etc..
    """


class InvalidManifest(ConverterError):
    pass


class FileMissingError(ConverterError):
    pass


class MetaFileNameWithoutPlatform(ConverterError):
    pass


class PSNonMigratedRegionError(ConverterError):
    def __init__(self):
        super().__init__("PS report contains not migrated region")


class FileExtractionError(ConverterError):
    """Errors caused invalid zip archives and csv files. Thrown in situations when
    the file with raw report is garbage and can't even be opened.
    """


class AdditionalDataFileNotFound(FileNotFoundError):
    def __init__(self):
        super().__init__("Additional Data file not found")


class InvalidAdditionalData(ValueError):
    def __init__(self):
        super().__init__("Invalid additional data")


class ManifestFileNotFound(FileNotFoundError):
    def __init__(self):
        super().__init__("Manifest file not found")


class ConverterNotFound(ConverterError):
    def __init__(self, source):
        super().__init__(f"Converter for source {source} not found")


class InvalidObservationType(ValueError):
    def __init__(self, observation_type: str) -> None:
        super().__init__(f"Invalid observation type: {observation_type}")
