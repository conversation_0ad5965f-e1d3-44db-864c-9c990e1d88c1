from zipfile import BadZipFile

import numpy as np
import pandas as pd
import pandera as pa
from pandera import Column

from core_silver.dictionaries import currencies
from core_silver.dictionaries.constants import <PERSON><PERSON><PERSON>, Constant, Origin, RevenueFactor
from core_silver.observation_converter.common_columns import (
    generate_portal_platform_region,
)
from core_silver.observation_converter.converters.base_converter import (
    BaseSalesConverter,
    extract_csvs_from_raw_file,
)
from core_silver.observation_converter.converters.exceptions import (
    FileExtractionError,
    FileSchemaError,
)
from core_silver.observation_converter.pandas_utils import InvalidSchema, enforce_schema
from core_silver.utils.math_tools import js_round
from core_silver.utils.string_format import translate_column, unescape_html
from data_sdk.domain import DisplayPortal, Portal, countries
from data_sdk.domain.domain_types import ReportMetadataWithRawFile
from data_sdk.domain.platform import DisplayPlatform
from data_sdk.domain.regions import Region
from data_sdk.domain.stores import Store
from data_sdk.domain.tables import ExternalCurrencyExchangeRatesTable


class GoogleConverter(BaseSalesConverter):
    _schema = pa.DataFrameSchema(
        columns={
            "Order Number": Column(pa.String, coerce=True),
            "Order Charged Date": Column(pa.DateTime, coerce=True),
            "Order Charged Timestamp": Column(pa.Int, coerce=True),
            "Financial Status": Column(pa.String, coerce=True),
            "Device Model": Column(pa.String, coerce=True, nullable=True),
            "Product Title": Column(pa.String, coerce=True),
            "Product ID": Column(pa.String, coerce=True),
            "Product Type": Column(pa.String, coerce=True),
            "SKU ID": Column(pa.String, coerce=True, nullable=True),
            "Currency of Sale": Column(pa.String, coerce=True),
            "Item Price": Column(pa.Float, coerce=True),
            "Taxes Collected": Column(pa.Float, coerce=True),
            "Charged Amount": Column(pa.Float, coerce=True),
            "City of Buyer": Column(pa.String, coerce=True, nullable=True),
            "State of Buyer": Column(pa.String, coerce=True, nullable=True),
            "Postal Code of Buyer": Column(pa.String, coerce=True, nullable=True),
            "Country of Buyer": Column(pa.String, coerce=True),
            "Base Plan ID": Column(
                pa.String, coerce=True, required=False, nullable=True
            ),
            "Offer ID": Column(pa.String, coerce=True, required=False, nullable=True),
            "Group ID": Column(pa.String, coerce=True, required=False, nullable=True),
            "First USD 1M Eligible": Column(
                pa.String, coerce=True, required=False, nullable=True
            ),
            "Promotion ID": Column(
                pa.String, coerce=True, required=False, nullable=True
            ),
            "Coupon Value": Column(
                pa.String, coerce=True, required=False, nullable=True
            ),
            "Discount Rate": Column(
                pa.String, coerce=True, required=False, nullable=True
            ),
        }
    )

    def __init__(
        self,
        raw_report: ReportMetadataWithRawFile,
        external_currency_exchange_rates_table: ExternalCurrencyExchangeRatesTable,
    ):
        self._external_currency_exchange_rates_table = (
            external_currency_exchange_rates_table
        )
        super().__init__(raw_report)

    def _parse(self, raw_file: bytes) -> pd.DataFrame:
        df = self._extract_file(raw_file)
        df.sort_index(ascending=True, inplace=True)
        try:
            return enforce_schema(df, self._schema)
        except InvalidSchema as ex:
            raise FileSchemaError(str(ex))

    def _convert(self) -> pd.DataFrame:
        parsed_df = self._parse(self._raw_report.raw_file)
        manifest = self._raw_report.metadata

        if parsed_df.empty:
            return pd.DataFrame()

        parsed_df = (
            parsed_df.groupby(
                by=[
                    "Order Charged Date",
                    "Financial Status",
                    "Product Title",
                    "Product ID",
                    "Product Type",
                    "SKU ID",
                    "Currency of Sale",
                    "Item Price",
                    "Taxes Collected",
                    "Charged Amount",
                    "Country of Buyer",
                ],
                as_index=False,
                dropna=False,
            )
            .count()
            .rename(columns={"Order Number": "Units"})
        )
        converted_df = pd.DataFrame()
        converted_df = converted_df.assign(
            country_code=parsed_df["Country of Buyer"].map(
                countries.get_country_alpha_3
            ),
            currency_code=parsed_df["Currency of Sale"],
            sku_id=parsed_df["SKU ID"],
            bundle_name=Constant.NOT_APPLICABLE.value,
            portal=DisplayPortal.GOOGLE,
            platform=DisplayPlatform.GOOGLE,
            transaction_type=Constant.UNKNOWN.value,
            payment_instrument=Constant.UNKNOWN.value,
            tax_type=Constant.UNKNOWN.value,
            sale_modificator=Constant.NOT_APPLICABLE.value,
            acquisition_platform=Constant.UNKNOWN.value,
            iap_flag=Boolean.FALSE.value,
            date=parsed_df["Order Charged Date"].map(lambda x: x.strftime("%Y-%m-%d")),
            retailer_tag=Constant.NOT_APPLICABLE.value,
            human_name=translate_column(parsed_df["Product Title"], unescape_html),
            store_id=Constant.UNKNOWN.value,
            region=Region.GLOBAL.value,
            studio_id=manifest.studio_id,
            price_local=js_round(parsed_df["Item Price"], 2),
            base_price_local=np.nan,
            report_id=manifest.report_id,
            acquisition_origin=Origin.MAIN_STORE.value,
            units_sold=parsed_df["Units"],
            free_units=0,
            units_returned=0,
            price_usd=0.0,
            net_sales=0.0,
            net_sales_approx=0.0,
            gross_returned=0.0,
            store=Store.GOOGLE.value,
            abbreviated_name=Store.GOOGLE.abbreviate(),
        )

        # Calculate Returns
        converted_df.loc[
            parsed_df["Financial Status"] == "Refund", "units_returned"
        ] = converted_df["units_sold"]
        converted_df.loc[
            parsed_df["Financial Status"] == "Refund", "units_sold"
        ] = -converted_df["units_sold"]

        converted_df["currency_exchange_rate"] = (
            currencies.generate_currency_exchange_rates(
                converted_df, self._external_currency_exchange_rates_table
            )
        )

        # Calculate Sales
        converted_df["gross_sales"] = js_round(
            (
                converted_df["price_local"]
                * converted_df["units_sold"]
                / converted_df["currency_exchange_rate"]
            ),
            2,
        )

        converted_df.loc[
            converted_df["units_returned"] > 0, "gross_returned"
        ] = -js_round(converted_df["gross_sales"], 2)

        converted_df["net_sales"] = converted_df["gross_sales"]
        converted_df["net_sales_approx"] = js_round(
            converted_df["net_sales"] * RevenueFactor.STD.value, 2
        )

        # Calculate Price
        converted_df.loc[converted_df["units_sold"] != 0, "price_usd"] = js_round(
            converted_df["gross_sales"] / converted_df["units_sold"], 2
        )

        # Change IAP flag
        converted_df.loc[
            parsed_df["Product Type"] == "inapp",
            "iap_flag",
        ] = Boolean.TRUE.value

        converted_df.loc[
            converted_df["iap_flag"] == Boolean.FALSE.value,
            "sku_id",
        ] = parsed_df["Product ID"]

        converted_df["unique_sku_id"] = (
            converted_df["sku_id"].astype(str)
            + "-"
            + Portal.GOOGLE.value
            + ":"
            + converted_df["studio_id"].astype(str)
        ).replace("\\s+", "_", regex=True)
        converted_df["portal_platform_region"] = generate_portal_platform_region(
            converted_df["portal"], converted_df["platform"], converted_df["region"]
        )

        return converted_df

    def _extract_file(self, raw_file: bytes) -> pd.DataFrame:
        try:
            raw_df = extract_csvs_from_raw_file(
                raw_file,
                sep=",",
                header=0,
                skiprows=0,
                parse_dates=True,
                quotechar='"',
                keep_default_na=False,
                na_values=[""],
                thousands=",",
                dtype={
                    "Order Number": str,
                    "Order Charged Date": str,
                    "Order Charged Timestamp": np.int64,
                    "Financial Status": str,
                    "Device Model": str,
                    "Product Title": str,
                    "Product ID": str,
                    "Product Type": str,
                    "SKU ID": str,
                    "Currency of Sale": str,
                    "Item Price": np.float64,
                    "Taxes Collected": np.float64,
                    "Charged Amount": np.float64,
                    "City of Buyer": str,
                    "State of Buyer": str,
                    "Postal Code of Buyer": str,
                    "Country of Buyer": str,
                    "Base Plan ID": str,
                    "Offer ID": str,
                    "Group ID": str,
                    "First USD 1M Eligible": str,
                    "Promotion ID": str,
                    "Coupon Value": str,
                    "Discount Rate": str,
                },
            )
        except (ValueError, BadZipFile) as ex:
            raise FileExtractionError(str(ex))
        return raw_df
