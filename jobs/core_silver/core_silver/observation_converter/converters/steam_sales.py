import io
import logging
from dataclasses import dataclass, field
from zipfile import BadZipFile, ZipFile

import numpy as np
import pandas as pd
import pandera as pa
from pandera import Column

from core_silver.dictionaries.constants import (
    <PERSON>olean,
    Constant,
    Origin,
    RevenueFactor,
)
from core_silver.dictionaries.currencies import NO_CURRENCY_TRANSACTION_ISO_4217_CODE
from core_silver.observation_converter.common_columns import (
    generate_portal_platform_region,
    generate_unique_sku_id,
)
from core_silver.observation_converter.converters.base_converter import (
    BaseSalesConverter,
    extract_csvs_from_raw_file,
)
from core_silver.observation_converter.converters.exceptions import (
    AdditionalDataFileNotFound,
    FileExtractionError,
    FileSchemaError,
    InvalidAdditionalData,
)
from core_silver.observation_converter.pandas_utils import InvalidSchema, enforce_schema
from core_silver.observation_converter.zip import (
    get_additional_data_from_zip,
)
from core_silver.utils.math_tools import js_round
from core_silver.utils.string_format import translate_column, unescape_html
from data_sdk.domain import DisplayPortal, Portal, countries
from data_sdk.domain.platform import DisplayPlatform
from data_sdk.domain.regions import Region
from data_sdk.domain.stores import Store

log = logging.getLogger(__name__)


@dataclass
class SteamRawDfs:
    sales_df: pd.DataFrame = field(default_factory=pd.DataFrame)
    iap_df: pd.DataFrame = field(default_factory=pd.DataFrame)
    complementary_package_df: pd.DataFrame = field(default_factory=pd.DataFrame)


class SteamSalesConverter(BaseSalesConverter):
    _sales_schema = pa.DataFrameSchema(
        columns={
            "Date": Column(pa.DateTime, coerce=True),
            " Bundle(ID#)": Column(
                pa.Int, coerce=True
            ),  # SPACE in " Bundle(ID#)" is important!!!
            "Bundle Name": Column(pa.String, coerce=True),
            "Product(ID#)": Column(pa.Int, coerce=True),
            "Product Name": Column(pa.String, coerce=True),
            "Type": Column(pa.String, coerce=True),
            "Game": Column(pa.String, coerce=True),
            "Platform": Column(pa.String, coerce=True),
            "Country Code": Column(pa.String, coerce=True),
            "Country": Column(pa.String, coerce=True),
            "Region": Column(pa.String, coerce=True),
            "Gross Units Sold": Column(pa.Int, coerce=True),
            "Chargeback/Returns": Column(pa.Int, coerce=True),
            "Net Units Sold": Column(pa.Int, coerce=True),
            "Base Price": Column(pa.Float, coerce=True),
            "Sale Price": Column(pa.Float, coerce=True),
            "Currency": Column(pa.String, coerce=True),
            "Gross Steam Sales (USD)": Column(pa.Float, coerce=True),
            "Chargeback/Returns (USD)": Column(pa.Float, coerce=True),
            "VAT/Tax (USD)": Column(pa.Float, coerce=True),
            "Net Steam Sales (USD)": Column(pa.Float, coerce=True),
            "Tag": Column(pa.String, coerce=True, nullable=True),
        }
    )

    _iap_schema = pa.DataFrameSchema(
        columns={
            "Date": Column(pa.DateTime, coerce=True),
            "Product(ID#)": Column(pa.Int, coerce=True),
            "Game": Column(pa.String, coerce=True),
            "Item(ID#)": Column(pa.Int, coerce=True),
            "Description": Column(pa.String, coerce=True, nullable=True),
            "Category": Column(pa.String, coerce=True, nullable=True),
            "Country Code": Column(pa.String, coerce=True),
            "Country": Column(pa.String, coerce=True),
            "Region": Column(pa.String, coerce=True),
            "Gross Units Sold": Column(pa.Int, coerce=True),
            "Chargeback/Returns": Column(pa.Int, coerce=True),
            "Net Units Sold": Column(pa.Int, coerce=True),
            " Average Price": Column(
                pa.Float, coerce=True
            ),  # Space in " Average Price" is important!!!
            "Currency": Column(pa.String, coerce=True),
            "Gross Steam Sales (USD)": Column(pa.Float, coerce=True),
            "Chargeback/Returns (USD)": Column(pa.Float, coerce=True),
            "VAT/Tax (USD)": Column(pa.Float, coerce=True),
            "Net Steam Sales (USD)": Column(pa.Float, coerce=True),
        }
    )

    def _parse(self, raw_file: bytes) -> SteamRawDfs:
        with io.BytesIO(raw_file) as stream:
            zip_file = ZipFile(stream)
            additional_data = get_additional_data_from_zip(zip_file)
            if not additional_data:
                raise AdditionalDataFileNotFound

            file_metadata = additional_data.get("fileMetaData")
            if not file_metadata:
                raise InvalidAdditionalData

            if file_metadata and "contentType" not in list(file_metadata.values())[0]:
                df = self._extract_sales_files(raw_file)
                df.sort_index(ascending=True, inplace=True)
                try:
                    return SteamRawDfs(sales_df=enforce_schema(df, self._sales_schema))
                except InvalidSchema as ex:
                    raise FileSchemaError(str(ex))

            sales_file_list = [
                file[0]
                for file in file_metadata.items()
                if file[1]["contentType"] == "sales"
            ]
            iap_file_list = [
                file[0]
                for file in file_metadata.items()
                if file[1]["contentType"] == "in-app-sales"
            ]

            sales_df = pd.DataFrame()
            iap_df = pd.DataFrame()
            complementary_package_df = pd.DataFrame()
            if sales_file_list:
                df = self._extract_sales_files(raw_file, sales_file_list)
                df.sort_index(ascending=True, inplace=True)
                try:
                    sales_df = enforce_schema(df, self._sales_schema)
                except InvalidSchema as ex:
                    raise FileSchemaError(str(ex))
            if iap_file_list:
                df = self._extract_iap_files(raw_file, iap_file_list)
                df.sort_index(ascending=True, inplace=True)
                try:
                    iap_df = enforce_schema(df, self._iap_schema)
                except InvalidSchema as ex:
                    raise FileSchemaError(str(ex))

            return SteamRawDfs(
                sales_df=sales_df,
                iap_df=iap_df,
                complementary_package_df=complementary_package_df,
            )

    def _convert(self) -> pd.DataFrame:
        raw_dfs = self._parse(self._raw_report.raw_file)
        sales_parsed_df = self._convert_sales_files(raw_dfs.sales_df)
        iap_parsed_df = self._convert_iap_files(raw_dfs.iap_df)

        non_empty_dfs = [df for df in [sales_parsed_df, iap_parsed_df] if not df.empty]
        if not non_empty_dfs:
            return pd.DataFrame()

        return pd.concat(non_empty_dfs, ignore_index=True)

    def _convert_iap_files(self, iap_df: pd.DataFrame) -> pd.DataFrame:
        manifest = self._raw_report.metadata

        if iap_df.empty:
            return pd.DataFrame()
        converted_df = pd.DataFrame()
        converted_df = converted_df.assign(
            country_code=iap_df["Country"].map(countries.get_country_alpha_3),
            currency_code=iap_df["Currency"],
            sku_id=iap_df["Product(ID#)"].astype(str)
            + "-IAP-"
            + iap_df["Item(ID#)"].astype(str),
            bundle_name=Constant.NOT_APPLICABLE.value,
            portal=DisplayPortal.STEAM,
            platform=DisplayPlatform.PC,
            transaction_type=Constant.IN_APP_PURCHASE.value,
            payment_instrument=Constant.UNKNOWN.value,
            tax_type=Constant.UNKNOWN.value,
            sale_modificator=Constant.NOT_APPLICABLE.value,
            acquisition_platform=Constant.UNKNOWN.value,
            iap_flag=Boolean.TRUE.value,
            date=iap_df["Date"].map(lambda x: x.strftime("%Y-%m-%d")),
            retailer_tag=Constant.NOT_APPLICABLE.value,
            human_name=translate_column(
                iap_df["Game"]
                + "-"
                + iap_df["Description"].fillna(iap_df["Item(ID#)"].astype(str)),
                unescape_html,
            ),
            store_id=iap_df["Item(ID#)"].astype(str),
            region=Region.GLOBAL.value,
            studio_id=manifest.studio_id,
            price_local=js_round(iap_df[" Average Price"], 2),
            base_price_local=0.0,
            # Chargeback/Returns are in negative numbers, so we negate them
            units_returned=-iap_df["Chargeback/Returns"],
            report_id=manifest.report_id,
            acquisition_origin=Origin.MAIN_STORE.value,
            units_sold=0,
            free_units=0,
            price_usd=0.0,
            net_sales=js_round(iap_df["Net Steam Sales (USD)"], 2),
            # Chargeback/Returns (USD) are in negative numbers, so we negate them
            gross_returned=js_round(-iap_df["Chargeback/Returns (USD)"], 2),
            net_sales_approx=0.0,
            store=Store.STEAM.value,
            abbreviated_name=Store.STEAM.abbreviate(),
        )
        converted_df["gross_sales"] = js_round(
            iap_df["Gross Steam Sales (USD)"] - converted_df["gross_returned"], 2
        )
        converted_df.loc[
            (converted_df["price_local"] > 0) & (iap_df["Gross Units Sold"] > 0),
            "units_sold",
        ] = iap_df["Net Units Sold"]
        converted_df.loc[
            (converted_df["price_local"] == 0) & (iap_df["Gross Units Sold"] > 0),
            "units_sold",
        ] = iap_df["Net Units Sold"]
        parsed_gross_sales = converted_df.loc[
            converted_df["units_sold"] != 0, "gross_sales"
        ]
        parsed_units_sold = converted_df.loc[
            converted_df["units_sold"] != 0, "units_sold"
        ]
        converted_df.loc[converted_df["units_sold"] != 0, "price_usd"] = js_round(
            parsed_gross_sales / parsed_units_sold, 2
        )
        converted_df["unique_sku_id"] = generate_unique_sku_id(
            converted_df, Portal.STEAM
        )
        converted_df["net_sales_approx"] = js_round(
            converted_df["net_sales"] * RevenueFactor.STD.value, 2
        )
        converted_df["portal_platform_region"] = generate_portal_platform_region(
            converted_df["portal"], converted_df["platform"], converted_df["region"]
        )
        return converted_df

    def _convert_sales_files(self, sales_df: pd.DataFrame) -> pd.DataFrame:
        manifest = self._raw_report.metadata

        if sales_df.empty:
            return pd.DataFrame()
        converted_df = pd.DataFrame()
        converted_df = converted_df.assign(
            country_code=sales_df["Country"].map(countries.get_country_alpha_3),
            currency_code=sales_df["Currency"],
            sku_id=sales_df["Product(ID#)"].astype(str),
            bundle_name=Constant.NOT_APPLICABLE.value,
            portal=DisplayPortal.STEAM,
            platform=DisplayPlatform.PC,
            transaction_type=sales_df["Type"],
            payment_instrument=Constant.UNKNOWN.value,
            tax_type=Constant.UNKNOWN.value,
            sale_modificator=Constant.NOT_APPLICABLE.value,
            acquisition_platform=sales_df["Platform"],
            iap_flag=Boolean.FALSE.value,
            date=sales_df["Date"].map(lambda x: x.strftime("%Y-%m-%d")),
            retailer_tag=sales_df["Tag"],
            human_name=translate_column(sales_df["Product Name"], unescape_html),
            store_id=sales_df["Product(ID#)"].astype(str),
            region=Region.GLOBAL.value,
            studio_id=manifest.studio_id,
            price_local=js_round(sales_df["Sale Price"], 2),
            base_price_local=js_round(sales_df["Base Price"], 2),
            # Chargeback/Returns are in positive numbers
            units_returned=sales_df["Chargeback/Returns"],
            report_id=manifest.report_id,
            acquisition_origin=Origin.MAIN_STORE.value,
            units_sold=0,
            free_units=0,
            price_usd=0.0,
            net_sales_approx=0.0,
            # Chargeback/Returns (USD) are in positive numbers
            gross_returned=js_round(sales_df["Chargeback/Returns (USD)"], 2),
            store=Store.STEAM.value,
            abbreviated_name=Store.STEAM.abbreviate(),
        )
        converted_df.loc[sales_df["Bundle Name"].notna(), "bundle_name"] = sales_df[
            "Bundle Name"
        ]
        converted_df.loc[sales_df["Tag"].isna(), "retailer_tag"] = (
            Constant.NOT_APPLICABLE.value
        )
        converted_df.loc[sales_df["Tag"].notna(), "acquisition_origin"] = (
            Origin.RETAIL.value
        )
        converted_df["net_sales"] = js_round(sales_df["Net Steam Sales (USD)"], 2)
        converted_df["gross_sales"] = js_round(
            sales_df["Gross Steam Sales (USD)"] - converted_df["gross_returned"], 2
        )
        converted_df.loc[
            (sales_df["Tag"].notna()) | (sales_df["Sale Price"] > 0),
            "units_sold",
        ] = sales_df["Net Units Sold"]
        converted_df.loc[
            (sales_df["Tag"].isna()) & (sales_df["Sale Price"] == 0),
            "free_units",
        ] = sales_df["Net Units Sold"]
        parsed_gross_sales = converted_df.loc[
            converted_df["units_sold"] != 0, "gross_sales"
        ]
        parsed_units_sold = converted_df.loc[
            converted_df["units_sold"] != 0, "units_sold"
        ]
        converted_df.loc[converted_df["units_sold"] != 0, "price_usd"] = js_round(
            parsed_gross_sales / parsed_units_sold, 2
        )
        converted_df["unique_sku_id"] = generate_unique_sku_id(
            converted_df, Portal.STEAM
        )

        converted_df["net_sales_approx"] = js_round(
            converted_df["net_sales"] * RevenueFactor.STD.value, 2
        )
        converted_df["portal_platform_region"] = generate_portal_platform_region(
            converted_df["portal"], converted_df["platform"], converted_df["region"]
        )
        # ISO 4217: The code XXX is used to denote a "transaction" involving no currency or incorrect code.
        converted_df.loc[
            ~converted_df["currency_code"].str.match(r"^[A-Z]{3}$"),
            "currency_code",
        ] = NO_CURRENCY_TRANSACTION_ISO_4217_CODE

        # enforce schema
        return converted_df.reset_index(drop=True)

    def _extract_sales_files(
        self, raw_file: bytes, file_list: list[str] | None = None
    ) -> pd.DataFrame:
        try:
            raw_df = extract_csvs_from_raw_file(
                raw_file,
                df_transform=None,
                file_list=file_list if file_list else None,
                sep=",",
                header=0,
                skiprows=3,
                parse_dates=True,
                quotechar='"',
                keep_default_na=False,
                na_values=[""],
                dtype={
                    "Date": str,
                    " Bundle(ID#)": np.int64,
                    "Bundle Name": str,
                    "Product(ID#)": np.int64,
                    "Product Name": str,
                    "Type": str,
                    "Game": str,
                    "Platform": str,
                    "Country Code": str,
                    "Country": str,
                    "Region": str,
                    "Gross Units Sold": np.int64,
                    "Chargeback/Returns": np.int64,
                    "Net Units Sold": np.int64,
                    "Base Price": np.float64,
                    "Sale Price": np.float64,
                    "Currency": str,
                    "Gross Steam Sales (USD)": np.float64,
                    "Chargeback/Returns (USD)": np.float64,
                    "VAT/Tax (USD)": np.float64,
                    "Net Steam Sales (USD)": np.float64,
                    "Tag": str,
                },
            )
        except (ValueError, BadZipFile) as ex:
            raise FileExtractionError(str(ex))
        return raw_df

    def _extract_iap_files(self, raw_file: bytes, file_list: list[str]) -> pd.DataFrame:
        try:
            raw_df = extract_csvs_from_raw_file(
                raw_file,
                df_transform=None,
                file_list=file_list,
                sep=",",
                header=0,
                skiprows=3,
                parse_dates=True,
                quotechar='"',
                keep_default_na=False,
                na_values=[""],
                dtype={
                    "Date": str,
                    "Product(ID#)": np.int64,
                    "Game": str,
                    "Item(ID#)": np.int64,
                    "Description": str,
                    "Category": str,
                    "Country Code": str,
                    "Country": str,
                    "Region": str,
                    "Gross Units Sold": np.int64,
                    "Chargeback/Returns": np.int64,
                    "Net Units Sold": np.int64,
                    " Average Price": np.float64,  # Space in " Average Price" is important!!!
                    "Currency": str,
                    "Gross Steam Sales (USD)": np.float64,
                    "Chargeback/Returns (USD)": np.float64,
                    "VAT/Tax (USD)": np.float64,
                    "Net Steam Sales (USD)": np.float64,
                },
            )
        except (ValueError, BadZipFile) as ex:
            raise FileExtractionError(str(ex))
        return raw_df
