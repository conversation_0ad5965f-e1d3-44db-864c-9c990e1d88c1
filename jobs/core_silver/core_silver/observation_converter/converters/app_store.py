from zipfile import BadZipFile

import numpy as np
import pandas as pd
import pandera as pa
from pandera import Column

from core_silver.dictionaries import currencies, vat
from core_silver.dictionaries.app_store_product_type import (
    app_bundle,
    free_or_paid_app,
    inapp_purchase,
    paid_app,
)
from core_silver.dictionaries.constants import <PERSON><PERSON><PERSON>, Constant, Origin
from core_silver.observation_converter.common_columns import (
    generate_portal_platform_region,
)
from core_silver.observation_converter.converters.base_converter import (
    BaseSalesConverter,
    extract_tsvs_from_raw_file,
)
from core_silver.observation_converter.converters.exceptions import (
    FileExtractionError,
    FileSchemaError,
)
from core_silver.observation_converter.pandas_utils import InvalidSchema, enforce_schema
from core_silver.utils.math_tools import js_round
from core_silver.utils.string_format import translate_column, unescape_html
from data_sdk.domain import DisplayPortal, Portal, countries
from data_sdk.domain.domain_types import ReportMetadataWithRawFile
from data_sdk.domain.platform import (
    DisplayPlatform,
    get_display_platform_from_platform,
)
from data_sdk.domain.regions import Region
from data_sdk.domain.stores import Store
from data_sdk.domain.tables import ExternalCurrencyExchangeRatesTable


class AppStoreConverter(BaseSalesConverter):
    _schema = pa.DataFrameSchema(
        columns={
            "Provider": Column(pa.String, coerce=True),
            "Provider Country": Column(pa.String, coerce=True),
            "SKU": Column(pa.String, coerce=True),
            "Developer": Column(pa.String, coerce=True),
            "Title": Column(pa.String, coerce=True),
            "Version": Column(pa.String, coerce=True),
            "Product Type Identifier": Column(pa.String, coerce=True),
            "Units": Column(pa.Int, coerce=True),
            "Developer Proceeds": Column(pa.Float, coerce=True),
            "Begin Date": Column(pa.DateTime, coerce=True),
            "End Date": Column(pa.DateTime, coerce=True),
            "Customer Currency": Column(pa.String, coerce=True),
            "Country Code": Column(pa.String, coerce=True),
            "Currency of Proceeds": Column(pa.String, coerce=True),
            "Apple Identifier": Column(pa.Int, coerce=True),
            "Customer Price": Column(pa.Float, coerce=True),
            "Promo Code": Column(pa.String, coerce=True),
            "Parent Identifier": Column(pa.String, coerce=True),
            "Subscription": Column(pa.String, coerce=True),
            "Period": Column(pa.String, coerce=True),
            "Category": Column(pa.String, coerce=True),
            "CMB": Column(pa.String, coerce=True),
            "Device": Column(pa.String, coerce=True),
            "Supported Platforms": Column(pa.String, coerce=True),
            "Proceeds Reason": Column(pa.String, coerce=True),
            "Preserved Pricing": Column(pa.String, coerce=True),
            "Client": Column(pa.String, coerce=True),
            "Order Type": Column(pa.String, coerce=True),
        }
    )

    def __init__(
        self,
        raw_report: ReportMetadataWithRawFile,
        external_currency_exchange_rates_table: ExternalCurrencyExchangeRatesTable,
    ):
        self._external_currency_exchange_rates_table = (
            external_currency_exchange_rates_table
        )
        super().__init__(raw_report)

    def _parse(self, raw_file: bytes) -> pd.DataFrame:
        df = self._extract_file(raw_file)
        df.sort_index(ascending=True, inplace=True)

        purchase_product_type_identifiers = (
            self._get_product_type_identifiers_for_specified_type([
                app_bundle,
                free_or_paid_app,
                inapp_purchase,
                paid_app,
            ])
        )
        df = df[df["Product Type Identifier"].isin(purchase_product_type_identifiers)]
        if df.empty:
            return pd.DataFrame()  # when raw file consist only non-sales data

        try:
            return enforce_schema(df, self._schema)
        except InvalidSchema as ex:
            raise FileSchemaError(str(ex))

    def _convert(self) -> pd.DataFrame:
        parsed_df = self._parse(self._raw_report.raw_file)
        manifest = self._raw_report.metadata

        if parsed_df.empty:
            return pd.DataFrame()

        converted_df = pd.DataFrame()
        converted_df = converted_df.assign(
            country_code=parsed_df["Country Code"].map(countries.get_country_alpha_3),
            currency_code=parsed_df["Customer Currency"],
            sku_id=parsed_df["Apple Identifier"],
            bundle_name=Constant.NOT_APPLICABLE.value,
            portal=DisplayPortal.APPLE,
            platform=parsed_df["Device"].map(get_display_platform_from_platform),
            transaction_type=Constant.UNKNOWN.value,
            payment_instrument=Constant.UNKNOWN.value,
            tax_type=Constant.UNKNOWN.value,
            sale_modificator=Constant.NOT_APPLICABLE.value,
            acquisition_platform=Constant.UNKNOWN.value,
            iap_flag=Boolean.FALSE.value,
            date=parsed_df["Begin Date"].map(lambda x: x.strftime("%Y-%m-%d")),
            retailer_tag=Constant.NOT_APPLICABLE.value,
            human_name=translate_column(parsed_df["Title"], unescape_html),
            store_id=Constant.UNKNOWN.value,
            region=Region.GLOBAL.value,
            studio_id=manifest.studio_id,
            price_local=js_round(parsed_df["Customer Price"], 2),
            base_price_local=np.nan,
            report_id=manifest.report_id,
            acquisition_origin=Origin.MAIN_STORE.value,
            units_sold=0,
            free_units=0,
            units_returned=0,
            price_usd=0.0,
            net_sales=0.0,
            net_sales_approx=0.0,
            gross_returned=0.0,
            store=Store.APPLE.value,
            abbreviated_name=Store.APPLE.abbreviate(),
        )

        # Fix empty platform
        converted_df["platform"] = converted_df.apply(
            lambda row: Constant.UNKNOWN.value
            if row["platform"] not in [platform.value for platform in DisplayPlatform]
            else row["platform"],
            axis=1,
        )

        # Calculate Units
        converted_df.loc[parsed_df["Customer Price"] != 0, "units_sold"] = parsed_df[
            "Units"
        ]
        converted_df.loc[converted_df["price_local"] == 0, "free_units"] = (
            parsed_df.loc[parsed_df["Customer Price"] == 0, "Units"]
        )
        converted_df.loc[
            converted_df["units_sold"] < 0, "units_returned"
        ] = -converted_df["units_sold"]

        converted_df["currency_exchange_rate"] = (
            currencies.generate_currency_exchange_rates(
                converted_df,
                self._external_currency_exchange_rates_table,
            )
        )

        # Calculate Sales
        converted_df["gross_sales"] = js_round(
            parsed_df["Units"]
            * parsed_df["Customer Price"]
            / converted_df["currency_exchange_rate"],
            2,
        )  # TODO: check if it is ok, to replace this with converted_df["units_sold"] * converted_df["price_local"] / rates

        converted_df.loc[converted_df["units_returned"] > 0, "gross_returned"] = (
            js_round(converted_df["gross_sales"], 2)
        )

        converted_df.loc[
            converted_df["units_returned"] > 0, "gross_sales"
        ] = -converted_df["gross_sales"]

        converted_df["net_sales"] = converted_df.apply(
            lambda row: vat.convert_gross_to_net(
                row["gross_sales"], row["country_code"]
            ),
            axis=1,
        )

        converted_df["net_sales_approx"] = js_round(
            parsed_df["Units"]
            * parsed_df["Developer Proceeds"]
            / converted_df["currency_exchange_rate"],
            2,
        )

        converted_df["net_sales_approx"] = converted_df.apply(
            lambda row: vat.convert_gross_to_net(
                row["net_sales_approx"], row["country_code"]
            ),
            axis=1,
        )

        # Calculate Price
        converted_df.loc[converted_df["units_sold"] != 0, "price_usd"] = js_round(
            converted_df["gross_sales"] / converted_df["units_sold"], 2
        )
        converted_df.loc[
            converted_df["units_returned"] > 0, "price_local"
        ] = -converted_df.loc[converted_df["units_returned"] > 0, "price_local"]

        # Change IAP flag
        converted_df.loc[
            parsed_df["Product Type Identifier"].isin(
                self._get_product_type_identifiers_for_specified_type([inapp_purchase])
            ),
            "iap_flag",
        ] = Boolean.TRUE.value

        # Bundle Name
        bundle = self._get_product_type_identifiers_for_specified_type([app_bundle])
        converted_df.loc[
            parsed_df["Product Type Identifier"].isin(bundle),
            "bundle_name",
        ] = converted_df.loc[
            parsed_df["Product Type Identifier"].isin(bundle),
            "human_name",
        ]

        # create unique unique_sku_id to allow sales per platform tracking
        converted_df["unique_sku_id"] = (
            converted_df["sku_id"].astype(str)
            + "-"
            + Portal.APPLE.value
            + "-"
            + converted_df["platform"].str.lower()
            + ":"
            + converted_df["studio_id"].astype(str)
        ).replace("\\s+", "_", regex=True)
        converted_df["portal_platform_region"] = generate_portal_platform_region(
            converted_df["portal"], converted_df["platform"], converted_df["region"]
        )

        return converted_df

    def _extract_file(self, raw_file: bytes) -> pd.DataFrame:
        try:
            raw_df = extract_tsvs_from_raw_file(
                raw_file,
                sep="\t",
                header=0,
                skiprows=0,
                parse_dates=True,
                quotechar='"',
                keep_default_na=False,
                na_values=[""],
                dtype={
                    "Provider": str,
                    "Provider Country": str,
                    "SKU": str,
                    "Developer": str,
                    "Title": str,
                    "Version": str,
                    "Product Type Identifier": str,
                    "Units": np.int64,
                    "Developer Proceeds": np.float64,
                    "Begin Date": str,
                    "End Date": str,
                    "Customer Currency": str,
                    "Country Code": str,
                    "Currency of Proceeds": str,
                    "Apple Identifier": np.int64,
                    "Customer Price": np.float64,
                    "Promo Code": str,
                    "Parent Identifier": str,
                    "Subscription": str,
                    "Period": str,
                    "Category": str,
                    "CMB": str,
                    "Device": str,
                    "Supported Platforms": str,
                    "Proceeds Reason": str,
                    "Preserved Pricing": str,
                    "Client": str,
                    "Order Type": str,
                },
            )
        except (ValueError, BadZipFile) as ex:
            raise FileExtractionError(str(ex))
        return raw_df

    def _get_product_type_identifiers_for_specified_type(self, types: list) -> list:
        if types:
            return list({key for type_dict in types for key in type_dict})
        else:
            return []
