import pandas as pd
import pandera as pa

from core_silver.observation_converter.converters.base_daily_active_users_converter import (
    BaseDailyActiveUsersConverter,
)
from core_silver.observation_converter.converters.playstation_utils import (
    assing_common_playstation_information,
)
from data_sdk.domain import DisplayPortal, Portal
from data_sdk.domain.countries import get_country_alpha_3
from data_sdk.domain.domain_types import ReportMetadata
from data_sdk.domain.platform import DisplayPlatform


class PlaystationDailyActiveUsersConverter(BaseDailyActiveUsersConverter):
    PORTAL = Portal.PLAYSTATION
    DISPLAY_PORTAL = DisplayPortal.PLAYSTATION

    _schema = pa.DataFrameSchema({
        **BaseDailyActiveUsersConverter._schema.columns,
        "region": pa.Column(pa.String, coerce=True),
    })

    def _convert(self) -> pd.DataFrame:
        manifest: ReportMetadata = self._raw_report.metadata
        parsed_df = self.parse(self._raw_report.raw_file)

        if parsed_df.empty:
            return pd.DataFrame()

        converted_df = pd.DataFrame()
        converted_df = self._build_converted_dataframe(parsed_df, manifest)
        converted_df = assing_common_playstation_information(converted_df)

        return converted_df

    def _get_common_columns(
        self, parsed_df: pd.DataFrame, metadata: ReportMetadata
    ) -> dict:
        return {
            "sku_id": parsed_df["product_id"],
            "human_name": parsed_df["product"],
            "store_id": parsed_df["product_id"],
            "region": parsed_df["region"],
            "platform": DisplayPlatform.UNKNOWN.value,
            "portal": self.DISPLAY_PORTAL.value,
            "country_code": get_country_alpha_3("unknown"),
            "report_id": metadata.report_id,
            "studio_id": metadata.studio_id,
        }
