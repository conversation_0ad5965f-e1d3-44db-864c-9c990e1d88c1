import abc

import pandas as pd
import pandera as pa

from core_silver.observation_converter.converters.base_active_users_converter import (
    BaseActiveUsersConverter,
)
from data_sdk.domain.domain_types import ReportMetadata
from data_sdk.reports.schema import MonthlyActiveUsersConvertedReport


class BaseMonthlyActiveUsersConverter(BaseActiveUsersConverter, abc.ABC):
    """
    Base class for monthly active users converters.
    Handles monthly-specific logic like year/month parsing and column mapping.
    Now generates daily rows for each day in the date range.
    """

    converted_report_cls = MonthlyActiveUsersConvertedReport

    _schema = pa.DataFrameSchema({
        **BaseActiveUsersConverter._base_schema_fields,
        "year": pa.Column(pa.Int, coerce=True),
        "month": pa.Column(pa.Int, coerce=True),
    })

    def _build_converted_dataframe(
        self, parsed_df: pd.DataFrame, metadata: ReportMetadata
    ) -> pd.DataFrame:
        date_range = pd.date_range(
            start=metadata.date_from, end=metadata.date_to, freq="D"
        )

        expanded_df = (
            parsed_df.assign(key=1)
            .merge(pd.DataFrame({"date": date_range, "key": 1}), on="key")
            .drop("key", axis=1)
        )

        common_columns = self._get_common_columns(expanded_df, metadata)

        return pd.DataFrame({
            **common_columns,
            "date": expanded_df["date"].dt.strftime("%Y-%m-%d"),
            "monthly_active_users": expanded_df["count"],
        })

    def _get_dtype_mapping(self) -> dict:
        return {
            **self._get_common_dtype_mapping(),
            "year": str,
            "month": str,
        }
