import io
from datetime import datetime
from zipfile import BadZipFile, ZipFile

import numpy as np
import pandas as pd
import pandera as pa
from pandera import Column

from core_silver.observation_converter.common_columns import (
    generate_portal_platform_region,
)
from core_silver.observation_converter.converters.base_converter import (
    BaseWishlistCohortsConverter,
)
from core_silver.observation_converter.converters.exceptions import (
    AdditionalDataFileNotFound,
    FileExtractionError,
    FileSchemaError,
)
from core_silver.observation_converter.pandas_utils import InvalidSchema, enforce_schema
from core_silver.observation_converter.zip import (
    get_additional_data_from_zip,
    get_reports_from_zip,
)
from core_silver.utils.string_format import translate_column, unescape_html
from data_sdk.domain import DisplayPortal
from data_sdk.domain.platform import DisplayPlatform
from data_sdk.domain.regions import Region
from data_sdk.domain.stores import Store


class SteamWishlistCohortsConverter(BaseWishlistCohortsConverter):
    _schema = pa.DataFrameSchema({
        "DateLocal": Column(pa.DateTime, coerce=True),
        "Game": Column(pa.String, coerce=True),
        "MonthCohort": Column(pa.DateTime, coerce=True),
        "PurchasesAndActivations": Column(pa.Int, coerce=True),
        "Gifts": Column(pa.Int, coerce=True),
        "TotalConversions": Column(pa.Int, coerce=True),
        "sku_id": Column(pa.Int, coerce=True),
    })

    def _parse(self, raw_file: bytes) -> pd.DataFrame:
        try:
            with io.BytesIO(raw_file) as stream, ZipFile(stream) as zip_file:
                reports = get_reports_from_zip(zip_file, ".csv")
                additional_data = get_additional_data_from_zip(zip_file)
                dfs = [
                    _assign_sku_id_from_additional_data(
                        pd.read_csv(
                            report[1],
                            sep=",",
                            header=0,
                            skiprows=3,
                            parse_dates=True,
                            dtype={
                                "DateLocal": str,
                                "Game": str,
                                "MonthCohort": str,
                                "PurchasesAndActivations": np.int64,
                                "Gifts": np.int64,
                                "TotalConversions": np.int64,
                                "sku_id": np.int64,
                            },
                        ),
                        report[0],
                        additional_data,
                    )
                    for report in reports
                ]
        except (ValueError, BadZipFile) as ex:
            raise FileExtractionError(str(ex))

        if not dfs:
            return pd.DataFrame()

        parsed_df = pd.concat([
            _parse_month_cohort(df) for df in dfs if "MonthCohort" in df.columns
        ])
        try:
            return enforce_schema(parsed_df, self._schema)
        except InvalidSchema as ex:
            raise FileSchemaError(str(ex))

    def _convert(self) -> pd.DataFrame:
        parsed_df = self._parse(self._raw_report.raw_file)
        manifest = self._raw_report.metadata

        if parsed_df.empty:
            return pd.DataFrame()
        converted_df = pd.DataFrame()
        converted_df = converted_df.assign(
            date=parsed_df["DateLocal"].map(lambda x: x.strftime("%Y-%m-%d")),
            month_cohort=parsed_df["MonthCohort"],
            purchases_and_activations=parsed_df["PurchasesAndActivations"],
            gifts=parsed_df["Gifts"],
            total_conversions=parsed_df["TotalConversions"],
            store_id=parsed_df["sku_id"].astype(str),
            sku_id=parsed_df["sku_id"],
            human_name=translate_column(parsed_df["Game"], unescape_html),
            report_id=manifest.report_id,
            studio_id=manifest.studio_id,
            platform=DisplayPlatform.PC,
            portal=DisplayPortal.STEAM,
            region=Region.GLOBAL.value,
            store=Store.STEAM.value,
            abbreviated_name=Store.STEAM.abbreviate(),
        )
        converted_df["month_cohort"] = converted_df["month_cohort"].map(
            lambda x: x.strftime("%Y-%m-%d")
        )
        converted_df["unique_sku_id"] = (
            converted_df["sku_id"].astype(str)
            + "-store:"
            + converted_df["studio_id"].astype(str)
        )
        converted_df["portal_platform_region"] = generate_portal_platform_region(
            converted_df["portal"], converted_df["platform"], converted_df["region"]
        )

        return converted_df


def _assign_sku_id_from_additional_data(df, file_name, additional_data) -> pd.DataFrame:
    if df.empty:
        return df
    if not additional_data:
        raise AdditionalDataFileNotFound
    df["sku_id"] = int(
        additional_data.get("fileMetaData").get(file_name).get("productId")
    )
    return df


def _parse_month_cohort(df):
    df["MonthCohort"] = df["MonthCohort"].map(
        lambda x: datetime.strptime(x, "%Y-%m-%d")
    )
    return df
