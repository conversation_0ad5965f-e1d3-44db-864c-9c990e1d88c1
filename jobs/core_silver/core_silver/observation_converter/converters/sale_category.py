"""This module contains functions for calculating conditions
for sales category. Each function take a data frame with sales
observations and returns pd.Series that can be used in pandas
queries and selects.

"""

import pandas as pd

from core_silver.dictionaries.constants import Constant
from data_sdk.domain import DisplayPortal, SaleCategory


class SaleCategoryClassifier:
    @staticmethod
    def _is_sale(df: pd.DataFrame) -> pd.Series:
        return (
            (df["units_returned"] >= 0)
            & (df["free_units"] == 0)
            & ((df["gross_sales"] != 0) | (df["units_sold"] == 0))
            & ((df["portal"] != DisplayPortal.EPIC.value) | (df["price_local"] > 0.1))
            & (
                (df["gross_sales"] != 0)
                | (df["units_sold"] != 0)
                | (df["units_returned"] != 0)
            )
            & (
                (df["units_sold"] >= 0)
                & ((df["units_sold"] != 0) | (df["units_returned"] <= 0))
            )
            & (
                ((df["gross_sales"] != 0) | (df["gross_returned"] != 0))
                & ((df["units_sold"] != 0) | (df["units_returned"] != 0))
            )
        )

    @staticmethod
    def _is_return(df: pd.DataFrame) -> pd.Series:
        return (
            (df["units_returned"] >= 0)
            & (df["free_units"] == 0)
            & ((df["gross_sales"] != 0) | (df["units_sold"] == 0))
            & ((df["portal"] != DisplayPortal.EPIC.value) | (df["price_local"] > 0.1))
            & (
                (df["gross_sales"] != 0)
                | (df["units_sold"] != 0)
                | (df["units_returned"] != 0)
            )
            & (
                (df["units_sold"] < 0)
                | ((df["units_sold"] == 0) & (df["units_returned"] > 0))
            )
        )

    @staticmethod
    def _is_blank(df: pd.DataFrame) -> pd.Series:
        return (
            (df["units_returned"] >= 0)
            & (df["free_units"] == 0)
            & ((df["gross_sales"] != 0) | (df["units_sold"] == 0))
            & ((df["portal"] != DisplayPortal.EPIC.value) | (df["price_local"] > 0.1))
            & (df["gross_sales"] == 0)
            & (df["units_sold"] == 0)
            & (df["units_returned"] == 0)
        )

    @staticmethod
    def _is_sale_adjustment(df: pd.DataFrame) -> pd.Series:
        return (
            (df["units_returned"] >= 0)
            & (df["free_units"] == 0)
            & ((df["gross_sales"] != 0) | (df["units_sold"] == 0))
            & ((df["portal"] != DisplayPortal.EPIC.value) | (df["price_local"] > 0.1))
            & (df["gross_sales"] != 0)
            & (df["units_sold"] == 0)
            & (df["units_returned"] == 0)
        )

    @staticmethod
    def _is_non_billable_epic(df: pd.DataFrame) -> pd.Series:
        return (
            (df["units_returned"] >= 0)
            & (df["free_units"] == 0)
            & ((df["gross_sales"] != 0) | (df["units_sold"] == 0))
            & (df["portal"] == DisplayPortal.EPIC.value)
            & (df["price_local"] <= 0.1)
        )

    @staticmethod
    def _is_non_billable_other(df: pd.DataFrame) -> pd.Series:
        return (
            (df["units_returned"] >= 0)
            & (df["free_units"] == 0)
            & (df["units_sold"] != 0)
            & (df["gross_sales"] == 0)
            & (df["retailer_tag"] == Constant.NOT_APPLICABLE.value)
            & (df["sale_modificator"] != Constant.NOT_APPLICABLE.value)
            & (df["sale_modificator"] != "Disc")
            & (df["sale_modificator"] != "Disk")
            & (df["sale_modificator"] != "Prepaid Code")
        )

    @staticmethod
    def _is_retail_sale_modificator(df: pd.DataFrame) -> pd.Series:
        return (
            (df["units_returned"] >= 0)
            & (df["free_units"] == 0)
            & (df["units_sold"] != 0)
            & (df["gross_sales"] == 0)
            & (df["retailer_tag"] == Constant.NOT_APPLICABLE.value)
            & (df["sale_modificator"] != Constant.NOT_APPLICABLE.value)
            & (
                (df["sale_modificator"] == "Disc")
                | (df["sale_modificator"] == "Disk")
                | (df["sale_modificator"] == "Prepaid Code")
            )
        )

    @staticmethod
    def _is_non_billable_unknown(df: pd.DataFrame) -> pd.Series:
        return (
            (df["units_returned"] >= 0)
            & (df["free_units"] == 0)
            & (df["units_sold"] != 0)
            & (df["gross_sales"] == 0)
            & (df["retailer_tag"] == Constant.NOT_APPLICABLE.value)
            & (df["sale_modificator"] == Constant.NOT_APPLICABLE.value)
        )

    @staticmethod
    def _is_retail_steam(df: pd.DataFrame) -> pd.Series:
        return (
            (df["units_returned"] >= 0)
            & (df["free_units"] == 0)
            & (df["units_sold"] != 0)
            & (df["gross_sales"] == 0)
            & (df["retailer_tag"] != Constant.NOT_APPLICABLE.value)
        )

    @staticmethod
    def _is_invalid_free(df: pd.DataFrame) -> pd.Series:
        return (df["units_returned"] >= 0) & (df["free_units"] != 0)

    @staticmethod
    def _is_free(df: pd.DataFrame) -> pd.Series:
        return (
            (df["units_returned"] >= 0)
            & (df["free_units"] != 0)
            & (df["units_sold"] == 0)
            & (df["units_returned"] == 0)
            & (df["gross_sales"] == 0)
            & (df["gross_returned"] == 0)
        )

    @staticmethod
    def _is_free_with_return(df: pd.DataFrame) -> pd.Series:
        return (
            (df["units_returned"] >= 0)
            & (df["free_units"] != 0)
            & (df["units_sold"] <= 0)
            & (df["units_returned"] > 0)
            & (df["gross_sales"] < 0)
        )

    @staticmethod
    def _is_free_with_sale(df: pd.DataFrame) -> pd.Series:
        return (
            (df["units_returned"] >= 0)
            & (df["free_units"] != 0)
            & (df["units_sold"] > 0)
            & (df["gross_sales"] > 0)
        )

    @staticmethod
    def _is_invalid_return(df: pd.DataFrame) -> pd.Series:
        return df["units_returned"] < 0

    @classmethod
    def classify(cls, df: pd.DataFrame) -> pd.Series:
        """Compute category column based on sales data. Categories are assigned
        based on the following SQL:
        ```sql
        category = if('fact_sales'[units_returned] < 0, "Invalid Return",
            if('fact_sales'[free_units] <> 0,
                if('fact_sales'[units_sold] = 0 && 'fact_sales'[units_returned] = 0 && 'fact_sales'[gross_sales] = 0 && 'fact_sales'[gross_returned] = 0, "Free",
                if('fact_sales'[units_sold]<= 0 && 'fact_sales'[units_returned] > 0 && 'fact_sales'[gross_sales] < 0, "Free & Return",
                if('fact_sales'[units_sold] > 0 && 'fact_sales'[gross_sales] > 0, "Free & Sale",
                "Invalid Free"))),
            if('fact_sales'[gross_sales] = 0 && 'fact_sales'[units_sold] <> 0,
                if('fact_sales'[retailer_tag] <> blank(), "Retail: Steam",
                if(RELATED('dim_acquisition_properties'[sale_modificator]) = blank(), "Non-billable: Unknown Sale Type",
                if(RELATED('dim_acquisition_properties'[sale_modificator]) in {"Disc", "Disk", "Prepaid Code"}, "Retail: " & RELATED('dim_acquisition_properties'[sale_modificator]),
                "Non-billable: " & 'fact_sales'[_partition_platform]))),
            if('fact_sales'[_partition_platform] = "EPIC" && 'fact_sales'[price_local] <= 0.1, "Non-billable: Epic",
            if('fact_sales'[gross_sales] <> 0 && 'fact_sales'[units_sold] = 0 && 'fact_sales'[units_returned] = 0, "Sale Adjustment",
            if('fact_sales'[gross_sales] = 0 && 'fact_sales'[units_sold] = 0 && 'fact_sales'[units_returned] = 0, "Blank Record",
            if('fact_sales'[units_sold] < 0 || ('fact_sales'[units_sold] = 0 && 'fact_sales'[units_returned] > 0) , "Return",
            if(('fact_sales'[gross_sales] <> 0 || 'fact_sales'[gross_returned] <> 0) && ('fact_sales'[units_sold] <> 0 || 'fact_sales'[units_returned] <> 0), "Sale",
            "Invalid Sale"))))))))

        Previously named _calculate_category in ReportProcessor
        ```

        Args:
            df: DataFrame containing observations

        Returns:
            Series with categories for each row

        """

        # categorize observations based on sale data
        # execution order very much matters here
        # start from the end of the SQL statement above
        # work your way up
        # mark everything as Invalid Sale
        category = pd.Series(
            data=[SaleCategory.SALE_INVALID.value] * len(df.index),
            name="category",
            index=df.index,
        )

        # categorize sales
        category.loc[cls._is_sale(df)] = SaleCategory.SALE.value

        # categorize returns
        category.loc[cls._is_return(df)] = SaleCategory.RETURN.value

        # blank records
        category.loc[cls._is_blank(df)] = SaleCategory.BLANK_RECORD.value

        # sale adjustments
        category.loc[cls._is_sale_adjustment(df)] = SaleCategory.SALE_ADJUSTMENT.value

        # non-billable for EPIC
        category.loc[cls._is_non_billable_epic(df)] = (
            f"{SaleCategory.NON_BILLABLE.value}: {DisplayPortal.EPIC.value}"
        )

        # non-billable other portals
        category.loc[cls._is_non_billable_other(df)] = (
            f"{SaleCategory.NON_BILLABLE.value}: " + df["platform"]
        )

        # retail based on sale modificator
        category.loc[cls._is_retail_sale_modificator(df)] = (
            f"{SaleCategory.RETAIL.value}: " + df["sale_modificator"]
        )

        # non-billable unknown sale type
        category.loc[cls._is_non_billable_unknown(df)] = (
            f"{SaleCategory.NON_BILLABLE.value}: Unknown Sale Type"
        )

        # invalid free units
        category.loc[cls._is_retail_steam(df)] = (
            f"{SaleCategory.RETAIL.value}: {DisplayPortal.STEAM.value}"
        )

        category.loc[cls._is_invalid_free(df)] = SaleCategory.FREE_INVALID.value

        # free "purchase"
        category.loc[cls._is_free(df)] = SaleCategory.FREE.value

        # free with return
        category.loc[cls._is_free_with_return(df)] = SaleCategory.FREE_AND_RETURN.value

        # free with sale
        category.loc[cls._is_free_with_sale(df)] = SaleCategory.FREE_AND_SALE.value

        # invalid return
        category.loc[cls._is_invalid_return(df)] = SaleCategory.RETURN_INVALID.value

        return category
