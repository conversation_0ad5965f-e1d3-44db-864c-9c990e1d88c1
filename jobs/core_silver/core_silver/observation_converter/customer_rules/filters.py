from datetime import date, datetime
from enum import Enum
from pathlib import Path

import pandas as pd
import polars as pl

from data_sdk.domain.domain_types import StudioId


class FilterType(str, Enum):
    SKU_FILTERS = "sku_filters"


class RuleType(str, Enum):
    FILTERS = "filters"


class SKUFilterOptions:
    def __init__(self, sku_id: str, date_to: date):
        self.sku_id: str = sku_id
        self.date_to: date = date_to

    def __eq__(self, other):
        if isinstance(other, SKUFilterOptions):
            return self.sku_id == other.sku_id and self.date_to == other.date_to
        return False


class CustomerRules:
    """
    type: FilterType
    value: dict[RuleType, list[SKUFilterOptions]]

    is one level too deep, consider it while changing the structure in the future
    """

    def __init__(
        self,
        studio_id: StudioId,
        filter_type: FilterType,
        value: dict[RuleType, list[SKUFilterOptions]],
    ):
        self.studio_id: StudioId = studio_id
        self.type: FilterType = filter_type
        self.value: dict[RuleType, list[SKUFilterOptions]] = value

    def __eq__(self, other):
        if isinstance(other, CustomerRules):
            return (
                self.studio_id == other.studio_id
                and self.type == other.type
                and self.value == other.value
            )
        return False


def load_customer_rules(
    studio_id: StudioId, filter_type: FilterType = FilterType.SKU_FILTERS
) -> CustomerRules | None:
    path = Path(__file__).with_name("customer_rules.json")
    json_database = pd.read_json(path)
    df = json_database[
        (json_database["studio_id"] == studio_id)
        & (json_database["type"] == filter_type)
    ]
    if df.empty:
        return None

    sku_id_filters: dict[RuleType, list[SKUFilterOptions]] = {
        RuleType.FILTERS: [
            SKUFilterOptions(
                sku_id=sku_id["sku"],
                date_to=datetime.strptime(sku_id["date_to"], "%Y-%m-%d").date(),
            )
            for sku_id in df["value"].values[0][RuleType.FILTERS]
        ]
    }

    return CustomerRules(
        studio_id=StudioId(df["studio_id"].values[0]),
        filter_type=FilterType(df["type"].values[0]),
        value=sku_id_filters,
    )


def apply_customer_filters(
    observations: pl.DataFrame, customer_rules: CustomerRules
) -> pl.DataFrame:
    """
    Customer filters are applied to the observations DataFrame at a very last
    step, after converters and deduplication, because they cover very specific cases
    and are not applied to most of the customers. Covered scenario is comparable
    to presentation layer filters, where user can filter out specific SKUs
    from the view and can change over time.
    """

    observations_df: pl.DataFrame = observations.clone()
    if customer_rules.type != FilterType.SKU_FILTERS:
        return observations_df

    # discounts observation_type has different observations schema
    sku_id_column_name = (
        "sku_id" if "sku_id" in observations_df.columns else "base_sku_id"
    )
    date_column_name = "date" if "date" in observations_df.columns else "datetime_from"

    for sku in customer_rules.value[RuleType.FILTERS]:
        sku_data = observations_df.filter(
            (pl.col(sku_id_column_name) == sku.sku_id)
            & (pl.col(date_column_name).cast(pl.Date) >= sku.date_to)
        )
        observations_df = observations_df.filter(
            pl.col(sku_id_column_name) != sku.sku_id
        )
        if not sku_data.is_empty():
            observations_df = pl.concat([observations_df, sku_data])

    return observations_df
