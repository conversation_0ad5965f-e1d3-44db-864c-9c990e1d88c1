import html
import logging
from datetime import date, datetime

import pandas as pd
from dateutil import tz

from data_sdk.domain.domain_types import (
    ReportMetadata,
)
from data_sdk.utils.date_utils import datetime_to_string

log = logging.getLogger(__name__)


def translate_column(column: pd.Series, function) -> pd.Series:
    """
    Given a column of strings, returns a new column with the same strings translated using the given function
    """
    translations = pd.DataFrame()
    translations["name"] = column
    translations = translations.drop_duplicates()
    translations["translated"] = translations["name"].map(function)
    foo = translations.set_index("name")["translated"]
    return column.map(foo)


def unescape_html(html_str):
    """
    Unescapes HTML entities in a string like &amp; to & or &quot; to " etc
    """
    return html.unescape(html_str)


def filter_nonalphanumeric(input_str: str):
    """
    Given a string, returns its version containing only alphanumeric chars (letters and numbers, no special chars, whitespace etc)
    """
    return "".join(e for e in input_str if e.isalnum())


def timestamp_to_datetime_str(unix_timestamp: int) -> str:
    return datetime_to_string(datetime.utcfromtimestamp(unix_timestamp))


def str_datetime_to_utc_datetime(str_dt: str) -> datetime:
    return datetime.strptime(str_dt, "%Y-%m-%dT%H:%M:%S%z").astimezone(tz.tzutc())


def generate_expected_filename(file: ReportMetadata) -> str:
    return f"{file.report_id}_{file.date_from}_{file.date_to}.parquet"


def get_report_id_from_filename(file_name: str) -> int:
    return int(file_name.split("_")[0])  # Simulated extraction


def get_date_from_from_filename(file_name: str) -> date:
    date_str = file_name.split("_")[1]
    return datetime.strptime(date_str, "%Y-%m-%d").date()


def get_date_to_from_filename(file_name: str) -> date:
    date_str = file_name.split("_")[2].split(".")[0]
    return datetime.strptime(date_str, "%Y-%m-%d").date()
