from enum import Enum

from data_sdk.domain import DisplayPortal
from data_sdk.domain.platform import DisplayPlatform
from data_sdk.domain.ppr import get_portal_platform_region_id
from data_sdk.domain.regions import Region


class Constant(Enum):
    NOT_APPLICABLE = "NOT_APPLICABLE"
    UNKNOWN = "Unknown"
    UNASSIGNED = "UNASSIGNED"
    STOREFRONT = "STOREFRONT"
    DEFAULT_SORTING_ORDER = 9
    BIG_SKU_SALES_THRESHOLD = 1_000
    BIG_SKU_FREE_UNITS_THRESHOLD = 1_000
    FUTURE_EVENT_PROMO_LENGTH = 10
    IN_APP_PURCHASE = "InAppPurchase"


class Origin(Enum):
    MAIN_STORE = "MAIN_STORE"
    RETAIL = "RETAIL"


class Boolean(Enum):
    """
    Boolean can be expressed as a True/False, Y/N, Yes/No etc.
    The Enum may evolve using next combinations when necessary.
    """

    TRUE = "True"
    FALSE = "False"


class StringLength(Enum):
    """Predefined lengths for various types of text data that
    can be present in reports. Should be used for validation checks
    that would verify if data doesn't excede expected length.

    For data which length is known beforehand (e.g. `country_code` etc.)
    exact length should be used in those checks.

    """

    TINY = 32
    SMALL = 255
    MEDIUM = 511
    HUGE = 1023


class RevenueFactor(Enum):
    """Predefined revenue factor used to calculate approximate net revenue, based on
    sales provision taken by portal:
    Epic: 12%
    All other portals: 30%
    """

    EPIC = 0.88
    STD = 0.7


# NOA - Nintendo of America
# NOE - Nintendo of Europe
# NAL - Nintendo Australia
# NOK - Nintendo of Korea
# NCL - Nintendo Co., Ltd. Kyoto, Japan
# NHK - Nintendo Hong Kong

noa_ppr_id = get_portal_platform_region_id(
    DisplayPortal.NINTENDO.value
    + ":"
    + DisplayPlatform.SWITCH.value
    + ":"
    + Region.NINTENDO_AMERICAS.value
)
noe_ppr_id = get_portal_platform_region_id(
    DisplayPortal.NINTENDO.value
    + ":"
    + DisplayPlatform.SWITCH.value
    + ":"
    + Region.NINTENDO_EUROPE_AUSTRALIA.value
)
nok_ppr_id = get_portal_platform_region_id(
    DisplayPortal.NINTENDO.value
    + ":"
    + DisplayPlatform.SWITCH.value
    + ":"
    + Region.NINTENDO_KOREA.value
)
nhk_ppr_id = get_portal_platform_region_id(
    DisplayPortal.NINTENDO.value
    + ":"
    + DisplayPlatform.SWITCH.value
    + ":"
    + Region.NINTENDO_TAIWAN_HONG_KONG.value
)
nja_ppr_id = get_portal_platform_region_id(
    DisplayPortal.NINTENDO.value
    + ":"
    + DisplayPlatform.SWITCH.value
    + ":"
    + Region.NINTENDO_JAPAN.value
)
# nhn is probably not a real abbreviation but couldn't find the official one
nhn_ppr_id = get_portal_platform_region_id(
    DisplayPortal.NINTENDO.value
    + ":"
    + DisplayPlatform.SWITCH.value
    + ":"
    + Region.NINTENDO_CHINA.value
)
ncl_ppr_ids = {nja_ppr_id, nok_ppr_id, nhk_ppr_id, nhn_ppr_id}
nintendo_ppr_ids = {
    noa_ppr_id,  # americas
    noe_ppr_id,  # europe
    nja_ppr_id,  # japan
    nok_ppr_id,  # korea
    nhk_ppr_id,  # hong kong
    nhn_ppr_id,  # china
}

steam_ppr_id = get_portal_platform_region_id(
    DisplayPortal.STEAM.value
    + ":"
    + DisplayPlatform.PC.value
    + ":"
    + Region.GLOBAL.value
)

humble_ppr_id = get_portal_platform_region_id(
    DisplayPortal.HUMBLE.value
    + ":"
    + DisplayPlatform.PC.value
    + ":"
    + Region.GLOBAL.value
)

PLATFORMID_PROMO_COOLDOWN_PERIOD = {
    steam_ppr_id: {"2000-01-01": 42, "2022-03-28": 28, "2022-11-17": 30},
    noa_ppr_id: {
        "2000-01-01": 21
    },  # the 28 is a placeholder, NOA can range from 7 to 21.
    noe_ppr_id: {"2000-01-01": 27},
    nja_ppr_id: {"2000-01-01": 28},
    humble_ppr_id: {"2000-01-01": 56},
    nhn_ppr_id: {"2000-01-01": 28},
    nok_ppr_id: {"2000-01-01": 28},
    nhk_ppr_id: {"2000-01-01": 28},
}

# need to convert to platfrom_region, nintendo doesn'thave the same split across regions
PLATFORM_PROMO_STRATEGY_DAYS = {
    "high_strategy": {
        "epic": 100,
        "gog": 60,
        "humble": 60,
        "meta": 60,
        "microsoft": 60,
        "nintendo": 100,
        "playsation": 50,
        "steam": 120,
    },
    "low_strategy": {
        "epic": 40,
        "gog": 0,
        "humble": 0,
        "meta": 0,
        "microsoft": 0,
        "nintendo": 50,
        "playsation": 0,
        "steam": 50,
    },
}

PORTAL_PLATFORM_REGION_ID_GAMES_WITH_BASE_PRICES = {steam_ppr_id, *nintendo_ppr_ids}
