from typing import TypeVar

import numpy as np

from core_silver.utils.math_tools import js_round
from data_sdk.domain.countries import CountryCodeAlpha3

_vat_rates: dict[CountryCodeAlpha3, float] = {
    "AGO": 0.07,  # Angola
    "ALB": 0.20,  # Albania
    "AND": 0.045,  # Andorra
    "ARE": 0.05,  # United Arab Emirates
    "ARG": 0.21,  # Argentina
    "ARM": 0.20,  # Armenia
    "AUS": 0.10,  # Australia
    "AUT": 0.20,  # Austria
    "AZE": 0.18,  # Azerbaijan
    "BEL": 0.21,  # Belgium
    "BGD": 0.20,  # Bangladesh
    "BGR": 0.20,  # Bulgaria
    "BIH": 0.17,  # Bosnia and Herzegovina
    "BLR": 0.20,  # Belarus
    "BOL": 0.13,  # Bolivia
    "BRA": 0.20,  # Brazil
    "CAN": 0.05,  # Canada
    "CHE": 0.081,  # Switzerland
    "CHL": 0.19,  # Chile
    "CHN": 0.13,  # China
    "COG": 0.16,  # Congo
    "COL": 0.19,  # Colombia
    "CYP": 0.19,  # Cyprus
    "CZE": 0.21,  # Czech Republic
    "DEU": 0.19,  # Germany
    "DNK": 0.25,  # Denmark
    "DZA": 0.19,  # Algeria
    "EGY": 0.14,  # Egypt
    "ESP": 0.21,  # Spain
    "EST": 0.20,  # Estonia
    "ETH": 0.15,  # Ethiopia
    "FIN": 0.24,  # Finland
    "FRA": 0.20,  # France
    "GBR": 0.20,  # Great Britain
    "GRC": 0.24,  # Greece
    "GTM": 0.12,  # Guatemala
    "HRV": 0.25,  # Croatia
    "HUN": 0.27,  # Hungary
    "IDN": 0.11,  # Indonesia
    "IND": 0.18,  # India
    "IRL": 0.23,  # Ireland
    "IRN": 0.09,  # Iran
    "ISL": 0.24,  # Iceland
    "ISR": 0.17,  # Israel
    "ITA": 0.22,  # Italy
    "JPN": 0.08,  # Japan
    "KAZ": 0.12,  # Kazakhstan
    "KEN": 0.16,  # Kenya
    "KOR": 0.10,  # South Korea
    "LTU": 0.21,  # Lithuania
    "LUX": 0.17,  # Luxembourg
    "LVA": 0.21,  # Latvia
    "MAR": 0.20,  # Morocco
    "MEX": 0.16,  # Mexico
    "MLT": 0.18,  # Malta
    "MNE": 0.21,  # Montenegro
    "MYS": 0.06,  # Malaysia
    "NGA": 0.075,  # Nigeria
    "NLD": 0.21,  # Netherlands
    "NOR": 0.25,  # Norway
    "NZL": 0.21,  # New Zealand
    "PAK": 0.17,  # Pakistan
    "PER": 0.18,  # Peru
    "PHL": 0.12,  # Philippines
    "POL": 0.23,  # Poland
    "PRT": 0.23,  # Portugal
    "ROU": 0.19,  # Romania
    "SAU": 0.15,  # Saudia Arabia
    "RUS": 0.20,  # Russia
    "SGP": 0.09,  # Singapore
    "SVK": 0.20,  # Slovakia
    "SVN": 0.22,  # Slovenia
    "SWE": 0.25,  # Sweden
    "THA": 0.10,  # Thailand
    "TUR": 0.18,  # Turkey
    "TWN": 0.05,  # Taiwan
    "UKR": 0.20,  # Ukraine
    "USA": 0.07,  # USA
    "VEN": 0.12,  # Venezuela
    "VNM": 0.12,  # Vietnam
    "ZAF": 0.15,  # South Africa
}


@np.vectorize
def get_vat_rate(alpha3_country_code) -> float:
    return round(_vat_rates.get(alpha3_country_code, 0.0), ndigits=2)


FloatOrSeries = TypeVar("FloatOrSeries")
StrOrSeries = TypeVar("StrOrSeries")


def convert_net_to_gross(
    net: FloatOrSeries,
    alpha3_country_code: StrOrSeries,  # type: ignore[reportInvalidTypeVarUse]
) -> FloatOrSeries:
    result = net * (1 + get_vat_rate(alpha3_country_code))
    return js_round(result, 2)


def convert_gross_to_net(
    gross: FloatOrSeries,
    alpha3_country_code: StrOrSeries,  # type: ignore[reportInvalidTypeVarUse]
) -> FloatOrSeries:
    result = gross / (1 + get_vat_rate(alpha3_country_code))
    return js_round(result, 2)
