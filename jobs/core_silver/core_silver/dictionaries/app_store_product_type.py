app_bundle: dict = {
    "1-B": "iOS, iPadOS app bundle",
    "F1-B": "Mac app bundle",
}

free_or_paid_app: dict = {
    "1": "iOS, iPadOS, watchOS",
    "1F": "Universal app, excluding tvOS",
    "1T": "iPad apps",
    "F1": "Mac",
}

paid_app: dict = {
    "1E": "Custom iOS app",
    "1EP": "Custom iPadOS app",
    "1EU": "Custom universal app",
}

inapp_purchase: dict = {
    "FI1": "Mac",
    "IA1": "In-app purchase (iOS, iPadOs)",
    "IA1-M": "In-app purchase (Mac)",
    "IA9": "Non-renewing subscription (iOS, iPadOS)",
    "IA9-M": "Subscription (Mac)",
    "IAY": "Auto-renewable subscription (iOS, iPadOS)",
    "IAY-M": "Auto-renewable subscription (Mac)",
}

update: dict = {
    "7": "App update (iOS, watchOS, tvOS)",
    "7F": "Universal app, excluding tvOS",
    "7T": "App update (iPadOS)",
    "F7": "App update (Mac)",
}

re_download: dict[str, str] = {
    "3": "App update (iOS, watchOS, tvOS)",
    "3F": "Universal app, excluding tvOS",
}

restored_inapp_purchase: dict = {"IA3": "Non consumable in-app purchase"}
