import logging

import pandas as pd

from data_sdk.domain.tables import ExternalCurrencyExchangeRatesTable

DEFAULT_CURRENCY_EXCHANGE_RATE = 1
NO_CURRENCY_TRANSACTION_ISO_4217_CODE = "XXX"

log = logging.getLogger(__name__)


def generate_currency_exchange_rates(
    source_df: pd.DataFrame, table: ExternalCurrencyExchangeRatesTable
):
    rates_df = table.df.to_pandas()

    original_index = source_df.index.copy()

    source_df["original_order"] = range(len(source_df))
    source_df.sort_values(["date", "currency_code"], inplace=True)

    rates_df["datetime"] = pd.to_datetime(rates_df["date"]).astype("datetime64[ns]")
    source_df["datetime"] = pd.to_datetime(source_df["date"]).astype("datetime64[ns]")

    merged_df = (
        pd.merge_asof(
            source_df,
            rates_df,
            on="datetime",
            by="currency_code",
            direction="backward",
        )
        .sort_values(by="original_order")
        .set_index(original_index)
    )

    source_df.sort_values(by="original_order", inplace=True)
    source_df.drop(columns=["original_order", "datetime"], inplace=True)

    rows_with_missing_rates = merged_df[merged_df["rate_to_usd"].isnull()]
    if not rows_with_missing_rates.empty:
        log.warning(
            f"Missing exchange rates for {rows_with_missing_rates['currency_code'].unique()}"
        )

    merged_df["rate_to_usd"] = merged_df["rate_to_usd"].fillna(
        DEFAULT_CURRENCY_EXCHANGE_RATE
    )

    return merged_df["rate_to_usd"]
