import polars as pl

from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.tables import (
    ObservationSalesTable,
    ObservationVisibilityTable,
    ObservationWishlistActionsTable,
    ObservationWishlistCohortsTable,
    SilverPortalsTable,
)


class PortalsAggregator(BaseAggregator):
    table_cls = SilverPortalsTable

    def __init__(
        self,
        observation_sales: ObservationSalesTable,
        observation_visibility: ObservationVisibilityTable,
        observation_wishlist_actions: ObservationWishlistActionsTable,
        observation_wishlist_cohorts: ObservationWishlistCohortsTable,
    ) -> None:
        self._tables = [
            observation_sales,
            observation_visibility,
            observation_wishlist_actions,
            observation_wishlist_cohorts,
        ]

    def _aggregate(self) -> pl.DataFrame:
        lazy_frames = [
            table.df.lazy()
            .select(
                pl.col("portal").cast(pl.String),
                pl.col("platform").cast(pl.String),
                pl.col("region").cast(pl.String),
                pl.col("store").cast(pl.String),
                pl.col("abbreviated_name").cast(pl.String),
            )
            .rename({"store": "store_name"})
            .unique()
            for table in self._tables
        ]

        combined_lazy_df = pl.concat(lazy_frames).unique().collect()

        combined_df = combined_lazy_df.with_columns(
            (
                pl.col("portal") + ":" + pl.col("platform") + ":" + pl.col("region")
            ).alias("portal_platform_region")
        ).sort("portal_platform_region")

        return combined_df
