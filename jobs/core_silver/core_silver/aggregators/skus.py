import emoji
import pandas as pd
import polars as pl

from core_silver.aggregators.sku_types import SkuType
from core_silver.utils.string_format import translate_column
from data_sdk.aggregator import (
    BaseAggregator,
)
from data_sdk.domain.observations import ObservationType
from data_sdk.domain.tables import (
    ExternalSKUsTable,
    ObservationSalesTable,
    ObservationVisibilityTable,
    ObservationWishlistActionsTable,
    ObservationWishlistCohortsTable,
    SilverSKUsTable,
)


def _remove_emoji(text: str) -> str:
    return emoji.replace_emoji(text, replace="")


class SkusAggregator(BaseAggregator):
    table_cls = SilverSKUsTable

    def __init__(
        self,
        observation_sales: ObservationSalesTable,
        observation_visibility: ObservationVisibilityTable,
        observation_wishlist_actions: ObservationWishlistActionsTable,
        observation_wishlist_cohorts: ObservationWishlistCohortsTable,
        external_skus: ExternalSKUsTable,
    ) -> None:
        self._silver_sales: ObservationSalesTable = observation_sales
        self._silver_visibility: ObservationVisibilityTable = observation_visibility
        self._silver_wishlist_actions: ObservationWishlistActionsTable = (
            observation_wishlist_actions
        )
        self._silver_wishlist_cohorts: ObservationWishlistCohortsTable = (
            observation_wishlist_cohorts
        )
        self._external_skus: ExternalSKUsTable = external_skus

    def _aggregate(self) -> pl.DataFrame:
        skus_df = self._get_base_skus()
        skus_df = self._get_most_relevant_rows(skus_df=skus_df)
        skus_df = self._select_skus_with_best_human_name(skus_df)
        skus_df = self._join_with_external_skus(skus_df)

        return pl.DataFrame(skus_df)

    def _get_base_skus(self) -> pd.DataFrame:
        sales_df = self._silver_sales.df.to_pandas()
        sales_df["human_name_indicator"] = ObservationType.SALES
        sales_df["sku_type"] = SkuType.SALES

        visibility_df = self._silver_visibility.df.to_pandas()
        visibility_df["human_name_indicator"] = ObservationType.VISIBILITY
        visibility_df["sku_type"] = SkuType.STORE

        wishlist_actions_df = self._silver_wishlist_actions.df.to_pandas()
        wishlist_actions_df["human_name_indicator"] = ObservationType.WISHLIST_ACTIONS
        wishlist_actions_df["sku_type"] = SkuType.STORE

        wishlist_cohorts_df = self._silver_wishlist_cohorts.df.to_pandas()
        wishlist_cohorts_df["human_name_indicator"] = ObservationType.WISHLIST_COHORTS
        wishlist_cohorts_df["sku_type"] = SkuType.STORE

        observations_df = pd.concat([
            sales_df,
            visibility_df,
            wishlist_actions_df,
            wishlist_cohorts_df,
        ])

        result = pd.DataFrame()
        release_date = self._extract_release_date(observations_df)

        result = result.assign(
            base_sku_id=observations_df["sku_id"],
            human_name=translate_column(observations_df["human_name"], _remove_emoji),
            store_id=observations_df["store_id"],
            unique_sku_id=observations_df["unique_sku_id"],
            studio_id=observations_df["studio_id"],
            portal_platform_region=observations_df["portal"].astype(str)
            + ":"
            + observations_df["platform"].astype(str)
            + ":"
            + observations_df["region"].astype(str),
            human_name_indicator=observations_df["human_name_indicator"],
            sku_type=observations_df["sku_type"],
            date=observations_df["date"],
            release_date=release_date,
            # Consider adding is_discountable here, I can't see why now, but one day you'd read this comment
            # and ask, why I haven't added it before :D
        ).drop_duplicates()
        return result

    def _extract_release_date(self, obs_df):
        if "gross_sales" in obs_df.columns and "gross_returned" in obs_df.columns:
            return (
                obs_df["unique_sku_id"]
                .map(
                    obs_df[obs_df["gross_sales"] + obs_df["gross_returned"] > 0]
                    .groupby("unique_sku_id")["date"]
                    .agg("min")
                )
                .astype(
                    "datetime64[ms]"
                )  # convert to datetime because pandas takes wrong dtype from unique_sku_id
            )

        return None

    def _join_with_external_skus(self, skus_df: pd.DataFrame):
        external_skus_df = self._external_skus.df.to_pandas()

        external_skus_df = external_skus_df[
            ["unique_sku_id", "product_name", "product_type"]
        ]
        return skus_df.merge(
            external_skus_df,
            how="left",
            on=[
                "unique_sku_id",
            ],
        )

    def _get_most_relevant_rows(self, skus_df: pd.DataFrame):
        # Old version was droping duplicates on the whole row `drop_duplicates()`
        # but that was generating multiple rows for the same unique_sku_id if name was changed
        # for given SKU. While usually it was ok, for some cases we wre using older names.
        # While we have test that validates basic scenario, I have no clue how to reproduce
        # this old bug on fake data.
        return (
            skus_df.sort_values("date", na_position="last", ascending=False)
            .drop(["date"], axis=1)
            .drop_duplicates("unique_sku_id")
        )

    def _select_skus_with_best_human_name(self, skus_df: pd.DataFrame) -> pd.DataFrame:
        # groupby doesn't work correctly with Categorical
        skus_df["unique_sku_id"] = skus_df["unique_sku_id"].astype(str)
        skus_df["sort_by"] = skus_df["human_name_indicator"].map(
            lambda human_name_indicator: {
                "sales": 0,
                "visibility": 1,
                "wishlist_actions": 2,
                "wishlist_cohorts": 3,
            }[human_name_indicator]
        )
        return (
            skus_df.sort_values("sort_by", ascending=True)
            .groupby(by=["unique_sku_id"], as_index=False)
            .first()
            .drop(["sort_by"], axis=1)
        )
