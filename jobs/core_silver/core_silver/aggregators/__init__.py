from core_silver.aggregators.acquisition_properties import (
    AcquisitionPropertiesAggregator,
)
from core_silver.aggregators.portals import PortalsAggregator
from core_silver.aggregators.skus import SkusAggregator
from core_silver.aggregators.traffic_source.traffic_source import (
    SilverTrafficSourceAggregator,
)
from data_sdk.aggregator import BaseAggregator

aggregator_list: list[type[BaseAggregator]] = [
    AcquisitionPropertiesAggregator,
    SilverTrafficSourceAggregator,
    SkusAggregator,
    PortalsAggregator,
]
