import polars as pl

from core_silver.aggregators.traffic_source.page_categories import (
    get_page_category_group,
)
from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.tables import ObservationVisibilityTable, SilverTrafficSourceTable


class SilverTrafficSourceAggregator(BaseAggregator):
    table_cls = SilverTrafficSourceTable

    def __init__(self, observation_visibility: ObservationVisibilityTable) -> None:
        self._observation_visibility = observation_visibility

    def _aggregate(self) -> pl.DataFrame:
        observations_df = self._observation_visibility.df
        if observations_df.is_empty():
            return pl.DataFrame()
        dim_ts_df = pl.DataFrame()
        dim_ts_df = dim_ts_df.with_columns([
            observations_df["page_category"].alias("page_category"),
            observations_df["page_category"]
            .apply(get_page_category_group, return_dtype=pl.Utf8)
            .alias("page_category_group"),
            observations_df["page_feature"].alias("page_feature"),
            observations_df["hash_traffic_source"].alias("hash_traffic_source"),
        ])
        return dim_ts_df.unique()
