from enum import Enum


class PageCategoryGroup(Enum):
    TOP_CHARTS = "Top Charts"
    SEARCH_AND_BROWSE = "Search & Browse"
    PERSONALIZED = "Personalized"
    OTHER = "Other"
    HOME_PAGE = "Home Page & Similar"
    EXTERNAL_DIRECT = "External and Direct Navigation"
    COMMUNITY_SOCIAL = "Community & Social"


_page_category_group_page_category_map = {
    PageCategoryGroup.TOP_CHARTS.value: [
        "Coming Soon - Full List",
        "Game DLC List",
        "Games Under $10 USD - Full List",
        "Games Under $5 USD - Full List",
        "New Releases Queue",
        "Popular New Releases - Full List",
        "Popular Tags Page",
        "Specials - Full List",
        "Top Sellers - Full List",
        "Top Sellers (Global) - Full List",
        "Under $10 USD Page",
        "Under $5 USD Page",
        "Upcoming Releases Page",
        "Updated Apps - All",
        "Weeklong Deals - Full Listing",
    ],
    PageCategoryGroup.SEARCH_AND_BROWSE.value: [
        "Browse Search Results",
        "Browse Search Results By Tags",
        "Bundle Page",
        "Direct Search Results",
        "Interactive Recommender",
        "More Like This",
        "Other Product Pages",
        "Package Page",
        "Search Suggestions",
        "Tag Page",
    ],
    PageCategoryGroup.PERSONALIZED.value: [
        "All Upcoming Releases Queue",
        "Cart Page",
        "Discovery Queue",
        "Steam Client - Library",
        "Updated Apps - My Products",
        "Wishlist",
        "Your Account Page",
        "Your Discovery Queue Page",
    ],
    PageCategoryGroup.OTHER.value: [
        "#StoreMetrics_PageType_contenthubs_remoteplayphone",
        "#StoreMetrics_PageType_contenthubs_remoteplaytablet",
        "#StoreMetrics_PageType_contenthubs_remoteplaytogether",
        "#StoreMetrics_PageType_contenthubs_remoteplaytv",
        "(other pages)",
        "Age Gate",
        "Bot Traffic",
        "Free to Play Hub",
        "Friend Activity Page Tabbed Lists",
        "Game IFrame Widget",
        "Hardware Survey",
        "Redeem Wallet Code",
        "Remote Play Hubs",
        "Stats Page",
        "Video Hub",
    ],
    PageCategoryGroup.HOME_PAGE.value: [
        "About Steam",
        "Autumn Sale Home Page",
        "Content Hubs",
        "Demos Page",
        "Early Access Hub",
        "Free Demos Hub",
        "Games Hub",
        "Genre Page (Early Access or F2P)",
        "Home Page",
        "Join Steam Page",
        "Linux Hub",
        "Login Page",
        "Mac page",
        "MacOS Hub",
        "Marketing Message",
        "Mobile Storefront",
        "New on Steam Page",
        "News Hub and Events",
        "PC Café Hub",
        "Promotion Page",
        "Recommendation Feed",
        "Recommendations - Main",
        "Recommendations - View Friend Recommendaion",
        "Sales Page",
        "Salien Game",
        "Soundtracks Hub",
        "Special Offers Hub",
        "Steam Awards Page",
        "Steam Controller Friendly Hub",
        "Summer Sale Home Page",
        "VR Hub",
        "Winter Sale Home Page",
    ],
    PageCategoryGroup.EXTERNAL_DIRECT.value: [
        "Direct Navigation",
        "E-mail",
        "External Website",
        "New Release Notification Email",
        "Valve Website",
        "Wishlist Item On Sale Email",
    ],
    PageCategoryGroup.COMMUNITY_SOCIAL.value: [
        "A Steam Curator or Developer Homepage",
        "Community - Friend Activity Feed",
        "Community - User Generated Content",
        "Community Hub",
        "Community Hub - Discussions",
        "Friend Activity Page",
        "Steam Client - Friend Is In-Game Notification",
        "Steam Client - Friends &amp; Chat",
        "Steam Labs",
        "Steam Labs - Community Recommendations",
        "Steam Labs - Microtrailers",
    ],
}


def get_page_category_group(page_category: str):
    """
    Returns page category group for a given page category.
    >>> get_page_category_group('Game DLC List')
    'Top Charts'
    >>> get_page_category_group('some unknown category')
    'Other'
    """
    for key in _page_category_group_page_category_map:
        if page_category in _page_category_group_page_category_map[key]:
            return key
    return PageCategoryGroup.OTHER.value
