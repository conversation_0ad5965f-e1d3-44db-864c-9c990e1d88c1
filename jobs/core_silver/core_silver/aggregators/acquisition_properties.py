import polars as pl

from data_sdk.aggregator import BaseAggregator
from data_sdk.domain import TableMetadata, TableName
from data_sdk.domain.tables import (
    ObservationSalesTable,
    SilverAcquisitionPropertiesTable,
)


class AcquisitionPropertiesAggregator(BaseAggregator):
    table_cls = SilverAcquisitionPropertiesTable
    table_metadata = TableMetadata(
        table_name=TableName.SILVER_ACQUISITION_PROPERTIES,
    )

    def __init__(
        self,
        observation_sales: ObservationSalesTable,
    ) -> None:
        self._observation_sales = observation_sales

    def _aggregate(self) -> pl.DataFrame:
        observations_df = self._observation_sales.df.to_pandas()
        if observations_df.empty:
            return pl.DataFrame()
        dim_acquisition_properties = observations_df[
            [
                "transaction_type",
                "tax_type",
                "sale_modificator",
                "acquisition_platform",
                "acquisition_origin",
                "iap_flag",
                "hash_acquisition_properties",
            ]
        ].drop_duplicates()
        return pl.DataFrame(dim_acquisition_properties)
