import sentry_sdk

sentry_sdk.init(traces_sample_rate=1.0)

import warnings

warnings.simplefilter("ignore", FutureWarning)

import logging
from pathlib import Path

from pipeline_sdk.job import JobInput, JobOutput, JobRunner
from pipeline_sdk.monitoring.elastic_tracer import elastic_tracer
from pipeline_sdk.monitoring.logs import configure_logger

from core_silver.config import Config
from core_silver.job import JobInputParameters, run
from data_sdk.config import DLSConfig

configure_logger(
    "core_silver",
    custom_loggers_config={
        "data_sdk": {"level": "INFO"},
    },
)
log = logging.getLogger(__name__)


def handler(job_input: JobInput) -> JobOutput:
    config = Config()
    log.info(
        "Starting core_silver: %s (version %s, build ts: %s)",
        job_input.job_guid,
        config.docker_tag,
        config.docker_build_timestamp,
    )

    if (
        isinstance(config.output_cfg, DLSConfig)
        and config.output_cfg.base_dir == Path()
    ):
        config.output_cfg.base_dir = "result"

    params: JobInputParameters = JobInputParameters.model_validate(job_input.target)
    run(params=params, config=config)

    log.info("Finishing core_silver: %s", job_input.job_guid)
    return JobOutput({"studio_id": params.studio_id})


if __name__ == "__main__":
    with elastic_tracer():
        runner = JobRunner(handler)
        runner.run()
