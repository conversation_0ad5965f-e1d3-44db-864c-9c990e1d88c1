import logging
from typing import Any

import pandera as pa
import pandera.polars as pap
import polars as pl

from core_silver import reporters
from core_silver.aggregators.skus import SilverSKUsTable
from core_silver.external_sources.connectors.report_service import (
    ReportServiceClient,
)
from data_sdk.domain import Portal
from data_sdk.domain.tables import ExternalSKUsTable
from data_sdk.reporter import BaseReporter

log = logging.getLogger(__name__)


class CreatedSkuModel(pap.DataFrameModel):
    """SKU created model accepted by reports service SKU endpoint"""

    sku_studio: str = pa.Field()  # from unique_sku_id
    base_sku_id: str = pa.Field()
    studio_id: int = pa.Field()
    human_name: str = pa.Field()
    human_name_indicator: str = pa.Field()
    store_id: str = pa.Field()
    portal: str = pa.Field()
    product_name: str = pa.Field(nullable=True)
    product_type: str = pa.Field(nullable=True)
    sku_type: str = pa.Field()

    class Config:
        coerce = True
        strict = True


class UpdatedSkuModel(pap.DataFrameModel):
    """SKU updated model accepted by reports service SKU endpoint"""

    sku_studio: str = pa.Field()  # unique_sku_id
    human_name: str = pa.Field()
    human_name_indicator: str = pa.Field()
    store_id: str = pa.Field()

    class Config:
        coerce = True
        strict = True


class SkuReportServiceReporter(BaseReporter):
    def __init__(
        self,
        portal: Portal,
        external_skus: ExternalSKUsTable,
        silver_skus: SilverSKUsTable,
        report_service_client: ReportServiceClient,
    ):
        self._portal = portal
        self._external_skus: ExternalSKUsTable = external_skus
        self._silver_skus: SilverSKUsTable = silver_skus
        self._report_service_client: ReportServiceClient = report_service_client

    def _process(self) -> None:
        log.info("Reporting SKUs...")

        new_skus = self._get_new_skus()
        updated_skus = self._get_updated_skus()

        log.info(
            f"Sending SKU to report service..., {new_skus} new skus, {updated_skus} updated skus"
        )

        if reporters.REPORTING_ENABLED:
            self._report_service_client.send_sku(new_skus, updated_skus)

    def _get_new_skus(self) -> list[dict[str, Any]]:
        filtered = (
            self._silver_skus.df.filter(
                ~self._silver_skus.df["unique_sku_id"].is_in(
                    self._external_skus.df["unique_sku_id"]
                )
            )
            .with_columns(
                pl.lit(self._portal.value).alias("portal"),
                pl.col("unique_sku_id").alias("sku_studio"),
            )
            .select(*CreatedSkuModel.to_schema().columns)
        )
        validated = CreatedSkuModel.validate(filtered)
        return validated.to_dicts()

    def _get_updated_skus(self) -> list[dict[str, Any]]:
        columns_to_check = ["human_name", "human_name_indicator", "store_id"]

        filtered = (
            self._silver_skus.df.join(
                self._external_skus.df,
                on="unique_sku_id",
                how="inner",
                suffix="_external",
            )
            .filter(
                pl.any_horizontal(
                    pl.col(col) != pl.col(f"{col}_external") for col in columns_to_check
                )
            )
            .select(pl.col("unique_sku_id").alias("sku_studio"), *columns_to_check)
        )
        validated = UpdatedSkuModel.validate(filtered)
        return validated.to_dicts()
