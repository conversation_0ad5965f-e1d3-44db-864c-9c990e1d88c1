import logging

import polars as pl

from core_silver import reporters
from core_silver.external_sources.connectors.report_service import ReportServiceClient
from data_sdk.domain.tables import ExternalReportsTable, SilverReportsTable
from data_sdk.reporter import BaseReporter

log = logging.getLogger(__name__)


class StatusReportServiceReporter(BaseReporter):
    def __init__(
        self,
        report_service_client: ReportServiceClient,
        external_reports: ExternalReportsTable,
        silver_reports: SilverReportsTable,
    ) -> None:
        self.report_service_client = report_service_client
        self.external_reports = external_reports
        self.silver_reports = silver_reports

    def _process(self) -> None:
        log.info("Sending report status to report service...")

        if reporters.REPORTING_ENABLED:
            updated_reports = self.get_updated_reports()
            self.report_service_client.send_reports(updated_reports)

    def get_updated_reports(self):
        silver = self.silver_reports.df[["report_id", "state"]]
        external = self.external_reports.df[["report_id", "state"]]
        merged = silver.join(external, on="report_id", how="left", suffix="_ex")

        updated = merged.filter(
            (pl.col("state") != pl.col("state_ex")) | pl.col("state_ex").is_null()
        )

        return updated.select(pl.col("report_id").alias("id"), "state").to_dicts()
