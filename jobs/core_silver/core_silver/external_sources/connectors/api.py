import logging
from typing import Literal

import requests
from pydantic import AnyHttpUrl, HttpUrl
from pydantic_settings import BaseSettings

log = logging.getLogger(__name__)


class StaticConfig(BaseSettings):
    type: Literal["static"] = "static"


class APIConfig(BaseSettings):
    type: Literal["api"] = "api"
    url: AnyHttpUrl
    api_key: str


class APIClient:
    def __init__(self, base_url: HttpUrl, extra_headers: dict) -> None:
        self._base_url = base_url
        self._extra_headers: dict = extra_headers

    def get(self, url: str, **kwargs) -> requests.Response:
        return self.request("get", url=url, **kwargs)

    def get_paged(
        self, url: str, *, limit: int = 100, offset: int = 0, **kwargs
    ) -> list[dict]:
        """Fetch paginated data from REST service.

        Args:
            url: Service endpoint URL (without base_url)
            limit: Maximum number of fetched records at once, defaults to 100
            offset: Data offset, defaults to 0
            kwargs: passed to requests.get
        Returns:
            Concatenated records returned by the service
        """
        params = kwargs.get("params", {})
        params.update({"limit": limit, "offset": offset})
        kwargs["params"] = params

        data = []

        while True:
            response = self.request("get", url=url, **kwargs)
            response_data = response.json()
            data.extend(response_data["data"])

            if len(data) >= response_data["count"] or response_data["count"] == 0:
                break

            kwargs["params"]["offset"] += limit

        return data

    def post(self, url: str, **kwargs) -> requests.Response:
        return self.request("post", url=url, **kwargs)

    def put(self, url: str, **kwargs) -> requests.Response:
        return self.request("put", url=url, **kwargs)

    def patch(self, url: str, **kwargs) -> requests.Response:
        return self.request("patch", url=url, **kwargs)

    def delete(self, url: str, **kwargs) -> requests.Response:
        return self.request("delete", url=url, **kwargs)

    def request(self, method: str, url: str, **kwargs) -> requests.Response:
        log.debug("Running %s, %s", method, url)
        headers = {
            "Content-Type": "application/json",
            **self._extra_headers,
        }
        response = requests.request(
            method=method,
            url=f"{self._base_url}{url}",
            headers=headers,
            timeout=300,
            **kwargs,
        )
        response.raise_for_status()

        return response


class ConnectorError(Exception):
    def __init__(self, message: str | None = None):
        self.message = message

    def __repr__(self) -> str:
        s = self.__class__.__name__
        return s if not self.message else f'{s}: "{self.message}"'

    __str__ = __repr__
