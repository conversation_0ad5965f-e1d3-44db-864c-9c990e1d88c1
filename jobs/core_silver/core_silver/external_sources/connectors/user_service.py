from __future__ import annotations

import abc
import logging
from datetime import datetime
from functools import singledispatchmethod
from typing import Any

from dateutil.tz import tzutc
from pydantic import BaseModel, ValidationError

from core_silver.external_sources.connectors.api import (
    APIClient,
    APIConfig,
    ConnectorError,
    StaticConfig,
)
from data_sdk.domain.domain_error import DomainError
from data_sdk.domain.domain_types import OrganizationId, StudioId, UserId
from data_sdk.domain.users import (
    BaseOrganization,
    BaseUser,
    Organization,
    Permission,
    PermissionName,
    Role,
    RoleAssignment,
    User,
)

NAMESPACE_NAME = "data_pipeline"

log = logging.getLogger(__name__)


class FeatureFlag(BaseModel):
    namespace: str
    name: str


class FeatureFlagEntity(BaseModel):
    key: str
    namespace: str = NAMESPACE_NAME
    organization_id: OrganizationId
    version: int
    value: str = "True"


class FeatureFlagsResponse(BaseModel):
    data: list[FeatureFlagEntity]

    def to_feature_flags(self) -> list[FeatureFlag]:
        return [
            FeatureFlag(
                name=flag.key.split(".")[-1],
                namespace=flag.namespace,
            )
            for flag in self.data
        ]


class InsufficientStudioPermission(ConnectorError):
    def __init__(self):
        super().__init__(
            "Trying to process data for user without appropriate permission"
        )


class UserServiceClient(abc.ABC):
    @singledispatchmethod
    @staticmethod
    def get_client(config: Any) -> UserServiceClient:
        raise ValueError("Invalid User")  # noqa: TRY003

    @get_client.register
    @staticmethod
    def _(config: StaticConfig):
        return StaticUserServiceClient()

    @get_client.register
    @staticmethod
    def _(config: APIConfig):
        return APIUserServiceClient(config)

    @abc.abstractmethod
    def get_user(self, studio_id: StudioId) -> User:
        pass

    @abc.abstractmethod
    def get_organization(self, studio_id: StudioId) -> Organization:
        pass

    @abc.abstractmethod
    def get_permissions(
        self, user_id: UserId, organization_id: OrganizationId
    ) -> list[Permission]:
        pass

    @abc.abstractmethod
    def get_view_dashboard_role_assignments(
        self, organization_id: OrganizationId
    ) -> list[RoleAssignment]:
        pass

    @abc.abstractmethod
    def get_feature_flags(self, organization_id: OrganizationId) -> list[FeatureFlag]:
        """Get feature flags for the organization."""
        pass


class InvalidAccountSharingData(DomainError):
    def __init__(self):
        super().__init__("Invalid account sharing data")


class APIUserServiceClient(UserServiceClient):
    def __init__(self, user_service_config: APIConfig) -> None:
        self._api = APIClient(
            base_url=user_service_config.url,
            extra_headers={
                "x-api-key": user_service_config.api_key,
            },
        )

    def get_user(self, studio_id: StudioId) -> User:
        log.info(f"Getting user for studio {studio_id}")
        studio = self._api.get(f"user/legacy/{studio_id}")
        studio.raise_for_status()
        return User.model_validate(studio.json())

    def get_organization(self, studio_id: StudioId) -> Organization:
        log.info(f"Getting organization for studio {studio_id}")
        organization = self._api.get(url=f"organization/legacy/{studio_id}")
        organization.raise_for_status()
        return Organization.model_validate(organization.json())

    def get_permissions(
        self,
        user_id: str,
        organization_id: OrganizationId,
    ) -> list[Permission]:
        log.info(f"Getting permissions for user {user_id}")
        permissions = self._api.get(
            url=f"user/{user_id}/permissions",
            params={"organization_id": organization_id},
        )
        permissions.raise_for_status()
        valid_permissions = []
        for p in permissions.json():
            try:
                valid_permission = Permission.model_validate(p)
                valid_permissions.append(valid_permission)
            except ValidationError as e:
                if any(error["loc"] == ("name",) for error in e.errors()):
                    continue
                raise
        return valid_permissions

    def get_view_dashboard_role_assignments(
        self, organization_id: OrganizationId
    ) -> list[RoleAssignment]:
        log.info(
            f"Getting view dashboard role assignments for organization {organization_id}"
        )
        role_assignments = self._api.get_paged(
            url="role-assignment",
            params={
                "permission": PermissionName.DATASET_MANAGER_VIEW_DASHBOARDS,
                "organization_id": organization_id,
            },
        )

        for role_assignment in role_assignments:
            match role_assignment["filter"]:
                case None:
                    role_assignment["filter"] = None
                case str() as product:
                    role_assignment["filter"] = {"product": [product]}
                case {"product": str(product)}:
                    role_assignment["filter"] = {"product": [product]}
                case {"product": list(products)}:
                    role_assignment["filter"] = {"product": products}
                case _:
                    raise InvalidAccountSharingData

        return [
            RoleAssignment.model_validate(role_assignment)
            for role_assignment in role_assignments
        ]

    def get_feature_flags(self, organization_id: OrganizationId) -> list[FeatureFlag]:
        log.info(f"Getting feature flags for organization {organization_id}")
        response = self._api.get(
            url="organization-key-value/search",
            params={
                "namespace": NAMESPACE_NAME,
                "organization_id": organization_id,
                "key": "feautres.*",
                "value": "True",
            },
        )
        response.raise_for_status()
        return FeatureFlagsResponse.model_validate(response.json()).to_feature_flags()


class StaticUserServiceClient(UserServiceClient):
    def get_user(self, studio_id: StudioId) -> User:
        return User(
            email="<EMAIL>",
            first_name="R",
            last_name="Bi",
            company_name="IndieBI Test DEV",
            test_account=True,
            id=UserId("u-iVi2pB"),
            legacy_id=studio_id,
            verified=True,
            email_verification_token="123456",  # noqa: S106
            email_verification_deadline=datetime(
                2022, 10, 21, 11, 53, 25, tzinfo=tzutc()
            ),
            reset_password_token="sample_token",  # noqa: S106
            reset_password_deadline=datetime(2023, 2, 4, 13, 3, 19, tzinfo=tzutc()),
            agreement_date=datetime(2022, 10, 18, 11, 53, 25, tzinfo=tzutc()),
            unsuccessful_verification_attempts=0,
            force_password_change=False,
        )

    def get_organization(self, studio_id: StudioId) -> Organization:
        return Organization(
            name="IndieBI Test DEV",
            legacy_id=studio_id,
            id=OrganizationId("o-EGQfNf"),
        )

    def get_permissions(
        self, user_id: UserId, organization_id: OrganizationId
    ) -> list[Permission]:
        return [
            Permission(
                organization_id=organization_id,
                name=PermissionName.REPORT_SERVICE_UPLOAD_REPORTS,
                filters=[],
            ),
            Permission(
                organization_id=organization_id,
                name=PermissionName.REPORT_SERVICE_READ_REPORTS,
                filters=[],
            ),
            Permission(
                organization_id=organization_id,
                name=PermissionName.REPORT_SERVICE_READ_SKUS,
                filters=[],
            ),
            Permission(
                organization_id=organization_id,
                name=PermissionName.REPORT_SERVICE_MODIFY_REPORTS,
                filters=[],
            ),
            Permission(
                organization_id=organization_id,
                name=PermissionName.REPORT_SERVICE_MODIFY_SKUS,
                filters=[],
            ),
            Permission(
                organization_id=organization_id,
                name=PermissionName.REPORT_SERVICE_UPLOAD_REPORTS,
                filters=[],
            ),
            Permission(
                organization_id=organization_id,
                name=PermissionName.USER_SERVICE_V2_ASSIGN_PERMISSIONS,
                filters=[],
            ),
        ]

    def get_view_dashboard_role_assignments(
        self, organization_id: OrganizationId
    ) -> list[RoleAssignment]:
        return [
            RoleAssignment(
                user_id=UserId("u-t8eA5o"),
                organization_id=OrganizationId("o-EQfNf"),
                role=Role.EXTERNAL_READER,
                filter=None,
                created_by_user_id=None,
                id=45098277,
                created_at=datetime(2024, 2, 7, 11, 50, 0, 863000, tzinfo=tzutc()),
                updated_at=datetime(2024, 2, 7, 11, 50, 0, 863000, tzinfo=tzutc()),
                user=BaseUser(legacy_id=StudioId(2), email="<EMAIL>"),
                organization=BaseOrganization(
                    legacy_id=StudioId(1),
                    name="IndieBI Test DEV",
                ),
            ),
            RoleAssignment(
                user_id=UserId("u-iVi2pB"),
                organization_id=OrganizationId("o-EGQfNf"),
                role=Role.OWNER,
                filter=None,
                created_by_user_id=None,
                id=45100611,
                created_at=datetime(2024, 2, 7, 13, 51, 24, 147000, tzinfo=tzutc()),
                updated_at=datetime(2024, 2, 7, 13, 51, 24, 147000, tzinfo=tzutc()),
                user=BaseUser(
                    legacy_id=StudioId(1),
                    email="<EMAIL>",
                ),
                organization=BaseOrganization(
                    legacy_id=StudioId(1), name="IndieBI Test DEV"
                ),
            ),
        ]

    def get_feature_flags(self, organization_id: OrganizationId) -> list[FeatureFlag]:
        return [FeatureFlag(name="", namespace="")]
