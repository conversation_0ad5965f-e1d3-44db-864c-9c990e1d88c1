import polars as pl

from core_silver.external_sources.base import BaseExternalSourceProcessor
from data_sdk.crawled_public_data.reader import CrawledPublicDataReader, PublicDataType
from data_sdk.domain.tables import ExternalCurrencyExchangeRatesTable


class CurrencyExchangeRatesExternalProcessor(BaseExternalSourceProcessor):
    table = ExternalCurrencyExchangeRatesTable

    def __init__(self, reader: CrawledPublicDataReader) -> None:
        self.reader = reader
        super().__init__()

    def _process(self, *_args, **_kwargs) -> pl.DataFrame:
        return self.reader.read_resource(PublicDataType.CURRENCY_EXCHANGE_RATES)
