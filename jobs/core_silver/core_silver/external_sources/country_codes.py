import polars as pl

from core_silver import get_project_root
from core_silver.external_sources.base import BaseExternalSourceProcessor
from data_sdk.domain.domain_types import StudioId
from data_sdk.domain.tables import ExternalCountryCodesTable


class CountryCodesExternalProcessor(BaseExternalSourceProcessor):
    table = ExternalCountryCodesTable

    def _process(self, studio_id: StudioId) -> pl.DataFrame:
        return (
            pl.scan_csv(
                source=get_project_root()
                / "core_silver/external_sources/static/country_codes.csv",
            )
            .rename(
                mapping={
                    "Country": "country",
                    "Alpha-2 code": "alpha_2_code",
                    "Numeric": "numeric",
                }
            )
            .select([
                "country",
                "alpha_2_code",
                "country_code",
                "numeric",
                "country_currency",
                "region",
            ])
        ).collect()
