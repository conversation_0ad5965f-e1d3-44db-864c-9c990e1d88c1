from abc import ABC, abstractmethod
from typing import Type

import polars as pl

from data_sdk.domain.domain_types import StudioId
from data_sdk.domain.tables import TableDefinition
from data_sdk.segmentator import BaseSegmentator, SingleSegmentator


class BaseExternalSourceProcessor(ABC):
    table: Type[TableDefinition]
    segmentator: BaseSegmentator = SingleSegmentator()

    def process(self, studio_id: StudioId) -> TableDefinition:
        df = self._process(studio_id=studio_id)
        return self.table(df=df)

    @abstractmethod
    def _process(self, studio_id: StudioId) -> pl.DataFrame:
        pass
