from datetime import datetime
from inspect import get_annotations
from typing import Any

from elasticapm import capture_span

from core_silver.external_sources.base import BaseExternalSourceProcessor
from core_silver.external_sources.connectors.report_service import ReportServiceClient
from core_silver.external_sources.connectors.user_service import UserServiceClient
from core_silver.external_sources.country_codes import CountryCodesExternalProcessor
from core_silver.external_sources.currency_exchange_rates import (
    CurrencyExchangeRatesExternalProcessor,
)
from core_silver.external_sources.feature_flags import FeatureFlagsExternalProcessor
from core_silver.external_sources.reports import ReportsExternalProcessor
from core_silver.external_sources.shared import SharedExternalProcessor
from core_silver.external_sources.skus import SKUsExternalProcessor
from core_silver.external_sources.steam_events import SteamEventsExternalProcessor
from core_silver.external_sources.studios import StudiosExternalProcessor
from data_sdk.crawled_public_data.reader import CrawledPublicDataReader
from data_sdk.custom_partition.partitioner import get_table_partition
from data_sdk.custom_partition.writer import CustomPartitionsWriter
from data_sdk.domain.domain_types import StudioId


def init_with_dependencies(dependencies: dict, klass: type):
    init_kwargs = {}
    if hasattr(klass, "__init__"):
        init_kwargs = {
            key: dependencies[value]
            for key, value in get_annotations(klass.__init__).items()
            if key != "return"  # ignore return annotation type
        }
    return klass(**init_kwargs)


def process_external_sources(
    studio_id: StudioId,
    report_service_client: ReportServiceClient,
    user_service_client: UserServiceClient,
    writer: CustomPartitionsWriter,
    crawled_public_data_reader: CrawledPublicDataReader,
    creation_datetime: datetime,
):
    processor_list: list[type[BaseExternalSourceProcessor]] = [
        StudiosExternalProcessor,
        SharedExternalProcessor,
        ReportsExternalProcessor,
        SKUsExternalProcessor,
        CountryCodesExternalProcessor,
        SteamEventsExternalProcessor,
        CurrencyExchangeRatesExternalProcessor,
        FeatureFlagsExternalProcessor,
    ]
    dependencies: dict[Any, Any] = {
        ReportServiceClient: report_service_client,
        UserServiceClient: user_service_client,
        CrawledPublicDataReader: crawled_public_data_reader,
    }
    for processor_class in processor_list:
        with capture_span(processor_class.__name__):
            processor: BaseExternalSourceProcessor = init_with_dependencies(
                dependencies=dependencies, klass=processor_class
            )
            result_table = processor.process(studio_id)
            partition = get_table_partition(result_table, studio_id=studio_id)

            segments = processor.segmentator.create_segments(
                table=result_table, creation_datetime=creation_datetime
            )

            writer.save_table(result_table, partition, segments)
