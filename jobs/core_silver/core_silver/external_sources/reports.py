import polars as pl

from core_silver.external_sources.base import BaseExternalSourceProcessor
from core_silver.external_sources.connectors.report_service import ReportServiceClient
from data_sdk.domain.domain_types import StudioId
from data_sdk.domain.tables import ExternalReportsTable


class ReportsExternalProcessor(BaseExternalSourceProcessor):
    table = ExternalReportsTable

    def __init__(self, reports_service_client: ReportServiceClient) -> None:
        self._reports_service_client = reports_service_client
        super().__init__()

    def _process(self, studio_id: StudioId) -> pl.DataFrame:
        df = self._reports_service_client.get_report_metadata(
            studio_id=studio_id
        ).to_lazy_frame()

        return df.collect()
