import polars as pl

from core_silver.external_sources.base import BaseExternalSourceProcessor
from core_silver.external_sources.connectors.user_service import UserServiceClient
from data_sdk.domain.domain_types import StudioId
from data_sdk.domain.tables import ExternalFeatureFlagsTable


class FeatureFlagsExternalProcessor(BaseExternalSourceProcessor):
    table = ExternalFeatureFlagsTable

    def __init__(self, user_service_client: UserServiceClient) -> None:
        self._user_service_client = user_service_client
        super().__init__()

    def _process(self, studio_id: StudioId) -> pl.DataFrame:
        feature_flags = self._user_service_client.get_feature_flags(
            organization_id=self._user_service_client.get_organization(
                studio_id=studio_id
            ).id
        )
        return pl.DataFrame(
            {"name": flag.name, "namespace": flag.namespace} for flag in feature_flags
        )
