from datetime import datetime

import polars as pl
from dateutil.tz import tzutc

from core_silver.external_sources.base import BaseExternalSourceProcessor
from core_silver.external_sources.connectors.user_service import UserServiceClient
from data_sdk.domain.domain_types import ProductName, StudioId
from data_sdk.domain.tables import ExternalSharedTable
from data_sdk.domain.users import RoleAssignment, Shared


class SharedExternalProcessor(BaseExternalSourceProcessor):
    table = ExternalSharedTable

    def __init__(self, user_service_client: UserServiceClient) -> None:
        self._user_service_client = user_service_client
        super().__init__()

    def _get_shared(self, studio_id: StudioId) -> list[Shared]:
        organization = self._user_service_client.get_organization(studio_id=studio_id)
        role_assignments: list[RoleAssignment] = (
            self._user_service_client.get_view_dashboard_role_assignments(
                organization_id=organization.id
            )
        )
        shared: list[Shared] = []

        for role_assignment in role_assignments:
            if role_assignment.filter:
                for product in role_assignment.filter["product"]:
                    shared.append(
                        self._create_shared_entry(role_assignment, ProductName(product))
                    )
            else:
                shared.append(self._create_shared_entry(role_assignment))

        return shared

    def _create_shared_entry(
        self, role_assignment: RoleAssignment, product_name: ProductName | None = None
    ) -> Shared:
        return Shared(
            id=role_assignment.id,
            shared_with_id=role_assignment.user.legacy_id,
            shared_by_id=role_assignment.organization.legacy_id,
            product_name=product_name,
            date_from=datetime(1900, 1, 1, tzinfo=tzutc()),
            date_to=datetime(2099, 1, 31, tzinfo=tzutc()),
            portal_id=None,
            game_id=None,
        )

    def _process(self, studio_id: StudioId) -> pl.DataFrame:
        return pl.DataFrame(self._get_shared(studio_id=studio_id))
