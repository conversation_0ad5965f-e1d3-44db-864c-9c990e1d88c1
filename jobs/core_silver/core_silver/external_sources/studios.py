import polars as pl

from core_silver.external_sources.base import BaseExternalSourceProcessor
from core_silver.external_sources.connectors.user_service import (
    InsufficientStudioPermission,
    UserServiceClient,
)
from data_sdk.domain.domain_types import OrganizationId, StudioId, UserId
from data_sdk.domain.tables import ExternalStudiosTable
from data_sdk.domain.users import PermissionName, Studio


class StudiosExternalProcessor(BaseExternalSourceProcessor):
    table = ExternalStudiosTable

    def __init__(self, user_service_client: UserServiceClient) -> None:
        self._user_service_client = user_service_client
        super().__init__()

    def _has_permission(
        self,
        user_id: UserId,
        organization_id: OrganizationId,
        permission: PermissionName,
    ) -> bool:
        list_of_permissions = self._user_service_client.get_permissions(
            user_id=user_id, organization_id=organization_id
        )

        return any(p.name == permission for p in list_of_permissions)

    def get_studio_metadata(self, studio_id: StudioId) -> Studio:
        user = self._user_service_client.get_user(studio_id)
        organization = self._user_service_client.get_organization(studio_id)

        if not self._has_permission(
            user_id=user.id,
            organization_id=OrganizationId(organization.id),
            permission=PermissionName.REPORT_SERVICE_UPLOAD_REPORTS,
        ):
            raise InsufficientStudioPermission

        return Studio(
            studio_id=user.legacy_id,
            organization_id=organization.id,
            email=user.email,
            company_name=user.company_name,
            is_test_account=user.test_account,
            is_verified=user.verified,
            agreement_date=user.agreement_date,
            studio_parent_id=None,
        )

    def _process(self, studio_id: StudioId) -> pl.DataFrame:
        return pl.DataFrame([self.get_studio_metadata(studio_id)])
