import polars as pl

from core_silver.external_sources.base import BaseExternalSourceProcessor
from core_silver.external_sources.connectors.report_service import ReportServiceClient
from data_sdk.domain.domain_types import StudioId
from data_sdk.domain.tables import ExternalSKUsTable


class SKUsExternalProcessor(BaseExternalSourceProcessor):
    table = ExternalSKUsTable

    def __init__(self, reports_service_client: ReportServiceClient) -> None:
        self._reports_service_client = reports_service_client
        super().__init__()

    def _process(self, studio_id: StudioId) -> pl.DataFrame:
        skus = pl.DataFrame(self._reports_service_client.get_skus(studio_id=studio_id))
        if not skus.is_empty():
            # Rename the column to match updated naming
            skus = skus.rename({"sku_studio": "unique_sku_id"})
        return skus
