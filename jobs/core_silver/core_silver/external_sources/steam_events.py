import polars as pl

from core_silver import get_project_root
from core_silver.external_sources.base import BaseExternalSourceProcessor
from data_sdk.domain.domain_types import StudioId
from data_sdk.domain.tables import ExternalSteamEventsTable


class SteamEventsExternalProcessor(BaseExternalSourceProcessor):
    table = ExternalSteamEventsTable

    def _process(self, studio_id: StudioId) -> pl.DataFrame:
        return (
            pl.scan_csv(
                source=get_project_root()
                / "core_silver/external_sources/static/steam_events_history.csv",
            )
        ).collect()
