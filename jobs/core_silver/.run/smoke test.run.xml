<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="smoke test" type="tests" factoryName="Autodetect">
    <module name="core_silver" />
    <option name="ENV_FILES" value="" />
    <option name="INTERPRETER_OPTIONS" value="" />
    <option name="PARENT_ENVS" value="true" />
    <option name="SDK_HOME" value="" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/jobs/core_silver" />
    <option name="IS_MODULE_SDK" value="true" />
    <option name="ADD_CONTENT_ROOTS" value="true" />
    <option name="ADD_SOURCE_ROOTS" value="true" />
    <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
    <option name="_new_additionalArguments" value="&quot;--runslow --log-cli-level\u003dinfo -vv&quot;" />
    <option name="_new_target" value="&quot;test_smoke_test.test_smoke_test&quot;" />
    <option name="_new_targetType" value="&quot;PYTHON&quot;" />
    <method v="2" />
  </configuration>
</component>
