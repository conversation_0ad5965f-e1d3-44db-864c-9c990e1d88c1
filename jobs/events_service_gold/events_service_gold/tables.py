from enum import Enum
from typing import ClassVar, Literal

from data_sdk.aggregator import StrictBaseSchema
from data_sdk.domain.tables import TablePartitionedByStudio
from data_sdk.validators.fields import (
    BasicField,
    MediumStringField,
    NonNegativeNumberField,
    SmallStringField,
    TinyStringField,
)
from polars import Date, Datetime


class EventServiceGoldTable(str, Enum):
    BASELINE = "baseline"
    DETECTED_EVENTS = "detected_events"
    EXTERNAL_STEAM_EVENTS = "external_steam_events"
    SKUS = "skus"
    SCRAPED_EVENTS = "scraped_events"
    SCRAPED_EVENTS_DETAILS = "scraped_events_details"
    SCRAPED_DATES = "scraped_dates"
    STORES = "stores"


class EventsServiceBaselineModel(StrictBaseSchema):
    country_code: str = BasicField(str_length={"min_value": 3, "max_value": 3})
    studio_id: int = NonNegativeNumberField()
    sku_studio: str = MediumStringField()
    unique_sku_id: str = MediumStringField()
    date: str = TinyStringField(nullable=True)
    portal: str = MediumStringField()
    date_sku_studio: str = MediumStringField()
    portal_platform_region: str = MediumStringField()
    portal_platform_region_id: int = BasicField(ge=101000, le=999999)
    product_id: str = MediumStringField()
    baseline_units_sold_directly: int = BasicField()
    baseline_units_sold_non_billable: int = BasicField()
    baseline_units_sold_retail: int = BasicField()
    baseline_units_returned: int = BasicField()
    baseline_gross_sales: float = BasicField()
    baseline_gross_returned: float = BasicField()
    baseline_free_units: int = BasicField()
    baseline_net_sales_approx: float = BasicField()
    uplift_units_sold_directly: int = BasicField()
    uplift_units_sold_non_billable: int = BasicField()
    uplift_units_sold_retail: int = BasicField()
    uplift_units_returned: int = BasicField()
    uplift_gross_sales: float = BasicField()
    uplift_gross_returned: float = BasicField()
    uplift_free_units: int = BasicField()
    uplift_net_sales_approx: float = BasicField()


class EventsServiceBaselineTable(TablePartitionedByStudio):
    table_name: ClassVar[Literal[EventServiceGoldTable.BASELINE]] = (
        EventServiceGoldTable.BASELINE
    )
    model = EventsServiceBaselineModel


class EventsServiceExternalSteamEventsModel(StrictBaseSchema):
    start_date: Date
    end_date: Date
    major: bool = BasicField()
    name: str = MediumStringField()
    start_day: int = NonNegativeNumberField()
    start_month: int = NonNegativeNumberField()
    start_year: int = NonNegativeNumberField()
    end_day: int = NonNegativeNumberField()
    end_month: int = NonNegativeNumberField()
    end_year: int = NonNegativeNumberField()
    portal: str = MediumStringField()


class EventsServiceExternalSteamEventsTable(TablePartitionedByStudio):
    table_name: ClassVar[Literal[EventServiceGoldTable.EXTERNAL_STEAM_EVENTS]] = (
        EventServiceGoldTable.EXTERNAL_STEAM_EVENTS
    )
    model = EventsServiceExternalSteamEventsModel


class EventsServiceDetectedEventsModel(StrictBaseSchema):
    unique_sku_id: str = MediumStringField()
    studio_id: int = NonNegativeNumberField()
    release_date: str = TinyStringField()
    release_datetime: Datetime(time_zone="UTC") = BasicField()  # type: ignore
    last_sales_date: str = TinyStringField()
    last_sales_datetime: Datetime(time_zone="UTC") = BasicField()  # type: ignore
    discount_depth: float = NonNegativeNumberField(nullable=True)
    discount_type: str = MediumStringField(nullable=True)
    datetime_from: Datetime(time_zone="UTC") = BasicField()  # type: ignore
    datetime_to: Datetime(time_zone="UTC") = BasicField()  # type: ignore
    major: int = NonNegativeNumberField()
    event_name: str = MediumStringField()
    portal_platform_region_id: int = NonNegativeNumberField()
    base_sku_id: str = MediumStringField()
    human_name: str = MediumStringField()
    product_name: str = MediumStringField(nullable=True)
    gso: int = BasicField()
    event_id: str = MediumStringField()
    event_type: str = MediumStringField()
    promo_length: float = NonNegativeNumberField(nullable=True)


class EventsServiceDetectedEventsTable(TablePartitionedByStudio):
    table_name: ClassVar[Literal[EventServiceGoldTable.DETECTED_EVENTS]] = (
        EventServiceGoldTable.DETECTED_EVENTS
    )
    model = EventsServiceDetectedEventsModel


class EventsServiceScrapedEventsModel(StrictBaseSchema):
    report_id: int = NonNegativeNumberField()
    create_time: str = TinyStringField()
    update_time: str = TinyStringField()
    base_sku_id: str = MediumStringField()
    source_specific_discount_sku_id: str = BasicField()
    unique_sku_id: str = MediumStringField()
    studio_id: int = NonNegativeNumberField()
    discount_depth: int = NonNegativeNumberField()
    discount_type: str = TinyStringField()
    datetime_from: Datetime(time_zone="UTC") = BasicField()  # type: ignore
    datetime_to: Datetime(time_zone="UTC") = BasicField()  # type: ignore
    is_event_joined: bool = BasicField()
    triggers_cooldown: bool = BasicField()
    major: bool = BasicField()
    event_name: str = MediumStringField()
    base_event_id: str = BasicField(nullable=True)
    unique_event_id: str = MediumStringField()
    promo_length: int = NonNegativeNumberField()
    max_discount_percentage: int = NonNegativeNumberField()
    price_increase_time: str = TinyStringField()
    portal_platform_region_id: int = NonNegativeNumberField()


class EventsServiceScrapedEventsTable(TablePartitionedByStudio):
    table_name: ClassVar[Literal[EventServiceGoldTable.SCRAPED_EVENTS]] = (
        EventServiceGoldTable.SCRAPED_EVENTS
    )
    model = EventsServiceScrapedEventsModel


class EventsServiceScrapedEventsDetailsModel(StrictBaseSchema):
    unique_sku_id: str = MediumStringField()
    portal: str = MediumStringField()
    event_name: str = MediumStringField()
    datetime_from: Datetime(time_zone="UTC") = BasicField()  # type: ignore
    datetime_to: Datetime(time_zone="UTC") = BasicField()  # type: ignore
    discount_depth: int = NonNegativeNumberField()
    major: bool = BasicField()
    units: int = BasicField()
    revenue: float = BasicField()
    baseline_units: int = BasicField()
    baseline_revenue: float = BasicField()
    uplift_units: int = BasicField()
    uplift_revenue: float = BasicField()


class EventsServiceScrapedEventsDetailsTable(TablePartitionedByStudio):
    table_name: ClassVar[Literal[EventServiceGoldTable.SCRAPED_EVENTS_DETAILS]] = (
        EventServiceGoldTable.SCRAPED_EVENTS_DETAILS
    )
    model = EventsServiceScrapedEventsDetailsModel


class EventsServiceScrapedDatesModel(StrictBaseSchema):
    portal: str = MediumStringField()
    observation_type: str = MediumStringField()
    date: Datetime(time_zone="UTC") = BasicField()  # type: ignore


class EventsServiceScrapedDatesTable(TablePartitionedByStudio):
    table_name: ClassVar[Literal[EventServiceGoldTable.SCRAPED_DATES]] = (
        EventServiceGoldTable.SCRAPED_DATES
    )
    model = EventsServiceScrapedDatesModel


class EventsServiceSkusModel(StrictBaseSchema):
    base_sku_id: str = BasicField()
    human_name: str = BasicField()
    store_id: str = BasicField()
    studio_id: int = BasicField()
    portal_platform_region: str = BasicField()
    human_name_indicator: str = BasicField()
    sku_type: str = BasicField()
    product_name: str = BasicField(nullable=True)
    product_type: str = BasicField(nullable=True)
    unique_sku_id: str = BasicField()
    portal_platform_region_id: int = BasicField()
    product_id: str = BasicField()
    package_name: str = BasicField(nullable=True)
    custom_group: str = BasicField(nullable=True)
    ratio: float = BasicField()
    gso: int = BasicField()
    is_baseline_precalculated: bool = BasicField()
    release_date: str = BasicField(nullable=True)
    last_sales_date: str = BasicField(nullable=True)
    store_name: str = BasicField()
    human_name_clean: str = BasicField()
    to_keep: bool = BasicField()
    is_minor: bool = BasicField()


class EventsServiceSKUsTable(TablePartitionedByStudio):
    table_name: ClassVar[Literal[EventServiceGoldTable.SKUS]] = (
        EventServiceGoldTable.SKUS
    )
    model = EventsServiceSkusModel


class EventsServiceStoresModel(StrictBaseSchema):
    id: str = SmallStringField()  # lowercase of store
    studio_id: int = NonNegativeNumberField()
    portal: str = SmallStringField()
    region: str = SmallStringField()
    store: str = SmallStringField()
    abbreviated_name: str = SmallStringField()
    so_portal: int = BasicField(ge=1, le=99)
    so_region: int = BasicField(ge=0, le=9)


class EventsServiceStoresTable(TablePartitionedByStudio):
    table_name: ClassVar[Literal[EventServiceGoldTable.STORES]] = (
        EventServiceGoldTable.STORES
    )
    model = EventsServiceStoresModel
