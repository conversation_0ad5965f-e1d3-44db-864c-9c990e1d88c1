import pandera.polars as pa
import polars as pl
from data_sdk.domain import DisplayPortal
from data_sdk.domain.observations import ALL_OBSERVATION_TYPES
from pandera.engines.polars_engine import DateTime

ALL_DISPLAY_PORTALS: list[DisplayPortal] = [portal for portal in DisplayPortal]


class ScrapedEventsDetailsSchema(pa.DataFrameModel):
    unique_sku_id: pl.Categorical
    portal: pl.Enum(ALL_DISPLAY_PORTALS)  # type: ignore
    event_name: pl.String
    datetime_from: DateTime = pa.Field(dtype_kwargs={"time_zone": "UTC"})
    datetime_to: DateTime = pa.Field(dtype_kwargs={"time_zone": "UTC"})
    discount_depth: pl.UInt8
    major: pl.Boolean
    units: pl.Int64
    revenue: pl.Float64
    baseline_units: pl.Int64
    baseline_revenue: pl.Float64
    uplift_units: pl.Int64
    uplift_revenue: pl.Float64


class ScrapedDatesSchema(pa.DataFrameModel):
    portal: pl.Enum(ALL_DISPLAY_PORTALS)  # type: ignore
    observation_type: pl.Enum(ALL_OBSERVATION_TYPES)  # type: ignore
    date: DateTime = pa.Field(dtype_kwargs={"time_zone": "UTC"})


class ExternalSteamEventsSchema(pa.DataFrameModel):
    start_date: pl.Date
    end_date: pl.Date
    major: pl.Boolean
    name: pl.String
    start_day: pl.Int64
    start_month: pl.Int64
    start_year: pl.Int64
    end_day: pl.Int64
    end_month: pl.Int64
    end_year: pl.Int64
    portal: pl.Enum(ALL_DISPLAY_PORTALS)  # type: ignore
