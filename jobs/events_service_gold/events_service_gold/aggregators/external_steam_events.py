import polars as pl
from data_sdk.aggregator import BaseAggregator
from data_sdk.domain import DisplayPortal
from data_sdk.domain.tables import ExternalSteamEventsTable
from data_sdk.segmentator import BaseSegmentator

from events_service_gold.schemas import ExternalSteamEventsSchema
from events_service_gold.segments import PortalFieldSegmentator
from events_service_gold.tables import EventsServiceExternalSteamEventsTable

ALL_DISPLAY_PORTALS: list[DisplayPortal] = [portal for portal in DisplayPortal]


class ExternalSteamEventsAggregator(BaseAggregator):
    table_cls = EventsServiceExternalSteamEventsTable
    segmentator: BaseSegmentator = PortalFieldSegmentator()

    def __init__(
        self,
        external_steam_events: ExternalSteamEventsTable,
    ) -> None:
        self._external_steam_events = external_steam_events

    def _aggregate(self) -> pl.DataFrame:
        if self._external_steam_events.df.is_empty():
            return pl.DataFrame(
                [], schema=list(ExternalSteamEventsSchema.__annotations__.keys())
            )
        steam_events = self._external_steam_events.df.select(
            "start_date",
            "end_date",
            "major",
            "name",
            "start_day",
            "start_month",
            "start_year",
            "end_day",
            "end_month",
            "end_year",
        ).with_columns(portal=pl.lit("Steam").cast(pl.Enum(ALL_DISPLAY_PORTALS)))

        ExternalSteamEventsSchema.validate(steam_events.lazy())

        return steam_events
