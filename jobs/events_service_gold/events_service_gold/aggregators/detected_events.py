import polars as pl
from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.tables import LegacyFactDetectedEventsTable
from data_sdk.segmentator import BaseSegmentator

from events_service_gold.segments import PortalPPRIDSegmentator
from events_service_gold.tables import EventsServiceDetectedEventsTable


class DetectedEventsAggregator(BaseAggregator):
    table_cls = EventsServiceDetectedEventsTable
    segmentator: BaseSegmentator = PortalPPRIDSegmentator()

    def __init__(
        self,
        fact_detected_events: LegacyFactDetectedEventsTable,
    ) -> None:
        self._detected_events = fact_detected_events

    def _aggregate(self) -> pl.DataFrame:
        raw_detected_events = self._detected_events.df
        if raw_detected_events.is_empty():
            return pl.DataFrame()
        detected_events = raw_detected_events.with_columns(
            (pl.col("date_from") + " 00:00:00")
            .str.to_datetime()
            .cast(pl.datatypes.Datetime)
            .alias("datetime_from"),
            (pl.col("date_to") + " 23:59:59")
            .str.to_datetime()
            .cast(pl.datatypes.Datetime)
            .alias("datetime_to"),
            (pl.col("last_sales_date") + " 12:00:00")
            .str.to_datetime()
            .cast(pl.datatypes.Datetime)
            .alias("last_sales_datetime"),
            (pl.col("release_date") + " 12:00:00")
            .str.to_datetime()
            .cast(pl.datatypes.Datetime)
            .alias("release_datetime"),
        )
        # Drop legacy date columns
        detected_events = detected_events.drop("date_from", "date_to")

        # TODO It's nullable, so if all values are null then there will be incorrect type. Temporary until we have schema casting
        detected_events = detected_events.with_columns(
            pl.col("product_name").cast(pl.datatypes.String).alias("product_name"),
        )
        return detected_events
