import polars as pl
from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.domain_types import StudioId
from data_sdk.domain.tables import LegacyDimPortalsTable

from events_service_gold.tables import EventsServiceStoresTable
from events_service_gold.utils.string_utils import to_snake_case


class StoresAggregator(BaseAggregator):
    table_cls = EventsServiceStoresTable

    def __init__(
        self,
        studio_id: StudioId,
        dim_portals: LegacyDimPortalsTable,
    ) -> None:
        self._dim_portals = dim_portals
        self._studio_id = studio_id

    def _aggregate(self) -> pl.DataFrame:
        raw_dim_portals = self._dim_portals.df
        if raw_dim_portals.is_empty():
            return pl.DataFrame()
        portals = raw_dim_portals.with_columns(
            pl.col("store")
            .alias("id")
            .map_elements(to_snake_case, pl.datatypes.String),
            pl.lit(self._studio_id).alias("studio_id"),
        )
        # Drop columns that are more granular than store and would cause duplicates
        portals = portals.drop(
            [
                "platform",
                "portal_platform_region",
                "portal_platform_region_id",
                "so_platform",
                "pso",
            ]
        )
        portals = portals.unique()
        return portals
