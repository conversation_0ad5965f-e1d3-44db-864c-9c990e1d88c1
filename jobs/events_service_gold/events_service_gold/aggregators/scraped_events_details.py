import pendulum
import polars as pl
from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.tables import (
    LegacyFactBaselineTable,
    ObservationDiscountsTable,
    ObservationSalesTable,
)
from data_sdk.segmentator import BaseSegmentator

from events_service_gold.schemas import ScrapedEventsDetailsSchema
from events_service_gold.segments import PortalFieldSegmentator
from events_service_gold.tables import EventsServiceScrapedEventsDetailsTable


class ScrapedEventsDetailsAggregator(BaseAggregator):
    table_cls = EventsServiceScrapedEventsDetailsTable
    segmentator: BaseSegmentator = PortalFieldSegmentator()

    def __init__(
        self,
        observation_discounts: ObservationDiscountsTable,
        observation_sales: ObservationSalesTable,
        fact_baseline: LegacyFactBaselineTable,
    ) -> None:
        self._observation_discounts = observation_discounts
        self._observation_sales = observation_sales
        self._fact_baseline = fact_baseline

    def _aggregate(self) -> pl.DataFrame:
        if self._observation_discounts.df.is_empty():
            return pl.DataFrame(
                [], schema=list(ScrapedEventsDetailsSchema.__annotations__.keys())
            )

        discounts = self._observation_discounts.df.with_columns(
            pl.col("discount_depth").cast(pl.UInt8),
            date_from=pl.col("datetime_from").dt.truncate("1d"),
            date_to=pl.col("datetime_to").dt.truncate("1d"),
        )
        event_dates = discounts.select(
            "date_from", "date_to", "unique_event_id", "unique_sku_id"
        )

        event_dates = event_dates.with_columns(
            pl.struct(["date_from", "date_to"])
            .map_elements(
                lambda row: pl.datetime_range(
                    row["date_from"], row["date_to"], interval="1d", eager=True
                ),
                return_dtype=pl.List(pl.Datetime("us", "UTC")),
            )
            .alias("date")
        ).explode("date")

        event_dates = event_dates.with_columns(
            date=pl.col("date").cast(pl.Date),
            unique_sku_id=pl.col("unique_sku_id").cast(pl.String),
        )

        if self._observation_sales.df.is_empty():
            sales = pl.DataFrame(
                [
                    {
                        "date": pendulum.now().date(),
                        "unique_sku_id": "",
                        "revenue": 0.0,
                        "units": 0,
                    }
                ],
            )
        else:
            sales = self._observation_sales.df.with_columns(
                revenue=pl.col("gross_sales") - pl.col("gross_returned"),
                units=pl.col("units_sold") - pl.col("units_returned"),
                unique_sku_id=pl.col("unique_sku_id").cast(pl.String),
            )
            sales = sales.group_by(["date", "unique_sku_id"]).agg(
                pl.sum("revenue"), pl.sum("units")
            )

        event_dates = event_dates.join(
            sales, on=["unique_sku_id", "date"], how="left"
        ).fill_null(0)

        if self._fact_baseline.df.is_empty():
            baseline = pl.DataFrame(
                [
                    {
                        "date": pendulum.now().date(),
                        "unique_sku_id": "",
                        "baseline_revenue": 0.0,
                        "baseline_units": 0,
                        "uplift_revenue": 0.0,
                        "uplift_units": 0,
                    }
                ]
            )
        else:
            baseline = self._fact_baseline.df.with_columns(
                baseline_units_sold=pl.col("baseline_units_sold_directly")
                + pl.col("baseline_units_sold_non_billable")
                + pl.col("baseline_units_sold_retail"),
                uplift_units_sold=pl.col("uplift_units_sold_directly")
                + pl.col("uplift_units_sold_non_billable")
                + pl.col("uplift_units_sold_retail"),
            )

            baseline = baseline.with_columns(
                baseline_revenue=pl.col("baseline_gross_sales")
                - pl.col("baseline_gross_returned"),
                baseline_units=pl.col("baseline_units_sold")
                - pl.col("baseline_units_returned"),
                uplift_revenue=pl.col("uplift_gross_sales")
                - pl.col("uplift_gross_returned"),
                uplift_units=pl.col("uplift_units_sold")
                - pl.col("uplift_units_returned"),
            )
            baseline = baseline.group_by(["date", "unique_sku_id"]).agg(
                pl.sum("baseline_revenue"),
                pl.sum("baseline_units"),
                pl.sum("uplift_revenue"),
                pl.sum("uplift_units"),
            )

        event_dates = event_dates.join(
            baseline, on=["unique_sku_id", "date"], how="left"
        ).fill_null(0)

        sales_data = event_dates.group_by("unique_event_id").agg(
            pl.sum("revenue"),
            pl.sum("units"),
            pl.sum("baseline_revenue"),
            pl.sum("baseline_units"),
            pl.sum("uplift_revenue"),
            pl.sum("uplift_units"),
        )

        discounts = discounts.join(sales_data, on="unique_event_id", how="left").select(
            list(ScrapedEventsDetailsSchema.__annotations__.keys())
        )

        ScrapedEventsDetailsSchema.validate(discounts.lazy())

        return discounts
