import polars as pl
from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.tables import LegacyFactDiscountsTable
from data_sdk.segmentator import BaseSegmentator

from events_service_gold.segments import PortalPPRIDSegmentator
from events_service_gold.tables import EventsServiceScrapedEventsTable

LEGACY_FACT_DISCOUNTS_DATETIME_FORMAT = "%Y%m%dT%H%M%SZ"


class ScrapedEventsAggregator(BaseAggregator):
    table_cls = EventsServiceScrapedEventsTable
    segmentator: BaseSegmentator = PortalPPRIDSegmentator()

    def __init__(
        self,
        fact_discounts: LegacyFactDiscountsTable,
    ) -> None:
        self._fact_discounts = fact_discounts

    def _aggregate(self) -> pl.DataFrame:
        # TODO parse date
        raw_fact_discounts = self._fact_discounts.df
        if raw_fact_discounts.is_empty():
            return raw_fact_discounts

        discounts = raw_fact_discounts.with_columns(
            pl.col("datetime_from").str.strptime(
                pl.Datetime, LEGACY_FACT_DISCOUNTS_DATETIME_FORMAT
            ),
            pl.col("datetime_to").str.strptime(
                pl.Datetime, LEGACY_FACT_DISCOUNTS_DATETIME_FORMAT
            ),
            pl.col("create_time").str.strptime(
                pl.Datetime, LEGACY_FACT_DISCOUNTS_DATETIME_FORMAT
            ),
            pl.col("update_time").str.strptime(
                pl.Datetime, LEGACY_FACT_DISCOUNTS_DATETIME_FORMAT
            ),
        )
        return discounts
