import polars as pl
from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.tables import LegacyFactBaselineTable
from data_sdk.segmentator import BaseSegmentator

from events_service_gold.segments import PortalFieldSegmentator
from events_service_gold.tables import EventsServiceBaselineTable


class BaselineAggregator(BaseAggregator):
    table_cls = EventsServiceBaselineTable
    segmentator: BaseSegmentator = PortalFieldSegmentator()

    def __init__(
        self,
        fact_baseline: LegacyFactBaselineTable,
    ) -> None:
        self._fact_baseline = fact_baseline

    def _aggregate(self) -> pl.DataFrame:
        raw_fact_baseline = self._fact_baseline.df

        return raw_fact_baseline
