import polars as pl
from data_sdk.aggregator import BaseAggregator
from data_sdk.domain import DisplayPortal, Portal
from data_sdk.domain.observations import ALL_OBSERVATION_TYPES
from data_sdk.domain.tables import SilverReportsTable
from data_sdk.segmentator import BaseSegmentator

from events_service_gold.schemas import ALL_DISPLAY_PORTALS, ScrapedDatesSchema
from events_service_gold.segments import PortalFieldSegmentator
from events_service_gold.tables import EventsServiceScrapedDatesTable


class ScrapedDatesAggregator(BaseAggregator):
    table_cls = EventsServiceScrapedDatesTable
    segmentator: BaseSegmentator = PortalFieldSegmentator()

    def __init__(
        self,
        silver_reports: SilverReportsTable,
    ) -> None:
        self._silver_reports = silver_reports

    def _aggregate(self) -> pl.DataFrame:
        if self._silver_reports.df.is_empty():
            return pl.DataFrame(
                [], schema=list(ScrapedDatesSchema.__annotations__.keys())
            )
        reports = self._silver_reports.df.filter(pl.col("state") == "CONVERTED")
        reports = reports.group_by(
            "studio_id",
            "portal",
            "observation_type",
        ).agg(
            date=pl.col("upload_date").max(),
        )
        portals_display_names = {
            portal.value: getattr(DisplayPortal, portal.name).value for portal in Portal
        }
        reports = reports.with_columns(
            portal=pl.col("portal").map_dict(portals_display_names),
        )
        reports = reports.with_columns(
            portal=pl.col("portal").cast(pl.Enum(ALL_DISPLAY_PORTALS)),
            observation_type=pl.col("observation_type").cast(
                pl.Enum(ALL_OBSERVATION_TYPES)
            ),
        )

        ScrapedDatesSchema.validate(reports.lazy())
        return reports
