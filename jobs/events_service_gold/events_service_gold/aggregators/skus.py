import pandas as pd
import polars as pl
from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.tables import (
    LegacyDimPortalsTable,
    LegacyDimSKUsTable,
    LegacyFactSalesTable,
)

from events_service_gold.tables import EventsServiceSKUsTable
from events_service_gold.utils.string_utils import to_snake_case


class SkusAggregator(BaseAggregator):
    table_cls = EventsServiceSKUsTable

    def __init__(
        self,
        dim_sku: LegacyDimSKUsTable,
        fact_sales: LegacyFactSalesTable,
        dim_portals: LegacyDimPortalsTable,
    ) -> None:
        self._dim_sku = dim_sku
        # Using legacy dim_skus to have portal_platform_region_id and subsequently store_name
        self._fact_sales = fact_sales
        self._dim_portals = dim_portals

    # TODO: Maybe one day in polars... Rewriting without tests is risky
    def _aggregate(self) -> pl.DataFrame:
        if self._dim_sku.df.is_empty() or self._dim_portals.df.is_empty():
            return pl.DataFrame()

        # TODO handle empty fact sales and portals
        result = self._dim_sku.df.to_pandas()
        result = result.rename(columns={"sku_studio": "unique_sku_id"})
        result = result.reset_index()
        result["human_name_clean"] = (
            result["human_name"].str.lower().str.replace(r"\W|_| ", "", regex=True)
        )
        fact_sales = self._fact_sales.df.to_pandas()
        fact_sales = fact_sales.rename(columns={"sku_studio": "unique_sku_id"})
        dim_portals = self._dim_portals.df.to_pandas()
        dim_portals = dim_portals[["portal_platform_region_id", "store"]].rename(
            columns={"store": "store_name", "sku_studio": "unique_sku_id"}
        )
        result = result.merge(dim_portals, on="portal_platform_region_id", how="left")
        result["store_name"] = result["store_name"].apply(to_snake_case)

        if fact_sales.empty:
            result["to_keep"] = False
            result["is_minor"] = True
            result["release_date"] = None
            result["last_sales_date"] = None
        else:
            # Group by 'unique_sku_id' and get both the minimum and maximum 'date' where 'gross_sales' + 'gross_returned' > 0
            date_aggregations = (
                fact_sales[fact_sales["gross_sales"] + fact_sales["gross_returned"] > 0]
                .groupby("unique_sku_id")["date"]
                .agg(["min", "max"])
            )

            # Map these values to 'result'
            result["release_date"] = result["unique_sku_id"].map(
                date_aggregations["min"]
            )
            result["last_sales_date"] = result["unique_sku_id"].map(
                date_aggregations["max"]
            )
            result["is_minor"] = self.is_minor(result, fact_sales)

        result = result.sort_values("index").drop("index", axis=1)
        result_polars = pl.DataFrame(result)
        return result_polars

    def is_minor(self, sku_df, fact_sales) -> pd.DataFrame:
        interval_days = 60
        start_date = (
            pd.Timestamp.today() - pd.Timedelta(interval_days, unit="D")
        ).date()
        start_date_str = start_date.strftime("%Y-%m-%d")
        sales_thrhld_60d = 100
        sales_by_sku = (
            fact_sales[fact_sales["date"] > start_date_str][
                fact_sales["category"].isin(
                    [
                        "Sale",
                        "Free",
                        "Return",
                        "Free & Sale",
                        "Non-billable: Epic",
                        "Sale Adjustment",
                        "Free & Return",
                    ]
                )
            ]
            .groupby("unique_sku_id")
            .agg({"gross_sales": "sum"})
            .reset_index()
            .rename(
                columns={
                    "unique_sku_id": "unique_sku_id",
                    "gross_sales": "total_gross_sales_last_60d",
                }
            )
        )
        set_no_comm_license_skus = set(
            sku_df[
                (sku_df["gso"] > 0)
                & (~sku_df["human_name_clean"].str.contains("commerciallicense"))
            ]["unique_sku_id"]
        )

        # sorting by release date first:
        sku_df.sort_values(
            by=[
                "human_name_clean",
                "studio_id",
                "store_name",
                "release_date",
                "base_sku_id",
            ],
            ascending=True,
            na_position="first",
            inplace=True,
            ignore_index=True,
        )
        skus_to_keep_rel_date = set(
            sku_df.drop_duplicates(
                subset=["human_name_clean", "studio_id", "store_name"], keep="last"
            )["unique_sku_id"]
        )
        sku_df["to_keep"] = sku_df["unique_sku_id"].isin(skus_to_keep_rel_date)
        # ---
        # base SKUs to be removed before training and predicting
        set_bad_base_skus = set(sku_df[~sku_df["to_keep"]]["base_sku_id"].astype(str))
        set_high_sale_skus = set(
            sales_by_sku[
                sales_by_sku["total_gross_sales_last_60d"] >= sales_thrhld_60d
            ]["unique_sku_id"]
        )

        return ~(
            sku_df["unique_sku_id"].isin(set_no_comm_license_skus)
            & (~sku_df["base_sku_id"].isin(set_bad_base_skus))
            & sku_df["unique_sku_id"].isin(set_high_sale_skus)
        )
