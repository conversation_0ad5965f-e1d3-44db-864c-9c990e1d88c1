import datetime
import logging
import traceback

from data_sdk.aggregator import BaseAggregator, process_aggregators
from data_sdk.custom_partition.reader import CustomPartitionReader
from data_sdk.custom_partition.writer import CustomPartitionsWriter
from data_sdk.domain.domain_types import StudioId
from pydantic import BaseModel, ValidationError

from events_service_gold.aggregators import (
    BaselineAggregator,
    DetectedEventsAggregator,
    ExternalSteamEventsAggregator,
    ScrapedDatesAggregator,
    ScrapedEventsAggregator,
    ScrapedEventsDetailsAggregator,
    SkusAggregator,
    StoresAggregator,
)
from events_service_gold.config import Config
from events_service_gold.exceptions import InvalidMessage

log = logging.getLogger(__name__)


class Params(BaseModel):
    studio_id: int


def validate_message(params) -> Params:
    log.info(f"Validating message: {params}")
    try:
        return Params.validate(params)
    except ValidationError:
        log.exception("Received invalid message %s", str(params))
        raise InvalidMessage(traceback.format_exc())


aggregators: list[type[BaseAggregator]] = [
    BaselineAggregator,
    DetectedEventsAggregator,
    ExternalSteamEventsAggregator,
    ScrapedDatesAggregator,
    ScrapedEventsAggregator,
    ScrapedEventsDetailsAggregator,
    SkusAggregator,
    StoresAggregator,
]


def run(params: Params, config: Config):
    creation_datetime = datetime.datetime.now(datetime.UTC)
    reader = CustomPartitionReader.get_reader(config.reader_cfg)
    writer = CustomPartitionsWriter.get_writer(config.writer_cfg)
    process_aggregators(
        aggregators=aggregators,
        reader=reader,
        writer=writer,
        creation_datetime=creation_datetime,
        studio_id=StudioId(params.studio_id),
    )
