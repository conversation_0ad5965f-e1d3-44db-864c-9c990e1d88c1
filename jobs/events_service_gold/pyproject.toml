[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poe]
include = "../../common/poe_shared_tasks.toml"

[tool.poe.tasks]
_test = "pytest -vvv --color=yes"
hooks = { "shell" = "../../.hooks/install.sh 'jobs/events_service_gold'" }

[tool.poetry]
name = "events_service_gold"
version = "0.1.0"
description = ""
authors = ["Bartłomi<PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.ruff]
src = [".", "tests"]

[tool.ruff.lint]
select = [
    # Pyflakes
    "F",
    # Pycodestyle
    "E",
    "W",
    # isort
    "I",
]
ignore = ["E501", "E712"]


[tool.ruff.lint.isort]
known-first-party = ["events_service_gold"]

[tool.ruff.lint.per-file-ignores]
"events_service_gold/main.py" = ["E402"]

[tool.poetry.dependencies]
python = "~3.12"
pandas = "^2.2.0"
pandera = { extras = ["polars"], version = "^0.20.4" }
pyarrow = "~17.0.0"
pydantic-settings = "^2.1.0"
pydantic = "^2.6.1"
polars = "^0.20.10"
pipeline-sdk = {version = "2.5.2", source = "indiebi"}
data-sdk = {version = "1.6.4", source = "indiebi"}
typer = { extras = ["all"], version = "^0.12.0" }
sentry-sdk = "^2.0.0"
pendulum = "^3.0.0"

[tool.poetry.group.dev.dependencies]
ruff = "^0.4.5"
mypy = "^1.8.0"
pandas-stubs = "^2.1.4.231227"
pre-commit = "^3.7.1"
poethepoet = "^0.26.1"

[tool.poetry.group.test.dependencies]
pytest = "^8.2.2"
pytest-deadfixtures = "^2.2.1"
pytest-xdist = "^3.6.1"
pytest-random-order = "^1.1.1"
factory-boy = "^3.3.1"
time-machine = "^2.15.0"

[[tool.poetry.source]]
name = "indiebi"
url = "https://gitlab.com/api/v4/groups/4449864/-/packages/pypi/simple"
priority = "explicit"
