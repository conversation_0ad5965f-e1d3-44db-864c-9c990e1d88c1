from datetime import date, datetime, timedelta

import factory
import pandas as pd
import polars as pl
import pytest
from data_sdk.domain.observations import ObservationType
from data_sdk.domain.tables import (
    LegacyDimPortalsTable,
    LegacyDimSKUsTable,
    LegacyFactBaselineTable,
    LegacyFactDetectedEventsTable,
    LegacyFactDiscountsTable,
    LegacyFactSalesTable,
)
from factory import Sequence
from factory.fuzzy import FuzzyFloat, FuzzyInteger

from events_service_gold.aggregators.scraped_events import (
    LEGACY_FACT_DISCOUNTS_DATETIME_FORMAT,
)


class DimSKUSSalesFactory(factory.DictFactory):
    sku_studio = factory.LazyAttribute(lambda o: f"{o.base_sku_id}-steam:{o.studio_id}")
    base_sku_id = Sequence(lambda n: str(122222 + n))
    human_name = "SUPERHOT actions"
    human_name_indicator = ObservationType.SALES.value
    portal_platform_region = "Steam:PC:Global"
    portal_platform_region_id = 171010
    product_name = "SUPERHOT DEV"
    product_type = None
    product_id = "COOL GAME"
    package_name = None
    custom_group = None
    sku_type = "SALES"
    store_id = "Unknown"
    studio_id = 1
    ratio = 1.0
    gso = 14474737
    is_baseline_precalculated = True

    @classmethod
    def build_table(cls, size: int = 1, **kwargs) -> LegacyDimSKUsTable:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows)

    @classmethod
    def build_df_batch(cls, size, **kwargs) -> pl.DataFrame:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows).df

    @staticmethod
    def _converted_rows_to_table(converted_rows: list[dict]) -> LegacyDimSKUsTable:
        return LegacyDimSKUsTable(df=pl.DataFrame(converted_rows))


class DimSKUSStoreFactory(DimSKUSSalesFactory):
    base_sku_id = Sequence(lambda n: str(152222 + n))
    sku_studio = factory.LazyAttribute(
        lambda o: f"{o.base_sku_id}-{o.sku_type.lower()}:{o.studio_id}"
    )
    sku_type = "STORE"
    human_name_indicator = ObservationType.WISHLIST_ACTIONS.value
    store_id = "505511"
    gso = 0
    is_baseline_precalculated = False


class _FactBaselineFactory(factory.DictFactory):
    class Params:
        start_date = date(year=2000, month=1, day=1)

    country_code = "USA"
    studio_id = 1
    unique_sku_id = "122222-steam:1"
    portal = "steam"
    sku_studio = factory.LazyAttribute(lambda o: o.unique_sku_id)  # Legacy field
    date = factory.LazyAttributeSequence(
        lambda o, n: (o.start_date + timedelta(days=n)).isoformat()
    )
    date_sku_studio = "temp"
    portal_platform_region = "temp"
    portal_platform_region_id = 171010
    product_id = "temp"
    baseline_units_sold_directly = FuzzyInteger(-100, 100)
    baseline_units_sold_non_billable = FuzzyInteger(-100, 100)
    baseline_units_sold_retail = FuzzyInteger(-100, 100)
    baseline_units_returned = FuzzyInteger(-100, 100)
    baseline_gross_sales = FuzzyFloat(-100, 100)
    baseline_gross_returned = FuzzyFloat(-100, 100)
    baseline_free_units = FuzzyInteger(-100, 100)
    baseline_net_sales_approx = FuzzyFloat(-100, 100)
    uplift_units_sold_directly = FuzzyInteger(-100, 100)
    uplift_units_sold_non_billable = FuzzyInteger(-100, 100)
    uplift_units_sold_retail = FuzzyInteger(-100, 100)
    uplift_units_returned = FuzzyInteger(-100, 100)
    uplift_gross_sales = FuzzyFloat(-100, 100)
    uplift_gross_returned = FuzzyFloat(-100, 100)
    uplift_free_units = FuzzyInteger(-100, 100)
    uplift_net_sales_approx = FuzzyFloat(-100, 100)

    @classmethod
    def build_table(cls, size: int = 1, **kwargs) -> LegacyFactBaselineTable:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls.converted_rows_to_table(raw_rows)

    @classmethod
    def build_df_batch(cls, size: int = 1, **kwargs) -> pl.DataFrame:
        """Build a batch of instances of the given class, with overridden attrs.

        Args:
            size (int): the number of instances to build

        Returns:
            object pd.DataFrame: the built instances with js_round(ed) values
        """
        raw_rows = cls.build_batch(size=size, **kwargs)
        table = cls.converted_rows_to_table(raw_rows)
        return table.df

    @staticmethod
    def converted_rows_to_table(
        converted_rows: list[dict],
    ) -> LegacyFactBaselineTable:
        # TODO add validation once the legacy table has a schema
        df = pl.DataFrame(converted_rows)
        return LegacyFactBaselineTable(df=df)


class _FactDetectedEventsFactory(factory.DictFactory):
    class Params:
        start_date = date(year=2024, month=1, day=1)

    unique_sku_id = factory.LazyAttribute(
        lambda o: f"{o.base_sku_id}-steam:{o.studio_id}"
    )
    studio_id = 1
    release_date = "2017-05-25"
    last_sales_date = "2024-05-11"
    discount_depth = factory.LazyAttribute(
        lambda o: 99.9 if o.discount_type != "cooldown" else None
    )
    discount_type = factory.LazyAttribute(
        lambda o: {"discount": "custom", "store_discount": "store"}.get(
            o.event_type, None
        )
    )
    date_from = factory.LazyAttributeSequence(
        lambda o, n: (o.start_date + timedelta(days=n * 7)).isoformat()
    )
    date_to = factory.LazyAttributeSequence(
        lambda o, n: (o.start_date + timedelta(days=n * 7 + 5)).isoformat()
    )
    major = factory.LazyAttribute(
        lambda o: 1 if o.event_type == "store_discount" else 0
    )
    event_name = factory.Faker("word")
    portal_platform_region_id = 171010
    base_sku_id = "165761"
    human_name = factory.Faker("word")
    product_name = factory.LazyAttribute(lambda o: f"{o.human_name} - {o.base_sku_id}")
    gso = 2851009
    event_id = factory.LazyAttribute(lambda o: f"{o.unique_sku_id}:{o.date_from}")
    event_type = factory.Iterator(["cooldown", "discount", "store_discount", "unused"])
    promo_length = factory.LazyAttribute(
        lambda o: (date.fromisoformat(o.date_to) - date.fromisoformat(o.date_from)).days
        if o.event_name != "cooldown"
        else None
    )

    @classmethod
    def build_table(cls, size: int = 1, **kwargs) -> LegacyFactDetectedEventsTable:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls.converted_rows_to_table(raw_rows)

    @classmethod
    def build_df_batch(cls, size: int = 1, **kwargs) -> pl.DataFrame:
        """Build a batch of instances of the given class, with overridden attrs.

        Args:
            size (int): the number of instances to build

        Returns:
            object pd.DataFrame: the built instances with js_round(ed) values
        """
        raw_rows = cls.build_batch(size=size, **kwargs)
        table = cls.converted_rows_to_table(raw_rows)
        return table.df

    @staticmethod
    def converted_rows_to_table(
        converted_rows: list[dict],
    ) -> LegacyFactDetectedEventsTable:
        # TODO add validation once the legacy table has a schema
        df = pl.DataFrame(converted_rows)
        return LegacyFactDetectedEventsTable(df=df)


class _FactDiscountsFactory(factory.DictFactory):
    class Params:
        start_date = date(year=2024, month=1, day=1)

    report_id = FuzzyInteger(1, 100)
    create_time = factory.LazyAttribute(lambda o: o.datetime_from)
    update_time = factory.LazyAttribute(lambda o: o.datetime_from)
    base_sku_id = "165761"
    source_specific_discount_sku_id = "165761"
    unique_sku_id = factory.LazyAttribute(
        lambda o: f"{o.base_sku_id}-steam:{o.studio_id}"
    )
    studio_id = 10504
    discount_depth = 85.5
    discount_type = "custom"
    datetime_from = factory.LazyAttributeSequence(
        lambda o, n: (o.start_date + timedelta(days=n * 7)).strftime(
            LEGACY_FACT_DISCOUNTS_DATETIME_FORMAT
        )
    )
    datetime_to = factory.LazyAttributeSequence(
        lambda o, n: (o.start_date + timedelta(days=n * 7 + 5)).strftime(
            LEGACY_FACT_DISCOUNTS_DATETIME_FORMAT
        )
    )
    is_event_joined = factory.Iterator([True, False])
    triggers_cooldown = factory.Iterator([True, False])
    major = factory.Iterator([True, False])
    event_name = factory.Faker("word")
    base_event_id = "288877"
    unique_event_id = (
        "20240523T160000Z:May2024CustomSale:HACPAURNA-america-nintendo:10504"
    )
    promo_length = factory.LazyAttribute(
        lambda o: (
            datetime.strptime(o.datetime_to, LEGACY_FACT_DISCOUNTS_DATETIME_FORMAT)
            - datetime.strptime(o.datetime_from, LEGACY_FACT_DISCOUNTS_DATETIME_FORMAT)
        ).total_seconds()
        if o.event_name != "cooldown"
        else None
    )
    max_discount_percentage = 85.5
    price_increase_time = pd.Timestamp("2020-01-24 17:00:00.000000")
    portal_platform_region_id = 171010

    @classmethod
    def build_table(cls, size: int = 1, **kwargs) -> LegacyFactDiscountsTable:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls.converted_rows_to_table(raw_rows)

    @classmethod
    def build_df_batch(cls, size: int = 1, **kwargs) -> pl.DataFrame:
        """Build a batch of instances of the given class, with overridden attrs.

        Args:
            size (int): the number of instances to build

        Returns:
            object pd.DataFrame: the built instances with js_round(ed) values
        """
        raw_rows = cls.build_batch(size=size, **kwargs)
        table = cls.converted_rows_to_table(raw_rows)
        return table.df

    @staticmethod
    def converted_rows_to_table(
        converted_rows: list[dict],
    ) -> LegacyFactDiscountsTable:
        # TODO add validation once the legacy table has a schema
        df = pl.DataFrame(converted_rows)
        return LegacyFactDiscountsTable(df=df)


class _FactSalesFactory(factory.DictFactory):
    class Params:
        start_date = date(year=2024, month=1, day=1)

    country_code = factory.Iterator(["USA", "CAN", "MEX"])
    currency_code = factory.LazyAttribute(
        lambda o: "CAD" if o.country_code == "CAN" else "USD"
    )
    studio_id = 1
    sku_studio = "122222-steam:1"
    bundle_name = "Direct Package Sale"
    portal_platform_region = "Steam:PC:Global"
    portal_platform_region_id = 171010
    product_id = "Unassigned:171010:1"
    hash_acquisition_properties = "1c33c1969614a618bacad42696021c7d"
    date = factory.LazyAttributeSequence(
        lambda o, n: (o.start_date + timedelta(days=n)).isoformat()
    )
    date_sku_studio = factory.LazyAttribute(lambda o: f"{o.date}:{o.sku_studio}")
    source_file_id = 110159
    retailer_tag = "Hype SRO"
    base_price_local = FuzzyFloat(0, 400)
    calculated_base_price_usd = None
    net_sales = FuzzyFloat(0, 400)
    gross_returned = FuzzyFloat(0, 400)
    gross_sales = FuzzyFloat(0, 400)
    units_returned = FuzzyInteger(0, 100)
    units_sold = FuzzyInteger(0, 100)
    free_units = FuzzyInteger(0, 100)
    price_local = FuzzyFloat(0, 400)
    price_usd = FuzzyFloat(0, 400)
    net_sales_approx = FuzzyFloat(0, 400)
    category = factory.Iterator(["Sale", "Free", "Retail: Steam"])
    calculated_base_price_local_v2 = FuzzyFloat(0, 400)
    calculated_base_price_usd_v2 = FuzzyFloat(0, 400)

    @classmethod
    def build_table(cls, size: int = 1, **kwargs) -> LegacyFactSalesTable:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls.converted_rows_to_table(raw_rows)

    @classmethod
    def build_df_batch(cls, size: int = 1, **kwargs) -> pl.DataFrame:
        """Build a batch of instances of the given class, with overridden attrs.

        Args:
            size (int): the number of instances to build

        Returns:
            object pd.DataFrame: the built instances with js_round(ed) values
        """
        raw_rows = cls.build_batch(size=size, **kwargs)
        table = cls.converted_rows_to_table(raw_rows)
        return table.df

    @staticmethod
    def converted_rows_to_table(
        converted_rows: list[dict],
    ) -> LegacyFactSalesTable:
        # TODO add validation once the legacy table has a schema
        df = pl.DataFrame(converted_rows)
        return LegacyFactSalesTable(df=df)


@pytest.fixture
def dim_skus_store_factory():
    DimSKUSStoreFactory.reset_sequence(force=True)
    return DimSKUSStoreFactory


@pytest.fixture
def dim_skus_sales_factory() -> type[DimSKUSSalesFactory]:
    DimSKUSSalesFactory.reset_sequence(force=True)
    return DimSKUSSalesFactory


@pytest.fixture
def legacy_dim_portals() -> LegacyDimPortalsTable:
    return LegacyDimPortalsTable(
        df=pl.DataFrame(
            {
                "portal": [
                    "Steam",
                    "Nintendo",
                    "Nintendo",
                    "PlayStation",
                    "PlayStation",
                ],
                "platform": ["PC", "Switch", "Switch", "Home", "PS VR"],
                "region": [
                    "Global",
                    "Nintendo America",
                    "Nintendo Europe",
                    "PS America",
                    "PS America",
                ],
                "store": [
                    "Steam",
                    "Nintendo Switch America",
                    "Nintendo Switch Europe",
                    "PlayStation America",
                    "PlayStation America",
                ],
                "abbreviated_name": [
                    "Steam",
                    "Switch US",
                    "Switch EU",
                    "PS US",
                    "PS US",
                ],
                "portal_platform_region": [
                    "Steam:PC:Global",
                    "Nintendo:Switch:Nintendo America",
                    "Nintendo:Switch:Nintendo Europe",
                    "PlayStation:Home:PS America",
                    "PlayStation:PS VR:PS America",
                ],
                "portal_platform_region_id": [171010, 151511, 151513, 161715, 161915],
                "so_portal": [1, 5, 5, 4, 4],
                "so_platform": [0, 1, 1, 8, 9],
                "so_region": [0, 2, 3, 1, 1],
                "pso": [100, 512, 513, 481, 491],
            }
        )
    )


@pytest.fixture
def legacy_fact_baseline_factory() -> type[factory.Factory]:
    _FactBaselineFactory.reset_sequence(force=True)
    return _FactBaselineFactory


@pytest.fixture
def legacy_fact_detected_events_factory() -> type[factory.Factory]:
    _FactDetectedEventsFactory.reset_sequence(force=True)
    return _FactDetectedEventsFactory


@pytest.fixture
def legacy_fact_discounts_factory() -> type[factory.Factory]:
    _FactDiscountsFactory.reset_sequence(force=True)
    return _FactDiscountsFactory


@pytest.fixture
def legacy_fact_sales_factory() -> type[factory.Factory]:
    _FactSalesFactory.reset_sequence(force=True)
    return _FactSalesFactory
