import polars as pl
import pytest
from data_sdk.domain import TableName
from data_sdk.domain.tables import LegacyFactDetectedEventsTable

from events_service_gold.aggregators import BaselineAggregator
from events_service_gold.tables import EventsServiceBaselineTable


@pytest.fixture
def input_data(legacy_fact_baseline_factory):
    return {
        TableName.FACT_BASELINE.value: legacy_fact_baseline_factory.build_table(size=5),
    }


def test_baseline_aggregator_returns_valid_schema(input_data):
    baseline_data = BaselineAggregator(**input_data).aggregate()
    result = baseline_data.df
    EventsServiceBaselineTable.model.validate(result)
    assert len(result) == 5


def test_baseline_aggregator_handles_empty_inputs():
    input_data = {
        TableName.FACT_BASELINE.value: LegacyFactDetectedEventsTable(df=pl.DataFrame()),
    }
    baseline_data = BaselineAggregator(**input_data).aggregate()
    result = baseline_data.df
    assert result.is_empty() is True
    EventsServiceBaselineTable.model.validate(result)
