from unittest.mock import MagicMock

import polars as pl
import pytest
from data_sdk.domain import ALL_PORTALS
from data_sdk.domain.observations import ALL_OBSERVATION_TYPES

from events_service_gold.aggregators import ScrapedDatesAggregator
from events_service_gold.schemas import ALL_DISPLAY_PORTALS, ScrapedDatesSchema


def create_mock_table(data):
    df = pl.DataFrame(data)
    mock_table = MagicMock()
    mock_table.df = df
    return mock_table


@pytest.fixture
def sample_data():
    """Sample data for tests."""
    return {
        "external_reports": {
            "studio_id": [1, 1],
            "portal": ["steam", "playstation"],
            "state": ["CONVERTED", "CONVERTED"],
            "observation_type": ["sales", "sales"],
            "upload_date": ["2023-01-05", "2024-02-06"],
        },
    }


@pytest.fixture
def aggregator(sample_data):
    """Fixture to set up the aggregator with transformed mock data."""
    reports_df = pl.DataFrame(sample_data["external_reports"]).with_columns(
        pl.col("portal").cast(pl.Enum(ALL_PORTALS)),
        pl.col("upload_date").str.to_datetime().dt.convert_time_zone("UTC"),
    )

    reports = MagicMock()
    reports.df = reports_df

    return ScrapedDatesAggregator(
        silver_reports=reports,
    )


def test_schema_validation():
    """Test schema validation on a valid and invalid DataFrame."""
    valid_df = pl.DataFrame(
        {
            "studio_id": [1, 1],
            "portal": ["Steam", "PlayStation"],
            "observation_type": ["sales", "sales"],
            "date": ["2023-01-05", "2024-02-06"],
        }
    ).with_columns(
        pl.col("portal").cast(pl.Enum(ALL_DISPLAY_PORTALS)),
        pl.col("observation_type").cast(pl.Enum(ALL_OBSERVATION_TYPES)),
        pl.col("date").str.to_datetime().dt.convert_time_zone("UTC"),
    )

    validated_df = ScrapedDatesSchema.validate(valid_df.lazy())
    assert validated_df is not None

    invalid_df = valid_df.drop("observation_type")
    with pytest.raises(Exception):
        ScrapedDatesSchema.validate(invalid_df.lazy())


def test_aggregate(aggregator):
    """Test the aggregation logic."""
    result = aggregator._aggregate()

    assert isinstance(result, pl.DataFrame)
    assert not result.is_empty()


def test_aggregate_with_empty_input(sample_data):
    """Test that an empty observation_discounts results in an empty DataFrame with valid schema."""
    empty_reports = pl.DataFrame(
        {key: [] for key in sample_data["external_reports"].keys()}
    )

    reports = MagicMock()
    reports.df = empty_reports

    aggregator = ScrapedDatesAggregator(
        silver_reports=reports,
    )

    result = aggregator._aggregate()

    # Result should be empty but with valid schema
    assert (
        result.is_empty()
    ), "Result should be empty when observation_discounts is empty."
    assert set(result.columns) == set(ScrapedDatesSchema.__annotations__.keys())
