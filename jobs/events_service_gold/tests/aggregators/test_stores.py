import polars as pl
import pytest
from data_sdk.domain import TableName
from data_sdk.domain.domain_types import StudioId
from data_sdk.domain.tables import LegacyDimPortalsTable

from events_service_gold.aggregators import (
    StoresAggregator,
)
from events_service_gold.tables import (
    EventsServiceStoresTable,
)


@pytest.fixture()
def input_data(legacy_dim_portals):
    return {"studio_id": StudioId(1), TableName.DIM_PORTALS.value: legacy_dim_portals}


def test_stores_aggregator_returns_valid_schema(input_data):
    assert len(input_data["dim_portals"].df) == 5
    result = StoresAggregator(**input_data).aggregate().df
    EventsServiceStoresTable.model.validate(result)
    assert len(result) == 4, "Multiple portals for PS US should be deduplicated"
    assert result["id"].sort().to_list() == [
        "nintendo_switch_america",
        "nintendo_switch_europe",
        "playstation_america",
        "steam",
    ]
    assert result["studio_id"].unique().to_list() == [1]


def test_stores_aggregator_handles_empty_inputs():
    input_data = {
        "studio_id": StudioId(1),
        TableName.DIM_PORTALS.value: LegacyDimPortalsTable(df=pl.DataFrame()),
    }
    result = StoresAggregator(**input_data).aggregate().df
    assert result.is_empty() is True
    EventsServiceStoresTable.model.validate(result)
