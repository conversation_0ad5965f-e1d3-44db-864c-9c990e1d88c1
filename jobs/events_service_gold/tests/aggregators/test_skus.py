from datetime import datetime

import polars as pl
import pytest
from data_sdk.domain import TableName
from data_sdk.domain.tables import (
    LegacyDimPortalsTable,
    LegacyDimSKUsTable,
    LegacyFactSalesTable,
)

from events_service_gold.aggregators import SkusAggregator
from events_service_gold.tables import (
    EventsServiceSKUsTable,
)


@pytest.fixture()
def move_time_to_2024_02_28(time_machine):
    time_machine.move_to(datetime(2024, 2, 28))


@pytest.fixture
def input_data(
    dim_skus_store_factory,
    dim_skus_sales_factory,
    legacy_dim_portals,
    legacy_fact_sales_factory,
):
    store_skus = dim_skus_store_factory.build_table(size=5)
    dim_skus_sales_factory.reset_sequence(force=True)
    sales_skus = dim_skus_sales_factory.build_table(size=5)
    all_skus = pl.concat([store_skus.df, sales_skus.df])
    return {
        TableName.DIM_SKU.value: LegacyDimSKUsTable(df=all_skus),
        TableName.FACT_SALES.value: legacy_fact_sales_factory.build_table(size=10),
        TableName.DIM_PORTALS.value: legacy_dim_portals,
    }


def test_skus_aggregator_returns_expected_values(input_data, move_time_to_2024_02_28):
    result = SkusAggregator(**input_data).aggregate().df
    EventsServiceSKUsTable.model.validate(result)
    assert len(result) == 10

    enriched_sku_id = "122222-steam:1"
    (enriched_sku,) = result.filter(
        pl.col("unique_sku_id") == enriched_sku_id
    ).to_dicts()
    assert enriched_sku == {
        "unique_sku_id": "122222-steam:1",
        "base_sku_id": "122222",
        "human_name": "SUPERHOT actions",
        "human_name_indicator": "sales",
        "portal_platform_region": "Steam:PC:Global",
        "portal_platform_region_id": 171010,
        "product_name": "SUPERHOT DEV",
        "product_type": None,
        "product_id": "COOL GAME",
        "package_name": None,
        "custom_group": None,
        "sku_type": "SALES",
        "store_id": "Unknown",
        "studio_id": 1,
        "ratio": 1.0,
        "gso": 14474737,
        "is_baseline_precalculated": True,
        "human_name_clean": "superhotactions",
        "store_name": "steam",
        "release_date": "2024-01-06 00:00:00.000",
        "last_sales_date": "2024-01-15 00:00:00.000",
        "to_keep": True,
        "is_minor": False,
    }
    (unenriched_skus_fields,) = (
        result.filter(pl.col("unique_sku_id") != enriched_sku_id)
        .select(
            "to_keep",
            "is_minor",
            "release_date",
            "last_sales_date",
        )
        .unique()
        .to_dicts()
    )
    # Skus for which there are no sales the default values in these fields
    assert unenriched_skus_fields == {
        "to_keep": False,
        "is_minor": True,
        "release_date": None,
        "last_sales_date": None,
    }


def test_skus_aggregator_handles_empty_dim_sku():
    input_data = {
        TableName.DIM_SKU.value: LegacyDimSKUsTable(df=pl.DataFrame()),
        TableName.FACT_SALES.value: LegacyFactSalesTable(df=pl.DataFrame()),
        TableName.DIM_PORTALS.value: LegacyDimPortalsTable(df=pl.DataFrame()),
    }
    result = SkusAggregator(**input_data).aggregate().df
    assert result.is_empty() is True
    EventsServiceSKUsTable.model.validate(result)


def test_skus_aggregator_handles_empty_fact_sales(input_data):
    input_data[TableName.FACT_SALES.value] = LegacyFactSalesTable(df=pl.DataFrame())
    result = SkusAggregator(**input_data).aggregate().df
    assert len(result) == 10
    EventsServiceSKUsTable.model.validate(result)
    (unenriched_skus_fields,) = (
        result.select(
            "to_keep",
            "is_minor",
            "release_date",
            "last_sales_date",
        )
        .unique()
        .to_dicts()
    )
    # Skus for which there are no sales the default values in these fields
    assert unenriched_skus_fields == {
        "to_keep": False,
        "is_minor": True,
        "release_date": None,
        "last_sales_date": None,
    }
