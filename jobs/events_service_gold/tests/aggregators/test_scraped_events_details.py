from unittest.mock import MagicMock

import polars as pl
import pytest

from events_service_gold.aggregators import ScrapedEventsDetailsAggregator
from events_service_gold.schemas import ALL_DISPLAY_PORTALS, ScrapedEventsDetailsSchema


def create_mock_table(data):
    df = pl.DataFrame(data)
    mock_table = MagicMock()
    mock_table.df = df
    return mock_table


@pytest.fixture
def sample_data():
    """Sample data for tests."""
    return {
        "observation_discounts": {
            "unique_event_id": [1, 2],
            "portal": ["Steam", "PlayStation"],
            "event_name": ["Event 1", "Event 2"],
            "unique_sku_id": ["sku_1", "sku_2"],
            "discount_depth": [10, 20],
            "major": [True, True],
            "datetime_from": ["2023-01-01T00:00:00Z", "2024-02-02T00:00:00Z"],
            "datetime_to": ["2023-01-05T00:00:00Z", "2024-02-06T00:00:00Z"],
        },
        "observation_sales": {
            "date": ["2023-01-01", "2023-01-02"],
            "unique_sku_id": ["sku_1", "sku_2"],
            "gross_sales": [100.0, 200.0],
            "gross_returned": [10.0, 20.0],
            "units_sold": [10, 20],
            "units_returned": [1, 2],
        },
        "fact_baseline": {
            "date": ["2023-01-01", "2023-01-02"],
            "unique_sku_id": ["sku_1", "sku_2"],
            "baseline_units_sold_directly": [5, 10],
            "baseline_units_sold_non_billable": [2, 4],
            "baseline_units_sold_retail": [1, 1],
            "baseline_units_returned": [0, 0],
            "uplift_units_sold_directly": [3, 5],
            "uplift_units_sold_non_billable": [1, 2],
            "uplift_units_sold_retail": [0, 1],
            "baseline_gross_sales": [80.0, 160.0],
            "baseline_gross_returned": [8.0, 16.0],
            "uplift_gross_sales": [20.0, 40.0],
            "uplift_gross_returned": [2.0, 4.0],
            "uplift_units_returned": [0, 0],
        },
    }


@pytest.fixture
def aggregator(sample_data):
    """Fixture to set up the aggregator with transformed mock data."""
    observation_discounts_df = pl.DataFrame(
        sample_data["observation_discounts"]
    ).with_columns(
        pl.col("unique_sku_id").cast(pl.Categorical),
        pl.col("portal").cast(pl.Enum(ALL_DISPLAY_PORTALS)),
        pl.col("datetime_from").str.to_datetime(
            format="%Y-%m-%dT%H:%M:%SZ", time_zone="UTC"
        ),
        pl.col("datetime_to").str.to_datetime(
            format="%Y-%m-%dT%H:%M:%SZ", time_zone="UTC"
        ),
        pl.col("discount_depth").cast(pl.UInt8),
    )
    observation_sales_df = pl.DataFrame(sample_data["observation_sales"])
    fact_baseline_df = pl.DataFrame(sample_data["fact_baseline"])

    observation_discounts = MagicMock()
    observation_discounts.df = observation_discounts_df

    observation_sales = MagicMock()
    observation_sales.df = observation_sales_df.with_columns(
        pl.col("date").str.to_date()
    )

    fact_baseline = MagicMock()
    fact_baseline.df = fact_baseline_df.with_columns(pl.col("date").str.to_date())

    return ScrapedEventsDetailsAggregator(
        observation_discounts=observation_discounts,
        observation_sales=observation_sales,
        fact_baseline=fact_baseline,
    )


def test_schema_validation():
    """Test schema validation on a valid and invalid DataFrame."""
    valid_df = pl.DataFrame(
        {
            "unique_sku_id": ["sku_1", "sku_2"],
            "portal": ["Steam", "Steam"],
            "event_name": ["Event 1", "Event 2"],
            "datetime_from": ["2023-01-01T00:00:00Z", "2024-02-02T00:00:00Z"],
            "datetime_to": ["2023-01-05T00:00:00Z", "2024-02-06T00:00:00Z"],
            "discount_depth": [10, 20],
            "major": [True, True],
            "units": [100, 200],
            "revenue": [1000.0, 2000.0],
            "baseline_units": [80, 150],
            "baseline_revenue": [800.0, 1500.0],
            "uplift_units": [20, 50],
            "uplift_revenue": [200.0, 500.0],
        }
    ).with_columns(
        pl.col("unique_sku_id").cast(pl.Categorical),
        pl.col("portal").cast(pl.Enum(ALL_DISPLAY_PORTALS)),
        pl.col("datetime_from").str.to_datetime(
            format="%Y-%m-%dT%H:%M:%SZ", time_zone="UTC"
        ),
        pl.col("datetime_to").str.to_datetime(
            format="%Y-%m-%dT%H:%M:%SZ", time_zone="UTC"
        ),
        pl.col("discount_depth").cast(pl.UInt8),
    )

    validated_df = ScrapedEventsDetailsSchema.validate(valid_df.lazy())
    assert validated_df is not None

    invalid_df = valid_df.drop("revenue")
    with pytest.raises(Exception):
        ScrapedEventsDetailsSchema.validate(invalid_df.lazy())


def test_aggregate(aggregator):
    """Test the aggregation logic."""
    result = aggregator._aggregate()

    assert isinstance(result, pl.DataFrame)
    assert not result.is_empty()


def test_aggregate_with_empty_discounts(sample_data):
    """Test that an empty observation_discounts results in an empty DataFrame with valid schema."""
    empty_observation_discounts = pl.DataFrame(
        {key: [] for key in sample_data["observation_discounts"].keys()}
    )
    observation_sales_df = pl.DataFrame(sample_data["observation_sales"]).with_columns(
        pl.col("date").str.to_date()
    )
    fact_baseline_df = pl.DataFrame(sample_data["fact_baseline"]).with_columns(
        pl.col("date").str.to_date()
    )

    observation_discounts = MagicMock()
    observation_discounts.df = empty_observation_discounts

    observation_sales = MagicMock()
    observation_sales.df = observation_sales_df

    fact_baseline = MagicMock()
    fact_baseline.df = fact_baseline_df

    aggregator = ScrapedEventsDetailsAggregator(
        observation_discounts=observation_discounts,
        observation_sales=observation_sales,
        fact_baseline=fact_baseline,
    )

    result = aggregator._aggregate()

    # Result should be empty but with valid schema
    assert (
        result.is_empty()
    ), "Result should be empty when observation_discounts is empty."
    assert set(result.columns) == set(ScrapedEventsDetailsSchema.__annotations__.keys())


def test_aggregate_with_empty_sales(sample_data):
    """Test that an empty observation_sales results in units and revenue as 0."""
    observation_discounts_df = pl.DataFrame(
        sample_data["observation_discounts"]
    ).with_columns(
        pl.col("unique_sku_id").cast(pl.Categorical),
        pl.col("portal").cast(pl.Enum(ALL_DISPLAY_PORTALS)),
        pl.col("datetime_from").str.to_datetime(
            format="%Y-%m-%dT%H:%M:%SZ", time_zone="UTC"
        ),
        pl.col("datetime_to").str.to_datetime(
            format="%Y-%m-%dT%H:%M:%SZ", time_zone="UTC"
        ),
        pl.col("discount_depth").cast(pl.UInt8),
    )
    empty_observation_sales = pl.DataFrame(
        {key: [] for key in sample_data["observation_sales"].keys()}
    )
    fact_baseline_df = pl.DataFrame(sample_data["fact_baseline"]).with_columns(
        pl.col("date").str.to_date()
    )

    observation_discounts = MagicMock()
    observation_discounts.df = observation_discounts_df

    observation_sales = MagicMock()
    observation_sales.df = empty_observation_sales

    fact_baseline = MagicMock()
    fact_baseline.df = fact_baseline_df

    aggregator = ScrapedEventsDetailsAggregator(
        observation_discounts=observation_discounts,
        observation_sales=observation_sales,
        fact_baseline=fact_baseline,
    )

    result = aggregator._aggregate()

    # Units and revenue should be 0
    assert (
        not result.is_empty()
    ), "Result should not be empty when observation_discounts is not empty."
    assert all(
        result["units"] == 0
    ), "Units should be 0 when observation_sales is empty."
    assert all(
        result["revenue"] == 0
    ), "Revenue should be 0 when observation_sales is empty."


def test_aggregate_with_empty_baseline(sample_data):
    """Test that an empty fact_baseline results in baseline and uplift fields as 0."""
    observation_discounts_df = pl.DataFrame(
        sample_data["observation_discounts"]
    ).with_columns(
        pl.col("unique_sku_id").cast(pl.Categorical),
        pl.col("portal").cast(pl.Enum(ALL_DISPLAY_PORTALS)),
        pl.col("datetime_from").str.to_datetime(
            format="%Y-%m-%dT%H:%M:%SZ", time_zone="UTC"
        ),
        pl.col("datetime_to").str.to_datetime(
            format="%Y-%m-%dT%H:%M:%SZ", time_zone="UTC"
        ),
        pl.col("discount_depth").cast(pl.UInt8),
    )
    observation_sales_df = pl.DataFrame(sample_data["observation_sales"]).with_columns(
        pl.col("date").str.to_date()
    )
    empty_fact_baseline = pl.DataFrame(
        {key: [] for key in sample_data["fact_baseline"].keys()}
    )

    observation_discounts = MagicMock()
    observation_discounts.df = observation_discounts_df

    observation_sales = MagicMock()
    observation_sales.df = observation_sales_df

    fact_baseline = MagicMock()
    fact_baseline.df = empty_fact_baseline

    aggregator = ScrapedEventsDetailsAggregator(
        observation_discounts=observation_discounts,
        observation_sales=observation_sales,
        fact_baseline=fact_baseline,
    )

    result = aggregator._aggregate()

    # Baseline and uplift fields should be 0
    assert (
        not result.is_empty()
    ), "Result should not be empty when observation_discounts is not empty."
    assert all(
        result["baseline_units"] == 0
    ), "Baseline units should be 0 when fact_baseline is empty."
    assert all(
        result["baseline_revenue"] == 0
    ), "Baseline revenue should be 0 when fact_baseline is empty."
    assert all(
        result["uplift_units"] == 0
    ), "Uplift units should be 0 when fact_baseline is empty."
    assert all(
        result["uplift_revenue"] == 0
    ), "Uplift revenue should be 0 when fact_baseline is empty."
