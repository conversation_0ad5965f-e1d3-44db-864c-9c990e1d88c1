from unittest.mock import MagicMock

import polars as pl
import pytest

from events_service_gold.aggregators import ExternalSteamEventsAggregator
from events_service_gold.schemas import ALL_DISPLAY_PORTALS, ExternalSteamEventsSchema


def create_mock_table(data):
    df = pl.DataFrame(data)
    mock_table = MagicMock()
    mock_table.df = df
    return mock_table


@pytest.fixture
def sample_data():
    """Sample data for tests."""
    return {
        "external_steam_events": {
            "start_date": ["2023-01-01T00:00:00Z", "2023-02-01T00:00:00Z"],
            "end_date": ["2023-01-10T00:00:00Z", "2023-02-10T00:00:00Z"],
            "major": [True, False],
            "name": ["Winter Sale", "Lunar New Year"],
            "start_day": [1, 1],
            "start_month": [1, 2],
            "start_year": [2023, 2023],
            "end_day": [10, 10],
            "end_month": [1, 2],
            "end_year": [2023, 2023],
        }
    }


@pytest.fixture
def aggregator(sample_data):
    """Fixture to set up the aggregator with transformed mock data."""
    external_steam_events_df = pl.DataFrame(
        sample_data["external_steam_events"]
    ).with_columns(
        pl.col("start_date").str.strptime(pl.Date, format="%Y-%m-%dT%H:%M:%SZ"),
        pl.col("end_date").str.strptime(pl.Date, format="%Y-%m-%dT%H:%M:%SZ"),
    )

    external_steam_events = MagicMock()
    external_steam_events.df = external_steam_events_df

    return ExternalSteamEventsAggregator(external_steam_events=external_steam_events)


def test_schema_validation():
    """Test schema validation on a valid and invalid DataFrame."""
    valid_df = pl.DataFrame(
        {
            "start_date": ["2023-01-01T00:00:00Z", "2023-02-01T00:00:00Z"],
            "end_date": ["2023-01-10T00:00:00Z", "2023-02-10T00:00:00Z"],
            "major": [True, False],
            "name": ["Winter Sale", "Lunar New Year"],
            "start_day": [1, 1],
            "start_month": [1, 2],
            "start_year": [2023, 2023],
            "end_day": [10, 10],
            "end_month": [1, 2],
            "end_year": [2023, 2023],
            "portal": ["Steam", "Steam"],
        }
    ).with_columns(
        pl.col("start_date").str.strptime(pl.Date, format="%Y-%m-%dT%H:%M:%SZ"),
        pl.col("end_date").str.strptime(pl.Date, format="%Y-%m-%dT%H:%M:%SZ"),
        pl.col("portal").cast(pl.Enum(ALL_DISPLAY_PORTALS)),
    )

    validated_df = ExternalSteamEventsSchema.validate(valid_df.lazy())
    assert validated_df is not None

    # Test with an invalid DataFrame (missing required column)
    invalid_df = valid_df.drop("name")
    with pytest.raises(Exception):
        ExternalSteamEventsSchema.validate(invalid_df.lazy())


def test_aggregate(aggregator):
    """Test the aggregation logic."""
    result = aggregator._aggregate()

    # Check that the result is a DataFrame and not empty
    assert isinstance(result, pl.DataFrame)
    assert not result.is_empty()

    # Check that the portal column was added correctly
    assert "portal" in result.columns
    assert all(result["portal"] == "Steam")

    # Check that all required columns are present
    expected_columns = [
        "start_date",
        "end_date",
        "major",
        "name",
        "start_day",
        "start_month",
        "start_year",
        "end_day",
        "end_month",
        "end_year",
        "portal",
    ]
    for col in expected_columns:
        assert col in result.columns


def test_aggregate_with_empty_input():
    """Test aggregation with empty input data."""
    # Create an empty DataFrame with the correct schema
    empty_df = pl.DataFrame(
        {
            "start_date": [],
            "end_date": [],
            "major": [],
            "name": [],
            "start_day": [],
            "start_month": [],
            "start_year": [],
            "end_day": [],
            "end_month": [],
            "end_year": [],
        }
    )

    external_steam_events = MagicMock()
    external_steam_events.df = empty_df

    aggregator = ExternalSteamEventsAggregator(
        external_steam_events=external_steam_events
    )

    result = aggregator._aggregate()

    # Result should be empty but with valid schema including portal column
    assert result.is_empty(), "Result should be empty when input is empty"
    assert (
        "portal" in result.columns
    ), "Portal column should be added even for empty results"


def test_column_types(aggregator):
    """Test that output columns have the correct types."""
    result = aggregator._aggregate()

    # Check date columns
    assert result["start_date"].dtype == pl.Date
    assert result["end_date"].dtype == pl.Date

    # Check boolean column
    assert result["major"].dtype == pl.Boolean

    # Check string column
    assert result["name"].dtype == pl.Utf8

    # Check integer columns
    assert result["start_day"].dtype == pl.Int64
    assert result["start_month"].dtype == pl.Int64
    assert result["start_year"].dtype == pl.Int64
    assert result["end_day"].dtype == pl.Int64
    assert result["end_month"].dtype == pl.Int64
    assert result["end_year"].dtype == pl.Int64

    # Check enum column
    assert result["portal"].dtype
