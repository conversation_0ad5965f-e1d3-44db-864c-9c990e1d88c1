import polars as pl
import pytest
from data_sdk.domain import TableName
from data_sdk.domain.tables import LegacyFactDetectedEventsTable

from events_service_gold.aggregators import ScrapedEventsAggregator
from events_service_gold.tables import (
    EventsServiceScrapedEventsTable,
)


@pytest.fixture
def input_data(legacy_fact_discounts_factory):
    return {
        TableName.FACT_DISCOUNTS.value: legacy_fact_discounts_factory.build_table(
            size=5
        ),
    }


def test_baseline_aggregator_returns_valid_schema(input_data):
    discount_data = ScrapedEventsAggregator(**input_data).aggregate()
    result = discount_data.df
    EventsServiceScrapedEventsTable.model.validate(result)
    assert len(result) == 5


def test_baseline_aggregator_handles_empty_inputs():
    input_data = {
        TableName.FACT_DISCOUNTS.value: LegacyFactDetectedEventsTable(
            df=pl.DataFrame()
        ),
    }
    baseline_data = ScrapedEventsAggregator(**input_data).aggregate()
    result = baseline_data.df
    assert result.is_empty() is True
    EventsServiceScrapedEventsTable.model.validate(result)
