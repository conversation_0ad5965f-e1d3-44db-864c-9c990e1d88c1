from datetime import datetime

import polars as pl
import pytest
from data_sdk.domain import TableName
from data_sdk.domain.tables import LegacyFactDetectedEventsTable

from events_service_gold.aggregators import DetectedEventsAggregator
from events_service_gold.tables import EventsServiceDetectedEventsTable


@pytest.fixture
def input_data(legacy_fact_detected_events_factory):
    return {
        TableName.FACT_DETECTED_EVENTS.value: legacy_fact_detected_events_factory.build_table(
            size=5
        ),
    }


def test_detected_events_aggregator_returns_expected_values(input_data):
    detected_events_data = DetectedEventsAggregator(**input_data).aggregate()
    result = detected_events_data.df
    EventsServiceDetectedEventsTable.model.validate(result)
    assert len(result) == 5
    assert result[
        [
            "unique_sku_id",
            "studio_id",
            "release_date",
            "release_datetime",
            "last_sales_date",
            "last_sales_datetime",
            "discount_depth",
            "portal_platform_region_id",
            "base_sku_id",
            "gso",
            "promo_length",
        ]
    ].unique().to_dicts() == [
        {
            "unique_sku_id": "165761-steam:1",
            "studio_id": 1,
            "release_date": "2017-05-25",
            "release_datetime": datetime.fromisoformat("2017-05-25 12:00:00Z"),
            "last_sales_date": "2024-05-11",
            "last_sales_datetime": datetime.fromisoformat("2024-05-11 12:00:00Z"),
            "discount_depth": 99.9,
            "portal_platform_region_id": 171010,
            "base_sku_id": "165761",
            "gso": 2851009,
            "promo_length": 5.0,
        }
    ]

    assert result[
        [
            "discount_type",
            "event_type",
            "event_id",
            "datetime_from",
            "datetime_to",
            "major",
        ]
    ].to_dicts() == [
        {
            "discount_type": None,
            "event_type": "cooldown",
            "event_id": "165761-steam:1:2024-01-01",
            "datetime_from": datetime.fromisoformat("2024-01-01 00:00:00Z"),
            "datetime_to": datetime.fromisoformat("2024-01-06 23:59:59Z"),
            "major": 0,
        },
        {
            "discount_type": "custom",
            "event_type": "discount",
            "event_id": "165761-steam:1:2024-01-08",
            "datetime_from": datetime.fromisoformat("2024-01-08 00:00:00Z"),
            "datetime_to": datetime.fromisoformat("2024-01-13 23:59:59Z"),
            "major": 0,
        },
        {
            "discount_type": "store",
            "event_type": "store_discount",
            "event_id": "165761-steam:1:2024-01-15",
            "datetime_from": datetime.fromisoformat("2024-01-15 00:00:00Z"),
            "datetime_to": datetime.fromisoformat("2024-01-20 23:59:59Z"),
            "major": 1,
        },
        {
            "discount_type": None,
            "event_type": "unused",
            "event_id": "165761-steam:1:2024-01-22",
            "datetime_from": datetime.fromisoformat("2024-01-22 00:00:00Z"),
            "datetime_to": datetime.fromisoformat("2024-01-27 23:59:59Z"),
            "major": 0,
        },
        {
            "discount_type": None,
            "event_type": "cooldown",
            "event_id": "165761-steam:1:2024-01-29",
            "datetime_from": datetime.fromisoformat("2024-01-29 00:00:00Z"),
            "datetime_to": datetime.fromisoformat("2024-02-03 23:59:59Z"),
            "major": 0,
        },
    ]


def test_detected_events_aggregator_handles_empty_inputs():
    input_data = {
        TableName.FACT_DETECTED_EVENTS.value: LegacyFactDetectedEventsTable(
            df=pl.DataFrame()
        ),
    }
    detected_events_data = DetectedEventsAggregator(**input_data).aggregate()
    result = detected_events_data.df
    assert result.is_empty() is True
    EventsServiceDetectedEventsTable.model.validate(result)
