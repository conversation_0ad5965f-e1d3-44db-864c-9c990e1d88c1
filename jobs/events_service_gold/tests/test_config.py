from pathlib import Path

import pytest

from events_service_gold.config import Config


@pytest.fixture(autouse=True)
def set_env_variables(monkeypatch):
    monkeypatch.setenv("ENV", "production")
    monkeypatch.setenv("APP_VERSION", "2.0.0")
    monkeypatch.setenv("JOB_NAME", "event_service-gold-job")
    monkeypatch.setenv("DOCKER_TAG", "latest")
    monkeypatch.setenv("DOCKER_BUILD_TIMESTAMP", "2022-01-01")
    monkeypatch.setenv("DEV_USERNAME", "testuser")
    monkeypatch.setenv("READER_CFG__ACCOUNT_NAME", "dlsaggregateddevs7")
    monkeypatch.setenv("READER_CFG__CONTAINER_NAME", "processed-data")
    monkeypatch.setenv("READER_CFG__BASE_DIR", "processed-reports")
    monkeypatch.setenv("READER_CFG__TYPE", "dls")
    monkeypatch.setenv("WRITER_CFG__ACCOUNT_NAME", "dlsaggregateddevs7")
    monkeypatch.setenv("WRITER_CFG__CONTAINER_NAME", "event_service-gold-poc")
    monkeypatch.setenv("WRITER_CFG__BASE_DIR", "")
    monkeypatch.setenv("WRITER_CFG__TYPE", "dls")


def test_config_env():
    config = Config()

    assert config.env == "production"


def test_config_app_version():
    config = Config()

    assert config.app_version == "2.0.0"


def test_config_job_name():
    config = Config()

    assert config.job_name == "event_service-gold-job"


def test_config_docker_tag():
    config = Config()

    assert config.docker_tag == "latest"


def test_config_docker_build_timestamp():
    config = Config()

    assert config.docker_build_timestamp == "2022-01-01"


def test_config_dev_username():
    config = Config()

    assert config.dev_username == "testuser"


def test_config_silver_account_name():
    config = Config()

    assert config.reader_cfg.account_name == "dlsaggregateddevs7"


def test_config_silver_container_name():
    config = Config()

    assert config.reader_cfg.container_name == "processed-data"


def test_config_silver_base_dir():
    config = Config()

    assert config.reader_cfg.base_dir == Path("processed-reports")


def test_config_gold_account_name():
    config = Config()

    assert config.writer_cfg.account_name == "dlsaggregateddevs7"


def test_config_gold_container_name():
    config = Config()

    assert config.writer_cfg.container_name == "event_service-gold-poc"
