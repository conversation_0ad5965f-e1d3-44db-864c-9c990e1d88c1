import logging
from enum import Enum

import typer
from data_sdk.config import DLSConfig, DummyConfig, LocalConfig
from data_sdk.logging import force_human_readable_handler_for_tty
from pipeline_sdk.monitoring.logs import configure_logger

from events_service_gold.config import Config
from events_service_gold.job import Params, run

configure_logger(
    "events_service_gold",
    custom_loggers_config={
        "data_sdk": {"level": "INFO"},
    },
)

force_human_readable_handler_for_tty()

log = logging.getLogger(__name__)


class Env(str, Enum):
    DEV = "dev"
    PROD = "prod"
    LOCAL = "local"
    DRY = "dry"


class RunType(str, Enum):
    DELTA = "delta"
    CUSTOM_PARTITIONS = "cp"
    BOTH = "both"


def main(
    reader_env: Env = Env.DEV,
    writer_env: Env = Env.LOCAL,
    studio_id: int = 1,
    run_type: RunType = RunType.BOTH,
):
    if reader_env == Env.DRY:
        raise Exception("Dry run is not supported for reader")
    elif reader_env == Env.LOCAL:
        raise Exception("Local is not supported for reader")
    if reader_env == Env.DEV:
        reader_cfg = DLSConfig(
            account_name="dlsaggregateddevs7",
            container_name="core-silver",
            base_dir="result",
        )
    elif reader_env == Env.PROD:
        reader_cfg = DLSConfig(
            account_name="dlsaggregatedprodr9",
            container_name="core-silver",
            base_dir="result",
        )

    if writer_env == Env.DRY:
        writer_cfg = DummyConfig()
    elif writer_env == Env.LOCAL:
        writer_cfg = LocalConfig(local_dir="playground")
    elif writer_env == Env.DEV:
        writer_cfg = DLSConfig(
            account_name="dlsaggregateddevs7",
            container_name="events-service-gold-poc",
            base_dir="",
        )
    elif writer_env == Env.PROD:
        raise Exception("Prod is not supported for writer")

    log.info(
        "Starting event_service Gold Job. Source: %s studio_id: %i, saving destination: %s, Run Type %s.",
        reader_env,
        studio_id,
        writer_env,
        run_type,
    )

    config = Config(
        hostname="localhost",
        env="local",
        run_type=run_type,
        reader_cfg=reader_cfg,
        writer_cfg=writer_cfg,
    )

    run(params=Params(studio_id=studio_id), config=config)

    log.info("Finishing event_service Gold Job")


if __name__ == "__main__":
    typer.run(main)
