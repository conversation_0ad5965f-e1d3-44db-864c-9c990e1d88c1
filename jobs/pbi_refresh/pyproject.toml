[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poe]
include = "../../common/poe_shared_tasks.toml"

[tool.poe.tasks]
hooks = { "shell" = "../../.hooks/install.sh 'jobs/pbi_refresh'" }

[tool.poetry]
name = "pbi_refresh"
version = "0.1.0"
description = "PowerBI data refresh job"
authors = ["Your Name <<EMAIL>>"]

[tool.poetry.dependencies]
python = "~3.12"
azure-identity = "^1.13.0"
azure-servicebus = "^7.7.0"
pydantic = "^2.6.3"
sentry-sdk = "^2.0.0"
pipeline-sdk = {version = "2.5.2", source = "indiebi"}
data-sdk = {version = "1.6.4", source = "indiebi"}

[tool.poetry.group.dev.dependencies]
black = "^22.6.0"
mypy = "^1.0.0"
pylint = "^2.14.3"
types-requests = "^2.28.0"
responses = "^0.25.0"
time-machine = "^2.11.0"
ruff = "^0.4.5"
poethepoet = "^0.26.1"
pre-commit = "^3.7.1"

[tool.poetry.group.test.dependencies]
pytest = "^8.2.2"
pytest-deadfixtures = "^2.2.1"
pytest-xdist = "^3.6.1"
pytest-random-order = "^1.1.1"

[[tool.poetry.source]]
name = "indiebi"
url = "https://gitlab.com/api/v4/groups/4449864/-/packages/pypi/simple"
priority = "explicit"

[tool.ruff.lint.per-file-ignores]
"pbi_refresh/main.py" = [
    "E402", # Module level import not at top of file
]
