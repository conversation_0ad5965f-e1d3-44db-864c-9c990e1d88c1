import logging
from datetime import datetime
from enum import Enum

from pydantic import BaseModel
from pbi_refresh.config import Config
from pbi_refresh.requests import send_request_with_retry

log = logging.getLogger(__name__)


class RefreshType(Enum):
    FULL = "full"
    INCREMENTAL = "incremental"


class PBIRefreshStatus(Enum):
    """https://docs.microsoft.com/en-us/rest/api/power-bi/datasets/get-refresh-history"""

    UNKNOWN = "Unknown"
    COMPLETED = "Completed"
    FAILED = "Failed"
    CANCELLED = "Cancelled"
    DISABLED = "Disabled"
    MISSING = "Missing"

    def is_refreshing(self) -> bool:
        return self == self.UNKNOWN

    def is_completed(self) -> bool:
        return self == self.COMPLETED


class PBIRefresh(BaseModel):
    status: PBIRefreshStatus
    end_time: datetime | None
    details: dict | None


def trigger_pbi_refresh(shard_id: str, refresh_type: RefreshType, config: Config):
    headers = {"x-api-key": config.dataset_manager_key}

    response = send_request_with_retry(
        method="post",
        url=f"{config.dataset_manager_url}shard/{shard_id}/refresh/{refresh_type.value}",
        headers=headers,
    )
    log.info(
        "Sending PBI refresh request for shard id: %s, with response status: %i",
        shard_id,
        response.status_code,
    )
    return response


def last_refresh(shard_id: str, config: Config) -> PBIRefresh:
    response = send_request_with_retry(
        method="get",
        url=f"{config.dataset_manager_url}shard/{shard_id}/refresh-status",
        headers={"x-api-key": config.dataset_manager_key},
    ).json()
    log.info("Current shard %s status is: %s", shard_id, response["status"])
    return PBIRefresh(
        status=response["status"],
        end_time=response["end_time"],
        details=response.get("details"),
    )
