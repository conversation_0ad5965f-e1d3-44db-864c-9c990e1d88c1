from datetime import date

from pydantic import BaseModel

from pbi_refresh.config import Config
from pbi_refresh.requests import send_request_with_retry


class Report(BaseModel):
    id: int
    studio_id: int
    source: str
    date_from: date | None
    date_to: date | None


def get_reports_by_ids(report_ids: list[int], config: Config) -> list[Report]:
    """
    Sends http GET /report request to Report Service.
    Returns a list of manifests for a given sourceIds or rises ??? error.
    """
    response = send_request_with_retry(
        method="get",
        url=f"{config.report_service_url}reports?",
        headers={"x-api-key": config.report_service_key},
        params={"ids": report_ids, "sort_by": "+id"},
    )

    reports = response.json()["data"]
    return [
        Report(
            id=report["id"],
            studio_id=report["studio_id"],
            source=report["source"],
            date_from=report["date_from"],
            date_to=report["date_to"],
        )
        for report in reports
    ]
