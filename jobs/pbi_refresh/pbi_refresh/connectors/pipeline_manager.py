from pbi_refresh.config import Config
from pbi_refresh.requests import send_request_with_retry


def trigger_reasons(job_guid: str, config: Config):
    # TODO: automatically iterate for offset and limit if page is bigger than 1000
    response = send_request_with_retry(
        method="get",
        url=f"{config.pipeline_manager_url}pipeline/trigger-reasons",
        headers={"x-api-key": config.pipeline_manager_key},
        params={
            "job_guid": job_guid,
            "include_finished_pipelines": True,
        },
    )
    return response.json()
