from pydantic import AnyHttpUrl
from pydantic_settings import BaseSettings


class Config(BaseSettings):
    env: str = "local"
    app_version: str = "1.0.0"
    job_name: str = "pbi-refresh-job"
    docker_tag: str = ""
    docker_build_timestamp: str = ""

    report_service_url: AnyHttpUrl
    report_service_key: str

    pipeline_manager_url: AnyHttpUrl
    pipeline_manager_key: str

    dataset_manager_url: AnyHttpUrl
    dataset_manager_key: str

    pbi_sleep_time: int = 30
