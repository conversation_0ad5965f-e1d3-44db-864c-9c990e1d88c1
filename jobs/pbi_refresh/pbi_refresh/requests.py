import logging
import requests
from contextlib import contextmanager
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

log = logging.getLogger(__name__)


def send_request_with_retry(
    method: str,
    url: str,
    headers: dict,
    json: dict | None = None,
    params: dict | None = None,
    backoff_factor: float = 1.0,
) -> requests.Response:
    with _retry_session(backoff_factor=backoff_factor) as session:
        response = session.request(
            method.lower(),
            url=url,
            json=json,
            headers=headers,
            params=params,
            timeout=180,
        )
        response.raise_for_status()
        log.info(
            "Session URL: %s, method %s, status: %i", url, method, response.status_code
        )
        return response


@contextmanager
def _retry_session(
    backoff_factor,
    retries: int = 3,
    force_on_statuses: list[int] | None = None,
):
    if force_on_statuses is None:
        force_on_statuses = [429, 500, 502, 503, 504]
    retry_strategy = Retry(
        total=retries,
        status_forcelist=force_on_statuses,
        raise_on_status=True,
        backoff_factor=backoff_factor,
        allowed_methods={"GET", "PUT", "POST"},
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session = requests.Session()
    session.mount("https://", adapter)
    session.mount("http://", adapter)
    try:
        yield session
    finally:
        session.close()
