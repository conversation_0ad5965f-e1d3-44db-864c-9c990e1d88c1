import logging
import time
import traceback

from elasticapm import capture_span
from pipeline_sdk.job import JobInput
from pydantic import BaseModel, ValidationError

from pbi_refresh.config import Config
from pbi_refresh.connectors.dataset_manager import (
    last_refresh,
    trigger_pbi_refresh,
)
from pbi_refresh.effective_date import get_refresh_type
from pbi_refresh.exceptions import InvalidMessage, PBIModelRefreshFailed

log = logging.getLogger(__name__)


class Params(BaseModel):
    shard_id: str


def run(job_input: JobInput, config: Config):
    params = validate_message(job_input.target)
    refresh_type = get_refresh_type(job_input.job_guid, config)
    if refresh_type is None:
        log.info("Skipping refresh for shard %s", params.shard_id)
        return

    log.info(
        "Triggering refresh for shard %s, refresh type: %s",
        params.shard_id,
        refresh_type.value,
    )

    trigger_pbi_refresh(
        shard_id=params.shard_id,
        refresh_type=refresh_type,
        config=config,
    )

    wait_for_refresh_to_finish(params.shard_id, config)
    log.info("Refresh for shard %s is COMPLETED", params.shard_id)


def validate_message(params) -> Params:
    log.info(f"Validating message: {params}")
    try:
        return Params.model_validate(params)
    except ValidationError:
        log.exception("Received invalid message %s", str(params))
        raise InvalidMessage(traceback.format_exc())


@capture_span()
def wait_for_refresh_to_finish(shard_id: str, config: Config):
    time.sleep(config.pbi_sleep_time)
    refresh_status = last_refresh(shard_id=shard_id, config=config)
    while refresh_status.status.is_refreshing():
        time.sleep(config.pbi_sleep_time)
        refresh_status = last_refresh(shard_id=shard_id, config=config)

    if not refresh_status.status.is_completed():
        raise PBIModelRefreshFailed(
            f"Refresh for shard {shard_id} failed with status {refresh_status.status}"
        )
