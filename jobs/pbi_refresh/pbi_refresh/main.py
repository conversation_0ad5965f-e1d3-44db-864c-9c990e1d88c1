import sentry_sdk

sentry_sdk.init(traces_sample_rate=1.0)

import logging

from pipeline_sdk.job import JobInput, JobOutput, JobRunner
from pipeline_sdk.monitoring.elastic_tracer import elastic_tracer
from pipeline_sdk.monitoring.logs import configure_logger

from pbi_refresh.config import Config
from pbi_refresh.job import run

configure_logger("pbi_refresh")
log = logging.getLogger(__name__)


def handler(job_input: JobInput) -> JobOutput:
    config = Config()
    log.info(
        "Starting pbi_refresh: %s (version %s, build ts: %s)",
        job_input.job_guid,
        config.docker_tag,
        config.docker_build_timestamp,
    )

    run(job_input=job_input, config=config)

    log.info("Finishing pbi_refresh: %s", job_input.job_guid)

    return JobOutput({"shard_id": job_input.target["shard_id"]})


if __name__ == "__main__":
    with elastic_tracer():
        runner = JobRunner(handler)
        runner.run()
