import pytest
import responses
from pydantic import AnyHttpUrl
from pydantic_settings import BaseSettings


@pytest.fixture
def fake_config():
    class FakeConfig(BaseSettings):
        report_service_url: AnyHttpUrl
        report_service_key: str
        dataset_manager_url: AnyHttpUrl
        dataset_manager_key: str
        pipeline_manager_url: AnyHttpUrl
        pipeline_manager_key: str
        pbi_sleep_time: float = 0.1

    return FakeConfig(
        report_service_url="https://report-service.not_existing",
        report_service_key="qwerty",
        dataset_manager_url="https://dataset-manager.not_existing",
        dataset_manager_key="asdf",
        pipeline_manager_url="https://pipeline-manager.not_existing",
        pipeline_manager_key="pipeline-api-key",
    )


@pytest.fixture
def responses_mock():
    with responses.RequestsMock() as mock:
        yield mock


@pytest.fixture
def get_no_trigger_reasons_for_shard(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://pipeline-manager.not_existing/pipeline/trigger-reasons?job_guid=a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378&include_finished_pipelines=True",
        json={
            "data": [],
            "count": 0,
        },
    )


@pytest.fixture
def get_trigger_reasons_for_shard_with_allowed_events(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://pipeline-manager.not_existing/pipeline/trigger-reasons?job_guid=a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378&include_finished_pipelines=True",
        json={
            "data": [
                {
                    "guid": "a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378",
                    "event_name": "REPORTS_CREATED",
                    "pipeline_definition_name": "v2_reports",
                    "event_params": {
                        "studio_id": 1,
                        "observation_type": "sales",
                        "portal": "steam",
                        "report_ids": [43698, 43716],
                    },
                    "creation_timestamp": "2023-07-14T12:58:57.610000Z",
                    "finished_timestamp": "2023-07-17T12:58:51.050000Z",
                    "status": "SUCCESS",
                }
            ],
            "count": 1,
        },
    )


@pytest.fixture
def get_trigger_reasons_for_shard_with_sales_and_discounts(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://pipeline-manager.not_existing/pipeline/trigger-reasons?job_guid=a282fbc3-aaaa-4f0b-b4e9-1eb1e7be3378&include_finished_pipelines=True",
        json={
            "data": [
                {
                    "guid": "a282fbc3-aaaa-4f0b-b4e9-1eb1e7be3378",
                    "event_name": "REPORTS_CREATED",
                    "pipeline_definition_name": "v2_reports",
                    "event_params": {
                        "studio_id": 1,
                        "observation_type": "sales",
                        "portal": "steam",
                        "report_ids": [43698, 43716],
                    },
                    "creation_timestamp": "2023-07-14T12:58:57.610000Z",
                    "finished_timestamp": "2023-07-17T12:58:51.050000Z",
                    "status": "SUCCESS",
                },
                {
                    "guid": "b282fbc3-ac86-4f0b-b4e9-1eb1e7be3378",
                    "event_name": "REPORTS_CREATED",
                    "pipeline_definition_name": "v2_reports",
                    "event_params": {
                        "studio_id": 1,
                        "observation_type": "discounts",
                        "portal": "steam",
                        "report_ids": [53399, 53388],
                    },
                    "creation_timestamp": "2023-07-14T12:58:57.610000Z",
                    "finished_timestamp": "2023-07-17T12:58:51.050000Z",
                    "status": "SUCCESS",
                },
            ],
            "count": 2,
        },
    )


@pytest.fixture
def get_trigger_reasons_for_shard_with_discounts(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://pipeline-manager.not_existing/pipeline/trigger-reasons?job_guid=a282fbc3-bbbb-4f0b-b4e9-1eb1e7be3378&include_finished_pipelines=True",
        json={
            "data": [
                {
                    "guid": "a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378",
                    "event_name": "REPORTS_CREATED",
                    "pipeline_definition_name": "v2_reports",
                    "event_params": {
                        "studio_id": 1,
                        "observation_type": "discounts",
                        "portal": "steam",
                        "report_ids": [53399, 53388],
                    },
                    "creation_timestamp": "2023-07-14T12:58:57.610000Z",
                    "finished_timestamp": "2023-07-17T12:58:51.050000Z",
                    "status": "SUCCESS",
                },
            ],
            "count": 2,
        },
    )


@pytest.fixture
def get_trigger_reasons_for_shard_with_allowed_events_but_no_reports(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://pipeline-manager.not_existing/pipeline/trigger-reasons?job_guid=a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378&include_finished_pipelines=True",
        json={
            "data": [
                {
                    "guid": "a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378",
                    "event_name": "REPORTS_CREATED",
                    "pipeline_definition_name": "v2_reports",
                    "event_params": {
                        "studio_id": 2,
                        "observation_type": "sales",
                        "portal": "steam",
                        "report_ids": [],
                    },
                    "creation_timestamp": "2023-07-14T11:58:57.610000Z",
                    "finished_timestamp": "2023-07-17T11:58:51.050000Z",
                    "status": "SUCCESS",
                },
                {
                    "guid": "a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378",
                    "event_name": "REPORTS_CREATED",
                    "pipeline_definition_name": "v2_reports",
                    "event_params": {
                        "studio_id": 1,
                        "observation_type": "sales",
                        "portal": "steam",
                        "report_ids": [43698, 43716],
                    },
                    "creation_timestamp": "2023-07-14T12:58:57.610000Z",
                    "finished_timestamp": "2023-07-17T12:58:51.050000Z",
                    "status": "SUCCESS",
                },
            ],
            "count": 2,
        },
    )


@pytest.fixture
def get_trigger_reasons_for_shard_with_not_allowed_events(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://pipeline-manager.not_existing/pipeline/trigger-reasons?job_guid=a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378&include_finished_pipelines=True",
        json={
            "data": [
                {
                    "guid": "a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378",
                    "event_name": "SKUS_UPDATED",
                    "pipeline_definition_name": "v2_reports",
                    "event_params": {
                        "studio_id": 2,
                        "observation_type": "sales",
                        "portal": "steam",
                        "sku_ids": ["SOME_SKU_ID:1"],
                    },
                    "creation_timestamp": "2023-07-14T11:58:57.610000Z",
                    "finished_timestamp": "2023-07-17T11:58:51.050000Z",
                    "status": "SUCCESS",
                }
            ],
            "count": 1,
        },
    )


@pytest.fixture
def get_valid_reports_details_by_id_for_2023_03_31(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://report-service.not_existing/reports?ids=43698&ids=43716&sort_by=%2Bid",
        status=504,
    )
    responses_mock.get(
        "https://report-service.not_existing/reports?ids=43698&ids=43716&sort_by=%2Bid",
        json={
            "data": [
                {
                    "studio_id": 1,
                    "original_name": "microsoft_sales-2019-07-01_2020-01-18.zip",
                    "upload_date": "2023-01-31T16:13:32.147000Z",
                    "file_path_raw": "upload/6a34b18cf8a04e9495e8c7cf70585da5",
                    "upload_type": "SCRAPER",
                    "id": 43698,
                    "state": "CONVERTED",
                    "source": "microsoft_sales",
                    "portal": "microsoft",
                    "no_data": True,
                    "date_from": "2023-03-31",
                    "date_to": "2023-04-18",
                },
                {
                    "studio_id": 1,
                    "original_name": "epic_sales-2023-01-29_2023-02-01.zip",
                    "upload_date": "2023-02-01T00:12:06.200000Z",
                    "file_path_raw": "upload/45dd27054d414a3ab072836bf3d2f2e3",
                    "upload_type": "SCRAPER",
                    "id": 43716,
                    "state": "CONVERTED",
                    "source": "epic_sales",
                    "portal": "epic",
                    "no_data": False,
                    "date_from": "2023-04-29",
                    "date_to": "2023-05-01",
                },
            ],
            "count": 2,
        },
    )


@pytest.fixture
def get_valid_reports_details_for_sales_and_discounts(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://report-service.not_existing/reports?ids=43698&ids=43716&ids=53399&ids=53388&sort_by=%2Bid",
        status=504,
    )
    responses_mock.get(
        "https://report-service.not_existing/reports?ids=43698&ids=43716&ids=53399&ids=53388&sort_by=%2Bid",
        json={
            "data": [
                {
                    "studio_id": 1,
                    "original_name": "microsoft_sales-2024-07-10_2024-07-31.zip",
                    "upload_date": "2024-07-31T16:13:32.147000Z",
                    "file_path_raw": "upload/6a34b18cf8a04e9495e8c7cf70585da5",
                    "upload_type": "SCRAPER",
                    "id": 43698,
                    "state": "CONVERTED",
                    "source": "microsoft_sales",
                    "portal": "microsoft",
                    "no_data": True,
                    "date_from": "2024-07-10",
                    "date_to": "2024-07-31",
                },
                {
                    "studio_id": 1,
                    "original_name": "epic_sales-2024-07-10_2024-07-31.zip",
                    "upload_date": "2024-07-31T00:12:06.200000Z",
                    "file_path_raw": "upload/45dd27054d414a3ab072836bf3d2f2e3",
                    "upload_type": "SCRAPER",
                    "id": 43716,
                    "state": "CONVERTED",
                    "source": "epic_sales",
                    "portal": "epic",
                    "no_data": False,
                    "date_from": "2024-07-10",
                    "date_to": "2024-07-31",
                },
                {
                    "studio_id": 1,
                    "original_name": "steam_discounts-2010-01-01_2024-07-31.zip",
                    "upload_date": "2024-07-31T00:12:06.200000Z",
                    "file_path_raw": "upload/45dd27054d414a3ab072836bf3d2f2e3",
                    "upload_type": "SCRAPER",
                    "id": 53399,
                    "state": "CONVERTED",
                    "source": "steam_discounts",
                    "portal": "steam",
                    "no_data": False,
                    "date_from": "2010-01-01",
                    "date_to": "2024-07-31",
                },
                {
                    "studio_id": 1,
                    "original_name": "nintendo_discounts-2010-01-01_2024-07-31.zip",
                    "upload_date": "2024-07-31T00:12:06.200000Z",
                    "file_path_raw": "upload/45dd27054d414a3ab072836bf3d2f2e3",
                    "upload_type": "SCRAPER",
                    "id": 53388,
                    "state": "CONVERTED",
                    "source": "nintendo_discounts",
                    "portal": "nintendo",
                    "no_data": False,
                    "date_from": "2010-01-01",
                    "date_to": "2024-07-31",
                },
            ],
            "count": 4,
        },
    )


@pytest.fixture
def get_valid_reports_details_for_discounts(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://report-service.not_existing/reports?ids=53399&ids=53388&sort_by=%2Bid",
        status=504,
    )
    responses_mock.get(
        "https://report-service.not_existing/reports?ids=53399&ids=53388&sort_by=%2Bid",
        json={
            "data": [
                {
                    "studio_id": 1,
                    "original_name": "steam_discounts-2010-01-01_2024-07-31.zip",
                    "upload_date": "2024-07-31T00:12:06.200000Z",
                    "file_path_raw": "upload/45dd27054d414a3ab072836bf3d2f2e3",
                    "upload_type": "SCRAPER",
                    "id": 53399,
                    "state": "CONVERTED",
                    "source": "steam_discounts",
                    "portal": "steam",
                    "no_data": False,
                    "date_from": "2010-01-01",
                    "date_to": "2024-07-31",
                },
                {
                    "studio_id": 1,
                    "original_name": "nintendo_discounts-2010-01-01_2024-07-31.zip",
                    "upload_date": "2024-07-31T00:12:06.200000Z",
                    "file_path_raw": "upload/45dd27054d414a3ab072836bf3d2f2e3",
                    "upload_type": "SCRAPER",
                    "id": 53388,
                    "state": "CONVERTED",
                    "source": "nintendo_discounts",
                    "portal": "nintendo",
                    "no_data": False,
                    "date_from": "2010-01-01",
                    "date_to": "2024-07-31",
                },
            ],
            "count": 2,
        },
    )


@pytest.fixture
def get_valid_reports_details_by_id_for_2022_12_31(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://report-service.not_existing/reports?ids=43698&ids=43716&sort_by=%2Bid",
        status=504,
    )
    responses_mock.get(
        "https://report-service.not_existing/reports?ids=43698&ids=43716&sort_by=%2Bid",
        json={
            "data": [
                {
                    "studio_id": 1,
                    "original_name": "microsoft_sales-2019-07-01_2020-01-18.zip",
                    "upload_date": "2023-01-31T16:13:32.147000Z",
                    "file_path_raw": "upload/6a34b18cf8a04e9495e8c7cf70585da5",
                    "upload_type": "SCRAPER",
                    "id": 43698,
                    "state": "CONVERTED",
                    "source": "microsoft_sales",
                    "portal": "microsoft",
                    "no_data": True,
                    "date_from": "2022-12-31",
                    "date_to": "2023-04-18",
                },
                {
                    "studio_id": 1,
                    "original_name": "epic_sales-2023-01-29_2023-02-01.zip",
                    "upload_date": "2023-02-01T00:12:06.200000Z",
                    "file_path_raw": "upload/45dd27054d414a3ab072836bf3d2f2e3",
                    "upload_type": "SCRAPER",
                    "id": 43716,
                    "state": "CONVERTED",
                    "source": "epic_sales",
                    "portal": "epic",
                    "no_data": False,
                    "date_from": "2023-04-29",
                    "date_to": "2023-05-01",
                },
            ],
            "count": 2,
        },
    )


@pytest.fixture
def get_valid_reports_details_by_id_for_2023_01_01(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://report-service.not_existing/reports?ids=43698&ids=43716&sort_by=%2Bid",
        status=504,
    )
    responses_mock.get(
        "https://report-service.not_existing/reports?ids=43698&ids=43716&sort_by=%2Bid",
        json={
            "data": [
                {
                    "studio_id": 1,
                    "original_name": "microsoft_sales-2019-07-01_2020-01-18.zip",
                    "upload_date": "2023-01-31T16:13:32.147000Z",
                    "file_path_raw": "upload/6a34b18cf8a04e9495e8c7cf70585da5",
                    "upload_type": "SCRAPER",
                    "id": 43698,
                    "state": "CONVERTED",
                    "source": "microsoft_sales",
                    "portal": "microsoft",
                    "no_data": True,
                    "date_from": "2023-01-01",
                    "date_to": "2023-04-18",
                },
                {
                    "studio_id": 1,
                    "original_name": "epic_sales-2023-01-29_2023-02-01.zip",
                    "upload_date": "2023-02-01T00:12:06.200000Z",
                    "file_path_raw": "upload/45dd27054d414a3ab072836bf3d2f2e3",
                    "upload_type": "SCRAPER",
                    "id": 43716,
                    "state": "CONVERTED",
                    "source": "epic_sales",
                    "portal": "epic",
                    "no_data": False,
                    "date_from": "2023-04-29",
                    "date_to": "2023-05-01",
                },
            ],
            "count": 2,
        },
    )


@pytest.fixture
def get_valid_reports_details_by_id_for_2022_10_01(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://report-service.not_existing/reports?ids=43698&ids=43716&sort_by=%2Bid",
        status=504,
    )
    responses_mock.get(
        "https://report-service.not_existing/reports?ids=43698&ids=43716&sort_by=%2Bid",
        json={
            "data": [
                {
                    "studio_id": 1,
                    "original_name": "microsoft_sales-2019-07-01_2020-01-18.zip",
                    "upload_date": "2023-01-31T16:13:32.147000Z",
                    "file_path_raw": "upload/6a34b18cf8a04e9495e8c7cf70585da5",
                    "upload_type": "SCRAPER",
                    "id": 43698,
                    "state": "CONVERTED",
                    "source": "microsoft_sales",
                    "portal": "microsoft",
                    "no_data": True,
                    "date_from": "2022-10-01",
                    "date_to": "2023-04-18",
                },
                {
                    "studio_id": 1,
                    "original_name": "epic_sales-2023-01-29_2023-02-01.zip",
                    "upload_date": "2023-02-01T00:12:06.200000Z",
                    "file_path_raw": "upload/45dd27054d414a3ab072836bf3d2f2e3",
                    "upload_type": "SCRAPER",
                    "id": 43716,
                    "state": "CONVERTED",
                    "source": "epic_sales",
                    "portal": "epic",
                    "no_data": False,
                    "date_from": "2023-04-29",
                    "date_to": "2023-05-01",
                },
            ],
            "count": 2,
        },
    )


@pytest.fixture
def get_valid_reports_details_by_id_with_null_date_from(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://report-service.not_existing/reports?ids=43698&ids=43716&sort_by=%2Bid",
        status=504,
    )
    responses_mock.get(
        "https://report-service.not_existing/reports?ids=43698&ids=43716&sort_by=%2Bid",
        json={
            "data": [
                {
                    "studio_id": 1,
                    "original_name": "microsoft_sales-2019-07-01_2020-01-18.zip",
                    "upload_date": "2023-01-31T16:13:32.147000Z",
                    "file_path_raw": "upload/6a34b18cf8a04e9495e8c7cf70585da5",
                    "upload_type": "SCRAPER",
                    "id": 43698,
                    "state": "CONVERTED",
                    "source": "microsoft_sales",
                    "portal": "microsoft",
                    "no_data": True,
                    "date_from": None,
                    "date_to": "2023-04-18",
                },
                {
                    "studio_id": 1,
                    "original_name": "epic_sales-2023-01-29_2023-02-01.zip",
                    "upload_date": "2023-02-01T00:12:06.200000Z",
                    "file_path_raw": "upload/45dd27054d414a3ab072836bf3d2f2e3",
                    "upload_type": "SCRAPER",
                    "id": 43716,
                    "state": "CONVERTED",
                    "source": "epic_sales",
                    "portal": "epic",
                    "no_data": False,
                    "date_from": "2023-04-29",
                    "date_to": "2023-05-01",
                },
            ],
            "count": 2,
        },
    )


@pytest.fixture
def get_valid_reports_details_by_id_retry_exceeded(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://report-service.not_existing/reports?ids=43698&ids=43716&sort_by=%2Bid",
        status=504,
    )
    responses_mock.get(
        "https://report-service.not_existing/reports?ids=43698&ids=43716&sort_by=%2Bid",
        status=504,
    )
    responses_mock.get(
        "https://report-service.not_existing/reports?ids=43698&ids=43716&sort_by=%2Bid",
        status=504,
    )
    responses_mock.get(
        "https://report-service.not_existing/reports?ids=43698&ids=43716&sort_by=%2Bid",
        status=504,
    )


@pytest.fixture
def call_full_refresh(
    responses_mock: responses.RequestsMock,
):
    responses_mock.post(
        "https://dataset-manager.not_existing/shard/22222222-1758-40f1-bf7d-13880a5c3ce5/refresh/full",
    )


@pytest.fixture
def call_full_refresh_on_busy_shard(
    responses_mock: responses.RequestsMock,
):
    responses_mock.post(
        "https://dataset-manager.not_existing/shard/22222222-1758-40f1-bf7d-13880a5c3ce5/refresh/full",
        status=202,
        json={"detail": "Another refresh request is already executing"},
    )


@pytest.fixture
def call_incremental_refresh(
    responses_mock: responses.RequestsMock,
):
    responses_mock.post(
        "https://dataset-manager.not_existing/shard/22222222-1758-40f1-bf7d-13880a5c3ce5/refresh/incremental",
    )


@pytest.fixture
def refresh_status_completed(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://dataset-manager.not_existing/shard/22222222-1758-40f1-bf7d-13880a5c3ce5/refresh-status",
        json={"status": "Completed", "end_time": "2022-08-08T10:10:10.000000+00:00"},
    )


@pytest.fixture
def refresh_status_retry_twice_then_completed(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://dataset-manager.not_existing/shard/22222222-1758-40f1-bf7d-13880a5c3ce5/refresh-status",
        json={"status": "Unknown", "end_time": None},
    )
    responses_mock.get(
        "https://dataset-manager.not_existing/shard/22222222-1758-40f1-bf7d-13880a5c3ce5/refresh-status",
        json={"status": "Unknown", "end_time": None},
    )
    responses_mock.get(
        "https://dataset-manager.not_existing/shard/22222222-1758-40f1-bf7d-13880a5c3ce5/refresh-status",
        json={"status": "Completed", "end_time": "2022-08-08T10:10:10.000000+00:00"},
    )


@pytest.fixture
def refresh_status_retry_twice_then_failed(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://dataset-manager.not_existing/shard/22222222-1758-40f1-bf7d-13880a5c3ce5/refresh-status",
        json={"status": "Unknown", "end_time": None},
    )
    responses_mock.get(
        "https://dataset-manager.not_existing/shard/22222222-1758-40f1-bf7d-13880a5c3ce5/refresh-status",
        json={"status": "Unknown", "end_time": None},
    )
    responses_mock.get(
        "https://dataset-manager.not_existing/shard/22222222-1758-40f1-bf7d-13880a5c3ce5/refresh-status",
        json={"status": "Failed", "end_time": None},
    )
