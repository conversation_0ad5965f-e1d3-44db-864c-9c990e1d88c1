import logging
from datetime import datetime

import pytest
import requests
from pipeline_sdk.job import JobInput

from pbi_refresh.connectors.dataset_manager import RefreshType, trigger_pbi_refresh
from pbi_refresh.exceptions import PBIModelRefreshFailed
from pbi_refresh.job import run

log = logging.getLogger(__name__)


def test_call_full_refresh_for_no_artifact_triggers(
    fake_config,
    get_no_trigger_reasons_for_shard,
    call_full_refresh,
    refresh_status_completed,
    caplog,
    time_machine,
):
    time_machine.move_to(datetime(2023, 2, 10, 10, 00))
    caplog.set_level(logging.INFO)

    run(
        job_input=JobInput(
            job_guid="a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378",
            pipeline_guids=["a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378"],
            target={"shard_id": "*************-40f1-bf7d-13880a5c3ce5"},
            execution_try_count=0,
        ),
        config=fake_config,
    )

    assert (
        "Incremental Refresh has not been fired because no Events have been detected"
        in caplog.text
    )


def test_call_full_refresh_for_artifact_status_out_of_allowed_list(
    fake_config,
    get_trigger_reasons_for_shard_with_not_allowed_events,
    call_full_refresh,
    refresh_status_completed,
    caplog,
    time_machine,
):
    time_machine.move_to(datetime(2023, 2, 10, 10, 00))
    caplog.set_level(logging.INFO)

    run(
        job_input=JobInput(
            job_guid="a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378",
            pipeline_guids=["a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378"],
            target={"shard_id": "*************-40f1-bf7d-13880a5c3ce5"},
            execution_try_count=0,
        ),
        config=fake_config,
    )

    assert (
        "Incremental Refresh has not been fired because not allowed Events have been detected: ['SKUS_UPDATED']"
        in caplog.text
    )


def test_call_full_refresh_for_no_report_ids(
    fake_config,
    get_trigger_reasons_for_shard_with_allowed_events_but_no_reports,
    call_full_refresh,
    refresh_status_completed,
    caplog,
    time_machine,
):
    time_machine.move_to(datetime(2023, 2, 10, 10, 00))
    caplog.set_level(logging.INFO)

    run(
        job_input=JobInput(
            job_guid="a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378",
            pipeline_guids=["a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378"],
            target={"shard_id": "*************-40f1-bf7d-13880a5c3ce5"},
            execution_try_count=0,
        ),
        config=fake_config,
    )

    assert (
        "Incremental Refresh has not been fired because no report_ids have been passed"
        in caplog.text
    )


def test_call_full_refresh_for_report_dates_before_first_day_of_previous_quarter_same_year(
    fake_config,
    get_trigger_reasons_for_shard_with_allowed_events,
    get_valid_reports_details_by_id_for_2023_03_31,
    call_full_refresh,
    refresh_status_completed,
    caplog,
    time_machine,
):
    time_machine.move_to(datetime(2023, 7, 1, 10, 00))
    caplog.set_level(logging.INFO)

    run(
        job_input=JobInput(
            job_guid="a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378",
            pipeline_guids=["a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378"],
            target={"shard_id": "*************-40f1-bf7d-13880a5c3ce5"},
            execution_try_count=0,
        ),
        config=fake_config,
    )

    assert (
        "Incremental Refresh has not been fired because 2023-03-31 is earlier than start of previous quarter"
        in caplog.text
    )


def test_call_full_refresh_for_report_dates_before_first_day_of_previous_quarter_previous_year(
    fake_config,
    get_trigger_reasons_for_shard_with_allowed_events,
    get_valid_reports_details_by_id_for_2022_12_31,
    call_full_refresh,
    refresh_status_completed,
    caplog,
    time_machine,
):
    time_machine.move_to(datetime(2023, 4, 1, 10, 00))
    caplog.set_level(logging.INFO)

    run(
        job_input=JobInput(
            job_guid="a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378",
            pipeline_guids=["a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378"],
            target={"shard_id": "*************-40f1-bf7d-13880a5c3ce5"},
            execution_try_count=0,
        ),
        config=fake_config,
    )

    assert (
        "Incremental Refresh has not been fired because 2022-12-31 is earlier than start of previous quarter"
        in caplog.text
    )


def test_call_incremental_refresh_for_allowed_statuses_and_report_dates_at_first_day_of_previous_quarter_same_year(
    fake_config,
    get_trigger_reasons_for_shard_with_allowed_events,
    get_valid_reports_details_by_id_for_2023_01_01,
    call_incremental_refresh,
    refresh_status_completed,
    caplog,
    time_machine,
):
    time_machine.move_to(datetime(2023, 4, 1, 10, 00))
    caplog.set_level(logging.INFO)

    run(
        job_input=JobInput(
            job_guid="a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378",
            pipeline_guids=["a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378"],
            target={"shard_id": "*************-40f1-bf7d-13880a5c3ce5"},
            execution_try_count=0,
        ),
        config=fake_config,
    )

    assert (
        "Incremental Refresh has been fired because 2023-01-01 is after start of previous quarter"
        in caplog.text
    )


def test_call_incremental_refresh_for_allowed_statuses_and_report_dates_at_first_day_of_previous_quarter_previous_year(
    fake_config,
    get_trigger_reasons_for_shard_with_allowed_events,
    get_valid_reports_details_by_id_for_2022_10_01,
    call_incremental_refresh,
    refresh_status_completed,
    caplog,
    time_machine,
):
    time_machine.move_to(datetime(2023, 3, 31, 10, 00))
    caplog.set_level(logging.INFO)

    run(
        job_input=JobInput(
            job_guid="a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378",
            pipeline_guids=["a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378"],
            target={"shard_id": "*************-40f1-bf7d-13880a5c3ce5"},
            execution_try_count=0,
        ),
        config=fake_config,
    )

    assert (
        "Incremental Refresh has been fired because 2022-10-01 is after start of previous quarter"
        in caplog.text
    )


def test_call_incremental_refresh_for_allowed_statuses_for_sales_and_discounts_reports(
    fake_config,
    get_trigger_reasons_for_shard_with_sales_and_discounts,
    get_valid_reports_details_for_sales_and_discounts,
    call_incremental_refresh,
    refresh_status_completed,
    caplog,
    time_machine,
):
    time_machine.move_to(datetime(2024, 7, 31, 10, 00))
    caplog.set_level(logging.INFO)

    run(
        job_input=JobInput(
            job_guid="a282fbc3-aaaa-4f0b-b4e9-1eb1e7be3378",
            pipeline_guids=["a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378"],
            target={"shard_id": "*************-40f1-bf7d-13880a5c3ce5"},
            execution_try_count=0,
        ),
        config=fake_config,
    )

    assert (
        "Incremental Refresh has been fired because 2024-07-10 is after start of previous quarter"
        in caplog.text
    )


def test_dont_call_incremental_refresh_for_allowed_statuses_and_only_discounts_reports(
    fake_config,
    get_trigger_reasons_for_shard_with_discounts,
    get_valid_reports_details_for_discounts,
    caplog,
    time_machine,
):
    time_machine.move_to(datetime(2024, 7, 31, 10, 00))
    caplog.set_level(logging.INFO)

    run(
        job_input=JobInput(
            job_guid="a282fbc3-bbbb-4f0b-b4e9-1eb1e7be3378",
            pipeline_guids=["a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378"],
            target={"shard_id": "*************-40f1-bf7d-13880a5c3ce5"},
            execution_try_count=0,
        ),
        config=fake_config,
    )
    assert "All received reports are ignored" in caplog.text
    assert (
        "Skipping refresh for shard *************-40f1-bf7d-13880a5c3ce5" in caplog.text
    )


def test_wait_for_refresh_status_unknown_until_complited(
    fake_config,
    get_no_trigger_reasons_for_shard,
    call_full_refresh,
    refresh_status_retry_twice_then_completed,
):
    run(
        job_input=JobInput(
            job_guid="a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378",
            pipeline_guids=["a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378"],
            target={"shard_id": "*************-40f1-bf7d-13880a5c3ce5"},
            execution_try_count=0,
        ),
        config=fake_config,
    )


def test_call_full_refresh(
    fake_config,
    call_full_refresh,
):
    response = trigger_pbi_refresh(
        shard_id="*************-40f1-bf7d-13880a5c3ce5",
        refresh_type=RefreshType.FULL,
        config=fake_config,
    )

    response.raise_for_status()
    assert response.status_code == 200


def test_call_full_refresh_for_busy_shard(
    fake_config,
    call_full_refresh_on_busy_shard,
):
    response = trigger_pbi_refresh(
        shard_id="*************-40f1-bf7d-13880a5c3ce5",
        refresh_type=RefreshType.FULL,
        config=fake_config,
    )

    response.raise_for_status()
    assert response.status_code == 202
    assert response.json() == {"detail": "Another refresh request is already executing"}


def test_wait_for_refresh_on_busy_shard_with_status_unknown_until_complited(
    fake_config,
    get_no_trigger_reasons_for_shard,
    call_full_refresh_on_busy_shard,
    refresh_status_retry_twice_then_completed,
):
    run(
        job_input=JobInput(
            job_guid="a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378",
            pipeline_guids=["a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378"],
            target={"shard_id": "*************-40f1-bf7d-13880a5c3ce5"},
            execution_try_count=0,
        ),
        config=fake_config,
    )


def test_wait_for_refresh_status_unknown_until_failed(
    fake_config,
    get_no_trigger_reasons_for_shard,
    call_full_refresh,
    refresh_status_retry_twice_then_failed,
):
    with pytest.raises(PBIModelRefreshFailed):
        run(
            job_input=JobInput(
                job_guid="a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378",
                pipeline_guids=["a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378"],
                target={"shard_id": "*************-40f1-bf7d-13880a5c3ce5"},
                execution_try_count=0,
            ),
            config=fake_config,
        )


def test_incremental_refresh_for_requested_shard_for_reports_without_valid_date_from_field(
    fake_config,
    get_trigger_reasons_for_shard_with_allowed_events,
    get_valid_reports_details_by_id_with_null_date_from,
    call_full_refresh,
    refresh_status_completed,
    caplog,
):
    caplog.set_level(logging.INFO)

    run(
        job_input=JobInput(
            job_guid="a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378",
            pipeline_guids=["a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378"],
            target={"shard_id": "*************-40f1-bf7d-13880a5c3ce5"},
            execution_try_count=0,
        ),
        config=fake_config,
    )

    assert (
        "Incremental Refresh has not been fired because reports do not have valid date_from field"
        in caplog.text
    )


def test_rsv2_retry_mechanism_retry_exceeded(
    fake_config,
    get_trigger_reasons_for_shard_with_allowed_events,
    get_valid_reports_details_by_id_retry_exceeded,
    caplog,
):
    with pytest.raises(requests.exceptions.RetryError):
        run(
            job_input=JobInput(
                job_guid="a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378",
                pipeline_guids=["a282fbc3-ac86-4f0b-b4e9-1eb1e7be3378"],
                target={"shard_id": "*************-40f1-bf7d-13880a5c3ce5"},
                execution_try_count=0,
            ),
            config=fake_config,
        )
