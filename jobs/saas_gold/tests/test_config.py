from pathlib import Path

import pytest

from data_sdk.config import Extension
from saas_gold.config import Config


@pytest.fixture(autouse=True)
def set_env_variables(monkeypatch):
    monkeypatch.setenv("ENV", "production")
    monkeypatch.setenv("APP_VERSION", "2.0.0")
    monkeypatch.setenv("JOB_NAME", "saas-gold-job")
    monkeypatch.setenv("DOCKER_TAG", "latest")
    monkeypatch.setenv("DOCKER_BUILD_TIMESTAMP", "2022-01-01")
    monkeypatch.setenv("DEV_USERNAME", "testuser")

    monkeypatch.setenv("INPUT_CFG__TYPE", "dls")
    monkeypatch.setenv("INPUT_CFG__ACCOUNT_NAME", "dlsaggregateddevs7")
    monkeypatch.setenv("INPUT_CFG__CONTAINER_NAME", "processed-data")
    monkeypatch.setenv("INPUT_CFG__BASE_DIR", "processed-reports")

    monkeypatch.setenv("PBI_OUTPUT_CFG__TYPE", "dls")
    monkeypatch.setenv("PBI_OUTPUT_CFG__ACCOUNT_NAME", "dlsaggregateddevs7")
    monkeypatch.setenv("PBI_OUTPUT_CFG__CONTAINER_NAME", "saas-gold-poc")
    monkeypatch.setenv("PBI_OUTPUT_CFG__BASE_DIR", "data")

    monkeypatch.setenv("DIRECT_DATA_ACCESS_CFG__TYPE", "dls")
    monkeypatch.setenv("DIRECT_DATA_ACCESS_CFG__ACCOUNT_NAME", "dlsaggregateddevs7")
    monkeypatch.setenv(
        "DIRECT_DATA_ACCESS_CFG__CONTAINER_NAME", "saas-gold-direct-data-access"
    )
    monkeypatch.setenv("DIRECT_DATA_ACCESS_CFG__BASE_DIR", "data")
    monkeypatch.setenv("DIRECT_DATA_ACCESS_CFG__FILE_EXTENSION", "csv")


def test_config_env():
    config = Config()

    assert config.env == "production"


def test_config_app_version():
    config = Config()

    assert config.app_version == "2.0.0"


def test_config_job_name():
    config = Config()

    assert config.job_name == "saas-gold-job"


def test_config_docker_tag():
    config = Config()

    assert config.docker_tag == "latest"


def test_config_docker_build_timestamp():
    config = Config()

    assert config.docker_build_timestamp == "2022-01-01"


def test_config_silver_account_name():
    config = Config()

    assert config.input_cfg.account_name == "dlsaggregateddevs7"


def test_config_silver_container_name():
    config = Config()

    assert config.input_cfg.container_name == "processed-data"


def test_config_silver_base_dir():
    config = Config()

    assert config.input_cfg.base_dir == Path("processed-reports")


def test_config_gold_account_name():
    config = Config()

    assert config.pbi_output_cfg.account_name == "dlsaggregateddevs7"


def test_config_gold_container_name():
    config = Config()

    assert config.pbi_output_cfg.container_name == "saas-gold-poc"


def test_export_config_gold_account_name():
    config = Config()

    assert config.direct_data_access_cfg.account_name == "dlsaggregateddevs7"


def test_export_config_gold_container_name():
    config = Config()

    assert (
        config.direct_data_access_cfg.container_name == "saas-gold-direct-data-access"
    )


def test_export_config_gold_file_extension():
    config = Config()

    assert config.direct_data_access_cfg.file_extension == Extension.CSV
