from saas_gold.job import aggregators


def test_all_aggregators_included():
    required_aggregators = {
        "DimPortalsAggregator",
        "DimSkuAggregator",
        "DimProductsAggregator",
        "DimStudioAggregator",
        "DimTrafficSourceAggregator",
        "ExportFactSalesAggregator",
        "ExportFactVisibilityWishlistAggregator",
        "FactSalesAggregator",
        "FactSalesCumulativeAggregator",
        "FactSalesIndicatorAggregator",
        "FactVisibilityAggregator",
        "FactWishlistActionsAggregator",
        "FactWishlistCohortsAggregator",
        "FactEventDayAggregator",
        "FactEventsAggregator",
        "FactBaselineAggregator",
        "FactRetailerTagsAggregator",
    }
    aggregator_names = {aggregator.__name__ for aggregator in aggregators}
    assert len(aggregator_names) == 17
    assert aggregator_names.issubset(required_aggregators)
