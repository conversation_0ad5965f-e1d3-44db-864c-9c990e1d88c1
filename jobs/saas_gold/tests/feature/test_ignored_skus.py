from datetime import date
from unittest.mock import MagicMock

import polars as pl

from saas_gold.aggregators.dim_products import DimProductsAggregator
from saas_gold.aggregators.dim_sku import DimSkuAggregator
from saas_gold.aggregators.export_fact_sales import ExportFactSalesAggregator
from saas_gold.aggregators.export_fact_visibility_wishlist import (
    ExportFactVisibilityWishlistAggregator,
)
from saas_gold.aggregators.fact_baseline import FactBaselineAggregator
from saas_gold.aggregators.fact_event_day import FactEventDayAggregator
from saas_gold.aggregators.fact_events import FactEventsAggregator
from saas_gold.aggregators.fact_retailer_tags import FactRetailerTagsAggregator
from saas_gold.aggregators.fact_sales import FactSalesAggregator
from saas_gold.aggregators.fact_sales_cumulative import FactSalesCumulativeAggregator
from saas_gold.aggregators.fact_sales_indicator import FactSalesIndicatorAggregator
from saas_gold.aggregators.fact_visibility import FactVisibilityAggregator
from saas_gold.aggregators.fact_wishlist_actions import FactWishlistActionsAggregator
from saas_gold.aggregators.fact_wishlist_cohorts import FactWishlistCohortsAggregator


def test_DimProductsAggregator_without_ignored_skus(legacy_skus_factory):
    sku_a = legacy_skus_factory.build_table(product_name="Product A")
    sku_b = legacy_skus_factory.build_table(product_name="Product B")

    result = DimProductsAggregator(sku_a + sku_b).aggregate()

    assert sorted(result.df["product_name"].to_list()) == ["Product A", "Product B"]


def test_DimProductsAggregator_with_ignored_skus(legacy_skus_factory):
    valid_skus = legacy_skus_factory.build_table(product_name="SH")
    ignored_skus = legacy_skus_factory.build_table(product_name="__IGNORE")

    legacy_dim_sku = valid_skus + ignored_skus

    result = DimProductsAggregator(legacy_dim_sku).aggregate()

    assert result.df["product_name"].to_list() == ["SH"]


def test_DimProductsAggregator_all_ignored_products_returns_empty_df(
    legacy_skus_factory,
):
    ignored_skus = legacy_skus_factory.build_table(product_name="__IGNORE")

    result = DimProductsAggregator(ignored_skus).aggregate()

    assert result.df.is_empty()


def test_TestDimSkuAggregator_without_ignored_skus(
    mock_legacy_dim_skus, mock_legacy_fact_sales
):
    legacy_dim_sku = MagicMock()
    legacy_dim_sku.df = mock_legacy_dim_skus

    legacy_fact_sales = MagicMock()
    legacy_fact_sales.df = mock_legacy_fact_sales

    aggregator = DimSkuAggregator(legacy_dim_sku, legacy_fact_sales)

    result_df = aggregator._aggregate()

    expected_data = {
        "unique_sku_id": ["sku1", "sku2", "sku5"],
        "base_sku_id": [1, 1, 3],
        "portal_platform_region": ["ham", "ham", "eggs"],
        "human_name": ["Product A", "Product A", "Product C"],
        "product_id": ["1", "1", "3"],
        "sku_type": ["SALES", "STORE", "SALES"],
        "gso": [5, 7, 9],
        "release_date": [date(2021, 1, 1)] * 2 + [None],
    }
    expected_df = pl.DataFrame(expected_data)

    assert result_df.equals(expected_df)


def test_TestExportFactSalesAggregator_without_ignored_skus(
    mock_legacy_fact_sales,
    mock_legacy_dim_skus,
    mock_legacy_dim_portals,
    mock_legacy_fact_event_day,
    mock_observation_discounts,
):
    legacy_fact_sales = MagicMock()
    legacy_fact_sales.df = mock_legacy_fact_sales

    legacy_dim_sku = MagicMock()
    legacy_dim_sku.df = mock_legacy_dim_skus

    legacy_dim_portals = MagicMock()
    legacy_dim_portals.df = mock_legacy_dim_portals

    legacy_fact_event_day = MagicMock()
    legacy_fact_event_day.df = mock_legacy_fact_event_day

    observation_discounts = MagicMock()
    observation_discounts.df = mock_observation_discounts

    aggregator = ExportFactSalesAggregator(
        legacy_fact_sales,
        legacy_dim_sku,
        legacy_dim_portals,
        legacy_fact_event_day,
        observation_discounts,
    )

    result_df = aggregator._aggregate()

    product_ids = sorted(result_df["product_id"].unique().to_list())
    assert product_ids == ["1"]


def test_TestExportFactVisibilityWishlistAggregator_without_ignored_skus(
    mock_legacy_fact_visibility,
    mock_legacy_fact_wishlist_actions,
    mock_legacy_dim_skus,
    mock_legacy_dim_portals,
):
    legacy_fact_visibility = MagicMock()
    legacy_fact_visibility.df = mock_legacy_fact_visibility

    legacy_fact_wishlist_actions = MagicMock()
    legacy_fact_wishlist_actions.df = mock_legacy_fact_wishlist_actions

    legacy_dim_skus = MagicMock()
    legacy_dim_skus.df = mock_legacy_dim_skus

    legacy_dim_portals = MagicMock()
    legacy_dim_portals.df = mock_legacy_dim_portals

    aggregator = ExportFactVisibilityWishlistAggregator(
        legacy_fact_visibility,
        legacy_fact_wishlist_actions,
        legacy_dim_skus,
        legacy_dim_portals,
    )

    result_df = aggregator._aggregate()

    product_ids = sorted(result_df["product_id"].unique().to_list())
    assert product_ids == ["1", "3"]


def test_TestFactBaselineAggregator_without_ignored_skus(
    legacy_skus_factory,
    legacy_fact_event_day_factory,
    observation_discounts_factory,
    observation_sales_factory,
    silver_skus_factory,
):
    legacy_dim_skus_table = legacy_skus_factory.build_table(
        sku_studio="10000-steam:13",
        product_name="Product A",
    ) + legacy_skus_factory.build_table(
        sku_studio="10001-steam:13",
        product_name="__IGNORE",
    )
    legacy_fact_event_day_table = legacy_fact_event_day_factory.build_table(size=2)

    observation_discounts_table = observation_discounts_factory.build_table(
        unique_sku_id="10000-steam:13",
    ) + observation_discounts_factory.build_table(
        unique_sku_id="10001-steam:13",
    )

    observation_sales_table = observation_sales_factory.build_table(
        unique_sku_id="10000-steam:13",
    ) + observation_sales_factory.build_table(
        unique_sku_id="10001-steam:13",
    )

    silver_skus_table = silver_skus_factory.build_table(
        unique_sku_id="10000-steam:13",
    ) + silver_skus_factory.build_table(
        unique_sku_id="10001-steam:13",
    )

    aggregator = FactBaselineAggregator(
        legacy_dim_skus_table,
        legacy_fact_event_day_table,
        observation_discounts_table,
        observation_sales_table,
        silver_skus_table,
    )

    result_df = aggregator._aggregate()

    product_ids = result_df["product_id"].unique().to_list()
    assert len(product_ids) == 1
    assert "__IGNORE:171010:1" not in product_ids


def test_TestFactEventDayAggregator_without_ignored_skus(
    mock_legacy_fact_event_day, mock_legacy_fact_sales, mock_legacy_dim_skus
):
    legacy_fact_event_day = MagicMock()
    legacy_fact_event_day.df = mock_legacy_fact_event_day

    legacy_fact_sales = MagicMock()
    legacy_fact_sales.df = mock_legacy_fact_sales

    legacy_dim_skus = MagicMock()
    legacy_dim_skus.df = mock_legacy_dim_skus

    observation_discounts = MagicMock()
    observation_discounts.df = pl.DataFrame()

    aggregator = FactEventDayAggregator(
        legacy_fact_event_day,
        observation_discounts,
        legacy_fact_sales,
        legacy_dim_skus,
    )

    result_df = aggregator._aggregate()

    product_ids = sorted(result_df["product_id"].unique().to_list())
    assert product_ids == ["1", "3"]


def test_TestFactEventsAggregator_without_ignored_skus(
    mock_legacy_fact_visibility,
    mock_legacy_fact_event_day,
    observation_discounts_factory,
    observation_sales_factory,
    mock_legacy_fact_sales,
    mock_legacy_dim_skus,
    silver_skus_factory,
):
    legacy_fact_visibility = MagicMock()
    legacy_fact_visibility.df = mock_legacy_fact_visibility

    legacy_fact_event_day = MagicMock()
    legacy_fact_event_day.df = mock_legacy_fact_event_day

    observation_sales = observation_sales_factory.build_table(
        unique_sku_id="10000-steam:13",
    )

    legacy_fact_sales = MagicMock()
    legacy_fact_sales.df = mock_legacy_fact_sales

    legacy_dim_sku = MagicMock()
    legacy_dim_sku.df = mock_legacy_dim_skus

    observation_discounts = MagicMock()
    observation_discounts.df = pl.DataFrame()

    silver_skus = silver_skus_factory.build_table(
        unique_sku_id="10000-steam:13",
    )

    aggregator = FactEventsAggregator(
        legacy_fact_visibility,
        legacy_fact_event_day,
        observation_discounts,
        observation_sales,
        legacy_fact_sales,
        legacy_dim_sku,
        silver_skus,
    )

    result_df = aggregator._aggregate()

    product_ids = sorted(result_df["product_id"].unique().to_list())
    assert product_ids == ["1", "3"]


def test_TestFactRetailerTagsAggregator_without_ignored_skus(
    mock_legacy_fact_sales, mock_legacy_dim_skus
):
    legacy_fact_sales = MagicMock()
    legacy_fact_sales.df = mock_legacy_fact_sales

    legacy_dim_sku = MagicMock()
    legacy_dim_sku.df = mock_legacy_dim_skus

    aggregator = FactRetailerTagsAggregator(legacy_fact_sales, legacy_dim_sku)

    result_df = aggregator._aggregate()

    has_ignored = result_df.filter(pl.col("product_id") == "3").height > 0

    assert has_ignored == False

    def test_TestFactSalesAggregator_without_ignored_skus(
        mock_legacy_fact_sales, mock_legacy_dim_skus
    ):
        legacy_fact_sales = MagicMock()
        legacy_fact_sales.df = mock_legacy_fact_sales

        legacy_dim_sku = MagicMock()
        legacy_dim_sku.df = mock_legacy_dim_skus

        aggregator = FactSalesAggregator(legacy_fact_sales, legacy_dim_sku)

        result_df = aggregator._aggregate()

        product_ids = sorted(result_df["product_id"].unique().to_list())
        assert product_ids == ["1", "2", "5"]


def test_TestFactSalesCumulativeAggregator_without_ignored_skus(
    mock_legacy_fact_sales,
    mock_legacy_fact_event_day,
    mock_legacy_dim_skus,
):
    legacy_fact_sales = MagicMock()
    legacy_fact_sales.df = mock_legacy_fact_sales

    legacy_fact_event_day = MagicMock()
    legacy_fact_event_day.df = mock_legacy_fact_event_day

    legacy_dim_sku = MagicMock()
    legacy_dim_sku.df = mock_legacy_dim_skus

    observation_discounts = MagicMock()
    observation_discounts.df = pl.DataFrame()

    aggregator = FactSalesCumulativeAggregator(
        legacy_fact_sales,
        legacy_fact_event_day,
        observation_discounts,
        legacy_dim_sku,
    )

    result_df = aggregator._aggregate()

    product_ids = sorted(result_df["product_id"].unique().to_list())
    assert product_ids == ["1", "2"]


def test_TestFactSalesIndicatorAggregator_without_ignored_skus(
    mock_legacy_fact_event_day,
    mock_legacy_fact_sales,
    mock_legacy_dim_skus,
):
    legacy_fact_event_day = MagicMock()
    legacy_fact_event_day.df = mock_legacy_fact_event_day

    legacy_fact_sales = MagicMock()
    legacy_fact_sales.df = mock_legacy_fact_sales

    legacy_dim_sku = MagicMock()
    legacy_dim_sku.df = mock_legacy_dim_skus

    observation_discounts = MagicMock()
    observation_discounts.df = pl.DataFrame()

    aggregator = FactSalesIndicatorAggregator(
        legacy_fact_event_day,
        observation_discounts,
        legacy_fact_sales,
        legacy_dim_sku,
    )

    result_df = aggregator._aggregate()

    product_ids = sorted(result_df["product_id"].unique().to_list())
    assert product_ids == ["1", "2"]


def test_TestFactVisibilityAggregator_without_ignored_skus(
    mock_legacy_fact_visibility, mock_legacy_dim_skus
):
    legacy_fact_visibility = MagicMock()
    legacy_fact_visibility.df = mock_legacy_fact_visibility

    legacy_dim_sku = MagicMock()
    legacy_dim_sku.df = mock_legacy_dim_skus

    aggregator = FactVisibilityAggregator(legacy_fact_visibility, legacy_dim_sku)

    result_df = aggregator._aggregate()

    product_ids = sorted(result_df["product_id"].unique().to_list())
    assert product_ids == ["1", "2", "5"]


def test_TestFactWishlistActionsAggregator_without_ignored_skus(
    mock_legacy_fact_wishlist_actions, mock_legacy_dim_skus
):
    legacy_fact_wishlist_actions = MagicMock()
    legacy_fact_wishlist_actions.df = mock_legacy_fact_wishlist_actions

    legacy_dim_sku = MagicMock()
    legacy_dim_sku.df = mock_legacy_dim_skus

    aggregator = FactWishlistActionsAggregator(
        legacy_fact_wishlist_actions, legacy_dim_sku
    )

    result_df = aggregator._aggregate()

    product_ids = sorted(result_df["product_id"].unique().to_list())
    assert product_ids == ["1", "2", "5"]


def test_TestFactWishlistCohortsAggregator_without_ignored_skus(
    mock_legacy_fact_wishlist_cohorts, mock_legacy_dim_skus
):
    legacy_fact_wishlist_cohorts = MagicMock()
    legacy_fact_wishlist_cohorts.df = mock_legacy_fact_wishlist_cohorts

    legacy_dim_sku = MagicMock()
    legacy_dim_sku.df = mock_legacy_dim_skus

    aggregator = FactWishlistCohortsAggregator(
        legacy_fact_wishlist_cohorts, legacy_dim_sku
    )

    result_df = aggregator._aggregate()

    product_ids = sorted(result_df["product_id"].unique().to_list())
    assert product_ids == ["1", "2", "5"]
