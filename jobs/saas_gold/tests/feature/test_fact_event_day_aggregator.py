from unittest.mock import patch

import polars as pl
import pytest

from saas_gold.aggregators.fact_event_day import (
    FactEventDayAggregator,
    combine_raw_data,
    generate_fact_event_day,
    generate_fact_event_day_sku,
)


@pytest.fixture
def fact_event_day_aggregator(
    legacy_fact_event_day,
    observation_discounts,
    legacy_fact_sales_event_day,
    legacy_dim_skus,
):
    """Create FactEventDayAggregator instance with test data."""
    return FactEventDayAggregator(
        legacy_fact_event_day=legacy_fact_event_day,
        observation_discounts=observation_discounts,
        legacy_fact_sales=legacy_fact_sales_event_day,
        legacy_dim_skus=legacy_dim_skus,
    )


def test_empty_fact_sales(fact_event_day_aggregator, legacy_fact_sales_event_day):
    """Test behavior when fact_sales is empty."""
    legacy_fact_sales_event_day.df = pl.DataFrame()
    result = fact_event_day_aggregator._aggregate()
    assert result.is_empty()


def test_empty_observation_discounts(
    fact_event_day_aggregator, observation_discounts, legacy_fact_event_day
):
    """Test behavior when observation_discounts is empty."""
    observation_discounts.df = pl.DataFrame()

    result = fact_event_day_aggregator._aggregate()

    assert not result.is_empty()


def test_combine_raw_data(legacy_fact_event_day, observation_discounts):
    """Test the combine_raw_data function."""
    result = combine_raw_data(
        observation_discounts.df,
        legacy_fact_event_day.df,
    )

    assert len(result) > len(legacy_fact_event_day.df)

    assert set(legacy_fact_event_day.df["unique_sku_id"].to_list()).issubset(
        set(result["unique_sku_id"].to_list())
    )

    sku3_days = result.filter(pl.col("unique_sku_id") == "sku3")
    assert len(sku3_days) == 6  # 6 days between Jan 10 and Jan 15 (inclusive)


def test_generate_fact_event_day(
    legacy_fact_event_day, legacy_fact_sales_event_day, legacy_dim_skus
):
    """Test the generate_fact_event_day function."""
    result = generate_fact_event_day(
        legacy_fact_event_day.df,
        legacy_fact_sales_event_day.df,
        legacy_dim_skus.df,
    )

    expected_columns = [
        "date",
        "product_id",
        "portal_platform_region_id",
        "studio_id",
        "discount",
        "date_from",
        "date_to",
        "dates_short",
        "promo_length",
        "event_status",
        "days_since_previous_discount",
        "event_name",
        "event_id",
        "event_name_2",
        "year",
    ]
    assert all(col in result.columns for col in expected_columns)

    assert len(result) <= len(legacy_fact_event_day.df)


def test_generate_fact_event_day_sku(
    legacy_fact_event_day, legacy_fact_sales_event_day, legacy_dim_skus
):
    """Test the generate_fact_event_day_sku function."""
    result = generate_fact_event_day_sku(
        legacy_fact_event_day.df,
        legacy_fact_sales_event_day.df,
        legacy_dim_skus.df,
    )

    assert len(result) <= len(legacy_fact_event_day.df)

    assert "unique_sku_id" in result.columns


def test_empty_generate_fact_event_day(legacy_fact_event_day, legacy_dim_skus):
    """Test generate_fact_event_day with empty sales data."""
    empty_sales = pl.DataFrame()
    result = generate_fact_event_day(
        legacy_fact_event_day.df,
        empty_sales,
        legacy_dim_skus.df,
    )

    assert result.is_empty()


def test_segmentator_configuration(fact_event_day_aggregator):
    """Test that the segmentator is configured correctly."""
    assert fact_event_day_aggregator.segmentator.date_column == "date"


@patch("saas_gold.aggregators.fact_event_day.combine_raw_data")
@patch("saas_gold.aggregators.fact_event_day.generate_fact_event_day")
def test_aggregate_integration(mock_generate, mock_combine, fact_event_day_aggregator):
    """Test the full _aggregate method with mocks."""
    mock_combine.return_value = pl.DataFrame({"test": [1]})
    mock_generate.return_value = pl.DataFrame({"result": [1]})

    result = fact_event_day_aggregator._aggregate()

    mock_combine.assert_called_once()
    mock_generate.assert_called_once()
    assert result.equals(pl.DataFrame({"result": [1]}))
