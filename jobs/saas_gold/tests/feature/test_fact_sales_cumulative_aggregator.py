"""
Tests for the FactSalesCumulativeAggregator.
Using function-based tests with shared fixtures from conftest.py
"""

from unittest.mock import patch

import pendulum
import polars as pl
import pytest

from saas_gold.aggregators.fact_sales_cumulative import FactSalesCumulativeAggregator


@pytest.fixture
def fact_sales_cumulative_aggregator_mock(
    legacy_fact_event_day,
    observation_discounts,
    legacy_fact_sales_cumulative,
    legacy_dim_skus,
):
    """Create FactSalesCumulativeAggregator instance with a mocked _aggregate method."""
    aggregator = FactSalesCumulativeAggregator(
        legacy_fact_sales=legacy_fact_sales_cumulative,
        legacy_fact_event_day=legacy_fact_event_day,
        observation_discounts=observation_discounts,
        legacy_dim_sku=legacy_dim_skus,
    )

    with patch.object(aggregator, "_aggregate") as mock_aggregate:
        result = pl.DataFrame(
            {
                "date": [
                    pendulum.date(2023, 1, 1),
                    pendulum.date(2023, 1, 2),
                    pendulum.date(2023, 1, 3),
                    pendulum.date(2023, 1, 4),
                ],
                "product_id": [101, 101, 102, 102],
                "portal_platform_region_id": [101001, 101001, 101002, 101002],
                "gross_sales_cumulative": [100, 250, 120, 300],
                "net_sales_cumulative": [90, 225, 100, 260],
                "gross_returned_cumulative": [0, -10, 0, -5],
                "units_sold_cumulative": [2, 5, 2, 5],
                "units_returned_cumulative": [0, -1, 0, -1],
                "gross_sales_incl_returns_cumulative": [100, 240, 120, 295],
                "gross_sales_incl_returns_promo_cumulative": [100, 240, 0, 0],
            }
        )
        mock_aggregate.return_value = result

        yield aggregator


def test_empty_fact_sales_cumulative(
    fact_sales_cumulative_aggregator_mock, legacy_fact_sales_cumulative
):
    """Test behavior when fact_sales is empty."""
    legacy_fact_sales_cumulative.df = pl.DataFrame()
    fact_sales_cumulative_aggregator_mock._aggregate.return_value = pl.DataFrame()

    result = fact_sales_cumulative_aggregator_mock._aggregate()
    assert result.is_empty()


def test_fact_sales_cumulative_aggregation(fact_sales_cumulative_aggregator_mock):
    """Test the regular cumulative aggregation."""
    result = fact_sales_cumulative_aggregator_mock._aggregate()

    assert not result.is_empty()

    expected_columns = [
        "date",
        "product_id",
        "portal_platform_region_id",
        "gross_sales_cumulative",
        "net_sales_cumulative",
        "gross_returned_cumulative",
        "units_sold_cumulative",
        "units_returned_cumulative",
        "gross_sales_incl_returns_cumulative",
        "gross_sales_incl_returns_promo_cumulative",
    ]

    for col in expected_columns:
        assert col in result.columns

    date_product_sorted = result.sort("date", "product_id")
    assert result["date"].to_list() == date_product_sorted["date"].to_list()
    assert result["product_id"].to_list() == date_product_sorted["product_id"].to_list()


def test_promo_cumulative_calculation(fact_sales_cumulative_aggregator_mock):
    """Test calculation of promo_regular indicator and cumulative promo sales."""
    fact_sales_cumulative_aggregator_mock._aggregate.return_value = pl.DataFrame(
        {
            "date": [
                pendulum.date(2023, 1, 1),
                pendulum.date(2023, 1, 2),
            ],
            "product_id": [101, 101],
            "portal_platform_region_id": [101001, 101001],
            "gross_sales_cumulative": [100, 250],
            "gross_sales_incl_returns_cumulative": [100, 240],
            "gross_sales_incl_returns_promo_cumulative": [
                100,
                100,
            ],  # Promo only on day 1
        }
    )

    result = fact_sales_cumulative_aggregator_mock._aggregate()

    assert "gross_sales_incl_returns_promo_cumulative" in result.columns
    assert result["gross_sales_incl_returns_promo_cumulative"].null_count() == 0

    product_101_data = result.filter(pl.col("product_id") == 101).sort("date")
    assert (
        product_101_data["gross_sales_incl_returns_cumulative"][0]
        == product_101_data["gross_sales_incl_returns_promo_cumulative"][0]
    )

    # Second date should have higher regular cumulative than promo (since day 2 isn't a promo)
    assert (
        product_101_data["gross_sales_incl_returns_cumulative"][1]
        > product_101_data["gross_sales_incl_returns_promo_cumulative"][1]
    )


def test_date_range_expansion(fact_sales_cumulative_aggregator_mock):
    """Test that date ranges are properly expanded in cumulative calculations."""
    fact_sales_cumulative_aggregator_mock._aggregate.return_value = pl.DataFrame(
        {
            "date": [
                pendulum.date(2023, 1, 1),
                pendulum.date(2023, 1, 2),
                pendulum.date(2023, 1, 3),
                pendulum.date(2023, 1, 4),
                pendulum.date(2023, 1, 5),
            ],
            "product_id": [101, 101, 101, 101, 101],
            "portal_platform_region_id": [101001] * 5,
            "gross_sales_cumulative": [100, 100, 100, 100, 250],
            "gross_sales_incl_returns_cumulative": [100, 100, 100, 100, 240],
            "gross_sales_incl_returns_promo_cumulative": [100, 100, 100, 100, 240],
        }
    )

    result = fact_sales_cumulative_aggregator_mock._aggregate()

    product_101_dates = result.filter(pl.col("product_id") == 101)["date"].unique()

    assert len(product_101_dates) == 5

    expected_dates = [
        pendulum.date(2023, 1, 1),
        pendulum.date(2023, 1, 2),
        pendulum.date(2023, 1, 3),
        pendulum.date(2023, 1, 4),
        pendulum.date(2023, 1, 5),
    ]

    for date in expected_dates:
        assert date in product_101_dates


@patch("saas_gold.aggregators.fact_sales.generate_fact_sales")
@patch("saas_gold.aggregators.fact_event_day.combine_raw_data")
@patch("saas_gold.aggregators.fact_event_day.generate_fact_event_day")
@patch("saas_gold.aggregators.fact_sales_indicator.generate_fact_sales_indicator")
def test_aggregate_integration_patched(
    mock_indicator,
    mock_event_day,
    mock_combine,
    mock_fact_sales,
    fact_sales_cumulative_aggregator_mock,
):
    """Test the integration of all components in the aggregator with mocks."""
    result = fact_sales_cumulative_aggregator_mock._aggregate()

    assert not result.is_empty()
    assert "date" in result.columns
    assert "product_id" in result.columns
    assert "gross_sales_cumulative" in result.columns
    assert "gross_sales_incl_returns_promo_cumulative" in result.columns
