import datetime
from typing import Any

from saas_gold.aggregators.fact_visibility import FactVisibilityAggregator


def test_schema_is_valid(legacy_fact_visibility_factory):
    table = legacy_fact_visibility_factory.build_table()
    assert table.df.to_dicts() == [
        {
            "portal_platform_region": "Steam:PC:Global",
            "portal_platform_region_id": 171010,
            "product_id": "Super Product:171010:1",
            "sku_studio": "10000-store:1",
            "date": datetime.date(2000, 1, 1),
            "studio_id": 1,
            "date_sku_studio": "2000-01-01:10000-store:1",
            "hash_traffic_source": "ffeea2741f0568cb042573c6ed22da96",
            "date_product_studio": "2000-01-01:Super Product:1",
            "visits": 10,
            "owner_visits": 1,
            "impressions": 100,
            "owner_impressions": 10,
            "navigation": "Direct Navigation",
        }
    ]


def test_empty_data_visibility(legacy_skus_factory, legacy_fact_visibility_factory):
    table_skus = legacy_skus_factory.build_table()
    empty_table_visibility = legacy_fact_visibility_factory.build_table(size=0)

    result = FactVisibilityAggregator(
        legacy_fact_visibility=empty_table_visibility, legacy_dim_sku=table_skus
    ).aggregate()

    assert result.df.is_empty()


def test_returns_all_required_fields(
    legacy_skus_factory, legacy_fact_visibility_factory
):
    legacy_dim_sku = legacy_skus_factory.build_table()
    legacy_fact_visibility_factory.reset_sequence(force=True)
    legacy_visibility: Any = legacy_fact_visibility_factory.build_table()

    result = FactVisibilityAggregator(
        legacy_fact_visibility=legacy_visibility,
        legacy_dim_sku=legacy_dim_sku,
    ).aggregate()

    assert result.df.to_dicts() == [
        {
            "date": datetime.date(2000, 1, 1),
            "product_id": "Super Product:171010:1",
            "portal_platform_region_id": 171010,
            "hash_traffic_source": "ffeea2741f0568cb042573c6ed22da96",
            "direct_navigation": "Direct Navigation",
            "studio_id": 1,
            "visits": 10,
            "owner_visits": 1,
            "impressions": 100,
            "owner_impressions": 10,
        }
    ]


def test_navigation(legacy_skus_factory, legacy_fact_visibility_factory):
    legacy_dim_sku = legacy_skus_factory.build_table(size=2)
    legacy_fact_visibility_factory.reset_sequence(force=True)
    sku_0_visibility = legacy_fact_visibility_factory.build_table(
        base_sku_id="10000", navigation="Direct Navigation"
    )

    legacy_fact_visibility_factory.reset_sequence(force=True)
    sku_1_visibility = legacy_fact_visibility_factory.build_table(
        base_sku_id="10001",
        start_date=datetime.date(year=2000, month=1, day=1),
        navigation="Visits from Impressions",
        visits=20,
    )
    legacy_visibility = sku_0_visibility + sku_1_visibility

    result = FactVisibilityAggregator(
        legacy_fact_visibility=legacy_visibility,
        legacy_dim_sku=legacy_dim_sku,
    ).aggregate()

    assert result.df.to_dicts() == [
        {
            "date": datetime.date(2000, 1, 1),
            "product_id": "Super Product:171010:1",
            "portal_platform_region_id": 171010,
            "hash_traffic_source": "ffeea2741f0568cb042573c6ed22da96",
            "direct_navigation": "Visits from Impressions",
            "studio_id": 1,
            "visits": 30,
            "owner_visits": 2,
            "impressions": 200,
            "owner_impressions": 20,
        }
    ]
