from saas_gold.aggregators.dim_studio import DimStudioAggregator


def test_schema_is_valid(legacy_dim_studio_factory):
    table = legacy_dim_studio_factory.build_table()
    assert table.df.to_dicts() == [
        {
            "studio_id": 13,
            "organization_id": "o-LSDrgad",
            "email": "<EMAIL>",
            "company_name": "<PERSON>mpani<PERSON><PERSON>",
            "is_test_account": False,
            "is_verified": True,
            "agreement_date": "2019-11-12T12:58:39.923000Z",
            "studio_parent_id": None,
        }
    ]


def test_empty_input_data(legacy_dim_studio_factory):
    empty_table = legacy_dim_studio_factory.build_table(size=0)

    result = DimStudioAggregator(empty_table).aggregate()

    assert result.df.is_empty()


def test_returns_all_required_fields(legacy_dim_studio_factory):
    legacy_dim_studio = legacy_dim_studio_factory.build_table()

    result = DimStudioAggregator(legacy_dim_studio).aggregate()

    assert result.df.to_dicts() == [{"studio_id": 13, "company_name": "Companiero<PERSON>"}]
