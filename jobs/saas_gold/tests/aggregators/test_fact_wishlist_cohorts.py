import datetime
from typing import Any

from saas_gold.aggregators.fact_wishlist_cohorts import FactWishlistCohortsAggregator


def test_schema_is_valid(legacy_fact_wishlist_cohorts_factory):
    table = legacy_fact_wishlist_cohorts_factory.build_table()
    assert table.df.to_dicts() == [
        {
            "portal_platform_region": "Steam:PC:Global",
            "portal_platform_region_id": 171010,
            "product_id": "Super Product:171010:1",
            "sku_studio": "10000-store:1",
            "date": datetime.date(2000, 1, 1),
            "studio_id": 1,
            "date_sku_studio": "2000-01-01:10000-store:1",
            "date_product_studio": "2000-01-01:Super Product:1",
            "month_cohort": "2000-01-01",
            "total_conversions": 3,
            "purchases_and_activations": 2,
            "gifts": 1,
        }
    ]


def test_empty_data_wishlists(
    legacy_skus_factory, legacy_fact_wishlist_cohorts_factory
):
    table_skus = legacy_skus_factory.build_table()
    empty_table_wishlist = legacy_fact_wishlist_cohorts_factory.build_table(size=0)

    result = FactWishlistCohortsAggregator(
        legacy_fact_wishlist_cohorts=empty_table_wishlist, legacy_dim_sku=table_skus
    ).aggregate()

    assert result.df.is_empty()


def test_returns_all_required_fields(
    legacy_skus_factory, legacy_fact_wishlist_cohorts_factory
):
    legacy_dim_sku = legacy_skus_factory.build_table()
    legacy_fact_wishlist_cohorts_factory.reset_sequence(force=True)
    legacy_wishlist_cohorts: Any = legacy_fact_wishlist_cohorts_factory.build_table()

    result = FactWishlistCohortsAggregator(
        legacy_fact_wishlist_cohorts=legacy_wishlist_cohorts,
        legacy_dim_sku=legacy_dim_sku,
    ).aggregate()

    assert result.df.to_dicts() == [
        {
            "date": datetime.date(2000, 1, 1),
            "product_id": "Super Product:171010:1",
            "portal_platform_region_id": 171010,
            "month_cohort": "2000-01-01",
            "studio_id": 1,
            "total_conversions": 3,
            "purchases_and_activations": 2,
            "gifts": 1,
        }
    ]


def test_group_by_product_and_cohort(
    legacy_skus_factory, legacy_fact_wishlist_cohorts_factory
):
    legacy_dim_sku = legacy_skus_factory.build_table(size=2)
    legacy_fact_wishlist_cohorts_factory.reset_sequence(force=True)
    sku_0_wishlist = legacy_fact_wishlist_cohorts_factory.build_table(
        base_sku_id="10000"
    )

    legacy_fact_wishlist_cohorts_factory.reset_sequence(force=True)
    sku_1_wishlist = legacy_fact_wishlist_cohorts_factory.build_table(
        base_sku_id="10001", start_date=datetime.date(year=2000, month=1, day=1)
    )
    legacy_wishlist_cohorts = sku_0_wishlist + sku_1_wishlist

    result = FactWishlistCohortsAggregator(
        legacy_fact_wishlist_cohorts=legacy_wishlist_cohorts,
        legacy_dim_sku=legacy_dim_sku,
    ).aggregate()

    assert result.df.to_dicts() == [
        {
            "date": datetime.date(2000, 1, 1),
            "product_id": "Super Product:171010:1",
            "portal_platform_region_id": 171010,
            "month_cohort": "2000-01-01",
            "studio_id": 1,
            "total_conversions": 6,
            "purchases_and_activations": 4,
            "gifts": 2,
        }
    ]
