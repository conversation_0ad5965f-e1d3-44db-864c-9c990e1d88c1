from datetime import date

from saas_gold.aggregators.fact_events import FactEventsAggregator


def test_empty_event_day_data(
    legacy_fact_visibility_factory,
    legacy_fact_event_day_factory,
    observation_discounts_factory,
    observation_sales_factory,
    legacy_fact_sales_factory,
    legacy_skus_factory,
    silver_skus_factory,
):
    fact_visibility_table = legacy_fact_visibility_factory.build_table(
        sku_studio="10000-steam:13",
    )

    fact_event_day_table = legacy_fact_event_day_factory.build_table(size=0)
    observation_discounts_table = observation_discounts_factory.build_table(
        unique_sku_id="10000-steam:13",
    )
    observation_sales_table = observation_sales_factory.build_table(
        unique_sku_id="10000-steam:13",
    )
    legacy_skus_table = legacy_skus_factory.build_table(
        sku_studio="10000-steam:13",
    )
    legacy_sales_table = legacy_fact_sales_factory.build_table(
        sku_studio="10000-steam:13",
    )
    silver_skus_table = silver_skus_factory.build_table(
        unique_sku_id="10000-steam:13",
    )
    result = FactEventsAggregator(
        fact_visibility_table,
        fact_event_day_table,
        observation_discounts_table,
        observation_sales_table,
        legacy_sales_table,
        legacy_skus_table,
        silver_skus_table,
    ).aggregate()

    assert result.df.is_empty()


def test_correct_output(
    legacy_fact_visibility_factory,
    legacy_fact_event_day_factory,
    observation_discounts_factory,
    observation_sales_factory,
    legacy_fact_sales_factory,
    legacy_skus_factory,
    silver_skus_factory,
):
    fact_visibility_table = legacy_fact_visibility_factory.build_table(
        sku_studio="10000-steam:13",
    )

    fact_event_day_table = legacy_fact_event_day_factory.build_table()
    observation_discounts_table = observation_discounts_factory.build_table(
        unique_sku_id="10000-steam:13",
    )
    observation_sales_table = observation_sales_factory.build_table(
        unique_sku_id="10000-steam:13",
    )
    legacy_skus_table = legacy_skus_factory.build_table(
        sku_studio="10000-steam:13",
    )
    legacy_sales_table = legacy_fact_sales_factory.build_table(
        sku_studio="10000-steam:13",
    )
    silver_skus_table = silver_skus_factory.build_table(
        unique_sku_id="10000-steam:13",
    )

    result = FactEventsAggregator(
        fact_visibility_table,
        fact_event_day_table,
        observation_discounts_table,
        observation_sales_table,
        legacy_sales_table,
        legacy_skus_table,
        silver_skus_table,
    ).aggregate()

    assert result.df.height == 1
    assert result.df.to_dicts() == [
        {
            "event_id": "Super Product:171010:1:2024-06-13",
            "date_ir": date(2024, 6, 13),
            "date_to": date(2024, 6, 20),
            "product_id": "Super Product:171010:1",
            "portal_platform_region_id": 171010,
            "studio_id": 13,
            "discount": 0.2,
            "dates_short": "Jun 13 Jun 20",
            "promo_length": 8,
            "event_status": "finished",
            "event_name": "True Hot Sale",
            "event_name_2": "2024-06-13 (8 days)",
            "year": "2024",
            "ctr": None,
            "conversion_rate": None,
            "non_owner_visits": 0,
            "non_owner_impressions": 0,
            "uplift_score": None,
            "ctr_normalized": None,
            "conversion_rate_normalized": None,
            "non_owner_visits_normalized": None,
            "non_owner_impressions_normalized": None,
            "uplift_score_normalized": None,
        }
    ]
