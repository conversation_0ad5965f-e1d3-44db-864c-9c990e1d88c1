import datetime

from saas_gold.aggregators.export_fact_sales import (
    ExportFactSalesAggregator,
)


def test_empty_fact_sales(
    legacy_skus_factory,
    legacy_dim_portals_factory,
    observation_discounts_factory,
    legacy_fact_event_day_factory,
    legacy_fact_sales_factory,
):
    table_skus = legacy_skus_factory.build_table()
    table_portals = legacy_dim_portals_factory.build_table()
    empty_table_fact_sales = legacy_fact_sales_factory.build_table(size=0)
    table_event_day = legacy_fact_event_day_factory.build_table()
    table_discounts = observation_discounts_factory.build_table()
    result = ExportFactSalesAggregator(
        legacy_dim_skus=table_skus,
        legacy_dim_portals=table_portals,
        legacy_fact_sales=empty_table_fact_sales,
        legacy_fact_event_day=table_event_day,
        observation_discounts=table_discounts,
    ).aggregate()

    assert result.df.is_empty()


def test_category_not_in_direct_categories(
    legacy_skus_factory,
    legacy_dim_portals_factory,
    observation_discounts_factory,
    legacy_fact_event_day_factory,
    legacy_fact_sales_factory,
):
    table_skus = legacy_skus_factory.build_table()
    table_portals = legacy_dim_portals_factory.build_table()
    table_fact_sales = legacy_fact_sales_factory.build_table(category="Other")
    table_event_day = legacy_fact_event_day_factory.build_table()
    table_discounts = observation_discounts_factory.build_table()
    result = ExportFactSalesAggregator(
        legacy_dim_skus=table_skus,
        legacy_dim_portals=table_portals,
        legacy_fact_sales=table_fact_sales,
        legacy_fact_event_day=table_event_day,
        observation_discounts=table_discounts,
    ).aggregate()
    assert result.df.select(["units_sold_directly"]).item() == 0


def test_category_non_billable(
    legacy_skus_factory,
    legacy_dim_portals_factory,
    observation_discounts_factory,
    legacy_fact_event_day_factory,
    legacy_fact_sales_factory,
):
    table_skus = legacy_skus_factory.build_table()
    table_portals = legacy_dim_portals_factory.build_table()
    table_fact_sales = legacy_fact_sales_factory.build_table(
        category="Non-billable Sale"
    )
    table_event_day = legacy_fact_event_day_factory.build_table()
    table_discounts = observation_discounts_factory.build_table()
    result = ExportFactSalesAggregator(
        legacy_dim_skus=table_skus,
        legacy_dim_portals=table_portals,
        legacy_fact_sales=table_fact_sales,
        legacy_fact_event_day=table_event_day,
        observation_discounts=table_discounts,
    ).aggregate()
    assert result.df.select(["units_sold_directly", "units_freely_distributed"]).row(
        0
    ) == (
        0,
        200,
    )


def test_category_retail(
    legacy_skus_factory,
    legacy_dim_portals_factory,
    observation_discounts_factory,
    legacy_fact_event_day_factory,
    legacy_fact_sales_factory,
):
    table_skus = legacy_skus_factory.build_table()
    table_portals = legacy_dim_portals_factory.build_table()
    table_fact_sales = legacy_fact_sales_factory.build_table(category="Retail Sale")
    table_event_day = legacy_fact_event_day_factory.build_table()
    table_discounts = observation_discounts_factory.build_table()
    result = ExportFactSalesAggregator(
        legacy_dim_skus=table_skus,
        legacy_dim_portals=table_portals,
        legacy_fact_sales=table_fact_sales,
        legacy_fact_event_day=table_event_day,
        observation_discounts=table_discounts,
    ).aggregate()
    assert result.df.select(["units_sold_directly", "units_sold_in_retail"]).row(0) == (
        0,
        100,
    )


def test_fact_sales_aggregations(
    legacy_skus_factory,
    legacy_dim_portals_factory,
    observation_discounts_factory,
    legacy_fact_event_day_factory,
    legacy_fact_sales_factory,
):
    table_skus = legacy_skus_factory.build_table()
    table_portals = legacy_dim_portals_factory.build_table(size=2)
    legacy_fact_sales_factory.reset_sequence(force=True)
    fact_sales_1 = legacy_fact_sales_factory.build_table(
        base_sku_id="10000", start_date=datetime.date(year=2000, month=1, day=1)
    )

    legacy_fact_sales_factory.reset_sequence(force=True)
    fact_sales_2 = legacy_fact_sales_factory.build_table(
        base_sku_id="10000",
        start_date=datetime.date(year=2000, month=1, day=1),
        base_price_local=24.99,
        calculated_base_price_usd=26.99,
        calculated_base_price_local_v2=24.99,
        calculated_base_price_usd_v2=26.99,
    )

    table_fact_sales = fact_sales_1 + fact_sales_2
    table_event_day = legacy_fact_event_day_factory.build_table()
    table_discounts = observation_discounts_factory.build_table()
    result = ExportFactSalesAggregator(
        legacy_dim_skus=table_skus,
        legacy_dim_portals=table_portals,
        legacy_fact_sales=table_fact_sales,
        legacy_fact_event_day=table_event_day,
        observation_discounts=table_discounts,
    ).aggregate()

    assert result.df.to_dicts() == [
        {
            "date": datetime.date(2000, 1, 1),
            "unique_sku_id": "10000-steam:1",
            "base_sku_id": "10000",
            "human_name": "SUPERHOT",
            "product_id": "Super Product:171010:1",
            "product_name": "Super Product",
            "portal_platform_region_id": 171010,
            "portal": "Steam",
            "store": "Steam",
            "bundle_name": "Direct Package Sale",
            "country_code": "USA",
            "region": "North America",
            "currency_code": "USD",
            "gross_revenue": 5018.0,
            "gross_returned": 20.0,
            "gross_sales": 4998.0,
            "net_sales_approx": 4000.0,
            "all_units": 402,
            "units_sold_directly": 200,
            "units_returned": 2,
            "units_freely_distributed": 200,
            "units_sold_in_retail": 0,
            "promo_regular": "regular",
            "base_price_local": 23.99,
            "calculated_base_price_usd": 25.99,
            "calculated_base_price_local_v2": 23.99,
            "calculated_base_price_usd_v2": 25.99,
        }
    ]


def test_returns_proper_net_approx(
    legacy_skus_factory,
    legacy_dim_portals_factory,
    observation_discounts_factory,
    legacy_fact_event_day_factory,
    legacy_fact_sales_factory,
):
    table_skus = legacy_skus_factory.build_table(base_sku_id="1102228", studio_id=11062)
    table_portals = legacy_dim_portals_factory.build_table(size=2)
    legacy_fact_sales_factory.reset_sequence(force=True)
    table_fact_sales = legacy_fact_sales_factory.build_table(
        base_sku_id="1102228",
        studio_id=11062,
        start_date=datetime.date(year=2025, month=4, day=1),
    )
    table_event_day = legacy_fact_event_day_factory.build_table(
        base_sku_id="1102228", studio_id=11062
    )
    table_discounts = observation_discounts_factory.build_table(
        base_sku_id="1102228", studio_id=11062
    )
    result = ExportFactSalesAggregator(
        legacy_dim_skus=table_skus,
        legacy_dim_portals=table_portals,
        legacy_fact_sales=table_fact_sales,
        legacy_fact_event_day=table_event_day,
        observation_discounts=table_discounts,
    ).aggregate()

    assert round(result.df.select("net_sales_approx").item(), 1) == 2285.7


def test_returns_all_required_fields(
    legacy_skus_factory,
    legacy_dim_portals_factory,
    observation_discounts_factory,
    legacy_fact_event_day_factory,
    legacy_fact_sales_factory,
):
    table_skus = legacy_skus_factory.build_table()
    table_portals = legacy_dim_portals_factory.build_table(size=2)
    legacy_fact_sales_factory.reset_sequence(force=True)
    table_fact_sales = legacy_fact_sales_factory.build_table()
    table_event_day = legacy_fact_event_day_factory.build_table()
    table_discounts = observation_discounts_factory.build_table()
    result = ExportFactSalesAggregator(
        legacy_dim_skus=table_skus,
        legacy_dim_portals=table_portals,
        legacy_fact_sales=table_fact_sales,
        legacy_fact_event_day=table_event_day,
        observation_discounts=table_discounts,
    ).aggregate()

    assert result.df.to_dicts() == [
        {
            "date": datetime.date(2000, 1, 1),
            "unique_sku_id": "10000-steam:1",
            "base_sku_id": "10000",
            "human_name": "SUPERHOT",
            "product_id": "Super Product:171010:1",
            "product_name": "Super Product",
            "portal_platform_region_id": 171010,
            "portal": "Steam",
            "store": "Steam",
            "bundle_name": "Direct Package Sale",
            "country_code": "USA",
            "region": "North America",
            "currency_code": "USD",
            "gross_revenue": 2509.0,
            "gross_returned": 10.0,
            "gross_sales": 2499.0,
            "net_sales_approx": 2000.0,
            "all_units": 201,
            "units_sold_directly": 100,
            "units_returned": 1,
            "units_freely_distributed": 100,
            "units_sold_in_retail": 0,
            "promo_regular": "regular",
            "base_price_local": 22.99,
            "calculated_base_price_usd": 24.99,
            "calculated_base_price_local_v2": 22.99,
            "calculated_base_price_usd_v2": 24.99,
        }
    ]
