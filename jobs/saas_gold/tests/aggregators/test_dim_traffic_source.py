from saas_gold.aggregators.dim_traffic_source import DimTrafficSourceAggregator


def test_schema_is_valid(silver_traffic_source_factory):
    table = silver_traffic_source_factory.build_table()
    assert table.df.to_dicts() == [
        {
            "page_category": "Home Page",
            "page_category_group": "Home Page & Similar",
            "page_feature": "Recommended by a Suggested Curator",
            "hash_traffic_source": "5a8477c9735a03986af22d1wa964ac99",
        }
    ]


def test_empty_input_data(silver_traffic_source_factory):
    empty_table = silver_traffic_source_factory.build_table(size=0)

    result = DimTrafficSourceAggregator(empty_table).aggregate()

    assert result.df.is_empty()


def test_returns_all_required_fields(silver_traffic_source_factory):
    legacy_dim_studio = silver_traffic_source_factory.build_table()

    result = DimTrafficSourceAggregator(legacy_dim_studio).aggregate()

    assert result.df.to_dicts() == legacy_dim_studio.df.to_dicts()
