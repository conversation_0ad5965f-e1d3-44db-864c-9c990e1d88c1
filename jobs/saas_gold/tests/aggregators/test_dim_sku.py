import datetime
from typing import Any

from saas_gold.aggregators.dim_sku import DimSkuAggregator


def test_schema_is_valid(legacy_skus_factory):
    table = legacy_skus_factory.build_table()
    assert table.df.to_dicts() == [
        {
            "base_sku_id": "10000",
            "human_name": "SUPERHOT",
            "store_id": "Unknown",
            "studio_id": 1,
            "portal_platform_region": "Steam:PC:Global",
            "human_name_indicator": "sales",
            "sku_type": "SALES",
            "product_name": "Super Product",
            "product_type": "GAME",
            "sku_studio": "10000-steam:1",
            "portal_platform_region_id": 171010,
            "product_id": "Super Product:171010:1",
            "package_name": None,
            "custom_group": None,
            "ratio": 1.0,
            "gso": 100,
            "is_baseline_precalculated": True,
        }
    ]


def test_empty_data_both_inputs(legacy_skus_factory, legacy_fact_sales_factory):
    empty_table_skus = legacy_skus_factory.build_table(size=0)
    empty_table_sales = legacy_fact_sales_factory.build_table(size=0)

    result = DimSkuAggregator(
        empty_table_skus, legacy_fact_sales=empty_table_sales
    ).aggregate()

    assert result.df.is_empty()


def test_empty_data_skus(legacy_skus_factory, legacy_fact_sales_factory):
    empty_table_skus = legacy_skus_factory.build_table(size=0)
    legacy_fact_sales: Any = legacy_fact_sales_factory.build_table()

    result = DimSkuAggregator(
        empty_table_skus, legacy_fact_sales=legacy_fact_sales
    ).aggregate()

    assert result.df.is_empty()


def test_returns_all_required_fields(legacy_skus_factory, legacy_fact_sales_factory):
    legacy_dim_sku = legacy_skus_factory.build_table()
    legacy_fact_sales: Any = legacy_fact_sales_factory.build_table()

    result = DimSkuAggregator(
        legacy_dim_sku, legacy_fact_sales=legacy_fact_sales
    ).aggregate()

    assert result.df.to_dicts() == [
        {
            "unique_sku_id": "10000-steam:1",
            "base_sku_id": "10000",
            "portal_platform_region": "Steam:PC:Global",
            "human_name": "SUPERHOT",
            "product_id": "Super Product:171010:1",
            "sku_type": "SALES",
            "gso": 100,
            "release_date": datetime.date(2000, 1, 2),
        }
    ]


def test_release_date_is_null_when_fact_sales_empty(
    legacy_skus_factory, legacy_fact_sales_factory
):
    legacy_dim_sku = legacy_skus_factory.build_table()
    legacy_fact_sales: Any = legacy_fact_sales_factory.build_table(size=0)

    result = DimSkuAggregator(
        legacy_dim_sku, legacy_fact_sales=legacy_fact_sales
    ).aggregate()

    assert result.df["release_date"].to_list() == [None]


def test_release_date_for_multiple_skus(legacy_skus_factory, legacy_fact_sales_factory):
    legacy_dim_sku = legacy_skus_factory.build_table(size=2)

    legacy_fact_sales_factory.reset_sequence(force=True)
    sku_0_sales = legacy_fact_sales_factory.build_table(base_sku_id="10000")

    legacy_fact_sales_factory.reset_sequence(force=True)
    sku_1_sales = legacy_fact_sales_factory.build_table(
        base_sku_id="10001", start_date=datetime.date(year=2025, month=4, day=16)
    )

    legacy_fact_sales = sku_0_sales + sku_1_sales

    result = DimSkuAggregator(
        legacy_dim_sku, legacy_fact_sales=legacy_fact_sales
    ).aggregate()

    assert result.df.select(["release_date", "unique_sku_id"]).to_dicts() == [
        {"release_date": datetime.date(2000, 1, 1), "unique_sku_id": "10000-steam:1"},
        {"release_date": datetime.date(2025, 4, 16), "unique_sku_id": "10001-steam:1"},
    ]
