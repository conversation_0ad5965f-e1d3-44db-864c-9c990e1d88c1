import datetime

from saas_gold.aggregators.fact_sales import FactSalesAggregator


def test_schema_is_valid(legacy_fact_sales_factory):
    table = legacy_fact_sales_factory.build_table()
    assert table.df.to_dicts() == [
        {
            "country_code": "USA",
            "currency_code": "USD",
            "studio_id": 1,
            "sku_studio": "10000-steam:1",
            "bundle_name": "Direct Package Sale",
            "portal_platform_region": "Steam:PC:Global",
            "portal_platform_region_id": 171010,
            "product_id": "Super Product:171010:1",
            "hash_acquisition_properties": "48eeba97a3e04ab96ae5fbfb87d1a8ad",
            "date": datetime.date(2000, 1, 1),
            "date_sku_studio": "2000-01-01:10000-steam:1",
            "source_file_id": 500123,
            "retailer_tag": "Direct Package Sale",
            "base_price_local": 22.99,
            "calculated_base_price_usd": 24.99,
            "net_sales": 2200.0,
            "gross_returned": 10.0,
            "gross_sales": 2499.0,
            "units_returned": 1,
            "units_sold": 100,
            "free_units": 100,
            "price_local": 22.99,
            "price_usd": 24.99,
            "net_sales_approx": 2000.0,
            "category": "Sale",
            "calculated_base_price_local_v2": 22.99,
            "calculated_base_price_usd_v2": 24.99,
        }
    ]


def test_empty_input_data(legacy_fact_sales_factory, legacy_skus_factory):
    legacy_fact_sales_table = legacy_fact_sales_factory.build_table(size=0)
    dim_sku_table = legacy_skus_factory.build_table()

    result = FactSalesAggregator(
        legacy_fact_sales_table,
        dim_sku_table,
    ).aggregate()

    assert result.df.is_empty()


def test_returns_proper_values(legacy_fact_sales_factory, legacy_skus_factory):
    legacy_dim_sku = legacy_skus_factory.build_table(size=2)

    legacy_fact_sales_factory.reset_sequence(force=True)
    sku_0_sales = legacy_fact_sales_factory.build_table(
        base_sku_id="10000", start_date=datetime.date(year=2025, month=4, day=1)
    )

    legacy_fact_sales_factory.reset_sequence(force=True)
    sku_1_sales = legacy_fact_sales_factory.build_table(
        base_sku_id="10001", start_date=datetime.date(year=2025, month=4, day=1)
    )

    legacy_fact_sales = sku_0_sales + sku_1_sales

    result = FactSalesAggregator(
        legacy_fact_sales=legacy_fact_sales,
        legacy_dim_sku=legacy_dim_sku,
    ).aggregate()
    assert result.df.to_dicts() == [
        {
            "date": datetime.date(year=2025, month=4, day=1),
            "product_id": "Super Product:171010:1",
            "country_code": "USA",
            "portal_platform_region_id": 171010,
            "bundled_sale": False,
            "studio_id": 1,
            "net_sales": 4400.0,
            "net_sales_approx": 4000.0,
            "gross_sales": 4998.0,
            "gross_returned": 20.0,
            "units_returned": 2,
            "units_sold_directly": 200,
            "units_freely_distributed": 200,
            "units_sold_in_retail": 0,
        }
    ]


def test_returns_proper_net_approx(legacy_fact_sales_factory, legacy_skus_factory):
    legacy_dim_sku = legacy_skus_factory.build_table(
        base_sku_id="1102228", studio_id=11062
    )
    legacy_fact_sales = legacy_fact_sales_factory.build_table(
        base_sku_id="1102228",
        studio_id=11062,
        start_date=datetime.date(year=2025, month=4, day=1),
    )
    result = FactSalesAggregator(
        legacy_fact_sales=legacy_fact_sales,
        legacy_dim_sku=legacy_dim_sku,
    ).aggregate()

    assert round(result.df.select("net_sales_approx").item(), 1) == 2285.7


def test_category_not_in_direct_categories(
    legacy_fact_sales_factory, legacy_skus_factory
):
    legacy_dim_sku = legacy_skus_factory.build_table()
    legacy_fact_sales = legacy_fact_sales_factory.build_table(category="Other")

    result = FactSalesAggregator(
        legacy_fact_sales=legacy_fact_sales,
        legacy_dim_sku=legacy_dim_sku,
    ).aggregate()
    assert result.df.select(["units_sold_directly"]).item() == 0


def test_category_non_billable(legacy_fact_sales_factory, legacy_skus_factory):
    legacy_dim_sku = legacy_skus_factory.build_table()
    legacy_fact_sales = legacy_fact_sales_factory.build_table(
        category="Non-billable Sale"
    )

    result = FactSalesAggregator(
        legacy_fact_sales=legacy_fact_sales,
        legacy_dim_sku=legacy_dim_sku,
    ).aggregate()
    assert result.df.select(["units_sold_directly", "units_freely_distributed"]).row(
        0
    ) == (
        0,
        200,
    )


def test_category_retail(legacy_fact_sales_factory, legacy_skus_factory):
    legacy_dim_sku = legacy_skus_factory.build_table()
    legacy_fact_sales = legacy_fact_sales_factory.build_table(category="Retail Sale")

    result = FactSalesAggregator(
        legacy_fact_sales=legacy_fact_sales,
        legacy_dim_sku=legacy_dim_sku,
    ).aggregate()
    assert result.df.select(["units_sold_directly", "units_sold_in_retail"]).row(0) == (
        0,
        100,
    )


def test_bundle_sale(legacy_fact_sales_factory, legacy_skus_factory):
    legacy_dim_sku = legacy_skus_factory.build_table()
    legacy_fact_sales = legacy_fact_sales_factory.build_table(
        bundle_name="Random Bundle"
    )

    result = FactSalesAggregator(
        legacy_fact_sales=legacy_fact_sales,
        legacy_dim_sku=legacy_dim_sku,
    ).aggregate()
    assert result.df.select(["bundled_sale"]).item() == True
