from datetime import date

from saas_gold.aggregators.fact_event_day import FactEventDayAggregator


def test_schema_is_valid(
    legacy_fact_event_day_factory,
):
    table = legacy_fact_event_day_factory.build_table()
    assert table.df.to_dicts() == [
        {
            "discount": 0.2,
            "date": date(2024, 6, 13),
            "studio_id": 13,
            "unique_sku_id": "10000-steam:13",
            "portal_platform_region_id": 171010,
            "date_from": date(2024, 6, 12),
            "date_to": date(2024, 6, 14),
            "promo_length": 3,
            "type": "Calculated",
            "event_name": "Hot Sale",
            "event_description": None,
            "event_status": "finished",
            "dates_short": "Jun 12 Jun 14",
            "days_since_previous_discount": 14,
            "event_day_number": 0.0,
            "event_id": "hot sale:2024-06-12",
        }
    ]


def test_empty_sales_input_data(
    legacy_fact_event_day_factory,
    observation_discounts_factory,
    legacy_fact_sales_factory,
    legacy_skus_factory,
):
    empty_table = legacy_fact_sales_factory.build_table(size=0)
    fact_event_day_table = legacy_fact_event_day_factory.build_table()
    observation_discounts_table = observation_discounts_factory.build_table()
    legacy_skus_table = legacy_skus_factory.build_table()
    result = FactEventDayAggregator(
        fact_event_day_table,
        observation_discounts_table,
        empty_table,
        legacy_skus_table,
    ).aggregate()

    assert result.df.is_empty()


def test_overwrite_event_name(
    legacy_fact_event_day_factory,
    observation_discounts_factory,
    legacy_fact_sales_factory,
    legacy_skus_factory,
):
    fact_event_day_table = legacy_fact_event_day_factory.build_table(
        event_name="not correct name",
    )

    observation_discounts_table = observation_discounts_factory.build_table(
        unique_sku_id="10000-steam:13",
        event_name="True Hot Sale",
    )

    legacy_skus_table = legacy_skus_factory.build_table(
        sku_studio="10000-steam:13",
    )

    legacy_sales_table = legacy_fact_sales_factory.build_table(
        sku_studio="10000-steam:13",
    )

    result = FactEventDayAggregator(
        fact_event_day_table,
        observation_discounts_table,
        legacy_sales_table,
        legacy_skus_table,
    ).aggregate()

    assert not result.df.is_empty()

    event_names = result.df["event_name"].to_list()
    assert "True Hot Sale" in event_names
