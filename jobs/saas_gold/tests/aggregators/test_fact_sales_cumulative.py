from datetime import date

from saas_gold.aggregators.fact_sales_cumulative import FactSalesCumulativeAggregator


def test_empty_fact_sales(
    legacy_fact_sales_factory,
    legacy_fact_event_day_factory,
    observation_discounts_factory,
    legacy_skus_factory,
):
    legacy_sales_table = legacy_fact_sales_factory.build_table(size=0)
    fact_event_day_table = legacy_fact_event_day_factory.build_table()
    observation_discounts_table = observation_discounts_factory.build_table(
        unique_sku_id="10000-steam:13",
    )
    legacy_skus_table = legacy_skus_factory.build_table(
        sku_studio="10000-steam:13",
    )
    result = FactSalesCumulativeAggregator(
        legacy_sales_table,
        fact_event_day_table,
        observation_discounts_table,
        legacy_skus_table,
    ).aggregate()

    assert result.df.is_empty()


def test_correct_output(
    legacy_fact_sales_factory,
    legacy_fact_event_day_factory,
    observation_discounts_factory,
    legacy_skus_factory,
):
    legacy_sales_table = legacy_fact_sales_factory.build_table()
    fact_event_day_table = legacy_fact_event_day_factory.build_table()
    observation_discounts_table = observation_discounts_factory.build_table(
        unique_sku_id="10000-steam:13",
    )
    legacy_skus_table = legacy_skus_factory.build_table(
        sku_studio="10000-steam:13",
    )
    result = FactSalesCumulativeAggregator(
        legacy_sales_table,
        fact_event_day_table,
        observation_discounts_table,
        legacy_skus_table,
    ).aggregate()

    assert result.df.to_dicts() == [
        {
            "date": date(2000, 1, 1),
            "product_id": "Super Product:171010:1",
            "portal_platform_region_id": 171010,
            "net_sales_cumulative": 2200.0,
            "net_sales_approx_cumulative": 2000.0,
            "gross_sales_cumulative": 2499.0,
            "gross_returned_cumulative": 10.0,
            "units_returned_cumulative": 1,
            "units_sold_directly_cumulative": 100,
            "units_freely_distributed_cumulative": 100,
            "units_sold_in_retail_cumulative": 0,
            "gross_sales_incl_returns_cumulative": 2509.0,
            "gross_sales_incl_returns_promo_cumulative": 0.0,
        }
    ]
