import datetime

from saas_gold.aggregators.export_fact_visibility_wishlist import (
    ExportFactVisibilityWishlistAggregator,
)


def test_empty_portals(
    legacy_skus_factory,
    legacy_fact_visibility_factory,
    legacy_fact_wishlist_actions_factory,
    legacy_dim_portals_factory,
):
    table_skus = legacy_skus_factory.build_table()
    empty_table_portals = legacy_dim_portals_factory.build_table(size=0)
    table_visibility = legacy_fact_visibility_factory.build_table(size=0)
    table_wishlist = legacy_fact_wishlist_actions_factory.build_table(size=0)

    result = ExportFactVisibilityWishlistAggregator(
        legacy_fact_visibility=table_visibility,
        legacy_fact_wishlist_actions=table_wishlist,
        legacy_dim_skus=table_skus,
        legacy_dim_portals=empty_table_portals,
    ).aggregate()

    assert result.df.is_empty()


def test_empty_data_visibility_and_wishlist(
    legacy_skus_factory,
    legacy_dim_portals_factory,
    legacy_fact_visibility_factory,
    legacy_fact_wishlist_actions_factory,
):
    table_skus = legacy_skus_factory.build_table()
    table_portals = legacy_dim_portals_factory.build_table()
    empty_table_wishlist = legacy_fact_wishlist_actions_factory.build_table(size=0)
    empty_table_visibility = legacy_fact_visibility_factory.build_table(size=0)

    result = ExportFactVisibilityWishlistAggregator(
        legacy_fact_visibility=empty_table_visibility,
        legacy_fact_wishlist_actions=empty_table_wishlist,
        legacy_dim_skus=table_skus,
        legacy_dim_portals=table_portals,
    ).aggregate()

    assert result.df.columns == [
        "date",
        "unique_sku_id",
        "base_sku_id",
        "human_name",
        "product_id",
        "product_name",
        "portal_platform_region_id",
        "portal",
        "store",
        "country_code",
        "non_owner_visits",
        "non_owner_impressions",
        "adds",
        "deletes",
        "purchases_activations_gifts",
    ]


def test_empty_data_visibility(
    legacy_skus_factory,
    legacy_dim_portals_factory,
    legacy_fact_visibility_factory,
    legacy_fact_wishlist_actions_factory,
):
    table_skus = legacy_skus_factory.build_table()
    table_portals = legacy_dim_portals_factory.build_table(size=2)
    legacy_fact_wishlist_actions_factory.reset_sequence(force=True)
    table_wishlist = legacy_fact_wishlist_actions_factory.build_table(
        sku_studio="10000-steam:1", country_code="USA"
    )
    empty_table_visibility = legacy_fact_visibility_factory.build_table(size=0)

    result = ExportFactVisibilityWishlistAggregator(
        legacy_fact_visibility=empty_table_visibility,
        legacy_fact_wishlist_actions=table_wishlist,
        legacy_dim_skus=table_skus,
        legacy_dim_portals=table_portals,
    ).aggregate()

    assert result.df.to_dicts() == [
        {
            "date": datetime.date(2000, 1, 1),
            "unique_sku_id": "10000-steam:1",
            "base_sku_id": "10000",
            "human_name": "SUPERHOT",
            "product_id": "Super Product:171010:1",
            "product_name": "Super Product",
            "portal_platform_region_id": 171010,
            "portal": "Steam",
            "store": "Steam",
            "country_code": "USA",
            "non_owner_visits": None,
            "non_owner_impressions": None,
            "adds": 10,
            "deletes": 5,
            "purchases_activations_gifts": 3,
        }
    ]


def test_empty_data_wishlist(
    legacy_skus_factory,
    legacy_dim_portals_factory,
    legacy_fact_visibility_factory,
    legacy_fact_wishlist_actions_factory,
):
    table_skus = legacy_skus_factory.build_table()
    table_portals = legacy_dim_portals_factory.build_table(size=2)
    table_wishlist = legacy_fact_wishlist_actions_factory.build_table(size=0)
    legacy_fact_visibility_factory.reset_sequence(force=True)
    table_visibility = legacy_fact_visibility_factory.build_table(
        sku_studio="10000-steam:1"
    )
    result = ExportFactVisibilityWishlistAggregator(
        legacy_fact_visibility=table_visibility,
        legacy_fact_wishlist_actions=table_wishlist,
        legacy_dim_skus=table_skus,
        legacy_dim_portals=table_portals,
    ).aggregate()
    assert result.df.to_dicts() == [
        {
            "date": datetime.date(2000, 1, 1),
            "unique_sku_id": "10000-steam:1",
            "base_sku_id": "10000",
            "human_name": "SUPERHOT",
            "product_id": "Super Product:171010:1",
            "product_name": "Super Product",
            "portal_platform_region_id": 171010,
            "portal": "Steam",
            "store": "Steam",
            "country_code": "YYY",
            "non_owner_visits": 9,
            "non_owner_impressions": 90,
            "adds": None,
            "deletes": None,
            "purchases_activations_gifts": None,
        }
    ]


def test_returns_all_required_fields(
    legacy_skus_factory,
    legacy_dim_portals_factory,
    legacy_fact_visibility_factory,
    legacy_fact_wishlist_actions_factory,
):
    table_skus = legacy_skus_factory.build_table()
    table_portals = legacy_dim_portals_factory.build_table(size=2)
    legacy_fact_wishlist_actions_factory.reset_sequence(force=True)
    table_wishlist = legacy_fact_wishlist_actions_factory.build_table(
        sku_studio="10000-steam:1", country_code="YYY"
    )
    legacy_fact_visibility_factory.reset_sequence(force=True)
    table_visibility = legacy_fact_visibility_factory.build_table(
        sku_studio="10000-steam:1"
    )
    result = ExportFactVisibilityWishlistAggregator(
        legacy_fact_visibility=table_visibility,
        legacy_fact_wishlist_actions=table_wishlist,
        legacy_dim_skus=table_skus,
        legacy_dim_portals=table_portals,
    ).aggregate()

    assert result.df.to_dicts() == [
        {
            "date": datetime.date(2000, 1, 1),
            "unique_sku_id": "10000-steam:1",
            "base_sku_id": "10000",
            "human_name": "SUPERHOT",
            "product_id": "Super Product:171010:1",
            "product_name": "Super Product",
            "portal_platform_region_id": 171010,
            "portal": "Steam",
            "store": "Steam",
            "country_code": "YYY",
            "non_owner_visits": 9,
            "non_owner_impressions": 90,
            "adds": 10,
            "deletes": 5,
            "purchases_activations_gifts": 3,
        }
    ]
