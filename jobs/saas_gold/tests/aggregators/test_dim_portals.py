from datetime import date

from saas_gold.aggregators.dim_portals import DimPortalsAggregator


def test_schema_is_valid(legacy_dim_portals_factory):
    table = legacy_dim_portals_factory.build_table()
    assert table.df.to_dicts() == [
        {
            "portal": "Steam",
            "platform": "PC",
            "region": "Global",
            "store": "Steam",
            "abbreviated_name": "Steam",
            "portal_platform_region": "Steam:PC:Global",
            "portal_platform_region_id": 171010,
            "so_portal": 1,
            "so_platform": 0,
            "so_region": 0,
            "pso": 100,
        }
    ]


def test_empty_input_data(
    legacy_dim_portals_factory, external_reports_factory, silver_reports_factory
):
    empty_table = legacy_dim_portals_factory.build_table(size=0)
    external_reports_table = external_reports_factory.build_table()
    silver_reports_table = silver_reports_factory.build_table()

    result = DimPortalsAggregator(
        empty_table,
        external_reports=external_reports_table,
        silver_reports=silver_reports_table,
    ).aggregate()

    assert result.df.is_empty()


def test_returns_all_required_rows(
    legacy_dim_portals_factory, external_reports_factory, silver_reports_factory
):
    legacy_dim_portals = legacy_dim_portals_factory.build_table(size=2)
    external_reports_table = external_reports_factory.build_table(size=2)
    silver_reports_table = silver_reports_factory.build_table(size=2)

    result = DimPortalsAggregator(
        legacy_dim_portals=legacy_dim_portals,
        external_reports=external_reports_table,
        silver_reports=silver_reports_table,
    ).aggregate()

    assert result.df.height == 2


def test_latest_date(
    legacy_dim_portals_factory, external_reports_factory, silver_reports_factory
):
    legacy_dim_portals = legacy_dim_portals_factory.build_table(size=2)
    external_reports_table = external_reports_factory.build_table(
        report_id=8001, state="PENDING", date_to=date(2023, 3, 1)
    ) + external_reports_factory.build_table(
        report_id=8002, state="PENDING", date_to=date(2023, 2, 2)
    )

    silver_reports_table = silver_reports_factory.build_table(
        report_id=8001, state="PENDING", date_to=date(2023, 3, 1)
    ) + silver_reports_factory.build_table(
        report_id=8002, state="CONVERTED", date_to=date(2023, 2, 2)
    )

    result = DimPortalsAggregator(
        legacy_dim_portals=legacy_dim_portals,
        external_reports=external_reports_table,
        silver_reports=silver_reports_table,
    ).aggregate()

    df = result.df.sort("portal")

    latest_dates = df["latest_date"].to_list()

    assert latest_dates[0] is None
    assert latest_dates[1] == date(2023, 2, 2)
