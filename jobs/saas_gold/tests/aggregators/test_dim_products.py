from saas_gold.aggregators.dim_products import DimProductsAggregator


def test_schema_is_valid(legacy_skus_factory):
    table = legacy_skus_factory.build_table()
    assert table.df.to_dicts() == [
        {
            "base_sku_id": "10000",
            "human_name": "SUPERHOT",
            "store_id": "Unknown",
            "studio_id": 1,
            "portal_platform_region": "Steam:PC:Global",
            "human_name_indicator": "sales",
            "sku_type": "SALES",
            "product_name": "Super Product",
            "product_type": "GAME",
            "sku_studio": "10000-steam:1",
            "portal_platform_region_id": 171010,
            "product_id": "Super Product:171010:1",
            "package_name": None,
            "custom_group": None,
            "ratio": 1.0,
            "gso": 100,
            "is_baseline_precalculated": True,
        }
    ]


def test_empty_input_data(legacy_skus_factory):
    empty_table = legacy_skus_factory.build_table(size=0)

    result = DimProductsAggregator(empty_table).aggregate()

    assert result.df.is_empty()


def test_returns_all_required_fields(legacy_skus_factory):
    legacy_dim_sku = legacy_skus_factory.build_table()

    result = DimProductsAggregator(legacy_dim_sku).aggregate()

    assert result.df.to_dicts() == [
        {
            "product_id": "Super Product:171010:1",
            "studio_id": 1,
            "portal_platform_region_id": 171010,
            "product_name": "Super Product",
            "gso": 100,
        }
    ]


def test_take_minimum_gso_for_product(legacy_skus_factory):
    minimum_gso = legacy_skus_factory.build_table(gso=1)
    maximum_gso = legacy_skus_factory.build_table(gso=2)

    legacy_dim_sku = minimum_gso + maximum_gso

    result = DimProductsAggregator(legacy_dim_sku).aggregate()

    assert result.df["gso"].to_list() == [1]
