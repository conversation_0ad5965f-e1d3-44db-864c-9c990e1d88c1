import datetime

import polars as pl

from data_sdk.domain.tables import TableDefinition
from data_sdk.segmentator import BaseSegmentator, SegmentDefinition
from saas_gold.utils.date_utils import datetime_to_string


class YearlySegmentator(BaseSegmentator):
    def __init__(self, date_column: str):
        self.date_column = date_column

    def create_segments(
        self, table: TableDefinition, creation_datetime: datetime.datetime
    ) -> list[SegmentDefinition]:
        date_series = table.df[self.date_column]

        if not isinstance(date_series.dtype, pl.datatypes.Date):
            date_series = date_series.apply(
                lambda d: datetime.datetime.strptime(str(d), "%Y-%m-%d").date(),
                return_dtype=pl.Date,
            )

        latest_date = date_series.max()
        earliest_date = date_series.min()
        earliest_date = datetime.date(earliest_date.year, 1, 1)

        chunks_folder_name = datetime_to_string(creation_datetime)

        custom_partitions = []
        range = pl.date_range(earliest_date, latest_date, "1y", eager=True).alias(
            "date"
        )
        for period_start in range:
            period_end = period_start.replace(
                year=period_start.year + 1
            ) - datetime.timedelta(days=1)
            custom_partitions.append(
                SegmentDefinition(
                    path=f"{chunks_folder_name}/{period_start}--{period_end}",
                    mask_expression=(pl.col(self.date_column) >= period_start)
                    & (pl.col(self.date_column) <= period_end),
                )
            )

        return custom_partitions
