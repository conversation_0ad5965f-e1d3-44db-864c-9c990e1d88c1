import datetime

from data_sdk.domain.tables import TableDefinition
from data_sdk.segmentator import BaseSegmentator, SegmentDefinition


class SingleFileNoTimestampSegmentator(BaseSegmentator):
    def create_segments(
        self, table: TableDefinition, creation_datetime: datetime.datetime | None
    ) -> list[SegmentDefinition]:
        return [
            SegmentDefinition(
                path=f"{table.table_name.value}",
                mask_expression=(True),
            )
        ]
