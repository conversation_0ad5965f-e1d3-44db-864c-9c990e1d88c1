from datetime import datetime

import polars as pl

from data_sdk.domain.tables import TableDefinition
from data_sdk.segmentator import BaseSegmentator, SegmentDefinition
from saas_gold.utils.date_utils import datetime_to_string


class MonolithSegmentator(BaseSegmentator):
    def __init__(self, date_column: str):
        self.date_column = date_column

    def create_segments(
        self,
        table: TableDefinition,
        creation_datetime: datetime,
    ) -> list[SegmentDefinition]:
        date_series = table.df[self.date_column]

        if not isinstance(date_series.dtype, pl.datatypes.Date):
            date_series = date_series.apply(
                lambda d: datetime.strptime(str(d), "%Y-%m-%d").date(),
                return_dtype=pl.Date,
            )

        latest_date = date_series.max()
        earliest_date = date_series.min()

        chunks_folder_name = datetime_to_string(creation_datetime)
        return [
            SegmentDefinition(
                path=f"{chunks_folder_name}/{earliest_date}--{latest_date}",
                mask_expression=(True),
            )
        ]
