from datetime import datetime

from data_sdk.domain.tables import TableDefinition
from data_sdk.segmentator import BaseSegmentator, SegmentDefinition
from saas_gold.segmentators.monolith import MonolithSegmentator
from saas_gold.segmentators.monthly import MonthlySegmentator
from saas_gold.segmentators.quarterly import QuarterlySegmentator
from saas_gold.segmentators.yearly import YearlySegmentator


class ChainedSegmentatorMoYQM(BaseSegmentator):
    def __init__(self, date_column: str):
        self.date_column = date_column

    def create_segments(
        self, table: TableDefinition, creation_datetime: datetime
    ) -> list[SegmentDefinition]:
        return (
            MonthlySegmentator(self.date_column).create_segments(
                table, creation_datetime
            )
            + QuarterlySegmentator(self.date_column).create_segments(
                table, creation_datetime
            )
            + YearlySegmentator(self.date_column).create_segments(
                table, creation_datetime
            )
            + MonolithSegmentator(self.date_column).create_segments(
                table, creation_datetime
            )
        )
