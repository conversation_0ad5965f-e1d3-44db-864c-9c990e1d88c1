import datetime

import polars as pl
from dateutil.relativedelta import relativedelta

from data_sdk.domain.tables import TableDefinition
from data_sdk.segmentator import BaseSegmentator, SegmentDefinition
from saas_gold.utils.date_utils import datetime_to_string


class QuarterlySegmentator(BaseSegmentator):
    def __init__(self, date_column: str):
        self.date_column = date_column

    def create_segments(
        self, table: TableDefinition, creation_datetime: datetime.datetime
    ) -> list[SegmentDefinition]:
        date_series = table.df[self.date_column]

        if not isinstance(date_series.dtype, pl.datatypes.Date):
            date_series = date_series.apply(
                lambda d: datetime.datetime.strptime(str(d), "%Y-%m-%d").date(),
                return_dtype=pl.Date,
            )

        latest_date = date_series.max()
        earliest_date = date_series.min()

        # start of a quarter
        year = earliest_date.year
        months_in_quarter = 3
        quarter_start_month = (
            (earliest_date.month - 1) // months_in_quarter
        ) * months_in_quarter + 1
        day = 1

        earliest_date = datetime.date(year, quarter_start_month, day)
        chunks_folder_name = datetime_to_string(creation_datetime)

        custom_partitions = []
        range = pl.date_range(earliest_date, latest_date, "3mo", eager=True).alias(
            "date"
        )
        for period_start in range:
            period_end = (period_start + relativedelta(months=3)) - datetime.timedelta(
                days=1
            )
            custom_partitions.append(
                SegmentDefinition(
                    path=f"{chunks_folder_name}/{period_start}--{period_end}",
                    mask_expression=(pl.col(self.date_column) >= period_start)
                    & (pl.col(self.date_column) <= period_end),
                )
            )

        return custom_partitions
