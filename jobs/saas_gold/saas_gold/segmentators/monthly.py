import calendar
from datetime import date, datetime

import polars as pl

from data_sdk.domain.tables import TableDefinition
from data_sdk.segmentator import BaseSegmentator, SegmentDefinition
from saas_gold.utils.date_utils import datetime_to_string


class MonthlySegmentator(BaseSegmentator):
    def __init__(self, date_column: str):
        self.date_column = date_column

    def create_segments(
        self, table: TableDefinition, creation_datetime: datetime
    ) -> list[SegmentDefinition]:
        date_series = table.df[self.date_column]

        if not isinstance(date_series.dtype, pl.datatypes.Date):
            date_series = date_series.apply(
                lambda d: datetime.strptime(str(d), "%Y-%m-%d").date(),
                return_dtype=pl.Date,
            )

        latest_date = date_series.max()
        earliest_date = date_series.min()
        earliest_date = date(earliest_date.year, earliest_date.month, 1)

        chunks_folder_name = datetime_to_string(creation_datetime)

        segments = []
        range = pl.date_range(earliest_date, latest_date, "1mo", eager=True).alias(
            "date"
        )
        for period_start in range:
            period_end = period_start.replace(
                day=calendar.monthrange(period_start.year, period_start.month)[1]
            )
            segments.append(
                SegmentDefinition(
                    path=f"{chunks_folder_name}/{period_start}--{period_end}",
                    mask_expression=(pl.col(self.date_column) >= period_start)
                    & (pl.col(self.date_column) <= period_end),
                )
            )

        return segments
