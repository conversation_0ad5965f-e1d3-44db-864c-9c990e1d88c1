from data_sdk.custom_partition.writer import CustomPartitionsWriter
from data_sdk.domain.tables import TableDefinition
from data_sdk.segmentator import SegmentDefinition
from saas_gold.segmentators.single_file_no_timestamp import (
    SingleFileNoTimestampSegmentator,
)


class ChainedWriter(CustomPartitionsWriter):
    def __init__(
        self,
        primary_writer: CustomPartitionsWriter,
        secondary_writers: list[CustomPartitionsWriter] | None,
    ) -> None:
        self._primary_writer = primary_writer
        self._secondary_writers = secondary_writers or []

    def save_table(
        self, table: TableDefinition, partition: str, segments: list[SegmentDefinition]
    ):
        self._primary_writer.save_table(table, partition, segments)
        # Save CSV
        for secondary_writer in self._secondary_writers:
            csv_segments = SingleFileNoTimestampSegmentator().create_segments(
                table, None
            )
            # TODO: dirty hack until refactor :) it assumes that path to Parquet is studio/table and I truncat till studio.
            csv_partition = partition.split("/")[0]
            secondary_writer.save_table(table, csv_partition, csv_segments)

    def close(self):
        self._primary_writer.close()
        for secondary_writer in self._secondary_writers:
            secondary_writer.close()
