from pathlib import Path

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict

from data_sdk.config import DLSConfig, Extension, LocalConfig


class Config(BaseSettings):
    model_config = SettingsConfigDict(env_nested_delimiter="__")

    hostname: str = "localhost"

    env: str = "local"
    app_version: str = "1.0.0"
    job_name: str = "saas-gold-job"

    docker_tag: str = ""
    docker_build_timestamp: str = ""

    input_cfg: DLSConfig | LocalConfig = Field(
        discriminator="type",
        default=LocalConfig(local_dir=Path("playground")),
    )
    pbi_output_cfg: DLSConfig | LocalConfig = Field(
        discriminator="type",
        default=LocalConfig(local_dir=Path("playground/pbi")),
    )
    direct_data_access_cfg: DLSConfig | LocalConfig = Field(
        discriminator="type",
        default=LocalConfig(
            local_dir=Path("playground/export"), file_extension=Extension.CSV
        ),
    )

    run_type: str = "cp"
    dry_run: bool = False
