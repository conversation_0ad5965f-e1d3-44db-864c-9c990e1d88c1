import polars as pl


def apply_net_sales_multipliers(
    obs_sales: pl.DataFrame, skus_table: pl.DataFrame
) -> pl.DataFrame:
    steam_target_studios = (pl.col("portal_platform_region_id") == 171010) & (
        pl.col("studio_id").is_in([11062, 10706])
    )

    obs_filtered = obs_sales.filter(steam_target_studios)

    obs_other = obs_sales.filter(~steam_target_studios)
    joined = obs_filtered.join(skus_table, on=["sku_studio", "studio_id"], how="left")

    adjusted = joined.with_columns(
        [
            pl.when(pl.col("date") >= pl.col("10M"))
            .then(pl.col("net_sales_approx") * 8 / 7)
            .when(pl.col("date") >= pl.col("5M"))
            .then(pl.col("net_sales_approx") * 75 / 70)
            .otherwise(pl.col("net_sales_approx"))
            .alias("net_sales_approx")
        ]
    )
    adjusted = adjusted.select(obs_other.columns)

    result = pl.concat([adjusted, obs_other])
    return result


def apply_net_sales_multipliers_observation(
    obs_sales: pl.LazyFrame, skus_table: pl.LazyFrame
) -> pl.LazyFrame:
    steam_target_studios = (pl.col("portal") == "Steam") & (
        pl.col("studio_id").is_in([11062, 10706])
    )

    obs_filtered = obs_sales.filter(steam_target_studios)
    obs_other = obs_sales.filter(~steam_target_studios)

    joined = obs_filtered.with_columns(pl.col("unique_sku_id").cast(pl.Utf8)).join(
        skus_table,
        left_on=["unique_sku_id", "studio_id"],
        right_on=["sku_studio", "studio_id"],
        how="left",
    )
    adjusted = joined.with_columns(
        [
            pl.when(pl.col("date") >= pl.col("10M"))
            .then(pl.col("net_sales_approx") * 8 / 7)
            .when(pl.col("date") >= pl.col("5M"))
            .then(pl.col("net_sales_approx") * 75 / 70)
            .otherwise(pl.col("net_sales_approx"))
            .alias("net_sales_approx")
        ]
    )
    adjusted = adjusted.select(obs_other.columns)
    obs_other = obs_other.with_columns(pl.col("unique_sku_id").cast(pl.Utf8))

    result = pl.concat([adjusted, obs_other])
    return result
