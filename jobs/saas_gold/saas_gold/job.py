import datetime
import logging
import traceback

from pydantic import BaseModel, ValidationError

from data_sdk.aggregator import BaseAggregator, process_aggregators
from data_sdk.config import Extension
from data_sdk.custom_partition.reader import CustomPartitionReader
from data_sdk.custom_partition.writer import CustomPartitionsWriter
from data_sdk.domain.domain_types import StudioId
from saas_gold.aggregators import (
    DimPortalsAggregator,
    DimProductsAggregator,
    DimSkuAggregator,
    DimStudioAggregator,
    DimTrafficSourceAggregator,
    ExportFactSalesAggregator,
    ExportFactVisibilityWishlistAggregator,
    FactBaselineAggregator,
    FactEventDayAggregator,
    FactEventsAggregator,
    FactRetailerTagsAggregator,
    FactSalesAggregator,
    FactSalesCumulativeAggregator,
    FactSalesIndicatorAggregator,
    FactVisibilityAggregator,
    FactWishlistActionsAggregator,
    FactWishlistCohortsAggregator,
)
from saas_gold.config import Config
from saas_gold.exceptions import InvalidMessage
from saas_gold.writers.chained_writer import ChainedWriter

log = logging.getLogger(__name__)


class Params(BaseModel):
    studio_id: int


def validate_message(params) -> Params:
    log.info(f"Validating message: {params}")
    try:
        return Params.validate(params)
    except ValidationError:
        log.exception("Received invalid message %s", str(params))
        raise InvalidMessage(traceback.format_exc())


aggregators: list[type[BaseAggregator]] = [
    DimPortalsAggregator,
    DimSkuAggregator,
    DimProductsAggregator,
    DimStudioAggregator,
    DimTrafficSourceAggregator,
    ExportFactSalesAggregator,
    ExportFactVisibilityWishlistAggregator,
    FactSalesAggregator,
    FactSalesCumulativeAggregator,
    FactSalesIndicatorAggregator,
    FactVisibilityAggregator,
    FactWishlistActionsAggregator,
    FactWishlistCohortsAggregator,
    FactEventDayAggregator,
    FactEventsAggregator,
    FactBaselineAggregator,
    FactRetailerTagsAggregator,
]

prod_export_studios = {
    9: ["csv"],
    253: ["csv"],
    258: ["csv"],
    275: ["csv"],
    551: ["csv"],
    611: ["csv"],
    703: ["csv"],
    705: ["csv"],
    1007: ["csv"],
    1024: ["csv"],
    1064: ["csv"],
    1144: ["csv"],
    1267: ["csv"],
    1480: ["csv"],
    10121: ["csv"],
    10720: ["csv"],
    10913: ["csv"],
    10940: ["csv"],
    10942: ["csv"],
    10943: ["csv"],
    10944: ["csv"],
    10946: ["csv"],
    11062: ["parquet"],
    11125: ["csv"],
}
dev_export_studios = {1: ["csv", "parquet"]}


def _create_export_writers(params: Params, config: Config) -> list:
    export_studios = prod_export_studios if config.env == "prod" else dev_export_studios
    export_formats = export_studios.get(params.studio_id, [])

    csv_config = config.direct_data_access_cfg
    parquet_config = config.direct_data_access_cfg.model_copy(
        update={"file_extension": Extension.PARQUET}
    )

    export_writers = []

    if "csv" in export_formats:
        export_writers.append(CustomPartitionsWriter.get_writer(csv_config))

    if "parquet" in export_formats:
        export_writers.append(CustomPartitionsWriter.get_writer(parquet_config))

    return export_writers


def run(params: Params, config: Config):
    pbi_result_writer = CustomPartitionsWriter.get_writer(config.pbi_output_cfg)

    chained_writer = ChainedWriter(
        primary_writer=pbi_result_writer,
        secondary_writers=_create_export_writers(params, config),
    )

    result_reader = CustomPartitionReader.get_reader(config.input_cfg)

    creation_datetime = datetime.datetime.now(datetime.UTC)

    process_aggregators(
        aggregators=aggregators,
        reader=result_reader,
        writer=chained_writer,
        creation_datetime=creation_datetime,
        studio_id=StudioId(params.studio_id),
    )
