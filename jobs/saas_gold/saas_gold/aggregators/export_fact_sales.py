from pathlib import Path

import polars as pl

from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.tables import (
    LegacyDimPortalsTable,
    LegacyDimSKUsTable,
    LegacyFactEventDayTable,
    LegacyFactSalesTable,
    ObservationDiscountsTable,
    TableWithGoldPartitions,
)
from data_sdk.validators.schemas import NoCheckSchema
from saas_gold.aggregators.fact_event_day import (
    combine_raw_data,
    generate_fact_event_day_sku,
)
from saas_gold.aggregators.fact_sales_indicator import generate_fact_sales_indicator_sku
from saas_gold.aggregators.tables import SaaSGoldTable
from saas_gold.segmentators.chained_mo_y_q_m import ChainedSegmentatorMoYQM
from saas_gold.utils.steam_fee_sale_multipliers import apply_net_sales_multipliers


class ExportFactSalesTable(TableWithGoldPartitions):
    table_name = SaaSGoldTable.EXPORT_FACT_SALES
    model = NoCheckSchema


class ExportFactSalesAggregator(BaseAggregator):
    table_cls = ExportFactSalesTable
    segmentator = ChainedSegmentatorMoYQM(date_column="date")

    saas_gold_dir = Path(__file__).parent.parent
    dim_country_path = saas_gold_dir.joinpath("shared_tables", "dim_country.parquet")

    def __init__(
        self,
        legacy_fact_sales: LegacyFactSalesTable,
        legacy_dim_skus: LegacyDimSKUsTable,
        legacy_dim_portals: LegacyDimPortalsTable,
        legacy_fact_event_day: LegacyFactEventDayTable,
        observation_discounts: ObservationDiscountsTable,
    ) -> None:
        self._legacy_fact_sales = legacy_fact_sales
        self._legacy_dim_skus = legacy_dim_skus
        self._legacy_dim_portals = legacy_dim_portals
        self._legacy_fact_event_day = legacy_fact_event_day
        self._observation_discounts = observation_discounts

    def _aggregate(self) -> pl.DataFrame:
        if self._legacy_fact_sales.df.is_empty():
            return pl.DataFrame()

        combined_raw_data = combine_raw_data(
            self._observation_discounts.df,
            self._legacy_fact_event_day.df,
        )

        fact_event_day_sku = generate_fact_event_day_sku(
            combined_raw_data,
            apply_net_sales_multipliers(
                self._legacy_fact_sales.df,
                pl.read_parquet("saas_gold/dictionaries/steam_fee_skus_table.parquet"),
            ),
            self._legacy_dim_skus.df,
        ).lazy()
        fact_sales_indicator = generate_fact_sales_indicator_sku(
            fact_event_day_sku,
            apply_net_sales_multipliers(
                self._legacy_fact_sales.df,
                pl.read_parquet("saas_gold/dictionaries/steam_fee_skus_table.parquet"),
            ).lazy(),
        ).lazy()
        dim_sku = self._legacy_dim_skus.df.lazy()
        dim_portals = self._legacy_dim_portals.df.lazy()
        dim_country = pl.scan_parquet(self.dim_country_path)

        direct_categories = [
            "Free & Sale",
            "Free & Return",
            "Sale",
            "Invalid Sale",
            "Return",
            "Invalid Return",
        ]

        fact_sales = (
            apply_net_sales_multipliers(
                self._legacy_fact_sales.df,
                pl.read_parquet("saas_gold/dictionaries/steam_fee_skus_table.parquet"),
            )
            .with_columns(
                pl.when(pl.col("category").is_in(direct_categories))
                .then(pl.col("units_sold"))
                .otherwise(0)
                .alias("units_sold_directly"),
                (
                    pl.when(pl.col("category").str.starts_with("Non-billable"))
                    .then(pl.col("units_sold"))
                    .otherwise(0)
                    + pl.col("free_units")
                ).alias("units_freely_distributed"),
                pl.when(pl.col("category").str.starts_with("Retail"))
                .then(pl.col("units_sold"))
                .otherwise(0)
                .alias("units_sold_in_retail"),
            )
            .lazy()
        )

        fact_sales = fact_sales.with_columns(
            (pl.col("gross_sales") + pl.col("gross_returned")).alias("gross_revenue"),
            (
                pl.col("units_sold_directly")
                + pl.col("units_returned")
                + pl.col("units_sold_in_retail")
                + pl.col("units_freely_distributed")
            ).alias("all_units"),
        )

        fact_sales = fact_sales.group_by(
            [
                "date",
                "sku_studio",
                "portal_platform_region_id",
                "country_code",
                "bundle_name",
            ]
        ).agg(
            pl.col("gross_revenue").sum(),
            pl.col("gross_returned").sum(),
            pl.col("gross_sales").sum(),
            pl.col("net_sales_approx").sum(),
            pl.col("all_units").sum(),
            pl.col("units_sold_directly").sum(),
            pl.col("units_returned").sum(),
            pl.col("units_freely_distributed").sum(),
            pl.col("units_sold_in_retail").sum(),
            pl.col("base_price_local").mean(),
            pl.col("calculated_base_price_usd").mean(),
            pl.col("calculated_base_price_local_v2").mean(),
            pl.col("calculated_base_price_usd_v2").mean(),
        )

        fact_sales = fact_sales.join(
            dim_sku.select(
                "sku_studio", "base_sku_id", "human_name", "product_id", "product_name"
            ),
            on="sku_studio",
            how="left",
            coalesce=True,
        )

        fact_sales = fact_sales.filter(
            (pl.col("product_name") != "__IGNORE") | (pl.col("product_name").is_null())
        )

        fact_sales = fact_sales.join(
            dim_portals.select("portal_platform_region_id", "portal", "store"),
            on="portal_platform_region_id",
            how="left",
            coalesce=True,
        )
        fact_sales = fact_sales.join(
            fact_sales_indicator.select(
                "date", "unique_sku_id", "promo_regular"
            ).unique(["date", "unique_sku_id"]),
            left_on=["date", "sku_studio"],
            right_on=["date", "unique_sku_id"],
            how="left",
            coalesce=True,
        )

        fact_sales = fact_sales.join(
            dim_country.select("country_code", "region", "currency_code"),
            on="country_code",
            how="left",
            coalesce=True,
        )

        fact_sales = (
            fact_sales.select(
                "date",
                "sku_studio",
                "base_sku_id",
                "human_name",
                "product_id",
                "product_name",
                "portal_platform_region_id",
                "portal",
                "store",
                "bundle_name",
                "country_code",
                "region",
                "currency_code",
                "gross_revenue",
                "gross_returned",
                "gross_sales",
                "net_sales_approx",
                "all_units",
                "units_sold_directly",
                "units_returned",
                "units_freely_distributed",
                "units_sold_in_retail",
                "promo_regular",
                "base_price_local",
                "calculated_base_price_usd",
                "calculated_base_price_local_v2",
                "calculated_base_price_usd_v2",
            )
            .sort("date", "product_id")
            .rename({"sku_studio": "unique_sku_id"})
        )
        return fact_sales.collect()
