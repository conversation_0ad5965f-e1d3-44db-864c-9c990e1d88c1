import polars as pl

from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.tables import (
    LegacyDimSKUsTable,
    LegacyFactVisibilityTable,
    TableWithGoldPartitions,
)
from data_sdk.validators.schemas import NoCheckSchema
from saas_gold.aggregators.tables import SaaSGoldTable
from saas_gold.segmentators.chained_mo_y_q_m import ChainedSegmentatorMoYQM


class FactVisibilityTable(TableWithGoldPartitions):
    table_name = SaaSGoldTable.FACT_VISIBILITY
    model = NoCheckSchema


class FactVisibilityAggregator(BaseAggregator):
    table_cls = FactVisibilityTable
    segmentator = ChainedSegmentatorMoYQM(date_column="date")

    def __init__(
        self,
        legacy_fact_visibility: LegacyFactVisibilityTable,
        legacy_dim_sku: LegacyDimSKUsTable,
    ) -> None:
        self._legacy_fact_visibility = legacy_fact_visibility
        self._legacy_dim_sku = legacy_dim_sku

    def _aggregate(self) -> pl.DataFrame:
        return generate_fact_visibility(
            self._legacy_fact_visibility.df, self._legacy_dim_sku.df
        )


def generate_fact_visibility(
    raw_fact_visibility: pl.DataFrame,
    raw_dim_sku: pl.DataFrame,
) -> pl.DataFrame:
    if raw_fact_visibility.is_empty():
        return pl.DataFrame()

    raw_fact_visibility_lf = raw_fact_visibility.lazy()
    raw_dim_sku_lf = raw_dim_sku.lazy()

    fact_visibility_lf = raw_fact_visibility_lf.join(
        raw_dim_sku_lf.select("sku_studio", "product_name"),
        on="sku_studio",
        how="left",
        coalesce=True,
    )

    fact_visibility_lf = fact_visibility_lf.filter(
        (pl.col("product_name") != "__IGNORE") | (pl.col("product_name").is_null())
    )

    fact_visibility_navigation = (
        fact_visibility_lf.group_by(
            [
                "date",
                "product_id",
                "portal_platform_region_id",
                "hash_traffic_source",
                "navigation",
            ]
        )
        .agg(
            visits_count=pl.sum("visits"),
        )
        .with_columns(
            rank=pl.col("visits_count")
            .rank(method="ordinal", descending=True)
            .over(
                "date",
                "product_id",
                "portal_platform_region_id",
                "hash_traffic_source",
            )
        )
        .filter(pl.col("rank") == 1)
        .drop("rank", "visits_count")
    )

    fact_visibility_aggregation_lf = fact_visibility_lf.group_by(
        ["date", "product_id", "portal_platform_region_id", "hash_traffic_source"]
    ).agg(
        pl.first("studio_id"),
        pl.sum("visits"),
        pl.sum("owner_visits"),
        pl.sum("impressions"),
        pl.sum("owner_impressions"),
    )

    fact_visibility_lf = fact_visibility_aggregation_lf.join(
        fact_visibility_navigation,
        on=[
            "date",
            "product_id",
            "portal_platform_region_id",
            "hash_traffic_source",
        ],
        how="left",
        coalesce=True,
    ).sort("date", "product_id")

    fact_visibility_lf = fact_visibility_lf.rename({"navigation": "direct_navigation"})

    return fact_visibility_lf.collect()
