import polars as pl

from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.tables import (
    LegacyDimSKUsTable,
    LegacyFactEventDayTable,
    LegacyFactSalesTable,
    ObservationDiscountsTable,
    TableWithGoldPartitions,
)
from data_sdk.validators.schemas import NoCheckSchema
from saas_gold.aggregators.fact_event_day import (
    combine_raw_data,
    generate_fact_event_day,
)
from saas_gold.aggregators.tables import SaaSGoldTable
from saas_gold.segmentators.chained_mo_y_q_m import ChainedSegmentatorMoYQM


class FactSalesIndicatorTable(TableWithGoldPartitions):
    table_name = SaaSGoldTable.FACT_SALES_INDICATOR
    model = NoCheckSchema


class FactSalesIndicatorAggregator(BaseAggregator):
    table_cls = FactSalesIndicatorTable
    segmentator = ChainedSegmentatorMoYQM(date_column="date")

    def __init__(
        self,
        legacy_fact_event_day: LegacyFactEventDayTable,
        observation_discounts: ObservationDiscountsTable,
        legacy_fact_sales: LegacyFactSalesTable,
        legacy_dim_sku: LegacyDimSKUsTable,
    ) -> None:
        self._legacy_fact_event_day = legacy_fact_event_day
        self._observation_discounts = observation_discounts
        self._legacy_fact_sales = legacy_fact_sales
        self._legacy_dim_sku = legacy_dim_sku

    def _aggregate(self) -> pl.DataFrame:
        if self._legacy_fact_sales.df.is_empty():
            return pl.DataFrame()

        combined_raw_data = combine_raw_data(
            self._observation_discounts.df,
            self._legacy_fact_event_day.df,
        )

        fact_event_day = generate_fact_event_day(
            combined_raw_data,
            self._legacy_fact_sales.df,
            self._legacy_dim_sku.df,
        ).lazy()

        fact_sales_indicator = generate_fact_sales_indicator(
            fact_event_day,
            self._legacy_fact_sales.df.lazy(),
            self._legacy_dim_sku.df.lazy(),
        ).collect()

        return fact_sales_indicator


def generate_fact_sales_indicator(
    generated_fact_event_day: pl.LazyFrame,
    raw_fact_sales: pl.LazyFrame,
    raw_dim_sku: pl.LazyFrame,
) -> pl.LazyFrame:
    fact_sales = raw_fact_sales.rename({"sku_studio": "unique_sku_id"})
    dim_sku = raw_dim_sku.rename({"sku_studio": "unique_sku_id"})
    fact_sales = fact_sales.join(
        dim_sku.select(
            [
                "unique_sku_id",
                "product_id",
                "product_name",
            ]
        ),
        on="unique_sku_id",
        how="left",
        coalesce=True,
    )

    fact_sales = fact_sales.filter(
        (pl.col("product_name") != "__IGNORE") | (pl.col("product_name").is_null())
    )

    fact_sales_indicator = (
        fact_sales.group_by(
            [
                "date",
                "product_id",
                "portal_platform_region_id",
            ]
        )
        .agg(
            pl.first("studio_id"),
        )
        .sort("date")
    )

    fact_sales_indicator = fact_sales_indicator.join(
        generated_fact_event_day.select(
            [
                "date",
                "product_id",
                "portal_platform_region_id",
                "event_id",
            ]
        ),
        on=["date", "product_id", "portal_platform_region_id"],
        how="left",
        coalesce=True,
    )

    fact_sales_indicator = fact_sales_indicator.with_columns(
        [
            pl.when(pl.col("event_id").is_not_null())
            .then(pl.lit("promo"))
            .otherwise(pl.lit("regular"))
            .alias("promo_regular"),
        ]
    ).select(
        [
            "date",
            "studio_id",
            "product_id",
            "portal_platform_region_id",
            "promo_regular",
        ]
    )

    fact_sales_indicator = fact_sales_indicator.sort("date", "product_id")

    return fact_sales_indicator


def generate_fact_sales_indicator_sku(
    generated_fact_event_day: pl.LazyFrame,
    raw_fact_sales: pl.LazyFrame,
) -> pl.LazyFrame:
    fact_sales = raw_fact_sales.rename({"sku_studio": "unique_sku_id"})
    fact_sales_indicator = (
        fact_sales.group_by(
            [
                "date",
                "unique_sku_id",
                "portal_platform_region_id",
            ]
        )
        .agg(
            pl.first("studio_id"),
        )
        .sort("date")
    )

    fact_sales_indicator = fact_sales_indicator.join(
        generated_fact_event_day.select(
            [
                "date",
                "portal_platform_region_id",
                "event_id",
                "unique_sku_id",
            ]
        ),
        on=["date", "unique_sku_id", "portal_platform_region_id"],
        how="left",
        coalesce=True,
    )

    fact_sales_indicator = fact_sales_indicator.with_columns(
        [
            pl.when(pl.col("event_id").is_not_null())
            .then(pl.lit("promo"))
            .otherwise(pl.lit("regular"))
            .alias("promo_regular"),
        ]
    ).select(
        [
            "date",
            "studio_id",
            "unique_sku_id",
            "portal_platform_region_id",
            "promo_regular",
        ]
    )

    fact_sales_indicator = fact_sales_indicator.sort("date", "unique_sku_id")

    return fact_sales_indicator
