import polars as pl

from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.tables import (
    LegacyDimSKUsTable,
    LegacyFactWishlistCohortsTable,
    TableWithGoldPartitions,
)
from data_sdk.validators.schemas import NoCheckSchema
from saas_gold.aggregators.tables import SaaSGoldTable
from saas_gold.segmentators.chained_mo_y_q_m import ChainedSegmentatorMoYQM


class FactWishlistCohortsTable(TableWithGoldPartitions):
    table_name = SaaSGoldTable.FACT_WISHLIST_COHORTS
    model = NoCheckSchema


class FactWishlistCohortsAggregator(BaseAggregator):
    table_cls = FactWishlistCohortsTable
    segmentator = ChainedSegmentatorMoYQM(date_column="date")

    def __init__(
        self,
        legacy_fact_wishlist_cohorts: LegacyFactWishlistCohortsTable,
        legacy_dim_sku: LegacyDimSKUsTable,
    ) -> None:
        self._legacy_fact_wishlist_cohorts = legacy_fact_wishlist_cohorts
        self._legacy_dim_sku = legacy_dim_sku

    def _aggregate(self) -> pl.DataFrame:
        if self._legacy_fact_wishlist_cohorts.df.is_empty():
            return pl.DataFrame()

        fact_wishlist_cohorts_lf = self._legacy_fact_wishlist_cohorts.df.lazy()
        dim_sku_lf = self._legacy_dim_sku.df.lazy()

        fact_wishlist_cohorts_lf = fact_wishlist_cohorts_lf.join(
            dim_sku_lf.select("sku_studio", "product_name"),
            on="sku_studio",
            how="left",
            coalesce=True,
        )

        fact_wishlist_cohorts_lf = fact_wishlist_cohorts_lf.filter(
            (pl.col("product_name") != "__IGNORE") | (pl.col("product_name").is_null())
        )

        fact_wishlist_cohorts_lf = (
            fact_wishlist_cohorts_lf.group_by(
                [
                    "date",
                    "product_id",
                    "portal_platform_region_id",
                    "month_cohort",
                ]
            )
            .agg(
                pl.first("studio_id"),
                pl.sum("total_conversions"),
                pl.sum("purchases_and_activations"),
                pl.sum("gifts"),
            )
            .sort("date", "product_id")
        )

        return fact_wishlist_cohorts_lf.collect()
