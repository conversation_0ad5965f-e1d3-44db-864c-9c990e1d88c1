import polars as pl

from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.tables import LegacyDimStudioTable, TableWithGoldPartitions
from data_sdk.validators.schemas import NoCheckSchema
from saas_gold.aggregators.tables import SaaSGoldTable


class DimStudioTable(TableWithGoldPartitions):
    table_name = SaaSGoldTable.DIM_STUDIO
    model = NoCheckSchema


class DimStudioAggregator(BaseAggregator):
    table_cls = DimStudioTable

    def __init__(self, legacy_dim_studio: LegacyDimStudioTable) -> None:
        self._legacy_dim_studio = legacy_dim_studio

    def _aggregate(self) -> pl.DataFrame:
        dim_studio = self._legacy_dim_studio.df.select(
            [
                "studio_id",
                "company_name",
            ]
        )

        return dim_studio
