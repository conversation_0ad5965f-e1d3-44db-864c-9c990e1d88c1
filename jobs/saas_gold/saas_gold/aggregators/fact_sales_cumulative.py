import polars as pl

from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.tables import (
    LegacyDimSKUsTable,
    LegacyFactEventDayTable,
    LegacyFactSalesTable,
    ObservationDiscountsTable,
    TableWithGoldPartitions,
)
from data_sdk.validators.schemas import NoCheckSchema
from saas_gold.aggregators.fact_event_day import (
    combine_raw_data,
    generate_fact_event_day,
)
from saas_gold.aggregators.fact_sales import generate_fact_sales
from saas_gold.aggregators.fact_sales_indicator import generate_fact_sales_indicator
from saas_gold.aggregators.tables import SaaSGoldTable
from saas_gold.segmentators.chained_mo_y_q_m import ChainedSegmentatorMoYQM


class FactSalesCumulativeTable(TableWithGoldPartitions):
    table_name = SaaSGoldTable.FACT_SALES_CUMULATIVE
    model = NoCheckSchema


class FactSalesCumulativeAggregator(BaseAggregator):
    table_cls = FactSalesCumulativeTable
    segmentator = ChainedSegmentatorMoYQM(date_column="date")

    def __init__(
        self,
        legacy_fact_sales: LegacyFactSalesTable,
        legacy_fact_event_day: LegacyFactEventDayTable,
        observation_discounts: ObservationDiscountsTable,
        legacy_dim_sku: LegacyDimSKUsTable,
    ) -> None:
        self._legacy_fact_sales = legacy_fact_sales
        self._legacy_fact_event_day = legacy_fact_event_day
        self._observation_discounts = observation_discounts
        self._legacy_dim_sku = legacy_dim_sku

    def _aggregate(self) -> pl.DataFrame:
        if self._legacy_fact_sales.df.is_empty():
            return pl.DataFrame()

        fact_sales_lf = generate_fact_sales(
            self._legacy_fact_sales.df, self._legacy_dim_sku.df
        ).lazy()
        observations = [
            column_name
            for column_name, data_type in fact_sales_lf.select(
                pl.all().exclude(["studio_id", "portal_platform_region_id"])
            ).schema.items()
            if data_type.is_numeric()
        ]

        fact_sales_lf = fact_sales_lf.group_by(
            ["date", "product_id", "portal_platform_region_id"]
        ).agg(pl.sum(observation) for observation in observations)

        fact_sales = fact_sales_lf.collect()
        max_date = fact_sales["date"].max()
        date_ranges = fact_sales.group_by("product_id").agg(
            min_date=pl.col("date").min(),
            max_date=pl.lit(max_date),
        )

        product_dates: list[pl.DataFrame] = []

        for row in date_ranges.iter_rows(named=True):
            product_id = row["product_id"]
            min_date = row["min_date"]
            max_date = row["max_date"]
            date_range = pl.date_range(
                pl.lit(min_date).cast(pl.Date),
                pl.lit(max_date).cast(pl.Date),
                interval="1d",
                eager=True,
            )

            product_date = pl.DataFrame(
                {
                    "date": date_range,
                    "product_id": [product_id] * len(date_range),
                }
            )

            product_dates.append(product_date)

        product_dates_lf = pl.concat(product_dates).lazy()

        product_dates_lf = product_dates_lf.join(
            fact_sales_lf.select(["product_id", "portal_platform_region_id"]).unique(),
            on="product_id",
            how="left",
            coalesce=True,
        )

        fact_sales_cumulative_lf = product_dates_lf.join(
            fact_sales_lf.select(pl.all().exclude(["portal_platform_region_id"])),
            on=["date", "product_id"],
            how="left",
            coalesce=True,
        )

        fact_sales_cumulative_lf = fact_sales_cumulative_lf.fill_null(0)

        fact_sales_cumulative_lf = (
            (
                fact_sales_cumulative_lf.sort(["date", "product_id"]).with_columns(
                    [
                        pl.col(observation)
                        .cum_sum()
                        .over("product_id")
                        .alias(f"{observation}_cumulative")
                        for observation in observations
                    ]
                )
            )
            .select(
                ["date", "product_id", "portal_platform_region_id"]
                + [f"{observation}_cumulative" for observation in observations]
            )
            .with_columns(
                gross_sales_incl_returns_cumulative=(
                    pl.col("gross_sales_cumulative")
                    + pl.col("gross_returned_cumulative")
                ),
            )
        )

        combined_raw_data = combine_raw_data(
            self._observation_discounts.df,
            self._legacy_fact_event_day.df,
        )

        fact_event_day_lf = generate_fact_event_day(
            combined_raw_data,
            self._legacy_fact_sales.df,
            self._legacy_dim_sku.df,
        ).lazy()

        fact_sales_indicator_lf = generate_fact_sales_indicator(
            fact_event_day_lf,
            self._legacy_fact_sales.df.lazy(),
            self._legacy_dim_sku.df.lazy(),
        ).lazy()

        fact_sales_cumulative = fact_sales_cumulative_lf.join(
            fact_sales_indicator_lf.select(
                "date",
                "product_id",
                "promo_regular",
            ),
            on=["date", "product_id"],
            how="left",
            coalesce=True,
        ).collect()

        fact_sales_cumulative = fact_sales_cumulative.with_columns(
            gross_sales_incl_returns_promo_cumulative=pl.when(
                fact_sales_cumulative["promo_regular"] == "promo"
            )
            .then(fact_sales_cumulative["gross_sales_incl_returns_cumulative"])
            .otherwise(None)
        ).select(pl.all().exclude(["promo_regular"]))

        fact_sales_cumulative = fact_sales_cumulative.with_columns(
            pl.col("gross_sales_incl_returns_promo_cumulative")
            .fill_null(strategy="forward")
            .over("product_id")
        ).fill_null(0)

        fact_sales_cumulative = fact_sales_cumulative.sort("date", "product_id")

        return fact_sales_cumulative
