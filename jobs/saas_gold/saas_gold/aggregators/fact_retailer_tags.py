import polars as pl

from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.tables import (
    LegacyDimSKUsTable,
    LegacyFactSalesTable,
    TableWithGoldPartitions,
)
from data_sdk.validators.schemas import NoCheckSchema
from saas_gold.aggregators.tables import SaaSGoldTable
from saas_gold.segmentators.chained_mo_y_q_m import ChainedSegmentatorMoYQM


class FactRetailerTagsTable(TableWithGoldPartitions):
    table_name = SaaSGoldTable.FACT_RETAILER_TAGS
    model = NoCheckSchema


class FactRetailerTagsAggregator(BaseAggregator):
    table_cls = FactRetailerTagsTable
    segmentator = ChainedSegmentatorMoYQM(date_column="date")

    def __init__(
        self,
        legacy_fact_sales: LegacyFactSalesTable,
        legacy_dim_sku: LegacyDimSKUsTable,
    ) -> None:
        self._legacy_fact_sales = legacy_fact_sales
        self._legacy_dim_sku = legacy_dim_sku

    def _aggregate(self) -> pl.DataFrame:
        fact_sales = self._legacy_fact_sales.df
        dim_sku = self._legacy_dim_sku.df

        if fact_sales.is_empty():
            return pl.DataFrame()

        direct_categories = [
            "Free & Sale",
            "Free & Return",
            "Sale",
            "Invalid Sale",
            "Return",
            "Invalid Return",
        ]

        fact_sales = fact_sales.join(
            dim_sku.select("sku_studio", "product_name"),
            on="sku_studio",
            how="inner",
        )

        fact_sales = fact_sales.filter(
            (pl.col("product_name") != "__IGNORE") | (pl.col("product_name").is_null())
        )

        fact_sales_lf = fact_sales.with_columns(
            pl.when(pl.col("category").is_in(direct_categories))
            .then(pl.col("units_sold"))
            .otherwise(0)
            .alias("units_sold_directly"),
            pl.when(pl.col("category").str.starts_with("Retail"))
            .then(pl.col("units_sold"))
            .otherwise(0)
            .alias("units_sold_in_retail"),
        ).lazy()

        fact_retailer_tags_lf = fact_sales_lf.group_by(
            "date",
            "product_id",
            "country_code",
            "portal_platform_region_id",
            "retailer_tag",
        ).agg(
            pl.first("studio_id"),
            pl.sum("units_sold_directly"),
            pl.sum("units_sold_in_retail"),
        )

        return fact_retailer_tags_lf.collect()
