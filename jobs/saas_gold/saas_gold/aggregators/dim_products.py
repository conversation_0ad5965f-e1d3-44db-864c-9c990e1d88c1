import polars as pl

from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.tables import LegacyDimSKUsTable, TableWithGoldPartitions
from data_sdk.validators.schemas import NoCheckSchema
from saas_gold.aggregators.tables import SaaSGoldTable


class DimProductsTable(TableWithGoldPartitions):
    table_name = SaaSGoldTable.DIM_PRODUCTS
    model = NoCheckSchema


class DimProductsAggregator(BaseAggregator):
    table_cls = DimProductsTable

    def __init__(self, legacy_dim_sku: LegacyDimSKUsTable) -> None:
        self._legacy_dim_sku = legacy_dim_sku

    def _aggregate(self) -> pl.DataFrame:
        if self._legacy_dim_sku.df.is_empty():
            return pl.DataFrame()

        dim_sku = self._legacy_dim_sku.df.lazy()
        dim_sku = dim_sku.select(
            [
                "product_id",
                "product_name",
                "portal_platform_region_id",
                "studio_id",
                "gso",
            ]
        )
        dim_sku = dim_sku.filter(
            (pl.col("product_name") != "__IGNORE") | (pl.col("product_name").is_null())
        )
        dim_products = (
            dim_sku.group_by(["product_id", "studio_id", "portal_platform_region_id"])
            .agg(
                pl.first("product_name"),
                pl.min("gso"),
            )
            .sort("gso")
        )

        return dim_products.collect()
