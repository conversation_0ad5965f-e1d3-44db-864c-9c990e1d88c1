from polars import Date

from data_sdk.validators.fields import (
    BasicField,
    MediumStringField,
    NonNegativeNumberField,
    TinyStringField,
)
from data_sdk.validators.schemas import StrictBaseSchema


class FactBaselineSchema(StrictBaseSchema):
    date: Date = BasicField()
    product_id: str = MediumStringField()
    country_code: str = TinyStringField()
    portal_platform_region_id: int = NonNegativeNumberField()
    studio_id: int = NonNegativeNumberField()
    baseline_units_sold_directly: int = NonNegativeNumberField()
    baseline_units_sold_non_billable: int = NonNegativeNumberField()
    baseline_units_sold_retail: int = NonNegativeNumberField()
    baseline_units_returned: int = NonNegativeNumberField()
    baseline_gross_sales: float = NonNegativeNumberField()
    baseline_gross_returned: float = NonNegativeNumberField()
    baseline_free_units: int = NonNegativeNumberField()
    baseline_net_sales_approx: float = NonNegativeNumberField()
    uplift_units_sold_directly: int = NonNegativeNumberField()
    uplift_units_sold_non_billable: int = NonNegativeNumberField()
    uplift_units_sold_retail: int = NonNegativeNumberField()
    uplift_units_returned: int = NonNegativeNumberField()
    uplift_gross_sales: float = NonNegativeNumberField()
    uplift_gross_returned: float = NonNegativeNumberField()
    uplift_free_units: int = NonNegativeNumberField()
    uplift_net_sales_approx: float = NonNegativeNumberField()
