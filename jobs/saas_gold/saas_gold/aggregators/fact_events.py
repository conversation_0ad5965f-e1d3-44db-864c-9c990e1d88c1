import polars as pl

from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.tables import (
    LegacyDimSKUsTable,
    LegacyFactEventDayTable,
    LegacyFactSalesTable,
    LegacyFactVisibilityTable,
    ObservationDiscountsTable,
    ObservationSalesTable,
    SilverSKUsTable,
    TableWithGoldPartitions,
)
from data_sdk.validators.schemas import NoCheckSchema
from saas_gold.aggregators.fact_baseline import generate_fact_baseline
from saas_gold.aggregators.fact_event_day import (
    combine_raw_data,
    generate_fact_event_day,
)
from saas_gold.aggregators.fact_sales import generate_fact_sales
from saas_gold.aggregators.fact_visibility import generate_fact_visibility
from saas_gold.aggregators.tables import SaaSGoldTable
from saas_gold.segmentators.chained_mo_y_q_m import ChainedSegmentatorMoYQM


class FactEventsTable(TableWithGoldPartitions):
    table_name = SaaSGoldTable.FACT_EVENTS
    model = NoCheckSchema


class FactEventsAggregator(BaseAggregator):
    table_cls = FactEventsTable
    segmentator = ChainedSegmentatorMoYQM(date_column="date_ir")

    def __init__(
        self,
        legacy_fact_visibility: LegacyFactVisibilityTable,
        legacy_fact_event_day: LegacyFactEventDayTable,
        observation_discounts: ObservationDiscountsTable,
        observation_sales: ObservationSalesTable,
        legacy_fact_sales: LegacyFactSalesTable,
        legacy_dim_sku: LegacyDimSKUsTable,
        silver_skus: SilverSKUsTable,
    ) -> None:
        self._legacy_fact_visibility = legacy_fact_visibility
        self._legacy_fact_event_day = legacy_fact_event_day
        self._observation_discounts = observation_discounts
        self._observation_sales = observation_sales
        self._legacy_fact_sales = legacy_fact_sales
        self._legacy_dim_sku = legacy_dim_sku
        self._silver_skus = silver_skus

    def _aggregate(self) -> pl.DataFrame:
        if self._legacy_fact_event_day.df.is_empty():
            return pl.DataFrame()

        combined_raw_data = combine_raw_data(
            self._observation_discounts.df,
            self._legacy_fact_event_day.df,
        )

        if combined_raw_data.is_empty():
            combined_raw_data = pl.DataFrame(
                schema={"unique_sku_id": pl.String, "date": pl.Date}
            )
        promo_days = combined_raw_data.with_columns(
            promo=pl.lit(True).cast(pl.Boolean),
        ).lazy()

        fact_event_day = generate_fact_event_day(
            combined_raw_data,
            self._legacy_fact_sales.df,
            self._legacy_dim_sku.df,
        )

        fact_visibility = generate_fact_visibility(
            self._legacy_fact_visibility.df, self._legacy_dim_sku.df
        )

        fact_sales = generate_fact_sales(
            self._legacy_fact_sales.df, self._legacy_dim_sku.df
        )

        fact_baseline = generate_fact_baseline(
            self._observation_sales.df,
            promo_days,
            self._silver_skus.df.lazy(),
            self._legacy_dim_sku.df.lazy(),
        )

        return generate_fact_events(
            fact_event_day,
            fact_visibility,
            fact_sales,
            fact_baseline,
        )


def generate_fact_events(
    fact_event_day: pl.DataFrame,
    fact_visibility: pl.DataFrame,
    fact_sales: pl.DataFrame,
    fact_baseline: pl.DataFrame,
) -> pl.DataFrame:
    if fact_event_day.is_empty():
        return pl.DataFrame()

    if fact_visibility.is_empty():
        fact_events_lf = fact_event_day.lazy().with_columns(
            non_owner_visits=pl.lit(0),
            non_owner_impressions=pl.lit(0),
        )
    else:
        fact_visibility_lf = (
            fact_visibility.lazy()
            .group_by("date", "product_id")
            .agg(
                pl.sum("visits"),
                pl.sum("impressions"),
                pl.sum("owner_visits"),
                pl.sum("owner_impressions"),
            )
        )
        fact_visibility_lf = fact_visibility_lf.with_columns(
            non_owner_visits=pl.col("visits") - pl.col("owner_visits"),
            non_owner_impressions=pl.col("impressions") - pl.col("owner_impressions"),
        )

        fact_events_lf = (
            fact_event_day.lazy()
            .join(
                fact_visibility_lf.select(
                    "date", "product_id", "non_owner_visits", "non_owner_impressions"
                ),
                on=["date", "product_id"],
                how="left",
                coalesce=True,
            )
            .fill_null(0)
        )

    if fact_sales.is_empty():
        fact_events_lf = fact_events_lf.with_columns(
            units_sold_directly=pl.lit(0),
        )
    else:
        fact_sales_lf = (
            fact_sales.lazy()
            .group_by("date", "product_id")
            .agg(
                pl.sum("units_sold_directly"),
            )
        )

        fact_events_lf = fact_events_lf.join(
            fact_sales_lf,
            on=["date", "product_id"],
            how="left",
            coalesce=True,
        ).fill_null(0)

    if fact_baseline.is_empty():
        fact_events_lf = fact_events_lf.with_columns(
            baseline_gross_sales=pl.lit(0),
            uplift_gross_sales=pl.lit(0),
        )
    else:
        fact_baseline_lf = (
            fact_baseline.lazy()
            .group_by("date", "product_id")
            .agg(
                pl.sum("baseline_gross_sales"),
                pl.sum("uplift_gross_sales"),
            )
        )

        fact_events_lf = fact_events_lf.join(
            fact_baseline_lf,
            on=["date", "product_id"],
            how="left",
            coalesce=True,
        ).fill_null(0)

    fact_events_lf = fact_events_lf.with_columns(
        event_id=(pl.col("product_id") + ":" + pl.col("date_from"))
    )

    fact_events_lf = fact_events_lf.filter(pl.col("event_id").is_not_null())

    fact_events_lf = fact_events_lf.group_by(
        "event_id",
    ).agg(
        pl.first("date_from"),
        pl.first("date_to"),
        pl.first("product_id"),
        pl.first("portal_platform_region_id"),
        pl.first("studio_id"),
        pl.mean("discount").round(2),
        pl.first("dates_short"),
        pl.first("promo_length"),
        pl.first("event_status"),
        pl.first("days_since_previous_discount"),
        pl.first("event_name"),
        pl.first("event_name_2"),
        pl.first("year"),
        pl.sum("non_owner_visits"),
        pl.sum("non_owner_impressions"),
        pl.sum("units_sold_directly"),
        pl.sum("baseline_gross_sales"),
        pl.sum("uplift_gross_sales"),
    )

    fact_events_lf = fact_events_lf.with_columns(
        uplift_score=(
            pl.col("baseline_gross_sales") + pl.col("uplift_gross_sales")
        ).truediv(pl.col("baseline_gross_sales")),
        date_ir=pl.col("date_from"),
    ).fill_nan(None)

    fact_events_lf = fact_events_lf.with_columns(
        ctr=(
            pl.col("non_owner_visits").truediv(pl.col("non_owner_impressions"))
        ).fill_nan(None),
        conversion_rate=(
            pl.col("units_sold_directly").truediv(pl.col("non_owner_visits"))
        ).fill_nan(None),
    )

    fact_events = _inf_to_null(
        fact_events_lf.collect(),
        ["ctr", "conversion_rate", "uplift_score", "conversion_rate"],
    )

    columns_for_normalization = [
        "ctr",
        "conversion_rate",
        "non_owner_visits",
        "non_owner_impressions",
        "uplift_score",
    ]

    for column in columns_for_normalization:
        fact_events = _add_score(fact_events, column)

    fact_events = fact_events.select(
        "event_id",
        "date_ir",
        "date_to",
        "product_id",
        "portal_platform_region_id",
        "studio_id",
        "discount",
        "dates_short",
        "promo_length",
        "event_status",
        "event_name",
        "event_name_2",
        "year",
        "ctr",
        "conversion_rate",
        "non_owner_visits",
        "non_owner_impressions",
        "uplift_score",
        "ctr_normalized",
        "conversion_rate_normalized",
        "non_owner_visits_normalized",
        "non_owner_impressions_normalized",
        "uplift_score_normalized",
    ).fill_nan(None)

    return fact_events


def _inf_to_null(df: pl.DataFrame, columns: list[str]) -> pl.DataFrame:
    for column in columns:
        pl.when(pl.col(column).is_infinite()).then(pl.lit(None)).otherwise(
            pl.col(column)
        ).alias(column)
    return df


def _add_score(df: pl.DataFrame, column: str) -> pl.DataFrame:
    df = df.with_columns(
        pl.col(column).quantile(0.25).over("product_id").alias("q1"),
        pl.col(column).quantile(0.75).over("product_id").alias("q3"),
    )

    df = df.with_columns(
        iqr=pl.col("q3") - pl.col("q1"),
    )

    df = df.with_columns(
        lower_bound=pl.col("q1") - 1.5 * pl.col("iqr"),
        upper_bound=pl.col("q3") + 1.5 * pl.col("iqr"),
    )

    # flatten outliers above the upper bound
    df = df.with_columns(
        pl.when(pl.col(column) > pl.col("upper_bound"))
        .then(pl.col("upper_bound"))
        .otherwise(pl.col(column))
        .alias(f"{column}_flattened"),
    )

    # min and max for normalization
    df = df.with_columns(
        min=pl.col(f"{column}_flattened").min().over("product_id"),
        max=pl.col(f"{column}_flattened").max().over("product_id"),
    )

    # normalize
    df = df.with_columns(
        [
            (
                (
                    (pl.col(f"{column}_flattened") - pl.col("min"))
                    / (pl.col("max") - pl.col("min"))
                )
                * 4
                + 1
            )
            .round(1)
            .alias(f"{column}_normalized")
        ]
    )

    return df.select(
        pl.all().exclude(
            f"{column}_flattened",
            "q1",
            "q3",
            "iqr",
            "lower_bound",
            "upper_bound",
            "min",
            "max",
        )
    )
