import polars as pl

from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.tables import (
    LegacyDimSKUsTable,
    LegacyFactSalesTable,
    TableWithGoldPartitions,
)
from data_sdk.validators.schemas import NoCheckSchema
from saas_gold.aggregators.tables import SaaSGoldTable


class DimSkuTable(TableWithGoldPartitions):
    table_name = SaaSGoldTable.DIM_SKU
    model = NoCheckSchema


class DimSkuAggregator(BaseAggregator):
    table_cls = DimSkuTable

    def __init__(
        self,
        legacy_dim_sku: LegacyDimSKUsTable,
        legacy_fact_sales: LegacyFactSalesTable,
    ) -> None:
        self._legacy_dim_sku = legacy_dim_sku
        self._legacy_fact_sales = legacy_fact_sales

    def _aggregate(self) -> pl.DataFrame:
        if self._legacy_dim_sku.df.is_empty():
            return pl.DataFrame()

        dim_sku: pl.LazyFrame = self._legacy_dim_sku.df.lazy()
        dim_sku = dim_sku.filter(
            (pl.col("product_name") != "__IGNORE") | (pl.col("product_name").is_null())
        )
        fact_sales: pl.DataFrame = self._legacy_fact_sales.df

        dim_sku = dim_sku.select(
            [
                "sku_studio",
                "base_sku_id",
                "portal_platform_region",
                "human_name",
                "product_id",
                "sku_type",
                "gso",
            ]
        )
        dim_sku = dim_sku.rename({"sku_studio": "unique_sku_id"})
        if fact_sales.is_empty():
            dim_sku = dim_sku.with_columns(release_date=pl.lit(None).cast(pl.Date))
        else:
            fact_sales_lf: pl.LazyFrame = (
                fact_sales.group_by("sku_studio")
                .agg(
                    release_date=pl.min("date"),
                )
                .lazy()
            )
            # Cast type in case of empty sales and skus from store
            fact_sales_lf = fact_sales_lf.select(
                pl.col("sku_studio").cast(pl.String).alias("unique_sku_id"),
                pl.col("release_date"),
            )
            dim_sku = dim_sku.join(
                fact_sales_lf,
                on="unique_sku_id",
                how="left",
                coalesce=True,
            )

        return dim_sku.collect()
