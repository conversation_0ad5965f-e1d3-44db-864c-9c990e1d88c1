import polars as pl

from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.tables import (
    ExternalReportsTable,
    LegacyDimPortalsTable,
    SilverReportsTable,
    TableWithGoldPartitions,
)
from data_sdk.validators.schemas import NoCheckSchema
from saas_gold.aggregators.tables import SaaSGoldTable


class GoldDimPortalsTable(TableWithGoldPartitions):
    table_name = SaaSGoldTable.DIM_PORTALS
    model = NoCheckSchema


class DimPortalsAggregator(BaseAggregator):
    table_cls = GoldDimPortalsTable

    def __init__(
        self,
        legacy_dim_portals: LegacyDimPortalsTable,
        external_reports: ExternalReportsTable,
        silver_reports: SilverReportsTable,
    ) -> None:
        self._legacy_dim_portals = legacy_dim_portals
        self._external_reports = external_reports
        self._silver_reports = silver_reports

    def _aggregate(self) -> pl.DataFrame:
        if self._legacy_dim_portals.df.is_empty():
            return pl.DataFrame()

        dim_portals = self._legacy_dim_portals.df.select(
            [
                "portal",
                "platform",
                "region",
                "store",
                "abbreviated_name",
                "portal_platform_region",
                "portal_platform_region_id",
                "pso",
            ]
        )

        if self._external_reports.df.is_empty():
            dim_portals = dim_portals.with_columns(
                latest_date=pl.lit(None).cast(pl.Date),
            )
        else:
            external_reports = self._external_reports.df.with_columns(
                portal_lower=pl.col("portal").str.to_lowercase(),
            )
            if not self._silver_reports.df.is_empty():
                external_reports = external_reports.select(pl.exclude("state"))
                external_reports = external_reports.join(
                    self._silver_reports.df.select("report_id", "state"),
                    on="report_id",
                    how="left",
                    coalesce=True,
                )

            latest_days = (
                external_reports.with_columns(
                    portal_lower=pl.col("portal").str.to_lowercase(),
                )
                .filter(
                    (pl.col("observation_type") == "sales")
                    & (pl.col("state") == "CONVERTED")
                )
                .group_by("portal_lower")
                .agg(latest_date=pl.max("date_to"))
            )
            dim_portals = (
                dim_portals.with_columns(
                    portal_lower=pl.col("portal").str.to_lowercase(),
                )
                .join(
                    latest_days,
                    on="portal_lower",
                    how="left",
                    coalesce=True,
                )
                .drop("portal_lower")
            )

        return dim_portals
