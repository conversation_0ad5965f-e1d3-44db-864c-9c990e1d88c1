from enum import Enum


class SaaSGoldTable(str, Enum):
    DIM_PORTALS = "dim_portals"
    DIM_PRODUCTS = "dim_products"
    DIM_ACQUISITION_PROPERTIES = "dim_acquisition_properties"
    DIM_SKU = "dim_sku"
    FACT_SALES = "fact_sales"
    DIM_SOURCE_FILE = "dim_source_file"
    DIM_STUDIO = "dim_studio"
    DIM_TRAFFIC_SOURCE = "dim_traffic_source"
    EXPORT_FACT_SALES = "export_fact_sales"
    EXPORT_FACT_VISIBILITY_WISHLIST = "export_fact_visibility_wishlist"
    FACT_BASELINE = "fact_baseline"
    FACT_EVENT_DAY = "fact_event_day"
    FACT_EVENTS = "fact_events"
    FACT_RETAILER_TAGS = "fact_retailer_tags"
    FACT_SALES_CUMULATIVE = "fact_sales_cumulative"
    FACT_SALES_INDICATOR = "fact_sales_indicator"
    FACT_VISIBILITY = "fact_visibility"
    FACT_WISHLIST_ACTIONS = "fact_wishlist_actions"
    FACT_WISHLIST_COHORTS = "fact_wishlist_cohorts"
