import polars as pl

from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.silver import SilverTrafficSourceModel
from data_sdk.domain.tables import (
    SilverTrafficSourceTable,
    TableWithGoldPartitions,
)
from saas_gold.aggregators.tables import SaaSGoldTable


class DimTrafficSourceTable(TableWithGoldPartitions):
    table_name = SaaSGoldTable.DIM_TRAFFIC_SOURCE
    model = SilverTrafficSourceModel


class DimTrafficSourceAggregator(BaseAggregator):
    table_cls = DimTrafficSourceTable

    def __init__(self, silver_traffic_source: SilverTrafficSourceTable) -> None:
        self._silver_traffic_source = silver_traffic_source

    def _aggregate(self) -> pl.DataFrame:
        dim_traffic_source = self._silver_traffic_source.df
        return dim_traffic_source
