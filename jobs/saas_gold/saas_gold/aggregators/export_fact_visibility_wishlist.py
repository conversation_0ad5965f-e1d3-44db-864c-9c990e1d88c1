import polars as pl

from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.tables import (
    LegacyDimPortalsTable,
    LegacyDimSKUsTable,
    LegacyFactVisibilityTable,
    LegacyFactWishlistActionsTable,
    TableWithGoldPartitions,
)
from data_sdk.validators.schemas import NoCheckSchema
from saas_gold.aggregators.tables import SaaSGoldTable
from saas_gold.segmentators.chained_mo_y_q_m import ChainedSegmentatorMoYQM


class ExportFactVisibilityWishlistTable(TableWithGoldPartitions):
    table_name = SaaSGoldTable.EXPORT_FACT_VISIBILITY_WISHLIST
    model = NoCheckSchema


class ExportFactVisibilityWishlistAggregator(BaseAggregator):
    table_cls = ExportFactVisibilityWishlistTable
    segmentator = ChainedSegmentatorMoYQM(date_column="date")

    def __init__(
        self,
        legacy_fact_visibility: LegacyFactVisibilityTable,
        legacy_fact_wishlist_actions: LegacyFactWishlistActionsTable,
        legacy_dim_skus: LegacyDimSKUsTable,
        legacy_dim_portals: LegacyDimPortalsTable,
    ) -> None:
        self._legacy_fact_visibility = legacy_fact_visibility
        self._legacy_fact_wishlist_actions = legacy_fact_wishlist_actions
        self._legacy_dim_skus = legacy_dim_skus
        self._legacy_dim_portals = legacy_dim_portals

    def _aggregate(self) -> pl.DataFrame:
        fact_visibility = self._legacy_fact_visibility.df
        fact_wishlist = self._legacy_fact_wishlist_actions.df
        dim_sku = self._legacy_dim_skus.df.lazy().rename(
            {"sku_studio": "unique_sku_id"}
        )
        dim_portals = self._legacy_dim_portals.df.lazy()

        visibility_has_data = not fact_visibility.is_empty()
        wishlist_has_data = not fact_wishlist.is_empty()
        empty_input_tables = not visibility_has_data and not wishlist_has_data

        columns = [
            "date",
            "unique_sku_id",
            "base_sku_id",
            "human_name",
            "product_id",
            "product_name",
            "portal_platform_region_id",
            "portal",
            "store",
            "country_code",
            "non_owner_visits",
            "non_owner_impressions",
            "adds",
            "deletes",
            "purchases_activations_gifts",
        ]

        if empty_input_tables:
            return pl.DataFrame(schema=columns)

        if visibility_has_data:
            fact_visibility_lf = fact_visibility.lazy().rename(
                {"sku_studio": "unique_sku_id"}
            )

            fact_visibility_lf = fact_visibility_lf.group_by(
                "date",
                "unique_sku_id",
                "portal_platform_region_id",
            ).agg(
                pl.sum("visits"),
                pl.sum("impressions"),
                pl.sum("owner_visits"),
                pl.sum("owner_impressions"),
            )

            fact_visibility_lf = fact_visibility_lf.with_columns(
                (pl.col("visits") - pl.col("owner_visits")).alias("non_owner_visits"),
                (pl.col("impressions") - pl.col("owner_impressions")).alias(
                    "non_owner_impressions"
                ),
                pl.lit("YYY").alias("country_code"),
            ).select(
                "date",
                "unique_sku_id",
                "portal_platform_region_id",
                "country_code",
                "non_owner_visits",
                "non_owner_impressions",
            )

        if wishlist_has_data:
            fact_wishlist_lf = fact_wishlist.lazy().rename(
                {"sku_studio": "unique_sku_id"}
            )
            fact_wishlist_lf = fact_wishlist_lf.group_by(
                "date",
                "unique_sku_id",
                "portal_platform_region_id",
                "country_code",
            ).agg(
                pl.sum("adds"),
                pl.sum("deletes"),
                pl.sum("purchases_and_activations"),
                pl.sum("gifts"),
            )

            fact_wishlist_lf = fact_wishlist_lf.with_columns(
                (pl.col("purchases_and_activations") + pl.col("gifts")).alias(
                    "purchases_activations_gifts"
                ),
            ).select(
                "date",
                "unique_sku_id",
                "portal_platform_region_id",
                "country_code",
                "adds",
                "deletes",
                "purchases_activations_gifts",
            )

        fact_visibility_wishlist_lf = pl.LazyFrame()

        if visibility_has_data and wishlist_has_data:
            fact_visibility_wishlist_lf = fact_visibility_lf.join(
                fact_wishlist_lf,
                on=[
                    "date",
                    "unique_sku_id",
                    "portal_platform_region_id",
                    "country_code",
                ],
                how="full",
                coalesce=True,
            )

        if visibility_has_data and not wishlist_has_data:
            fact_visibility_wishlist_lf = fact_visibility_lf.with_columns(
                pl.lit(None).alias("adds"),
                pl.lit(None).alias("deletes"),
                pl.lit(None).alias("purchases_activations_gifts"),
            )

        if not visibility_has_data and wishlist_has_data:
            fact_visibility_wishlist_lf = fact_wishlist_lf.with_columns(
                pl.lit(None).alias("non_owner_visits"),
                pl.lit(None).alias("non_owner_impressions"),
            )

        fact_visibility_wishlist_lf = fact_visibility_wishlist_lf.join(
            dim_portals.select("portal_platform_region_id", "portal", "store"),
            on="portal_platform_region_id",
            how="left",
            coalesce=True,
        )

        fact_visibility_wishlist_lf = fact_visibility_wishlist_lf.join(
            dim_sku.select(
                "unique_sku_id",
                "base_sku_id",
                "human_name",
                "product_id",
                "product_name",
            ),
            on="unique_sku_id",
            how="left",
            coalesce=True,
        )

        fact_visibility_wishlist_lf = fact_visibility_wishlist_lf.filter(
            (pl.col("product_name") != "__IGNORE") | (pl.col("product_name").is_null())
        )

        fact_visibility_wishlist_lf = fact_visibility_wishlist_lf.select(columns).sort(
            "date",
            "product_id",
        )

        return fact_visibility_wishlist_lf.collect()
