import polars as pl

from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.tables import (
    LegacyDimSKUsTable,
    LegacyFactSalesTable,
    TableWithGoldPartitions,
)
from data_sdk.validators.schemas import NoCheckSchema
from saas_gold.aggregators.tables import SaaSGoldTable
from saas_gold.segmentators.chained_mo_y_q_m import ChainedSegmentatorMoYQM
from saas_gold.utils.steam_fee_sale_multipliers import apply_net_sales_multipliers


class GoldFactSalesTable(TableWithGoldPartitions):
    table_name = SaaSGoldTable.FACT_SALES
    model = NoCheckSchema


class FactSalesAggregator(BaseAggregator):
    table_cls = GoldFactSalesTable
    segmentator = ChainedSegmentatorMoYQM(date_column="date")

    def __init__(
        self,
        legacy_fact_sales: LegacyFactSalesTable,
        legacy_dim_sku: LegacyDimSKUsTable,
    ) -> None:
        self._legacy_fact_sales = legacy_fact_sales
        self._legacy_dim_sku = legacy_dim_sku

    def _aggregate(self) -> pl.DataFrame:
        return generate_fact_sales(
            apply_net_sales_multipliers(
                self._legacy_fact_sales.df,
                pl.read_parquet("saas_gold/dictionaries/steam_fee_skus_table.parquet"),
            ),
            self._legacy_dim_sku.df,
        )


def generate_fact_sales(
    raw_fact_sales: pl.DataFrame, raw_dim_sku: pl.DataFrame
) -> pl.DataFrame:
    if raw_fact_sales.is_empty():
        return pl.DataFrame()

    direct_categories = [
        "Free & Sale",
        "Free & Return",
        "Sale",
        "Invalid Sale",
        "Return",
        "Invalid Return",
    ]

    fact_sales = raw_fact_sales.join(
        raw_dim_sku.select("sku_studio", "product_name"),
        on="sku_studio",
        how="left",
        coalesce=True,
    )

    fact_sales = fact_sales.filter(
        (pl.col("product_name") != "__IGNORE") | (pl.col("product_name").is_null())
    )

    fact_sales_lf = fact_sales.with_columns(
        pl.when(pl.col("category").is_in(direct_categories))
        .then(pl.col("units_sold"))
        .otherwise(0)
        .alias("units_sold_directly"),
        (
            pl.when(pl.col("category").str.starts_with("Non-billable"))
            .then(pl.col("units_sold"))
            .otherwise(0)
            + pl.col("free_units")
        ).alias("units_freely_distributed"),
        pl.when(pl.col("category").str.starts_with("Retail"))
        .then(pl.col("units_sold"))
        .otherwise(0)
        .alias("units_sold_in_retail"),
        pl.when(pl.col("bundle_name") == "Direct Package Sale")
        .then(False)
        .otherwise(True)
        .alias("bundled_sale"),
    ).lazy()

    fact_sales_lf = (
        fact_sales_lf.group_by(
            [
                "date",
                "product_id",
                "country_code",
                "portal_platform_region_id",
                "bundled_sale",
            ]
        )
        .agg(
            pl.first("studio_id"),
            pl.sum("net_sales"),
            pl.sum("net_sales_approx"),
            pl.sum("gross_sales"),
            pl.sum("gross_returned"),
            pl.sum("units_returned"),
            pl.sum("units_sold_directly"),
            pl.sum("units_freely_distributed"),
            pl.sum("units_sold_in_retail"),
        )
        .sort("date", "product_id")
    )

    return fact_sales_lf.collect()
