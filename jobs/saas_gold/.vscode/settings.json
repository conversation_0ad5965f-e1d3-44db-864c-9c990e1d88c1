{
    "[python]": {
        "editor.codeActionsOnSave": {
            "source.fixAll": "explicit",
            "source.organizeImports": "explicit",
            "source.sortImports": "explicit"
        },
        "editor.defaultFormatter": "charliermarsh.ruff",
        "editor.formatOnSave": true,
        "editor.rulers": [
            88
        ]
    },
    "editor.formatOnSave": true,
    "editor.trimAutoWhitespace": true,
    "files.exclude": {
        "**/.pytest_cache": true,
        "**/__pycache__": true,
        "**/.ruff_cache": true,
    },
    "files.insertFinalNewline": true,
    "files.trimTrailingWhitespace": true,
    "python.analysis.autoFormatStrings": true,
    "python.analysis.autoImportCompletions": true,
    "python.analysis.include": [
        ".",
        "../../libs/data-sdk"
    ],
    "python.analysis.inlayHints.callArgumentNames": "partial",
    "python.analysis.inlayHints.functionReturnTypes": true,
    "python.analysis.inlayHints.pytestParameters": true,
    "python.analysis.inlayHints.variableTypes": true,
    "python.analysis.typeCheckingMode": "basic",
    "python.testing.pytestArgs": [
        "-vvv",
        "-n=auto"
        "--random-order",
    ],
    "python.testing.pytestEnabled": true,
    "python.testing.unittestEnabled": false,
    "ruff.lint.args": [
        "--unfixable=F401"
    ],
}
