{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Run locally",
      "type": "debugpy",
      "request": "launch",
      "program": "./scripts/run_locally.py",
      "console": "integratedTerminal",
      "justMyCode": true,
      "args": [
        "--input-env",
        "${input:env}",
        "--studio-id",
        "${input:studio_id}",
        "--output-env",
        "${input:writer_env}"
      ]
    }
  ],
  "inputs": [
    {
      "id": "studio_id",
      "type": "promptString",
      "default": "1",
      "description": "Source studio_id"
    },
    {
      "id": "env",
      "type": "pickString",
      "options": ["dev", "prod"],
      "default": "dev",
      "description": "Silver env source"
    },
    {
      "id": "writer_env",
      "type": "pickString",
      "options": ["local", "dev"],
      "default": "local",
      "description": "Silver env destination"
    }
}
