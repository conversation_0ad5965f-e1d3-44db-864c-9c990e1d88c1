import logging
import os
from enum import Enum

import typer
from pipeline_sdk.monitoring.logs import configure_logger

from data_sdk.config import DLSConfig
from data_sdk.domain import ALL_PORTALS
from data_sdk.logging import force_human_readable_handler_for_tty
from data_sdk.silver import Reader
from saas_gold.job import aggregators

configure_logger(
    custom_loggers_config={
        "saas_gold": {"level": "INFO"},
        "data_sdk": {"level": "INFO"},
    }
)

force_human_readable_handler_for_tty()

log = logging.getLogger(__name__)


class Env(str, Enum):
    DEV = "dev"
    PROD = "prod"


def main(
    reader_env: Env = Env.DEV,
    studio_id: int = 1,
    output_dir: str = "local_output/fetch_data",
):
    if reader_env == Env.DEV:
        reader_cfg = DLSConfig(
            account_name="dlsaggregateddevs7",
            container_name="processed-data",
            base_dir="processed-reports",
        )
    elif reader_env == Env.PROD:
        reader_cfg = DLSConfig(
            account_name="dlsaggregatedprodr9",
            container_name="processed-data",
            base_dir="processed-reports",
        )

    reader = Reader.get_reader(config=reader_cfg)
    all_table_names = set()
    for aggregator in aggregators:
        all_table_names.update(aggregator.input_tables_names)

    log.info(
        "Reading tables %s for studio %s",
        studio_id,
        all_table_names,
    )
    input_data = reader.read_tables(
        table_names=all_table_names,
        studio_id=studio_id,
        portals=ALL_PORTALS,
    )
    os.makedirs(output_dir, exist_ok=True)
    # save tables as parquet
    for table_name, df in input_data.items():
        df.write_parquet(f"{output_dir}/{table_name.name}.parquet")


if __name__ == "__main__":
    typer.run(main)
