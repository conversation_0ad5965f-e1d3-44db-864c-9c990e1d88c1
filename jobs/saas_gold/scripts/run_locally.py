import logging
from enum import Enum
from pathlib import Path

import typer
from pipeline_sdk.monitoring.logs import configure_logger

from data_sdk.config import DLSConfig, Extension, LocalConfig
from data_sdk.logging import force_human_readable_handler_for_tty
from saas_gold.config import Config
from saas_gold.job import Params, run

configure_logger(
    custom_loggers_config={
        "saas_gold": {"level": "INFO"},
        "data_sdk": {"level": "INFO"},
    }
)

force_human_readable_handler_for_tty()

log = logging.getLogger(__name__)


class InputEnv(str, Enum):
    DEV = "dev"
    PROD = "prod"


class OutputEnv(str, Enum):
    DEV = "dev"
    LOCAL = "local"


class RunType(str, Enum):
    DELTA = "delta"
    CUSTOM_PARTITIONS = "cp"
    BOTH = "both"


def main(
    input_env: InputEnv = InputEnv.DEV,
    output_env: OutputEnv = OutputEnv.LOCAL,
    studio_id: int = 1,
    local_output_dir: str = "playground",
):
    if input_env == InputEnv.DEV:
        input_cfg = DLSConfig(
            account_name="dlsaggregateddevs7",
            container_name="core-silver",
            base_dir=Path("result"),
        )
    elif input_env == InputEnv.PROD:
        input_cfg = DLSConfig(
            account_name="dlsaggregatedprodr9",
            container_name="core-silver",
            base_dir=Path("result"),
        )

    if output_env == OutputEnv.LOCAL:
        pbi_output_cfg: LocalConfig | DLSConfig = LocalConfig(
            local_dir=Path(local_output_dir + "/saas_gold"),
        )
        direct_data_access_cfg: LocalConfig | DLSConfig = LocalConfig(
            local_dir=Path(local_output_dir + "/export"), file_extension=Extension.CSV
        )
    elif output_env == OutputEnv.DEV:
        pbi_output_cfg = DLSConfig(
            account_name="dlsaggregateddevs7",
            container_name="saas-gold",
            base_dir=Path("data"),
        )
        direct_data_access_cfg = DLSConfig(
            account_name="dlsaggregateddevs7",
            container_name="saas-gold-direct-data-access",
            base_dir=Path("data"),
            file_extension=Extension.CSV,
        )

    config = Config(
        hostname="localhost",
        env="local",
        run_type="cp",
        input_cfg=input_cfg,
        pbi_output_cfg=pbi_output_cfg,
        direct_data_access_cfg=direct_data_access_cfg,
    )

    run(params=Params(studio_id=studio_id), config=config)

    log.info("Finishing SaaS Gold Job")


if __name__ == "__main__":
    typer.run(main)
