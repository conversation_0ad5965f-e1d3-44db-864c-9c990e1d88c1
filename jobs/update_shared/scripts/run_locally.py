import logging
import logging.config
import logging.handlers

import numpy as np
import pandas as pd

from update_shared.config import Config
from update_shared.connectors.dataset_manager import get_permission_from_dataset_manager

log = logging.getLogger(__name__)


def sort_permission_set(permission_set):
    return sorted(
        permission_set,
        key=lambda permission: (
            permission["studio_id"],
            permission["product_name"] or "",
        ),
    )


def get_permission_set_for_studio(shared_df, studio_id: int) -> list:
    filtered_shared_df = shared_df.loc[shared_df["shared_with_id"] == studio_id]
    filtered_shared_df = filtered_shared_df.rename(
        columns={"shared_by_id": "studio_id"}
    )
    filtered_shared_df = (
        filtered_shared_df[["studio_id", "product_name"]]
        .replace(np.nan, None)
        .drop_duplicates()
    )
    permission_sets = [
        {"studio_id": studio_id, "product_name": product_name}
        for studio_id, product_name in zip(
            filtered_shared_df["studio_id"], filtered_shared_df["product_name"]
        )
    ]

    return sort_permission_set(permission_sets)


def run(config: Config):
    # shared_df = get_shared_df(config)
    # sku_df = get_skus_df(config)

    shared_table = pd.read_parquet("shared_table_prod.parquet")
    studio_list = pd.read_csv("studio_list.csv")
    studios_with_ps = []
    for studio_id in studio_list["studio_id"]:
        expected_permission_set = get_permission_set_for_studio(shared_table, studio_id)
        if expected_permission_set == []:
            expected_permission_set = [{"studio_id": 0, "product_name": None}]
        studios_with_ps.append(
            {
                "studio_id": studio_id,
                "actual_permission_set": get_permission_from_dataset_manager(
                    studio_id, config
                ),
                "expected_permission_set": expected_permission_set,
            }
        )

        # if expected_permission_set != actual_permission_set:
        #     print(
        #         "Actual is different than expected",
        #         studio_id,
        #         "expected:",
        #         expected_permission_set,
        #         "actual:",
        #         actual_permission_set,
        #     )

        #     for a in actual_permission_set:
        #         if a not in expected_permission_set:
        #             print("Extra in actual: ", a)
        #     for a in expected_permission_set:
        #         if a not in actual_permission_set:
        #             print("Missing in actual: ", a)

    pd.DataFrame(studios_with_ps).to_parquet("studios_with_ps.parquet", index=False)


def run_local():
    from pipeline_sdk.monitoring.logs import configure_logger

    configure_logger(
        custom_loggers_config={
            "__main__": {"level": "INFO"},
            "update_shared": {"level": "INFO"},
            "pipeline_sdk": {"level": "INFO"},
            "uamqp": {"level": "WARNING"},
            "azure.identity": {"level": "WARNING"},
            "azure.core.pipeline": {"level": "WARNING"},
        }
    )

    from pydantic import HttpUrl

    # config_dev = Config(
    #     report_service_url=HttpUrl("https://report-service.indiebi.dev"),
    #     report_service_api_key="ibit-OdpM3ozhkHsc8ihEOwbMRVqgnBuHbxR39g69Fq12kXWTHEIx2OUXM7tnVE7SJC3j3lmyhD24ACag0bX1xF6TdHMOXPAvvKciVT277kELjRM6cuINOomtDAWRN9g",
    #     user_service_url=HttpUrl("https://user-service-v2.indiebi.dev"),
    #     user_service_api_key="ibit-7soiSoYe4PWPZaPabyO1KjX2yzqRZLeLR91tBROSV5nlMQ3Ktw7X7ycszUR3qgdQsjUjRO1k2ACtvEbDu2zlG1T10sI76NeFwAjoMtcNC8gMjnRRhAuXzkyuAHa",
    #     dataset_manager_url=HttpUrl("https://x"),
    #     dataset_manager_api_key="x",
    # )
    config_prod = Config(
        report_service_url=HttpUrl("https://report-service.indiebi.com"),
        report_service_api_key="ibit-idTc2i9ViD8wwDmQnJihrOyMNvBKlaGwJePr0mx5SaZfEjz7nSZcXyTnzZRW3QgcNOd7z8AF6F35kVzNKmsORS78MxvKRBaWLzg786nXrVsfNPPoaXbtdRqvdW2",
        user_service_url=HttpUrl("https://user-service-v2.indiebi.com"),
        user_service_api_key="ibit-xD0TJdfubHeXGfmQJaGLbb4ID6mQbaECIPn4pGH21UbittW1Pz95u2B6ebZmg95FHlRzQbEyu8ayC5hEoeZZnLMsEPbRNY7LIyc1Yon4rW2h8oXH9PoaGzJ1V4K",
        dataset_manager_url=HttpUrl("https://dataset-manager.indiebi.com/"),
        dataset_manager_api_key="ibit-4mbqQTqcSpb0mWTc5fajF1teh2JxCndEVidVMgN5pwbhZl3oZz1cntDRmVpU0SmVScd6W1uYjmR0RWL4t36jLwVempKCOSvN9RnSFe8iS070kXDDN6BuvqvNen2",
    )

    run(config=config_prod)


if __name__ == "__main__":
    run_local()
