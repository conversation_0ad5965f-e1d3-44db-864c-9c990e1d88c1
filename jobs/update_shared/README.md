# Update Shared job

This job is responsible for updating processed_shared.parquet file on ADLS to support fetching data to shards. The job is deployed as KEDA `ScaledJob`.

## ScaledJob Configuration

Job's container is configured with following environment variables:

- `$ENV` - environment in which job is deployed
- `$SERVICE_BUS_NAMESPACE` - Azure Service Bus namespace that contains topic and subscription used by job
- `$REPORT_SERVICE_URL` - Report Service API URL, defaults to: http://report-service-v2-service
- `$RS_API_KEY` - Report Service API key
- `$API_GATEWAY_URL` - API Gateway URL, defaults to: http://api-gateway
- `$US_API_KEY` - User Service API key

KEDA job trigger is configured on `update-shared-topic` and `update-shared-sub` on `$SERVICE_BUS_NAMESPACE`.

### Scaling

* KEDA operator polls every 15 seconds for new messages.
  Configured by `.spec.pollingInterval: 15`.
* The job runs until completion **or** until 1 hour deadline is exceeded.
  Achieved by setting:
    - `.spec.jobTargetRef.parallelism` to `1`,
    - `.spec.jobTargetRef.completions` to `1`,
    - `.spec.jobTargetRef.activeDeadlineSeconds` to `3600`.
* If the job fails it should be re-triggered manually, failed messages can be found in SB dead letter queue.
  Facilitated by `.spec.jobTargetRef.backoffLimit: 0`.
* Only one job can run at once, meaning others have to wait.
  Achieved by setting `.spec.maxReplicaCount` to `1`

### Managed pod identity

To ba able to run job requires configured managed identity named `id-update-shared`.

## Deployment

For development and production environments deployment is facilitated through GitLab CI/CD.
For local dev the job can be deployed with `kubectl apply`, but first necessary definitions
have to be generated from templates:

```bash
cd k8s/
./generate-dev-k8s-conf.sh
kubectl apply -f generated/ -R
```
