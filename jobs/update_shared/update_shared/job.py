import logging
import logging.config
import logging.handlers
import traceback

from pipeline_sdk.job import JobInput
from pydantic import BaseModel, ValidationError
from update_shared.config import Config
from update_shared.connectors.report_service import get_skus_df
from update_shared.connectors.user_service import get_shared_df
from update_shared.exceptions import InvalidMessage
from update_shared.permission_sets import update_permission_sets
from update_shared.shared_table import generate_shared_table, get_affected_studios

log = logging.getLogger(__name__)


class Params(BaseModel):
    studio_id: int


def run(job_input: JobInput, config: Config):
    params = validate_message(job_input.target)

    shared_df = get_shared_df(config)
    sku_df = get_skus_df(config)

    shared_table = generate_shared_table(shared_df, sku_df)

    affected_studios = get_affected_studios(shared_table, params.studio_id)

    update_permission_sets(
        shared_df=shared_table,
        affected_studios=[params.studio_id],
        config=config,
    )
    return affected_studios


def validate_message(params) -> Params:
    log.info(f"Validating message: {params}")
    try:
        return Params.model_validate(params)
    except ValidationError:
        log.exception("Received invalid message %s", str(params))
        raise InvalidMessage(traceback.format_exc())
