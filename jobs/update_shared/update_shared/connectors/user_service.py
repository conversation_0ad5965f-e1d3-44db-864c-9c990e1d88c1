import json
import logging

import pandas as pd

from update_shared.config import Config
from update_shared.connectors.http import get_paged_data

log = logging.getLogger(__name__)


def get_shared_df(config: Config):
    log.info("Get shared data from user service")
    shared_df = pd.DataFrame(_get_shared(config))
    return shared_df.drop_duplicates(subset=shared_df.columns.delete(0))


def _create_shared(shared_entry: dict, product: str | None = None) -> dict:
    return {
        "id": shared_entry["id"],
        "shared_with_id": shared_entry["user"]["legacy_id"],
        "shared_by_id": shared_entry["organization"]["legacy_id"],
        "product_name": product,
        "date_from": "1900-01-01T00:00:00.000000Z",
        "date_to": "2099-01-31T00:00:00.000000Z",
        "portal": None,
        "game_id": None,
    }


def _convert_role_assignment_to_shards(role_assignment: dict) -> list[dict]:
    if role_assignment["filter"] is None:
        return [_create_shared(role_assignment)]
    product_filter = json.loads(role_assignment["filter"])["product"]
    if isinstance(product_filter, str):
        return [_create_shared(role_assignment, product_filter)]
    return [_create_shared(role_assignment, product) for product in product_filter]


def _get_shared(config: Config) -> list[dict]:
    """Retrieve studio's shared entries from User Service
    using HTTP GET /role-assignment endpoint.

    Args:
        studio_id: ID of the studio

    Returns:
        List of studio's shared entries

    """
    role_assignments = get_paged_data(
        f"{config.user_service_url}role-assignment",
        {"permission": "DATASET_MANAGER_VIEW_DASHBOARDS"},
        {"x-api-key": config.user_service_api_key},
    )
    shared: list[dict] = []
    for role_assignment in role_assignments:
        shared.extend(_convert_role_assignment_to_shards(role_assignment))

    return shared
