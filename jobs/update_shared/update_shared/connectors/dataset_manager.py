import logging

import requests
from pydantic import BaseModel

from update_shared.config import Config

log = logging.getLogger(__name__)


class Permission(BaseModel):
    studio_id: int
    product_name: str | None


class PermissionSet(BaseModel):
    studio_id: int
    permission_set: list[Permission]

    @property
    def permission_set_dict(self):
        return self.model_dump()["permission_set"]


def update_permission_set(permission_set: PermissionSet, config: Config):
    log.info("Sending permission set: %s", permission_set)

    # TODO: Remove this workaround when dataset will be ready to receive empty permission set
    permission_set_dict = permission_set.permission_set_dict
    if permission_set_dict == []:
        permission_set_dict = [{"studio_id": 0, "product_name": None}]

    response = requests.post(
        f"{config.dataset_manager_url}studio/{permission_set.studio_id}/permission-set",
        json={"permission_set": permission_set_dict},
        headers={"x-api-key": config.dataset_manager_api_key},
    )
    response.raise_for_status()
