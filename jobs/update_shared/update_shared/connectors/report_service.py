import logging
from typing import Any
import pandas as pd

import requests

from update_shared.config import Config

log = logging.getLogger(__name__)


def get_skus_df(config: Config):
    log.info("Get all SKU from report service")
    return pd.DataFrame(_get_skus(config))


def _get_skus(config: Config) -> list[dict[str, Any]]:
    """Retrieve studio's SKUs from Report Service for given portal
    using HTTP GET on /skus endpoint.

    Args:
        studio_id: ID of the studio that owns the SKUs
        portal: Name of the portal for which SKUs will be fetched
        sku_type: SKUs of that type will only be fetched

    Returns:
        List of studio's SKUs

    """
    return _fetch_skus_part(
        f"{config.report_service_url}skus",
        {"x-api-key": config.report_service_api_key},
    )


def _fetch_skus_part(
    service_url: str,
    headers: dict[str, str],
    limit: int = 1000,
    offset: int = 0,
) -> list[dict[str, Any]]:
    """Fetch (potentially incomplete) list of SKUs. If there is possibility
    that there is more SKUs in the service make recursive call to fetch next batch.

    Args:
        service_url: Report Service URL
        studio_id: ID of the studio that owns the SKUs
        portal: Name of the portal for which SKUs will be fetched
        headers: Headers for the request
        limit: Maximum number of SKUs to get, defaults to 1000
        offset: Number of records to skip, defaults to 0

    Returns:
        (Incomplete) list of SKU records or None in case of any API failure

    """
    query_params = {
        "offset": offset,
        "limit": limit,
    }
    response = requests.get(
        service_url,
        params=query_params,
        headers=headers,
    )
    response.raise_for_status()
    skus = response.json()["data"]

    if len(skus) == limit:
        skus_part = _fetch_skus_part(
            service_url,
            headers,
            limit=limit,
            offset=len(skus) + offset,
        )
        skus.extend(skus_part)
    return skus
