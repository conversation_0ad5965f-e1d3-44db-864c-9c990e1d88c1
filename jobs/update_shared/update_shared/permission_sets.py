import logging
import pandas as pd
import numpy as np

from elasticapm import capture_span

from update_shared.connectors.dataset_manager import (
    Permission,
    update_permission_set,
    PermissionSet,
)
from update_shared.config import Config

log = logging.getLogger(__name__)


@capture_span()
def update_permission_sets(
    shared_df: pd.DataFrame, affected_studios: list[int], config: Config
):
    log.info("Update permission sets for %s", affected_studios)
    for studio_id in affected_studios:
        permission_set = get_permission_set_for_studio(shared_df, studio_id)
        update_permission_set(permission_set, config)


@capture_span()
def get_permission_set_for_studio(
    shared_df: pd.DataFrame, studio_id: int
) -> PermissionSet:
    filtered_shared_df = shared_df.loc[shared_df["shared_with_id"] == studio_id]
    filtered_shared_df = filtered_shared_df.rename(
        columns={"shared_by_id": "studio_id"}
    )
    filtered_shared_df = (
        filtered_shared_df[["studio_id", "product_name"]]
        .replace(np.nan, None)
        .drop_duplicates()
    )
    permission_sets = [
        Permission(studio_id=studio_id, product_name=product_name)
        for studio_id, product_name in zip(
            filtered_shared_df["studio_id"], filtered_shared_df["product_name"]
        )
    ]

    return PermissionSet(studio_id=studio_id, permission_set=permission_sets)
