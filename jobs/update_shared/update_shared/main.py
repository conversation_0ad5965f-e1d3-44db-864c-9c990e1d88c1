import sentry_sdk

sentry_sdk.init(traces_sample_rate=1.0)

import logging

from pipeline_sdk.job import JobInput, JobOutput, JobRunner
from pipeline_sdk.monitoring.elastic_tracer import elastic_tracer
from pipeline_sdk.monitoring.logs import configure_logger

from update_shared.config import Config
from update_shared.job import run

configure_logger("update_shared")
log = logging.getLogger(__name__)


def handler(job_input: JobInput) -> JobOutput:
    config = Config()

    log.info(
        "Starting update_shared: %s (version %s, build ts: %s)",
        job_input.job_guid,
        config.docker_tag,
        config.docker_build_timestamp,
    )
    run(job_input=job_input, config=config)

    log.info("Finishing update_shared: %s", job_input.job_guid)

    return JobOutput({"studio_id": job_input.target["studio_id"]})


with elastic_tracer():
    runner = JobRunner(handler)
    runner.run()
