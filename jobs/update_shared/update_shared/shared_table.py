from elasticapm import capture_span
from enum import Enum
import logging
from typing import Optional

import pandas as pd
import pandera as pa

log = logging.getLogger(__name__)


class DisplayPortal(Enum):
    STEAM = "Steam"
    NINTENDO = "Nintendo"
    PLAYSTATION = "PlayStation"
    MICROSOFT = "Microsoft"
    HUMBLE = "Humble"
    GOG = "GOG"
    META = "Meta"
    EPIC = "Epic"


_raw_id_portal_map = {
    1: DisplayPortal.MICROSOFT,
    2: DisplayPortal.STEAM,
    3: DisplayPortal.HUMBLE,
    4: DisplayPortal.GOG,
    5: DisplayPortal.META,
    6: DisplayPortal.META,
    7: DisplayPortal.PLAYSTATION,
    8: DisplayPortal.PLAYSTATION,
    9: DisplayPortal.PLAYSTATION,
    10: DisplayPortal.PLAYSTATION,
    11: DisplayPortal.NINTENDO,
    13: DisplayPortal.PLAYSTATION,
    14: DisplayPortal.EPIC,
    15: DisplayPortal.PLAYSTATION,
}


def _get_portal_name_from_raw_id(raw_id) -> Optional[str]:
    """Return portal based on raw id from old service.

    Args:
        raw_id: raw ID of the portal

    Returns:
        Portal name mapped to the given ID

    >>> get_portal_name_from_raw_id(2)
    'Steam'
    >>> get_portal_name_from_raw_id(42) is None
    True

    """
    try:
        return _raw_id_portal_map[raw_id].value
    except KeyError:
        return None


_schema = pa.DataFrameSchema(
    columns={
        "id": pa.Column(pa.Int),
        "shared_with_id": pa.Column(pa.Int),
        "shared_by_id": pa.Column(pa.Int),
        "date_from": pa.Column(pa.String),
        "date_to": pa.Column(pa.String),
        "portal": pa.Column(pa.String, nullable=True),
        "product_name": pa.Column(pa.String, nullable=True, coerce=True),
        "unique_sku_id": pa.Column(pa.String, nullable=True),
    }
)

_sku_schema = pa.DataFrameSchema(
    columns={
        "studio_id": pa.Column(pa.Int),
        "sku_studio": pa.Column(pa.String),
        "product_name": pa.Column(pa.String, nullable=True),
    }
)


@capture_span()
def generate_shared_table(shared_df: pd.DataFrame, sku_df: pd.DataFrame):
    log.info("Generate shared table")
    sku_df = validate_schema(sku_df, _sku_schema)

    shared_df["portal"] = shared_df["portal"].map(_get_portal_name_from_raw_id)

    shared_df = shared_df.loc[
        (shared_df["game_id"].isnull()) | ~(shared_df["product_name"].isnull())
    ]
    shared_df = shared_df.drop(columns=["game_id"])
    sku_df = sku_df.loc[sku_df["product_name"].notnull()]
    sku_df = sku_df.rename(
        columns={"studio_id": "shared_by_id", "sku_studio": "unique_sku_id"}
    )
    shared_df = shared_df.merge(
        sku_df[["unique_sku_id", "product_name", "shared_by_id"]],
        on=["product_name", "shared_by_id"],
        how="left",
    )
    shared_df = shared_df[
        (shared_df["product_name"].isnull()) | ~(shared_df["unique_sku_id"].isnull())
    ]
    shared_df = shared_df.reset_index(drop=True)
    return validate_schema(shared_df, _schema)


@capture_span()
def get_affected_studios(shared_df: pd.DataFrame, studio_id: int) -> list[int]:
    log.info("Get affected studios for %s", studio_id)
    return (
        shared_df.loc[shared_df["shared_by_id"] == studio_id]["shared_with_id"]
        .unique()
        .tolist()
    )


class InvalidSchema(pa.errors.SchemaError):
    pass


def validate_schema(df: pd.DataFrame, schema: pa.DataFrameSchema) -> pd.DataFrame:
    """
    Validates a dataframe with a given schema.
    """
    if not df.empty:
        try:
            return schema.validate(df)
        except pa.errors.SchemaError as e:
            raise InvalidSchema(
                schema=e.schema,
                data=e.data,
                message=_message_from_pandera_exception(e),
                failure_cases=e.failure_cases,
                check=e.check,
                check_index=e.check_index,
                check_output=e.check_output,
            )
    return pd.DataFrame(columns=schema.columns.keys())


def _message_from_pandera_exception(e: pa.errors.SchemaError) -> str:
    raw_exception_msg = str(e)
    search_msg_config = [
        ("not in dataframe", True),
        ("contains null values", True),
        (": Could not coerce", False),
    ]
    for search_msg, include_search_msg in search_msg_config:
        common_msg_index = raw_exception_msg.find(search_msg)
        search_msg_length = len(search_msg)
        extra_msg_length = search_msg_length if include_search_msg else 0
        if common_msg_index >= 0:
            return (
                raw_exception_msg[0].upper()
                + raw_exception_msg[1 : common_msg_index + extra_msg_length]
            )

    return raw_exception_msg
