from pydantic import AnyHttpUrl
from pydantic_settings import BaseSettings


class Config(BaseSettings):
    env: str = "local"
    app_version: str = "1.0.0"
    job_name: str = "update_shared-job"
    docker_tag: str = ""
    docker_build_timestamp: str = ""

    dls_processed_data_filesystem: str = "processed-data"

    report_service_url: AnyHttpUrl
    report_service_api_key: str

    dataset_manager_url: AnyHttpUrl
    dataset_manager_api_key: str

    user_service_url: AnyHttpUrl
    user_service_api_key: str
