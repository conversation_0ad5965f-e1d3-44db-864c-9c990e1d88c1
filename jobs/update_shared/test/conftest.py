import pytest
import responses
from responses import matchers


@pytest.fixture
def responses_mock():
    with responses.RequestsMock() as mock:
        yield mock


@pytest.fixture
def update_permission_sets_for_all(
    responses_mock: responses.RequestsMock,
):
    responses_mock.post(
        "https://dataset-manager.not_existing/studio/2/permission-set",
        match=[
            matchers.json_params_matcher(
                {
                    "permission_set": [
                        {
                            "studio_id": 2,
                            "product_name": None,
                        }
                    ],
                }
            )
        ],
    )
    responses_mock.post(
        "https://dataset-manager.not_existing/studio/28/permission-set",
        match=[
            matchers.json_params_matcher(
                {
                    "permission_set": [
                        {
                            "studio_id": 2,
                            "product_name": None,
                        }
                    ],
                }
            )
        ],
    )
    responses_mock.post(
        "https://dataset-manager.not_existing/studio/21/permission-set",
        match=[
            matchers.json_params_matcher(
                {
                    "permission_set": [
                        {
                            "studio_id": 2,
                            "product_name": None,
                        }
                    ],
                }
            )
        ],
    )
    responses_mock.post(
        "https://dataset-manager.not_existing/studio/23/permission-set",
        match=[
            matchers.json_params_matcher(
                {
                    "permission_set": [
                        {
                            "studio_id": 2,
                            "product_name": None,
                        }
                    ],
                }
            )
        ],
    )
    responses_mock.post(
        "https://dataset-manager.not_existing/studio/24/permission-set",
        match=[
            matchers.json_params_matcher(
                {
                    "permission_set": [
                        {
                            "studio_id": 2,
                            "product_name": None,
                        }
                    ],
                }
            )
        ],
    )
    responses_mock.post(
        "https://dataset-manager.not_existing/studio/420/permission-set",
        match=[
            matchers.json_params_matcher(
                {
                    "permission_set": [
                        {
                            "studio_id": 2,
                            "product_name": "SUPERHOT VR",
                        }
                    ],
                }
            )
        ],
    )
    responses_mock.post(
        "https://dataset-manager.not_existing/studio/416/permission-set",
        match=[
            matchers.json_params_matcher(
                {
                    "permission_set": [
                        {
                            "studio_id": 2,
                            "product_name": "SUPERHOT",
                        }
                    ],
                }
            )
        ],
    )
    responses_mock.post(
        "https://dataset-manager.not_existing/studio/3/permission-set",
        match=[
            matchers.json_params_matcher(
                {
                    "permission_set": [
                        {
                            "studio_id": 3,
                            "product_name": None,
                        },
                        {
                            "studio_id": 2,
                            "product_name": None,
                        },
                    ],
                }
            )
        ],
    )
