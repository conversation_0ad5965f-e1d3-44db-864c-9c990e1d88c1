import numpy as np
import pandas as pd


def compare_dataframes(
    actual: pd.DataFrame, expected: pd.DataFrame, sort_by: list[str] | None = None
):
    if sort_by is not None:
        actual = actual.sort_values(sort_by)
        expected = expected.sort_values(sort_by)

    actual = actual.reset_index(drop=True)
    expected = expected.reset_index(drop=True)

    # ignore parsedIndex
    actual["parsedIndex"] = np.nan
    expected["parsedIndex"] = np.nan

    actual_columns = sorted(list(actual.columns.values))
    expected_columns = sorted(list(expected.columns.values))

    assert actual_columns == expected_columns, "Columns don't match"

    actual_rows = len(actual)
    expected_rows = len(expected)

    assert (
        actual_rows == expected_rows
    ), f"Number of rows don't match: {actual_rows} != {expected_rows}"
    diff = actual.compare(expected, keep_equal=True)
    assert (
        len(diff) == 0
    ), f"DataFrames are different, diff:\n{diff.to_string(max_rows=20)}"
