import pandas as pd
from update_shared.shared_table import generate_shared_table, get_affected_studios
from test.utils import compare_dataframes


def test_shared_is_generated_properly():
    shared_df = pd.read_csv("./test/data/shared_table_from_us.csv")
    sku_df = pd.read_csv("./test/data/sku_table_from_rs.csv")
    shared_table = generate_shared_table(shared_df, sku_df)
    expected_shared_table = pd.read_csv("./test/data/output_shared_table.csv")
    compare_dataframes(shared_table, expected_shared_table)


def test_shared_is_generated_properly_for_no_sku():
    shared_df = pd.read_csv("./test/data/shared_table_from_us_no_products.csv")
    shared_table = generate_shared_table(shared_df, pd.DataFrame())
    expected_shared_table = pd.DataFrame(
        [
            {
                "id": 10,
                "shared_by_id": 2,
                "shared_with_id": 2,
                "date_from": "1900-01-01 00:00:00.000",
                "date_to": "2099-01-31 00:00:00.000",
                "portal": None,
                "product_name": None,
                "unique_sku_id": None,
            },
            {
                "id": 12,
                "shared_by_id": 3,
                "shared_with_id": 3,
                "date_from": "1900-01-01 00:00:00.000",
                "date_to": "2099-01-31 00:00:00.000",
                "portal": None,
                "product_name": None,
                "unique_sku_id": None,
            },
            {
                "id": 66,
                "shared_by_id": 2,
                "shared_with_id": 28,
                "date_from": "1900-01-01 00:00:00.000",
                "date_to": "2099-01-31 00:00:00.000",
                "portal": None,
                "product_name": None,
                "unique_sku_id": None,
            },
        ]
    )
    compare_dataframes(shared_table, expected_shared_table)


def test_if_affected_studios_generated_properly():
    sharing_studio = 2
    expected_affected_studios_list = [2, 28, 21, 23, 24, 420, 416, 3]
    shared_table = pd.read_csv("./test/data/output_shared_table.csv")
    affected_studios = get_affected_studios(shared_table, sharing_studio)
    assert affected_studios == expected_affected_studios_list
