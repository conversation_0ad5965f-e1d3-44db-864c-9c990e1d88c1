import pandas as pd
from pydantic import AnyHttpUrl, BaseModel
import pytest

from update_shared.connectors.dataset_manager import Permission, PermissionSet
from update_shared.permission_sets import (
    get_permission_set_for_studio,
    update_permission_sets,
)
from update_shared.shared_table import get_affected_studios


@pytest.fixture
def config():
    class FakeConfig(BaseModel):
        dataset_manager_url: AnyHttpUrl = "https://dataset-manager.not_existing"
        dataset_manager_api_key: str = "aaaaa"
        env: str = "local"

    return FakeConfig(
        dataset_manager_url="https://dataset-manager.not_existing",
    )


def test_get_permission_set():
    shared_df = pd.read_csv("./test/data/shared_table_from_us.csv")

    permission_set = get_permission_set_for_studio(shared_df, 2)
    assert permission_set == PermissionSet(
        studio_id=2,
        permission_set=[Permission(studio_id=2, product_name=None)],
    )

    permission_set = get_permission_set_for_studio(shared_df, 416)
    assert permission_set == PermissionSet(
        studio_id=416,
        permission_set=[Permission(studio_id=2, product_name="SUPERHOT")],
    )

    permission_set = get_permission_set_for_studio(shared_df, 3)
    assert permission_set == PermissionSet(
        studio_id=3,
        permission_set=[
            Permission(studio_id=3, product_name=None),
            Permission(studio_id=2, product_name=None),
        ],
    )


def test_update_permission_set(update_permission_sets_for_all, config):
    shared_df = pd.read_csv("./test/data/shared_table_from_us.csv")
    affected_studios = get_affected_studios(shared_df, 2)
    update_permission_sets(shared_df, affected_studios, config)
