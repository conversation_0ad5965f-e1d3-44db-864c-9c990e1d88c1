import sentry_sdk

sentry_sdk.init(traces_sample_rate=1.0)

import logging

from pipeline_sdk.job import JobInput, JobOutput, JobRunner
from pipeline_sdk.monitoring.elastic_tracer import elastic_tracer
from pipeline_sdk.monitoring.logs import configure_logger

from find_shards.config import Config
from find_shards.job import run, validate_message

configure_logger("find_shards")
log = logging.getLogger(__name__)


def handler(job_input: JobInput) -> JobOutput:
    config = Config()

    log.info(
        "Starting find_shards: %s (version %s, build ts: %s)",
        job_input.job_guid,
        config.docker_tag,
        config.docker_build_timestamp,
    )

    params = validate_message(job_input.target)
    shard_ids = run(params=params, config=config)

    log.info("Finishing find_shards: %s", job_input.job_guid)

    output = [{"shard_id": shard_id} for shard_id in shard_ids]
    response: dict = {"shard_ids": output}
    if len(output) == 0:
        log.info("No shards found. Forcing pipeline finish...")
        response["force_finish_pipeline"] = True
    return JobOutput(response)


if __name__ == "__main__":
    with elastic_tracer():
        runner = JobRunner(handler)
        runner.run()
