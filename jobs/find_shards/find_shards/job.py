import logging
import logging.config
import logging.handlers
import traceback

from pydantic import BaseModel, ValidationError

from find_shards.connectors.dataset_manager import get_active_shards_for_studios
from find_shards.connectors.user_service import get_studio_shared_data
from find_shards.config import Config
from find_shards.exceptions import InvalidMessage


log = logging.getLogger(__name__)


class Params(BaseModel):
    studio_id: int


def run(params: Params, config: Config) -> list[str]:
    shared_with = get_studio_shared_data(params.studio_id, config)
    affected_studios = list(set(shared["shared_with_id"] for shared in shared_with))
    log.info(f"Found affected studios {affected_studios}")
    if len(affected_studios) > 0:
        shard_ids = get_active_shards_for_studios(affected_studios, config=config)
        log.info(f"Found shards for given studios: {shard_ids}")
    else:
        log.info("No affected studios. Skipping shards search.")
        shard_ids = []
    return shard_ids


def validate_message(params) -> Params:
    log.info(f"Validating message: {params}")
    try:
        return Params.model_validate(params)
    except ValidationError:
        log.exception("Received invalid message %s", str(params))
        raise InvalidMessage(traceback.format_exc())
