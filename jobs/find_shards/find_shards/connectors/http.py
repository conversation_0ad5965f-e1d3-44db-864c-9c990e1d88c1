from typing import Any

import requests


def get_paged_data(
    service_url: str,
    parameters: dict[str, Any],
    headers: dict[str, str],
    limit: int = 100,
    offset: int = 0,
) -> list[dict[str, Any]]:
    """Fetch paginated data from REST service.

    Args:
        service_url: Service URL, including endpoint
        parameters: Request query parameters
        headers: Request headers
        limit: Maximum number of fetched records at once, defaults to 100
        offset: Data offset, defaults to 0

    Returns:
        Concatenated records returned by the service

    """
    params = {"limit": limit, "offset": offset, **parameters}
    response = requests.get(service_url, params=params, headers=headers, timeout=120)
    response.raise_for_status()

    data = response.json()["data"]

    while len(data) < response.json()["count"] and response.json()["count"] > 0:
        params["offset"] += limit
        response = requests.get(
            service_url, params=params, headers=headers, timeout=120
        )
        response.raise_for_status()
        data.extend(response.json()["data"])

    return data
