class JobError(Exception):
    def __init__(self, message: str | None = None):
        self.message = message

    def __repr__(self) -> str:
        s = self.__class__.__name__
        return s if not self.message else f'{s}: "{self.message}"'

    __str__ = __repr__


class RetriableError(JobError):
    pass


class NotRetriableError(JobError):
    pass


class InvalidJobSetup(NotRetriableError):
    pass


class InvalidMessage(NotRetriableError):
    pass
