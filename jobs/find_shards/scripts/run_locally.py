import logging
import uuid

import typer
from pipeline_sdk.job import JobInput
from pipeline_sdk.monitoring.logs import configure_logger

from find_shards.main import handler

configure_logger(
    custom_loggers_config={
        "find_shards": {"level": "INFO"},
        "data_sdk": {"level": "INFO"},
    }
)

log = logging.getLogger(__name__)


def main(studio_id: int = 1):
    try:
        handler(
            JobInput(
                job_guid=str(uuid.uuid4()),
                target={"studio_id": studio_id},
                execution_try_count=1,
                pipeline_guids=[str(uuid.uuid4())],
            )
        )
    finally:
        log.info("Done")


if __name__ == "__main__":
    typer.run(main)
