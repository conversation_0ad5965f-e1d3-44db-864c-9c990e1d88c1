import pytest
import responses
from pydantic import AnyHttpUrl
from pydantic_settings import BaseSettings
from responses import matchers

from find_shards.job import Params, run


@pytest.fixture
def responses_mock():
    with responses.RequestsMock() as mock:
        yield mock


@pytest.fixture
def role_assignment_response():
    return {
        "data": [
            {
                "user_id": "u-000002",
                "organization_id": "o-000001",
                "role": "EXTERNAL_READER",
                "filter": None,
                "created_by_user_id": None,
                "id": 1,
                "created_at": "2024-02-09T00:00:00.0000Z",
                "updated_at": "2024-02-09T00:00:00.0000Z",
                "user": {"legacy_id": 2, "email": "<EMAIL>"},
                "organization": {"legacy_id": 1, "name": "Organization 1"},
            },
            {
                "user_id": "u-000003",
                "organization_id": "o-000001",
                "role": "EXTERNAL_READER",
                "filter": None,
                "created_by_user_id": None,
                "id": 2,
                "created_at": "2024-02-09T00:00:00.0000Z",
                "updated_at": "2024-02-09T00:00:00.0000Z",
                "user": {"legacy_id": 3, "email": "<EMAIL>"},
                "organization": {"legacy_id": 1, "name": "Organization 1"},
            },
            {
                "user_id": "u-000004",
                "organization_id": "o-000001",
                "role": "EXTERNAL_READER",
                "filter": '{"product": "Super HOT"}',
                "created_by_user_id": None,
                "id": 3,
                "created_at": "2024-02-09T00:00:00.0000Z",
                "updated_at": "2024-02-09T00:00:00.0000Z",
                "user": {"legacy_id": 4, "email": "<EMAIL>"},
                "organization": {"legacy_id": 1, "name": "Organization 1"},
            },
            {
                "user_id": "u-000004",
                "organization_id": "o-000001",
                "role": "EXTERNAL_READER",
                "filter": '{"product": "Super COLD"}',
                "created_by_user_id": None,
                "id": 4,
                "created_at": "2024-02-09T00:00:00.0000Z",
                "updated_at": "2024-02-09T00:00:00.0000Z",
                "user": {"legacy_id": 4, "email": "<EMAIL>"},
                "organization": {"legacy_id": 1, "name": "Organization 1"},
            },
        ],
        "count": 4,
    }


@pytest.fixture
def get_active_shards_for_studios(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://dataset-manager.not_existing/shard/active",
        match=[
            matchers.query_param_matcher(
                {"studio_ids": ["2", "3", "4"]},
                strict_match=True,
            )
        ],
        json=[
            {
                "version": "2.0.0",
                "dataset_id": "dataset_id-234-234-234",
                "workspace_id": "shard_2-1231-123-123-123",
                "workspace_name": "workspace_name_2",
                "capacity_id": "capacity_id",
                "creation_timestamp": "2022-11-24 14:32:00",
            },
            {
                "version": "2.0.0",
                "dataset_id": "dataset_id-345-345-345",
                "workspace_id": "shard_3_4-1231-123-123-123",
                "workspace_name": "workspace_name_3_4",
                "capacity_id": "capacity_id",
                "creation_timestamp": "2022-11-24 14:32:00",
            },
        ],
    )


@pytest.fixture
def config():
    class FakeConfig(BaseSettings):
        user_service_url: AnyHttpUrl
        user_service_api_key: str
        report_service_url: AnyHttpUrl
        report_service_api_key: str
        dataset_manager_url: AnyHttpUrl
        dataset_manager_api_key: str

    return FakeConfig(
        user_service_url="https://user-service.not_existing",
        user_service_api_key="qwerty",
        report_service_url="https://report-service.not_existing",
        report_service_api_key="asdfg",
        dataset_manager_url="https://dataset-manager.not_existing",
        dataset_manager_api_key="poiuyt",
    )


def test_sending_shards_for_requested_studio(
    responses_mock: responses.RequestsMock,
    get_active_shards_for_studios,
    role_assignment_response,
    config,
) -> None:
    responses_mock.get(
        "https://user-service.not_existing/organization/legacy/1",
        json={"name": "Organization 1", "legacy_id": 1, "id": "o-000001"},
    )
    responses_mock.get(
        "https://user-service.not_existing/role-assignment",
        match=[
            matchers.query_param_matcher(
                {
                    "permission": "DATASET_MANAGER_VIEW_DASHBOARDS",
                    "organization_id": "o-000001",
                    "offset": 0,
                    "limit": 100,
                },
                strict_match=True,
            )
        ],
        json=role_assignment_response,
    )
    result = run(params=Params(studio_id=1), config=config)

    assert result == ["shard_2-1231-123-123-123", "shard_3_4-1231-123-123-123"]


def test_not_searching_for_shards_if_studio_is_not_sharing_data(
    responses_mock: responses.RequestsMock, config
):
    responses_mock.get(
        "https://user-service.not_existing/role-assignment",
        match=[
            matchers.query_param_matcher(
                {
                    "permission": "DATASET_MANAGER_VIEW_DASHBOARDS",
                    "organization_id": "o-000003",
                    "offset": 0,
                    "limit": 100,
                },
                strict_match=True,
            )
        ],
        json={"data": [], "count": 0},
    )
    responses_mock.get(
        "https://user-service.not_existing/organization/legacy/3",
        json={"name": "Organization 3", "legacy_id": 3, "id": "o-000003"},
    )
    result = run(params=Params(studio_id=3), config=config)

    assert result == []
