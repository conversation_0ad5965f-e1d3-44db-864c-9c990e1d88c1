import random

import pytest
import responses
from pydantic import AnyHttpUrl, BaseModel

from find_shards.connectors.user_service import get_studio_shared_data


@pytest.fixture
def config():
    class FakeConfig(BaseModel):
        user_service_url: AnyHttpUrl = "https://user-service.not_existing/"
        user_service_api_key: str = "aaaaa"

    return FakeConfig()


@pytest.fixture
def role_assignment_factory():
    def _factory(
        user_legacy_id: int = 10,
        org_legacy_id: int = 1,
        role: str = "READER",
        filter: str | None = None,
    ):
        return {
            "user_id": f"u-{user_legacy_id}",
            "organization_id": f"o-{org_legacy_id}",
            "role": role,
            "filter": filter,
            "created_by_user_id": None,
            "id": random.randint(0, 1000000),
            "created_at": "2024-02-07T13:51:24.053000Z",
            "updated_at": "2024-02-07T13:51:24.053000Z",
            "user": {
                "legacy_id": user_legacy_id,
                "email": f"user-{user_legacy_id}@indiebi.com",
            },
            "organization": {
                "legacy_id": org_legacy_id,
                "name": f"Test Org {org_legacy_id}",
            },
        }

    return _factory


@pytest.fixture
def shared_record_factory():
    def _factory(role_assignment: dict, product_name: str | None = None):
        return {
            "id": role_assignment["id"],
            "shared_with_id": role_assignment["user"]["legacy_id"],
            "shared_by_id": role_assignment["organization"]["legacy_id"],
            "product_name": product_name,
            "date_from": "1900-01-01T00:00:00.000000Z",
            "date_to": "2099-01-31T00:00:00.000000Z",
            "portal_id": None,
            "game_id": None,
        }

    return _factory


@responses.activate
def test_get_shared_df_should_generate_df_based_on_role_assignments_returned_by_US(
    role_assignment_factory, shared_record_factory, config
):
    role_assignments = [
        role_assignment_factory(user_legacy_id=1),
        role_assignment_factory(user_legacy_id=2, role="EXTERNAL_READER"),
        role_assignment_factory(user_legacy_id=3, filter='{"product": "A"}'),
        role_assignment_factory(user_legacy_id=4, filter='{"product": ["X", "Y"]}'),
    ]
    expected_shared_table = [
        shared_record_factory(role_assignments[0]),
        shared_record_factory(role_assignments[1]),
        shared_record_factory(role_assignments[2], "A"),
        shared_record_factory(role_assignments[3], "X"),
        shared_record_factory(role_assignments[3], "Y"),
    ]
    responses.get(
        url=config.user_service_url + "organization/legacy/1", json={"id": "u-1"}
    )
    responses.get(
        url=config.user_service_url + "role-assignment",
        json={
            "data": role_assignments,
            "count": len(role_assignments),
        },
    )

    result = get_studio_shared_data(1, config)

    assert result == expected_shared_table
