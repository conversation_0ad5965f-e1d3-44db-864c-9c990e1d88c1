import sentry_sdk

sentry_sdk.init(traces_sample_rate=1.0)

import logging

from pipeline_sdk.job import JobInput, JobRunner
from pipeline_sdk.monitoring.elastic_tracer import elastic_tracer
from pipeline_sdk.monitoring.logs import configure_logger

from direct_data_access_gold.config import Config
from direct_data_access_gold.run import Params, run

configure_logger("direct_data_access_gold")
log = logging.getLogger(__name__)


def handler(job_input: JobInput) -> None:
    config = Config()

    log.info(
        "Starting direct_data_access_gold: %s (version %s, build ts: %s)",
        job_input.job_guid,
        config.docker_tag,
        config.docker_build_timestamp,
    )
    log.info(f"Validating message: {job_input.target}")

    params = Params.model_validate(job_input.target)
    run(params)
    log.info("Finishing direct_data_access_gold: %s", job_input.job_guid)


if __name__ == "__main__":
    with elastic_tracer():
        runner = JobRunner(handler)
        runner.run()
