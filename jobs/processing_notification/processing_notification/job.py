import logging
import logging.config
import logging.handlers
import traceback
from datetime import datetime, timedelta

from pipeline_sdk.job import InvalidServiceBusMessage, JobInput
from pydantic import BaseModel, ValidationError

from processing_notification.config import Config
from processing_notification.connectors import dataset_manager as dataset_manager_api
from processing_notification.connectors import one_signal as one_signal_api
from processing_notification.connectors import user_service as user_service_api
from processing_notification.connectors.user_service import (
    LastNotification,
    SchemaDriftDetected,
)
from processing_notification.connectors.dataset_manager import (
    NotificationMetrics,
    ShardIsEmpty,
)

log = logging.getLogger(__name__)


class Params(BaseModel):
    shard_id: str


def run(job_input: JobInput, config: Config) -> None:
    params = validate_message(job_input.target)
    try:
        current_metrics = dataset_manager_api.get_notification_metrics(
            params.shard_id, config
        )
    except ShardIsEmpty:
        log.info("Shard is empty")
        return
    log.info("Fetched current metrics: %s", current_metrics)
    legacy_users_ids = dataset_manager_api.get_shard_users_ids(params.shard_id, config)
    handle_notifications(legacy_users_ids, current_metrics, config)


def handle_notifications(
    legacy_users_ids: list[int],
    current_metrics: NotificationMetrics,
    config: Config,
) -> None:
    for legacy_user_id in legacy_users_ids:
        # required during migration from from lagacy_user_id to new user_id
        user_id = user_service_api.get_user_id_by_legacy_user_id(legacy_user_id, config)
        if user_id is None:
            log.info("User with legacy_id %i does not exist", user_id)
            continue
        user_service_api.set_last_processing(user_id, config)
        try:
            last_notification = user_service_api.get_users_last_notification(
                user_id, config
            )
        except SchemaDriftDetected:
            user_service_api.set_users_last_notification(
                user_id, current_metrics, config
            )
            continue
        log.info("Fetched last notification: %s", last_notification)
        handle_notification_for_user(
            user_id, current_metrics, last_notification, config
        )


def handle_notification_for_user(
    user_id: str,
    current_metrics: NotificationMetrics,
    last_notification: LastNotification | None,
    config: Config,
) -> None:
    if not _should_send_notification(current_metrics, last_notification, config):
        log.info("Not sending notification")
        return
    last_metrics = last_notification.notification_metrics if last_notification else None
    metrics_difference = current_metrics - last_metrics
    log.info(
        "Updating last notification key value: %s for %s", metrics_difference, user_id
    )
    user_service_api.set_users_last_notification(user_id, current_metrics, config)
    log.info(
        "Sending notification: metrics difference: %s for %s",
        metrics_difference,
        user_id,
    )
    one_signal_api.send_notification(user_id, metrics_difference, config)


def _should_send_notification(
    current_metrics: NotificationMetrics,
    last_notification: LastNotification | None,
    config: Config,
) -> bool:
    if last_notification is None:
        return True
    if (
        last_notification.trigger_date
        + timedelta(minutes=config.min_time_between_notifications)
        > datetime.now()
    ):
        return False
    if (
        current_metrics.gross_sales
        <= last_notification.notification_metrics.gross_sales
    ):
        return False
    return True


def validate_message(params) -> Params:
    log.info(f"Validating message: {params}")
    try:
        return Params.model_validate(params)
    except ValidationError:
        log.exception("Received invalid message %s", str(params))
        raise InvalidServiceBusMessage(traceback.format_exc())
