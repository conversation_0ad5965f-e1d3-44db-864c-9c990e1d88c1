from pydantic_settings import BaseSettings


class Config(BaseSettings):
    env: str = "local"
    app_version: str = "1.0.0"
    job_name: str = "processing-notification"
    docker_tag: str = ""
    docker_build_timestamp: str = ""

    min_time_between_notifications: int = 30

    user_service_url: str
    user_service_api_key: str

    dataset_manager_url: str
    dataset_manager_api_key: str

    one_signal_url: str = "https://onesignal.com/api/v1/notifications"
    one_signal_template_id: str = "6c518fca-f9a1-4c45-842d-8e006c95ba83"
    one_signal_app_id: str
    one_signal_api_key: str
