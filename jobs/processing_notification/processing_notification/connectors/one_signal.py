import logging

import requests
from elasticapm import capture_span

from processing_notification.config import Config
from processing_notification.connectors.dataset_manager import NotificationMetrics

log = logging.getLogger(__name__)


# if this connector will grow, consider introducing onesignal-python-api
@capture_span()
def send_notification(
    user_id: str, metrics_difference: NotificationMetrics, config: Config
) -> None:
    response = requests.post(
        config.one_signal_url,
        headers={
            "Authorization": f"Basic {config.one_signal_api_key}",
            "accept": "application/json",
            "content-type": "application/json",
        },
        json={
            "app_id": config.one_signal_app_id,
            "target_channel": "push",
            "include_aliases": {"external_id": [user_id]},
            "template_id": config.one_signal_template_id,
            "custom_data": {
                "new_revenue": "${:,.2f}".format(metrics_difference.gross_sales),
                "new_units": "{:,}".format(metrics_difference.units_sold),
                "new_wishlists": "{:,}".format(metrics_difference.wishlists),
            },
            "data": {
                "new_revenue": str(metrics_difference.gross_sales),
                "new_units": str(metrics_difference.units_sold),
                "new_wishlists": str(metrics_difference.wishlists),
            },
        },
        timeout=10,
    )
    response.raise_for_status()
    data = response.json()
    log.info("Received One Signal response: %s", data)
