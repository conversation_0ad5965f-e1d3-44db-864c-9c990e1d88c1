from datetime import datetime, timezone
import json

import requests
from pydantic import BaseModel, ValidationError

from processing_notification.config import Config
from processing_notification.connectors.dataset_manager import NotificationMetrics


NAMESPACE_NAME = "data_pipeline"
LAST_NOTIFICATION_KV_NAME = "last_notification"
LAST_PROCESSING_KV_NAME = "last_processing"


class LastNotification(BaseModel):
    trigger_date: datetime
    notification_metrics: NotificationMetrics


class LastProcessing(BaseModel):
    processing_date: datetime


class SchemaDriftDetected(Exception):
    pass


def get_user_id_by_legacy_user_id(legacy_id: int, config: Config) -> str | None:
    response = requests.get(
        f"{config.user_service_url}/user/legacy/{legacy_id}",
        headers={"x-api-key": config.user_service_api_key},
        timeout=10,
    )
    if response.status_code >= 200 and response.status_code < 400:
        return response.json()["id"]
    else:
        return None


def get_users_last_notification(
    user_id: str, config: Config
) -> LastNotification | None:
    response = requests.get(
        f"{config.user_service_url}/user/{user_id}/key-value/{NAMESPACE_NAME}/{LAST_NOTIFICATION_KV_NAME}",
        headers={"x-api-key": config.user_service_api_key},
        timeout=10,
    )
    if response.status_code >= 200 and response.status_code < 400:
        try:
            return LastNotification(**json.loads(response.json()["value"]))
        except ValidationError:
            raise SchemaDriftDetected
    return None


def set_users_last_notification(
    user_id: str, current_metrics: NotificationMetrics, config: Config
) -> None:
    new_notification = LastNotification(
        notification_metrics=current_metrics, trigger_date=datetime.now()
    )
    response = requests.put(
        f"{config.user_service_url}/user/{user_id}/key-value/{NAMESPACE_NAME}/{LAST_NOTIFICATION_KV_NAME}/no-lock",
        headers={"x-api-key": config.user_service_api_key},
        json={"value": new_notification.model_dump_json(), "version": 0},
        timeout=10,
    )
    response.raise_for_status()


def set_last_processing(user_id: str, config: Config) -> None:
    last_processing = LastProcessing(processing_date=datetime.now(timezone.utc))
    response = requests.put(
        f"{config.user_service_url}/user/{user_id}/key-value/{NAMESPACE_NAME}/{LAST_PROCESSING_KV_NAME}/no-lock",
        headers={"x-api-key": config.user_service_api_key},
        json={"value": last_processing.model_dump_json(), "version": 0},
        timeout=10,
    )
    response.raise_for_status()
