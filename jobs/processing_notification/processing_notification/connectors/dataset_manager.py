import requests
from pydantic import BaseModel, field_validator

from processing_notification.config import Config

DAX_QUERY = 'EVALUATE ROW("units", [Units Sold Directly], "wishlists", \
    [Wishlist Additions], "gross_sales", SUM(fact_sales[gross_sales]))'


class NotificationMetrics(BaseModel):
    gross_sales: float
    units_sold: int
    wishlists: int | None

    @field_validator("wishlists")
    @classmethod
    def set_name(cls, wishlists):
        return wishlists or 0

    def __sub__(self, other):
        if other is None:
            return self
        return NotificationMetrics(
            gross_sales=self.gross_sales - other.gross_sales,
            units_sold=self.units_sold - other.units_sold,
            wishlists=self.wishlists - other.wishlists,
        )


class ShardIsEmpty(Exception):
    pass


def get_shard_users_ids(shard_id: str, config: Config) -> list[int]:
    response = requests.get(
        f"{config.dataset_manager_url}/shard/{shard_id}/profiles",
        headers={"x-api-key": config.dataset_manager_api_key},
        timeout=10,
    )
    response.raise_for_status()
    data = response.json()
    return [profile["studio_id"] for profile in data]


def post_dax_query(shard_id: str, dax_query: str, config: Config) -> list[dict]:
    response = requests.post(
        f"{config.dataset_manager_url}/shard/{shard_id}/query",
        headers={"x-api-key": config.dataset_manager_api_key},
        json={"raw_query": dax_query},
        timeout=300,
    )
    response.raise_for_status()
    return response.json()


def get_notification_metrics(shard_id: str, config: Config) -> NotificationMetrics:
    dax_output = post_dax_query(
        shard_id,
        DAX_QUERY,
        config,
    )[0]
    if dax_output["[gross_sales]"] is None or dax_output["[units]"] is None:
        raise ShardIsEmpty
    return NotificationMetrics(
        gross_sales=dax_output["[gross_sales]"],
        units_sold=dax_output["[units]"],
        wishlists=dax_output["[wishlists]"],
    )
