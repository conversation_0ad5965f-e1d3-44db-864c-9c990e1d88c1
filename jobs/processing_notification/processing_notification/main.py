import sentry_sdk

sentry_sdk.init(traces_sample_rate=1.0)

import logging

from pipeline_sdk.job import JobInput, JobRunner
from pipeline_sdk.monitoring.logs import add_logging_context, configure_logger
from pipeline_sdk.monitoring.elastic_tracer import elastic_tracer

from processing_notification.config import Config
from processing_notification.job import run

configure_logger("processing_notification")
log = logging.getLogger(__name__)


def handler(job_input: JobInput) -> None:
    config = Config()

    with add_logging_context(**{"job_guid": job_input.job_guid, **job_input.target}):
        log.info(
            "Starting processing_notification: %s (version %s, build ts: %s)",
            job_input.job_guid,
            config.docker_tag,
            config.docker_build_timestamp,
        )

        run(job_input=job_input, config=config)

        log.info("Finishing processing_notification: %s", job_input.job_guid)


if __name__ == "__main__":
    with elastic_tracer():
        runner = JobRunner(handler)
        runner.run()
