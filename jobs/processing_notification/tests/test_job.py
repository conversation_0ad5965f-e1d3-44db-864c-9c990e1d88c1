import json
import re
from datetime import datetime, timedelta

from freezegun import freeze_time
from pipeline_sdk.job import JobInput

from processing_notification.connectors.dataset_manager import NotificationMetrics
from processing_notification.connectors.user_service import LastNotification
from processing_notification.job import (
    handle_notification_for_user,
    handle_notifications,
    run,
)


def test_job_when_last_notification_missing_should_send_notification_with_current_metrics(
    fake_config, responses_mock, key_value_set_mock, one_signal_notification_mock
):
    handle_notification_for_user(
        user_id="u-1",
        current_metrics=NotificationMetrics(
            gross_sales=99.99, units_sold=55, wishlists=22
        ),
        last_notification=None,
        config=fake_config,
    )

    one_signal_req = responses_mock.calls[1].request
    assert one_signal_req.url == f"{fake_config.one_signal_url}/"
    assert json.loads(one_signal_req.body)["custom_data"] == {
        "new_revenue": "$99.99",
        "new_units": "55",
        "new_wishlists": "22",
    }
    assert json.loads(one_signal_req.body)["data"] == {
        "new_revenue": "99.99",
        "new_units": "55",
        "new_wishlists": "22",
    }


def test_job_when_last_notification_missing_should_set_new_key_value_with_current_metrics(
    fake_config, responses_mock, key_value_set_mock, one_signal_notification_mock
):
    handle_notification_for_user(
        user_id="u-1",
        current_metrics=NotificationMetrics(
            gross_sales=99.99, units_sold=55, wishlists=22
        ),
        last_notification=None,
        config=fake_config,
    )

    key_value_req = responses_mock.calls[0].request
    assert (
        key_value_req.url
        == f"{fake_config.user_service_url}/user/u-1/key-value/data_pipeline/last_notification/no-lock"
    )
    assert json.loads(json.loads(key_value_req.body)["value"])[
        "notification_metrics"
    ] == {"gross_sales": 99.99, "units_sold": 55, "wishlists": 22}


def test_job_when_last_notification_older_than_threshold_should_send_notification_with_metrics_difference(
    fake_config, responses_mock, key_value_set_mock, one_signal_notification_mock
):
    handle_notification_for_user(
        user_id="u-1",
        current_metrics=NotificationMetrics(
            gross_sales=99999.99, units_sold=55555, wishlists=22222
        ),
        last_notification=LastNotification(
            notification_metrics=NotificationMetrics(
                gross_sales=88888.88, units_sold=44445, wishlists=11100
            ),
            trigger_date=datetime.now()
            - timedelta(minutes=fake_config.min_time_between_notifications),
        ),
        config=fake_config,
    )

    one_signal_req = responses_mock.calls[1].request
    assert one_signal_req.url == f"{fake_config.one_signal_url}/"
    assert json.loads(one_signal_req.body)["custom_data"] == {
        "new_revenue": "$11,111.11",
        "new_units": "11,110",
        "new_wishlists": "11,122",
    }
    assert json.loads(one_signal_req.body)["data"] == {
        "new_revenue": "11111.11",
        "new_units": "11110",
        "new_wishlists": "11122",
    }


def test_job_when_last_notification_older_than_threshold_should_set_new_key_value_with_current_metrics(
    fake_config, responses_mock, key_value_set_mock, one_signal_notification_mock
):
    handle_notification_for_user(
        user_id="u-1",
        current_metrics=NotificationMetrics(
            gross_sales=99.99, units_sold=55, wishlists=22
        ),
        last_notification=LastNotification(
            notification_metrics=NotificationMetrics(
                gross_sales=88.88, units_sold=42, wishlists=10
            ),
            trigger_date=datetime.now()
            - timedelta(minutes=fake_config.min_time_between_notifications),
        ),
        config=fake_config,
    )

    key_value_req = responses_mock.calls[0].request
    assert (
        key_value_req.url
        == f"{fake_config.user_service_url}/user/u-1/key-value/data_pipeline/last_notification/no-lock"
    )
    assert json.loads(json.loads(key_value_req.body)["value"])[
        "notification_metrics"
    ] == {"gross_sales": 99.99, "units_sold": 55, "wishlists": 22}


def test_job_when_last_notification_newer_than_threshold_should_not_send_notification_or_set_key_value(
    fake_config, responses_mock, key_value_set_mock, one_signal_notification_mock
):
    handle_notification_for_user(
        user_id="u-1",
        current_metrics=NotificationMetrics(
            gross_sales=99.99, units_sold=55, wishlists=22
        ),
        last_notification=LastNotification(
            notification_metrics=NotificationMetrics(
                gross_sales=88.88, units_sold=42, wishlists=10
            ),
            trigger_date=datetime.now()
            - timedelta(minutes=fake_config.min_time_between_notifications - 1),
        ),
        config=fake_config,
    )

    assert len(responses_mock.calls) == 0


# in case of failure we want to achieve at most once over at least once
def test_job_when_setting_users_last_notification_failed_should_not_send_notification(
    fake_config, responses_mock, one_signal_notification_mock
):
    responses_mock.put(
        re.compile(
            f"{fake_config.user_service_url}/user/.*/key-value/data_pipeline/last_notification/no-lock"
        ),
        status=500,
    )

    try:
        handle_notification_for_user(
            user_id="u-1",
            current_metrics=NotificationMetrics(
                gross_sales=99.99, units_sold=55, wishlists=22
            ),
            last_notification=LastNotification(
                notification_metrics=NotificationMetrics(
                    gross_sales=88.88, units_sold=42, wishlists=10
                ),
                trigger_date=datetime.now()
                - timedelta(minutes=fake_config.min_time_between_notifications),
            ),
            config=fake_config,
        )
    except Exception:
        pass

    assert len(responses_mock.calls) == 1
    key_value_req = responses_mock.calls[0].request
    assert (
        key_value_req.url
        == f"{fake_config.user_service_url}/user/u-1/key-value/data_pipeline/last_notification/no-lock"
    )


def test_job_when_key_value_schema_drifted_should_update_key_value_with_current_metrics_but_not_send_notification(
    fake_config,
    responses_mock,
    get_legacy_user_id_mock,
    key_value_set_mock,
    key_value_get_mock_factory,
):
    key_value_get_mock_factory({"gross_sales": 77.77})

    handle_notifications(
        [5],
        NotificationMetrics(gross_sales=99.99, units_sold=55, wishlists=22),
        fake_config,
    )

    assert len(responses_mock.calls) == 4
    key_value_req = responses_mock.calls[3].request
    assert (
        key_value_req.url
        == f"{fake_config.user_service_url}/user/u-1/key-value/data_pipeline/last_notification/no-lock"
    )
    assert json.loads(json.loads(key_value_req.body)["value"])[
        "notification_metrics"
    ] == {"gross_sales": 99.99, "units_sold": 55, "wishlists": 22}


def test_job_when_user_does_not_exists_in_us_should_disregard_that_user(
    fake_config,
    responses_mock,
):
    responses_mock.get(
        re.compile(f"{fake_config.user_service_url}/user/legacy/.*"), status=404
    )

    handle_notifications(
        [5],
        NotificationMetrics(gross_sales=99.99, units_sold=55, wishlists=22),
        fake_config,
    )

    assert len(responses_mock.calls) == 1


def test_job_when_user_does_not_have_wishlists_should_send_new_wishlists_equal_zero(
    fake_config, responses_mock, key_value_set_mock, one_signal_notification_mock
):
    handle_notification_for_user(
        user_id="u-1",
        current_metrics=NotificationMetrics(
            gross_sales=99.99, units_sold=55, wishlists=None
        ),
        last_notification=LastNotification(
            notification_metrics=NotificationMetrics(
                gross_sales=88.88, units_sold=40, wishlists=None
            ),
            trigger_date=datetime.now()
            - timedelta(minutes=fake_config.min_time_between_notifications),
        ),
        config=fake_config,
    )

    one_signal_req = responses_mock.calls[1].request
    assert one_signal_req.url == f"{fake_config.one_signal_url}/"
    assert json.loads(one_signal_req.body)["custom_data"] == {
        "new_revenue": "$11.11",
        "new_units": "15",
        "new_wishlists": "0",
    }
    assert json.loads(one_signal_req.body)["data"] == {
        "new_revenue": "11.11",
        "new_units": "15",
        "new_wishlists": "0",
    }


def test_job_when_there_is_no_data_is_shard_should_not_send_notification_or_update_key_value(
    fake_config, responses_mock, get_notification_metrics_mock_factory
):
    get_notification_metrics_mock_factory(None, None, None)

    run(
        JobInput(
            job_guid="awe-vsdf-221",
            pipeline_guids=["awe-vsdf-221"],
            target={"shard_id": "sadf3-eaf3-a3rwa"},
            execution_try_count=0,
        ),
        fake_config,
    )

    assert len(responses_mock.calls) == 1


@freeze_time("2024-04-15T12:46:25.146841Z")
def test_job_when_shard_not_empty_should_set_last_processing_flag_for_all_users_in_shard(
    fake_config,
    responses_mock,
    get_legacy_user_id_mock,
    key_value_set_mock,
    key_value_get_mock_factory,
):
    key_value_get_mock_factory({"gross_sales": 77.77})

    handle_notifications(
        [5, 7],
        NotificationMetrics(gross_sales=99.99, units_sold=55, wishlists=22),
        fake_config,
    )

    assert len(responses_mock.calls) == 8
    key_value_req = responses_mock.calls[1].request
    assert (
        key_value_req.url
        == f"{fake_config.user_service_url}/user/u-1/key-value/data_pipeline/last_processing/no-lock"
    )
    key_value_req = responses_mock.calls[5].request
    assert (
        key_value_req.url
        == f"{fake_config.user_service_url}/user/u-1/key-value/data_pipeline/last_processing/no-lock"
    )
    assert json.loads(json.loads(key_value_req.body)["value"]) == {
        "processing_date": "2024-04-15T12:46:25.146841Z"
    }
