import json
import re

import pytest
import responses


@pytest.fixture
def fake_config():
    class FakeConfig:
        dataset_manager_url = "https://dataset-manager.not_existing"
        dataset_manager_api_key = "dataset-manager-api-key"
        user_service_url = "https://user-service.not_existing"
        user_service_api_key = "user-service-api-key"
        one_signal_url = "https://onesignal.not_existing"
        one_signal_template_id: str = "template-id"
        one_signal_api_key = "one-signal-api-key"
        one_signal_app_id = "app-id"

        min_time_between_notifications = 30

    return FakeConfig()


@pytest.fixture
def responses_mock():
    with responses.RequestsMock(assert_all_requests_are_fired=False) as mock:
        yield mock


@pytest.fixture
def key_value_set_mock(fake_config, responses_mock):
    responses_mock.put(
        re.compile(
            f"{fake_config.user_service_url}/user/.*/key-value/data_pipeline/last_notification/no-lock"
        )
    )
    responses_mock.put(
        re.compile(
            f"{fake_config.user_service_url}/user/.*/key-value/data_pipeline/last_processing/no-lock"
        )
    )


@pytest.fixture
def one_signal_notification_mock(fake_config, responses_mock):
    responses_mock.post(
        fake_config.one_signal_url,
        json={
            "id": "ca29f3c8-61c6-4314-ac51-39ec7abb66f6",
            "external_id": None,
        },
    )


@pytest.fixture
def get_legacy_user_id_mock(fake_config, responses_mock):
    responses_mock.get(
        re.compile(f"{fake_config.user_service_url}/user/legacy/.*"),
        json={"id": "u-1"},
    )


@pytest.fixture
def key_value_get_mock_factory(fake_config, responses_mock):
    def _key_value_get_mock(notification_metrics: dict):
        responses_mock.get(
            re.compile(
                f"{fake_config.user_service_url}/user/.*/key-value/data_pipeline/last_notification"
            ),
            json={
                "value": json.dumps(
                    {
                        "trigger_date": "2024-03-11 21:21:30.507164",
                        "notification_metrics": notification_metrics,
                    }
                )
            },
        )

    return _key_value_get_mock


@pytest.fixture
def get_notification_metrics_mock_factory(fake_config, responses_mock):
    def _get_notification_metrics_mock(gross_sales, units, wishlists):
        responses_mock.post(
            re.compile(f"{fake_config.dataset_manager_url}/shard/.*/query"),
            json=[
                {
                    "[gross_sales]": gross_sales,
                    "[units]": units,
                    "[wishlists]": wishlists,
                }
            ],
        )

    return _get_notification_metrics_mock
