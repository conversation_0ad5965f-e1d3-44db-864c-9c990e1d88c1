from pathlib import Path

from data_sdk.config import DLSConfig, LocalConfig
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Config(BaseSettings):
    model_config = SettingsConfigDict(env_nested_delimiter="__")

    hostname: str = "localhost"

    env: str = "local"
    app_version: str = "1.0.0"
    job_name: str = "ppt-gold-job"

    docker_tag: str = ""
    docker_build_timestamp: str = ""

    dev_username: str = ""

    reader_cfg: DLSConfig | LocalConfig = Field(
        discriminator="type",
        default=LocalConfig(local_dir=Path("playground")),
    )
    writer_cfg: DLSConfig | LocalConfig = Field(
        discriminator="type",
        default=LocalConfig(local_dir=Path("playground/ppt_gold")),
    )

    dry_run: bool = False
    run_type: str = "cp"


class ExternalDataStorageConfig(BaseSettings):
    storage_account_name: str = "dlsaggregatedprodr9"
    container_name: str = "ppt-gold"
    base_dir: str = ""
