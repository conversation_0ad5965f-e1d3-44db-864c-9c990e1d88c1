from enum import Enum


class StringLength(Enum):
    """Predefined lengths for various types of text data that
    can be present in reports. Should be used for validation checks
    that would verify if data doesn't excede expected length.

    For data which length is known beforehand (e.g. `country_code` etc.)
    exact length should be used in those checks.

    """

    TINY = 32
    SMALL = 255
    MEDIUM = 511
    HUGE = 1023
