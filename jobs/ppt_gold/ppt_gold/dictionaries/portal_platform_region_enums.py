from data_sdk.domain.stores import Store


def store_to_snake_case(store_name: Store) -> str:
    """
    Convert a StoreName enum to a snake_case string.
    """
    return store_name.value.replace(" ", "_").replace("/", "_").lower()


ppri_to_store_name_dict = {
    171010: store_to_snake_case(Store.STEAM),
    151511: store_to_snake_case(Store.NINTENDO_SWITCH_AMERICAS),
    151513: store_to_snake_case(Store.NINTENDO_SWITCH_EUROPE_AUSTRALIA),
    151514: store_to_snake_case(Store.NINTENDO_SWITCH_JAPAN),
    151520: store_to_snake_case(Store.NINTENDO_SWITCH_CHINA),
    151521: store_to_snake_case(Store.NINTENDO_SWITCH_KOREA),
    151522: store_to_snake_case(Store.NINTENDO_SWITCH_TAIWAN_HONG_KONG),
}
