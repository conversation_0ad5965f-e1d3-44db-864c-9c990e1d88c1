import sentry_sdk

sentry_sdk.init(traces_sample_rate=1.0)

import logging

from pipeline_sdk.job import JobInput, JobOutput, JobRunner
from pipeline_sdk.monitoring.elastic_tracer import elastic_tracer
from pipeline_sdk.monitoring.logs import configure_logger

from ppt_gold.config import Config
from ppt_gold.job import run, validate_message

configure_logger(
    "ppt_gold",
    custom_loggers_config={
        "data_sdk": {"level": "INFO"},
    },
)
log = logging.getLogger(__name__)


def handler(job_input: JobInput) -> JobOutput:
    config = Config()

    log.info("Config: %s", config.model_dump())
    log.info(
        "Starting ppt_gold: %s (version %s, build ts: %s)",
        job_input.job_guid,
        config.docker_tag,
        config.docker_build_timestamp,
    )
    params = validate_message(job_input.target)
    run(params=params, config=config)

    log.info("Finishing ppt_gold: %s", job_input.job_guid)
    return JobOutput({"studio_id": params.studio_id})


if __name__ == "__main__":
    with elastic_tracer():
        runner = JobRunner(handler)
        runner.run()
