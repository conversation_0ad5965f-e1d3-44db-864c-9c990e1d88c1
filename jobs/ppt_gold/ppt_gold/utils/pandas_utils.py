import pandas as pd
import pandera as pa


def validate_schema(df: pd.DataFrame, schema: pa.DataFrameSchema) -> pd.DataFrame:
    """
    Validates a dataframe with a given schema.
    """
    if not df.empty:
        return schema.validate(df)
    return df


def get_release_date_and_last_date(df: pd.DataFrame, drop=None) -> pd.DataFrame:
    release_date_and_last_date_df = (
        df[(df["gross_sales"] > 0) & (df["units_sold"] > 0)][["sku_studio", "date"]]
        .groupby(["sku_studio"])
        .agg({"date": ["max", "min"]})
        .reset_index()
    )
    release_date_and_last_date_df.columns = [
        "sku_studio",
        "last_sales_date",
        "release_date",
    ]
    release_date_and_last_date_df["last_sales_date"] = release_date_and_last_date_df[
        "last_sales_date"
    ].astype(str)
    release_date_and_last_date_df["release_date"] = release_date_and_last_date_df[
        "release_date"
    ].astype(str)
    release_date_and_last_date_df["unique_sku_id"] = release_date_and_last_date_df[
        "sku_studio"
    ]
    if drop:
        release_date_and_last_date_df = release_date_and_last_date_df.drop(drop, axis=1)
    return release_date_and_last_date_df
