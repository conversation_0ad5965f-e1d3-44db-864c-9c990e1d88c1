from io import BytesIO

import polars as pl
from azure.storage.blob import BlobServiceClient
from data_sdk.connectors.azure_identity import credential


def save_dataframe_to_blob_parquet(storage_account_name, container_name, blob_name, df):
    account_url = f"https://{storage_account_name}.blob.core.windows.net"
    blob_service_client = BlobServiceClient(
        account_url=account_url, credential=credential
    )
    container_client = blob_service_client.get_container_client(container_name)

    parquet_buffer = BytesIO()
    df.to_parquet(parquet_buffer, engine="pyarrow", index=False)

    blob_client = container_client.get_blob_client(blob_name)
    parquet_buffer.seek(0)
    blob_client.upload_blob(parquet_buffer.getvalue(), overwrite=True)


# TODO: Add to data_sdk
def _get_latest_blob(container_name, container_client, subdirectory):
    # Ensure the subdirectory string ends with a slash
    if not subdirectory.endswith("/"):
        subdirectory += "/"

    # List all blobs in the container with the specified subdirectory as prefix
    blobs = list(container_client.list_blobs(name_starts_with=subdirectory))
    if not blobs:
        return None

    # Sort blobs by last modified date in descending order and select the latest
    latest_blob = max(blobs, key=lambda blob: blob.last_modified)
    return latest_blob.name


def read_parquet_from_blob(
    storage_account_name,
    container_name,
    subdirectory,
    reader_type="polars",
    credential=None,
) -> pl.LazyFrame | None:
    account_url = f"https://{storage_account_name}.blob.core.windows.net"
    blob_service_client = BlobServiceClient(
        account_url=account_url, credential=credential
    )
    container_client = blob_service_client.get_container_client(container_name)

    # Get the name of the latest blob within the specified subdirectory
    latest_blob_name = _get_latest_blob(container_name, container_client, subdirectory)
    if not latest_blob_name:
        print("No blobs found in the specified subdirectory.")
        return pl.LazyFrame()

    # Get a client for the latest blob
    blob_client = container_client.get_blob_client(latest_blob_name)

    # Download the latest blob data
    blob_data = blob_client.download_blob().readall()
    if blob_data is None:
        return pl.LazyFrame()

    # Convert blob_data to BytesIO
    blob_data_io = BytesIO(blob_data)

    # Check if the BytesIO object is empty
    if blob_data_io.getbuffer().nbytes == 0:
        return pl.LazyFrame()
    return pl.read_parquet(blob_data_io).lazy()
