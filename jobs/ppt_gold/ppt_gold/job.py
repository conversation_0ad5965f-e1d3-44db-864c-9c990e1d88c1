import datetime
import logging
import traceback

from data_sdk.aggregator import BaseAggregator, process_aggregators
from data_sdk.custom_partition.reader import CustomPartitionReader
from data_sdk.custom_partition.writer import CustomPartitionsWriter
from data_sdk.domain import Portal
from data_sdk.domain.domain_types import StudioId
from pydantic import BaseModel, ValidationError

from ppt_gold.aggregators import (
    DimSkuAggregator,
    DimSkuPptAggregator,
    DimStudioAggregator,
    FactBasePriceAggregator,
    FactDiscountDepthInputAggregator,
    FactDiscountsIntegratedAggregator,
    FactEventPeriodsAggregator,
    FactPotentialRevenueAggregator,
    FactPotentialRevenueDevAggregator,
)
from ppt_gold.config import Config
from ppt_gold.exceptions import InvalidMessage

log = logging.getLogger(__name__)


class Params(BaseModel):
    studio_id: int


def validate_message(params) -> Params:
    log.info(f"Validating message: {params}")
    try:
        return Params.model_validate(params)
    except ValidationError:
        log.exception("Received invalid message %s", str(params))
        raise InvalidMessage(traceback.format_exc())


common_aggregators: list[type[BaseAggregator]] = [
    FactDiscountDepthInputAggregator,
    DimSkuAggregator,
    FactDiscountsIntegratedAggregator,
    FactEventPeriodsAggregator,
    FactBasePriceAggregator,
]

prod_only_aggregators: list[type[BaseAggregator]] = [
    # These aggregators require prod data
    FactPotentialRevenueAggregator,
    DimStudioAggregator,
    DimSkuPptAggregator,
]
dev_only_aggregators: list[type[BaseAggregator]] = [
    FactPotentialRevenueDevAggregator,
]


def run(params: Params, config: Config, portal: Portal | None = None):
    reader = CustomPartitionReader.get_reader(config.reader_cfg)
    writer = CustomPartitionsWriter.get_writer(config.writer_cfg)

    creation_datetime = datetime.datetime.now(datetime.UTC)
    aggregators = (
        (common_aggregators + prod_only_aggregators)
        if config.env == "prod"
        else (common_aggregators + dev_only_aggregators)
    )
    process_aggregators(
        aggregators=aggregators,
        reader=reader,
        writer=writer,
        creation_datetime=creation_datetime,
        studio_id=StudioId(params.studio_id),
        portal=portal,
    )
