import logging

import pandas as pd
import polars as pl
from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.ppr import get_portal_platform_region_id
from data_sdk.domain.tables import (
    LegacyDimSKUsTable,
    LegacyFactSalesTable,
    ObservationDiscountsTable,
    TableWithGoldPartitions,
)
from data_sdk.validators.schemas import NoCheckSchema
from events_calculator import calculate_cooldown
from events_calculator.api_v2.calculate import find_unused_slots
from events_calculator.api_v2.model.discount_event import (
    DiscountEvent,
    DiscountEventType,
)
from pandera.typing import DataFrame

from ppt_gold.aggregators.tables import PPTGoldTable
from ppt_gold.dictionaries.portal_platform_region_enums import ppri_to_store_name_dict
from ppt_gold.utils.pandas_utils import get_release_date_and_last_date

log = logging.getLogger(__name__)


class FactEventPeriodsTable(TableWithGoldPartitions):
    table_name = PPTGoldTable.FACT_EVENT_PERIODS
    model = NoCheckSchema


def _prepare_fact_discounts(
    observation_discounts: ObservationDiscountsTable,
) -> pd.DataFrame:
    list_of_columns = [
        "report_id",
        "create_time",
        "update_time",
        "base_sku_id",
        "unique_sku_id",
        "studio_id",
        "discount_depth",
        "discount_type",
        "datetime_from",
        "datetime_to",
        "is_event_joined",
        "triggers_cooldown",
        "major",
        "event_name",
        "base_event_id",
        "unique_event_id",
        "promo_length",
        "max_discount_percentage",
        "price_increase_time",
        "portal_platform_region_id",
    ]

    def calculate_promo_length(df: pd.DataFrame) -> pd.Series:
        return (
            pd.to_datetime(df["datetime_to"]) - pd.to_datetime(df["datetime_from"])
        ).dt.total_seconds()

    def concatenate_portal_platform_region(df: pd.DataFrame) -> pd.Series:
        return df.apply(
            lambda row: f"{row['portal']}:{row['platform']}:{row['region']}", axis=1
        )

    def map_portal_platform_region_id(df: pd.DataFrame) -> pd.Series:
        return df["portal_platform_region"].map(get_portal_platform_region_id)

    observation_discounts_df = observation_discounts.df.to_pandas()
    observation_discounts_df["create_time"] = pd.to_datetime(
        observation_discounts_df["create_time"]
    ).dt.strftime("%Y%m%dT%H%M%SZ")
    observation_discounts_df["update_time"] = pd.to_datetime(
        observation_discounts_df["update_time"]
    ).dt.strftime("%Y%m%dT%H%M%SZ")
    return observation_discounts_df.assign(
        promo_length=calculate_promo_length,
        portal_platform_region=concatenate_portal_platform_region,
        portal_platform_region_id=map_portal_platform_region_id,
    )[list_of_columns]


class FactEventPeriodsAggregator(BaseAggregator):
    table_cls = FactEventPeriodsTable

    def __init__(
        self,
        dim_sku: LegacyDimSKUsTable,
        fact_sales: LegacyFactSalesTable,
        observation_discounts: ObservationDiscountsTable,
    ) -> None:
        self._dim_sku = dim_sku
        self._fact_sales = fact_sales
        self._observation_discounts = observation_discounts

    def _aggregate(self) -> pl.DataFrame:
        dim_sku = self._dim_sku.df.to_pandas()
        fact_sales = self._fact_sales.df.to_pandas()

        fact_discounts = _prepare_fact_discounts(self._observation_discounts)

        if fact_discounts.empty or fact_sales.empty:
            log.info("No discounts found in the dataset.")
            return pl.DataFrame()
        fact_discounts["store_name"] = fact_discounts["portal_platform_region_id"].map(
            ppri_to_store_name_dict
        )
        list_available_stores = fact_discounts["store_name"].unique().tolist()

        release_date_and_last_date_df = get_release_date_and_last_date(fact_sales)
        dim_sku, eligible_sku_list = _get_sku_list_from_dim_sku(
            dim_sku,
            release_date_and_last_date_df,
        )
        fact_discounts = _create_rename_edit_validate_columns(fact_discounts)
        fact_scrapped = _get_fact_periods(fact_discounts, list_available_stores)
        if fact_scrapped.empty:
            log.info(
                "Discount events overlap returning empty DataFrame."
            )  # TODO: Edit find_unused in events_calculator to SKUs with overlapping events until we find solution
            return pl.DataFrame()
        fact_scrapped = _rename_add_fill_merge_columns(fact_scrapped, dim_sku)
        fact_scrapped["gso"] = fact_scrapped["gso"].fillna(0).astype(int)

        return pl.DataFrame(
            fact_scrapped.sort_values(
                by=["unique_sku_id", "datetime_from"], ignore_index=True
            )
        )


def _get_sku_list_from_dim_sku(
    dim_sku_df: pd.DataFrame,
    release_date_and_last_date_df: pd.DataFrame,
):
    if "unique_sku_id" not in dim_sku_df.columns:
        dim_sku_df = dim_sku_df.rename(columns={"sku_studio": "unique_sku_id"})
    dim_sku_df["human_name_clean"] = (
        dim_sku_df["human_name"].str.lower().str.replace(r"\W|_| ", "", regex=True)
    )

    dim_sku_df = dim_sku_df[
        (dim_sku_df["gso"] > 0)
        & (~dim_sku_df["human_name_clean"].str.contains("commerciallicense"))
        & (dim_sku_df["sku_type"] == "SALES")
    ]

    dim_sku_df = pd.merge(
        dim_sku_df, release_date_and_last_date_df, on="unique_sku_id", how="left"
    )

    dim_sku_df["base_sku_id_ints"] = pd.to_numeric(
        dim_sku_df["base_sku_id"], errors="coerce"
    )
    dim_sku_df = dim_sku_df.sort_values(
        by=[
            "portal_platform_region_id",
            "studio_id",
            "human_name",
            "last_sales_date",
            "release_date",
            "base_sku_id_ints",
        ],
        ascending=True,
        na_position="first",
        ignore_index=True,
    ).drop_duplicates(
        subset=["portal_platform_region_id", "studio_id", "human_name"], keep="last"
    )

    return dim_sku_df, dim_sku_df["unique_sku_id"].unique().tolist()


def _create_rename_edit_validate_columns(df):
    df["discount_event_type"] = df.apply(
        lambda x: (
            DiscountEventType.STORE_DISCOUNT.value
            if x["discount_type"] == "store"
            else DiscountEventType.DISCOUNT.value
        ),
        axis=1,
    )
    df = df.rename(columns={"event_name": "name"})
    df["event_origin"] = "scraped"
    df["datetime_from"] = (
        pd.to_datetime(df["datetime_from"]).dt.tz_localize(None).astype(str)
    )
    df["datetime_to"] = (
        pd.to_datetime(df["datetime_to"]).dt.tz_localize(None).astype(str)
    )
    df = DataFrame[DiscountEvent](df)
    return df


def _get_fact_periods(fact_discounts, list_available_stores):
    fact_scrapped = pd.DataFrame()
    for store_name in list_available_stores:
        fact_discounts_store = fact_discounts[
            fact_discounts["store_name"] == store_name
        ].copy()

        store_name_cooldown_temp = calculate_cooldown(store_name, fact_discounts_store)

        store_name_cooldown_temp["event_type"] = "cooldown"
        try:
            store_name_unused_temp = find_unused_slots(  # OR USE the unused function
                store_name,
                fact_discounts_store,
                cool_df=store_name_cooldown_temp,
            )
            store_name_unused_temp["name"] = "unused"
            store_name_unused_temp["event_type"] = "unused"

            fact_scrapped = pd.concat(
                [
                    fact_scrapped,
                    store_name_unused_temp,
                    store_name_cooldown_temp,
                    fact_discounts_store,
                ],
                ignore_index=True,
            )
        except Exception:
            continue
    return fact_scrapped


def _rename_add_fill_merge_columns(fact_scrapped, dim_sku):
    fact_scrapped = fact_scrapped.copy()
    fact_scrapped["event_name"] = fact_scrapped["name"]
    fact_scrapped["triggers_cooldown"] = (
        fact_scrapped["triggers_cooldown"].fillna(False).infer_objects(copy=False)
    )
    fact_scrapped["major"] = (
        fact_scrapped["major"].infer_objects(copy=False).fillna(False)
    )
    dim_sku["unique_sku_id"] = dim_sku["sku_studio"]

    fact_scrapped["event_id"] = (
        fact_scrapped["unique_sku_id"]
        + ":"
        + fact_scrapped["datetime_from"].astype(str)
    )
    fact_scrapped = fact_scrapped.merge(
        dim_sku[
            [
                "unique_sku_id",
                "gso",
                "release_date",
                "last_sales_date",
                "product_name",
                "human_name",
                "product_id",
                "portal_platform_region",
            ]
        ],
        on="unique_sku_id",
        how="left",
    )
    fact_scrapped = fact_scrapped.drop(
        columns=[
            "max_discount_percentage",
            "price_increase_time",
            "base_event_id",
            "report_id",
        ]
    )
    return fact_scrapped
