import random
from datetime import datetime

import polars as pl
from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.tables import (
    LegacyDimSKUsTable,
    LegacyDimStudioTable,
    TableWithGoldPartitions,
)
from data_sdk.validators.schemas import NoCheckSchema

from ppt_gold.aggregators.tables import PPTGoldTable
from ppt_gold.dictionaries.portal_platform_region_enums import ppri_to_store_name_dict


class FactPotentialRevenueTable(TableWithGoldPartitions):
    table_name = PPTGoldTable.FACT_POTENTIAL_REVENUE
    model = NoCheckSchema


class FactPotentialRevenueDevAggregator(BaseAggregator):
    table_cls = FactPotentialRevenueTable

    def __init__(
        self, dim_sku: LegacyDimSKUsTable, dim_studio: LegacyDimStudioTable
    ) -> None:
        self._dim_sku = dim_sku
        self._dim_studio = dim_studio

    def _aggregate(self) -> pl.DataFrame:
        columns = [
            "studio_id",
            "product_id",
            "total_estimated_additional_sales",
            "sales_12m_eligible_skus",
            "n_products",
            "n_skus",
            "n_portals",
            "ppt",
            "ppt_saas",
            "saas",
            "gso_eligible_skus",
            "gso_all_skus_portals",
            "last_sales_date",
            "end_date_period_of_interest",
        ]

        dim_studio = self._dim_studio.df
        dim_sku = self._dim_sku.df.lazy()
        if dim_studio.is_empty() or dim_sku.collect().is_empty():
            return pl.DataFrame({}, schema={col: pl.Utf8 for col in columns})

        dim_sku_dev = dim_sku.filter(
            pl.col("portal_platform_region_id").is_in(
                list(ppri_to_store_name_dict.keys())
            )
        )
        dim_sku_c = dim_sku_dev.collect()
        if dim_sku_c.is_empty():
            return pl.DataFrame({}, schema={col: pl.Utf8 for col in columns})
        studio_id = dim_studio["studio_id"].unique()[0]

        estimated_additional_sales = random.uniform(30000, 10000000)
        product_id = dim_sku_c["product_id"].unique()[0]
        unique_sku_id = dim_sku_c["sku_studio"].unique()[0]
        filtered_external_fact_potential_revenue = pl.LazyFrame(
            {
                "studio_id": [studio_id],
                "estimated_additional_sales": [estimated_additional_sales],
                "gso_sku": [
                    random.randint(
                        int(estimated_additional_sales * 1.5),
                        int(estimated_additional_sales * 3),
                    )
                ],
                "gross_sales": [
                    random.randint(
                        int(estimated_additional_sales / 1.5),
                        int(estimated_additional_sales * 1.5),
                    )
                ],
                "n_products": [random.randint(1, 10)],
                "n_skus": [random.randint(1, 10)],
                "n_portals": [random.randint(1, 10)],
                "product_id": [product_id],
                "unique_sku_id": [unique_sku_id],
                "portal": ["Steam"],
                "gso_studio": [
                    random.randint(
                        int(estimated_additional_sales * 1.5),
                        int(estimated_additional_sales * 3),
                    )
                ],
                "last_sales_date": [datetime.today().strftime("%Y-%m-%d")],
                "end_date_period_of_interest": ["2024-03-31"],
            }
        )

        filtered_external_fact_potential_revenue = (
            filtered_external_fact_potential_revenue.collect()
        )
        if filtered_external_fact_potential_revenue.is_empty():
            return pl.DataFrame({}, schema={col: pl.Utf8 for col in columns})
        # convert back to lazy
        filtered_external_fact_potential_revenue = pl.DataFrame(
            filtered_external_fact_potential_revenue
        ).lazy()

        grouped_fact_potential_revenue = _group_sales_by_studio(
            filtered_external_fact_potential_revenue
        )
        grouped_fact_potential_revenue = _add_ppt_flag_column(
            grouped_fact_potential_revenue
        )
        return grouped_fact_potential_revenue.collect()


def _group_sales_by_studio(
    filtered_external_fact_potential_revenue: pl.LazyFrame,
) -> pl.LazyFrame:
    gso_sku = filtered_external_fact_potential_revenue.unique(
        subset=["studio_id", "unique_sku_id"], keep="first"
    ).select(["studio_id", "gso_sku"])

    gso_sku = gso_sku.group_by(["studio_id"]).agg(
        [
            pl.sum("gso_sku").alias("gso_eligible_skus"),
        ]
    )

    grouped = filtered_external_fact_potential_revenue.group_by("studio_id").agg(
        [
            pl.sum("estimated_additional_sales")
            .alias("total_estimated_additional_sales")
            .cast(pl.Float32)
            .round(2),
            pl.sum("gross_sales")
            .alias("sales_12m_eligible_skus")
            .cast(pl.Float32)
            .round(2),
            pl.n_unique("product_id").alias("n_products"),
            pl.n_unique("unique_sku_id").alias("n_skus"),
            pl.n_unique("portal").alias("n_portals"),
            pl.first("gso_studio").alias("gso_all_skus_portals"),
            pl.max("last_sales_date").alias("last_sales_date"),
            pl.max("end_date_period_of_interest").alias("end_date_period_of_interest"),
        ]
    )
    return grouped.join(
        gso_sku.select(["studio_id", "gso_eligible_skus"]),
        on="studio_id",
        how="left",
    )


def _filter_for_studio(
    external_fact_potential_revenue: pl.LazyFrame, studio_id: int
) -> pl.LazyFrame:
    return external_fact_potential_revenue.filter(pl.col("studio_id") == studio_id)


def _add_ppt_flag_column(
    external_fact_potential_revenue: pl.LazyFrame,
) -> pl.LazyFrame:
    max_number_products_ppt = 30
    saas_max_potential_extra_revenue = 600000
    ppt_minimum_potential_extra_revenue = 300000

    external_fact_potential_revenue = external_fact_potential_revenue.with_columns(
        [
            (
                (pl.col("n_products") <= max_number_products_ppt)
                & (
                    pl.col("total_estimated_additional_sales")
                    >= saas_max_potential_extra_revenue
                )
            ).alias("ppt"),
            (
                (pl.col("n_products") <= max_number_products_ppt)
                & (
                    pl.col("total_estimated_additional_sales")
                    >= ppt_minimum_potential_extra_revenue
                )
                & (
                    pl.col("total_estimated_additional_sales")
                    < saas_max_potential_extra_revenue
                )
            ).alias("ppt_saas"),
            (
                (pl.col("n_products") > max_number_products_ppt)
                | (
                    pl.col("total_estimated_additional_sales")
                    < ppt_minimum_potential_extra_revenue
                )
            ).alias("saas"),
        ]
    )

    return external_fact_potential_revenue
