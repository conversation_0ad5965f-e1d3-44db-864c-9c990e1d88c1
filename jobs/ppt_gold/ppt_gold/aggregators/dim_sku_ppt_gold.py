import logging

import polars as pl
from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.tables import (
    LegacyDimSKUsTable,
    LegacyDimStudioTable,
    LegacyFactDetectedEventsTable,
    LegacyFactDiscountsTable,
    SilverSKUsTable,
    TableWithGoldPartitions,
)
from data_sdk.validators.schemas import NoCheckSchema

from ppt_gold.aggregators.dim_studio_ppt_gold import generate_dim_studio
from ppt_gold.aggregators.tables import PPTGoldTable
from ppt_gold.config import ExternalDataStorageConfig

external_config = ExternalDataStorageConfig(base_dir="external_data/users")

log = logging.getLogger(__name__)


class DimSkuTable(TableWithGoldPartitions):
    table_name = PPTGoldTable.DIM_SKU
    model = NoCheckSchema


class DimSkuAggregator(BaseAggregator):
    table_cls = DimSkuTable

    def __init__(
        self,
        dim_sku: LegacyDimSKUsTable,
        fact_discounts: LegacyFactDiscountsTable,
        fact_detected_events: LegacyFactDetectedEventsTable,
        dim_studio: LegacyDimStudioTable,
        silver_sku: SilverSKUsTable,
    ) -> None:
        self._dim_sku = dim_sku
        self._fact_discounts = fact_discounts
        self._dim_studio = dim_studio
        self._fact_detected_events = fact_detected_events
        self._silver_sku = silver_sku

    def _aggregate(self) -> pl.DataFrame:
        return generate_dim_sku(
            dim_sku=self._dim_sku.df,
            fact_discounts=self._fact_discounts.df,
            fact_detected_events=self._fact_detected_events.df,
            dim_studio=self._dim_studio.df,
            silver_sku=self._silver_sku.df,
        )


def generate_dim_sku(
    dim_sku: pl.DataFrame,
    fact_discounts: pl.DataFrame,
    fact_detected_events: pl.DataFrame,
    dim_studio: pl.DataFrame,
    silver_sku: pl.DataFrame,
) -> pl.DataFrame:
    columns = [
        "base_sku_id",
        "human_name",
        "store_id",
        "sku_studio",
        "studio_id",
        "portal_platform_region",
        "portal_platform_region_id",
        "product_id",
        "human_name_indicator",
        "sku_type",
        "product_name",
        "package_name",
        "custom_group",
        "product_type",
        "ratio",
        "gso",
        "is_baseline_precalculated",
        "has_detected_detected_events",
        "has_scraped_discounts",
        "human_name_clean",
        "is_partner",
        "company_name",
        "release_date",
    ]

    if dim_sku.is_empty():
        return pl.DataFrame({}, schema={col: pl.Utf8 for col in columns})

    if fact_discounts.is_empty():
        dim_sku = dim_sku.with_columns(pl.lit(False).alias("has_scraped_discounts"))
    else:
        fact_discounts = fact_discounts.with_columns(
            pl.lit(True).alias("has_scraped_discounts")
        )
        dim_sku = dim_sku.join(
            fact_discounts.unique(subset="unique_sku_id").select(
                ["unique_sku_id", "has_scraped_discounts"]
            ),
            left_on="sku_studio",
            right_on="unique_sku_id",
            how="left",
        )
        dim_sku = dim_sku.with_columns(pl.col("has_scraped_discounts").fill_null(False))

    if fact_detected_events.is_empty():
        dim_sku = dim_sku.with_columns(
            pl.lit(False).alias("has_detected_detected_events")
        )
    else:
        fact_detected_events = fact_detected_events.with_columns(
            pl.lit(True).alias("has_detected_detected_events")
        )
        dim_sku = dim_sku.join(
            fact_detected_events.unique(subset="unique_sku_id").select(
                ["unique_sku_id", "has_detected_detected_events"]
            ),
            left_on="sku_studio",
            right_on="unique_sku_id",
            how="left",
        )
        dim_sku = dim_sku.with_columns(
            pl.col("has_detected_detected_events").fill_null(False)
        )
    dim_sku = dim_sku.sort("gso", descending=True)

    dim_sku = dim_sku.with_columns(
        pl.col("human_name")
        .str.to_lowercase()
        .str.replace_all(
            r"[^\p{L}\p{N}]", ""
        )  # removes anything that's not a letter or number prsrves unicode and other characters
        .alias("human_name_clean")
    )
    dim_studio_gold = generate_dim_studio(dim_studio=dim_studio)
    if dim_studio_gold.is_empty():
        return pl.DataFrame({}, schema={col: pl.Utf8 for col in columns})
    # cast studio_id to int
    dim_studio_gold = dim_studio_gold.with_columns(pl.col("studio_id").cast(pl.Int64))
    dim_sku = dim_sku.with_columns(
        pl.col("studio_id").cast(pl.Int64).alias("studio_id")
    )
    dim_sku = dim_sku.join(
        dim_studio_gold.select(["studio_id", "is_partner", "company_name"]),
        on="studio_id",
        how="left",
    )
    silver_sku = silver_sku.with_columns(pl.col("unique_sku_id").alias("sku_studio"))
    dim_sku = dim_sku.join(
        silver_sku.select(["sku_studio", "release_date"]),
        on="sku_studio",
        how="left",
    )
    return dim_sku
