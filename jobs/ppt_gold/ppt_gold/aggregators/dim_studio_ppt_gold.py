import logging

import polars as pl
from azure.core.exceptions import ServiceRequestError
from data_sdk.aggregator import BaseAggregator
from data_sdk.connectors.azure_identity import credential
from data_sdk.domain.tables import LegacyDimStudioTable, TableWithGoldPartitions
from data_sdk.validators.schemas import NoCheckSchema

from ppt_gold.aggregators.tables import PPTGoldTable
from ppt_gold.config import ExternalDataStorageConfig
from ppt_gold.utils.azure_storage_reader_writer import read_parquet_from_blob

external_config = ExternalDataStorageConfig(base_dir="external_data/users")

log = logging.getLogger(__name__)


class DimStudioTable(TableWithGoldPartitions):
    table_name = PPTGoldTable.DIM_STUDIO
    model = NoCheckSchema


class DimStudioAggregator(BaseAggregator):
    table_cls = DimStudioTable

    def __init__(self, dim_studio: LegacyDimStudioTable) -> None:
        self._dim_studio = dim_studio

    def _aggregate(self) -> pl.DataFrame:
        return generate_dim_studio(dim_studio=self._dim_studio.df)


def generate_dim_studio(dim_studio: pl.DataFrame) -> pl.DataFrame:
    columns = [
        "id",
        "studio_id",
        "legacy_id",
        "company_name",
        "email",
        "is_verified",
        "agreement_date",
        "first_name",
        "last_name",
        "is_test_account",
        "test_account",
        "is_partner",
        "studio_parent_id",
    ]

    if dim_studio.is_empty():
        return pl.DataFrame({}, schema={col: pl.Utf8 for col in columns})

    users = None
    try:
        users = read_parquet_from_blob(
            storage_account_name=external_config.storage_account_name,
            container_name=external_config.container_name,
            subdirectory=external_config.base_dir,
            credential=credential,
        )
    except ServiceRequestError as e:
        log.warning(
            (
                "Failed to read users from external storage: '%s'. "
                "This is expected for envs other than prod."
            ),
            e,
        )

    if users is None or users.collect().is_empty():
        return pl.DataFrame({}, schema={col: pl.Utf8 for col in columns})
    users = users.with_columns(pl.col("legacy_id").alias("studio_id"))
    columns_to_take_from_users = [
        "id",
        "studio_id",
        "legacy_id",
        "first_name",
        "last_name",
        "test_account",
        "is_partner",
    ]
    users = users.collect()
    users = users.with_columns(pl.col("studio_id").cast(pl.Int64))
    dim_studio = dim_studio.join(
        users.select(columns_to_take_from_users), on="studio_id", how="left"
    )
    dim_studio = dim_studio.with_columns(pl.col("is_partner").fill_null(False))

    return dim_studio
