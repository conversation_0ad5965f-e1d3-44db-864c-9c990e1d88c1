import logging

import polars as pl
from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.tables import (
    ExternalCountryCodesTable,
    LegacyFactSalesTable,
    TableWithGoldPartitions,
)
from data_sdk.validators.schemas import NoCheckSchema

from ppt_gold.aggregators.tables import PPTGoldTable
from ppt_gold.dictionaries.portal_platform_region_enums import ppri_to_store_name_dict

log = logging.getLogger(__name__)


class FactBasePriceTable(TableWithGoldPartitions):
    table_name = PPTGoldTable.FACT_BASE_PRICES_HISTORY
    model = NoCheckSchema


class FactBasePriceAggregator(BaseAggregator):
    table_cls = FactBasePriceTable

    def __init__(
        self,
        legacy_fact_sales: LegacyFactSalesTable,
        legacy_dim_country_code: ExternalCountryCodesTable,
    ) -> None:
        self._fact_sales = legacy_fact_sales
        self._dim_country_code = legacy_dim_country_code

    def _aggregate(self) -> pl.DataFrame:
        fact_sales = self._fact_sales.df.to_pandas()
        dim_country_code = self._dim_country_code.df.to_pandas()

        if fact_sales.empty:
            log.info("No discounts found in the dataset.")
            return pl.DataFrame()
        fact_sales["store_name"] = fact_sales["portal_platform_region_id"].map(
            ppri_to_store_name_dict
        )

        fact_sales_base_prices = _get_fact_base_prices(fact_sales)
        fact_sales_base_prices.columns = [
            "store_name",
            "sku_studio",
            "studio_id",
            "date",
            "country_code",
            "currency_code",
            "calculated_base_price_local_v2",
            "calculated_base_price_usd_v2",
            "calculated_base_price_local_v2_rounded",
            "base_price_local",
        ]
        fact_sales_base_prices = fact_sales_base_prices.merge(
            dim_country_code[["country_code", "country"]], on="country_code", how="left"
        )
        fact_sales_base_prices = _convert_usd_price_to_special_region_usd(
            fact_sales_base_prices
        )

        return pl.DataFrame(fact_sales_base_prices)


def _convert_usd_price_to_special_region_usd(fact_sales_base_prices):
    latam_countries = [
        "Belize",
        "El Salvador",
        "Guatemala",
        "Honduras",
        "Nicaragua",
        "Panama",
        "Argentina",
        "Bolivia",
        "Ecuador",
        "Guyana",
        "Paraguay",
        "Suriname",
        "Venezuela",
    ]

    fact_sales_base_prices.loc[
        (fact_sales_base_prices["country"].isin(latam_countries))
        & (fact_sales_base_prices["currency_code"] == "USD"),
        "currency_code",
    ] = "LATAM-USD"

    fact_sales_base_prices.loc[
        (fact_sales_base_prices["country"].isin(latam_countries))
        & (fact_sales_base_prices["currency_code"] == "USD"),
        "currency_code",
    ] = "LATAM-USD"

    mena_countries = [
        "Bahrain",
        "Egypt",
        "Iraq",
        "Jordan",
        "Lebanon",
        "Oman",
        "Palestine",
        "Turkey",
        "Yemen",
        "Algeria",
        "Libya",
        "Morocco",
        "Tunisia",
        "Sudan",
    ]

    fact_sales_base_prices.loc[
        (fact_sales_base_prices["country"].isin(mena_countries))
        & (fact_sales_base_prices["currency_code"] == "USD"),
        "currency_code",
    ] = "MENA-USD"

    cis_countries = [
        "Armenia",
        "Azerbaijan",
        "Belarus",
        "Georgia",
        "Kyrgyzstan",
        "Moldova",
        "Tajikistan",
        "Uzbekistan",
    ]

    fact_sales_base_prices.loc[
        (fact_sales_base_prices["country"].isin(cis_countries))
        & (fact_sales_base_prices["currency_code"] == "USD"),
        "currency_code",
    ] = "CIS-USD"

    sasia_countries = ["Bangladesh", "Bhutan", "Nepal", "Pakistan", "Sri Lanka"]

    fact_sales_base_prices.loc[
        (fact_sales_base_prices["country"].isin(sasia_countries))
        & (fact_sales_base_prices["currency_code"] == "USD"),
        "currency_code",
    ] = "SASIA-USD"

    return fact_sales_base_prices


def _get_fact_base_prices(df):
    df = df.copy()
    df = df[
        (df["gross_sales"] > 0) & (df["units_sold"] > 0) & (df["base_price_local"] > 0)
    ]
    df["calculated_base_price_local_v2"] = df["calculated_base_price_local_v2"].fillna(
        df["base_price_local"]
    )
    df = df.dropna(subset=["calculated_base_price_local_v2"])
    df["calculated_base_price_local_v2_rounded"] = df[
        "calculated_base_price_local_v2"
    ].round(0)

    df = df[
        [
            "store_name",
            "sku_studio",
            "studio_id",
            "date",
            "country_code",
            "currency_code",
            "calculated_base_price_local_v2",
            "calculated_base_price_usd_v2",
            "calculated_base_price_local_v2_rounded",
            "base_price_local",
        ]
    ].drop_duplicates(
        [
            "sku_studio",
            "country_code",
            "currency_code",
            "calculated_base_price_local_v2_rounded",
            # "date",
        ]
    )
    return df
