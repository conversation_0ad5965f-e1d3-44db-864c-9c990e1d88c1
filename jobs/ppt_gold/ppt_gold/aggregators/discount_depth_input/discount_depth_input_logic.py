import numpy as np
import pandas as pd
import scipy as scp
from pandas.tseries.holiday import USFederalHolidayCalendar


def _create_daily_sales(fact_sales_df: pd.DataFrame):
    """This function 'fixes' the daily-sales dataframe by addidng missing dates if any and filling them with zeros"""

    daily_sales = (
        fact_sales_df.groupby(["sku_studio", "date"])["gross_sales"]
        .agg(["sum"])
        .reset_index()
        .rename(
            columns={
                "sum": "gross_sales",
                "sku_studio": "unique_sku_id",
            }
        )
    )

    grid_df = (
        daily_sales.groupby("unique_sku_id")["date"]
        .apply(lambda x: pd.date_range(start=min(x), end=max(x), freq="D"))
        .to_frame()
        .reset_index()
        .explode("date")
    )
    grid_df["date"] = grid_df["date"].astype(str)
    daily_sales["date"] = daily_sales["date"].astype(str)

    daily_sales = pd.merge(
        grid_df, daily_sales, on=["unique_sku_id", "date"], how="left"
    )
    daily_sales["gross_sales"].fillna(0, inplace=True)

    daily_sales["date"] = daily_sales["date"].astype("datetime64[ns]")

    return daily_sales.sort_values(
        by=["unique_sku_id", "date"], ascending=True, ignore_index=True
    )


def _create_retarded_daily_features(
    daily_sales_df: pd.DataFrame, days_to_prediction=14, n_past_days=183
):
    """This function creates features that need to be retarded by a certain number of days
    ('days_to_predictions') because when we make a prediction for the next event, it won't start immediately,
    but a certain number of days later. In that time span the values of features like 'pct_sales_from_discounts'
    will change. Those features will be later added to event_agg. The variable 'n_past_days' gives the rolling
    time window over which we calculate the mean daily sales from regular days. That benchmark will later be used
    for normalization."""

    daily_sales_df["studio_id"] = daily_sales_df["unique_sku_id"].apply(
        lambda x: int(x.split(":")[-1])
    )

    daily_sales_df.sort_values(
        by=["unique_sku_id", "date"], ascending=True, ignore_index=True, inplace=True
    )

    daily_sales_df["age_in_days"] = 1
    daily_sales_df["age_in_days"] = daily_sales_df.groupby("unique_sku_id")[
        "age_in_days"
    ].cumsum()

    daily_sales_df["sales_discount"] = (
        daily_sales_df["promo_day"] * daily_sales_df["gross_sales"]
    )
    daily_sales_df["sales_regular"] = (
        1 - daily_sales_df["promo_day"]
    ) * daily_sales_df["gross_sales"]

    daily_sales_df["sales_regular_nan"] = np.where(
        daily_sales_df["promo_day"] < 1, daily_sales_df["sales_regular"], np.nan
    )
    daily_sales_df["benchmark_regular_H1"] = (
        daily_sales_df.groupby("unique_sku_id")["sales_regular_nan"]
        .rolling(window=n_past_days, min_periods=1)
        .agg(np.nanmean)
        .reset_index(drop=True)
        .fillna(0)
    )

    daily_sales_df["csum_sales_all"] = daily_sales_df.groupby("unique_sku_id")[
        "gross_sales"
    ].cumsum()
    daily_sales_df["csum_sales_discount"] = daily_sales_df.groupby("unique_sku_id")[
        "sales_discount"
    ].cumsum()

    daily_sales_df["pct_sales_from_discounts"] = np.where(
        daily_sales_df["csum_sales_all"] > 0,
        100 * daily_sales_df["csum_sales_discount"] / daily_sales_df["csum_sales_all"],
        0,
    )

    daily_sales_df["pct_sales_from_discounts"] = np.minimum(
        daily_sales_df["pct_sales_from_discounts"], 100
    )

    daily_sales_df["annual_gso"] = np.maximum(
        0, 365 * daily_sales_df["csum_sales_all"] / daily_sales_df["age_in_days"]
    )

    daily_sales_df["small_game_01"] = 1 * (daily_sales_df["annual_gso"] < 4300)
    daily_sales_df["large_game_01"] = 1 * (daily_sales_df["annual_gso"] >= 45000)

    studio_stats = (
        daily_sales_df.groupby(by=["studio_id", "date"])["found_dlc"]
        .agg(["count", "sum"])
        .rename(columns={"count": "n_products", "sum": "n_dlcs"})
        .reset_index()
    )

    studio_stats["n_games"] = studio_stats["n_products"] - studio_stats["n_dlcs"]

    # retarded date to later join on event_agg
    daily_sales_df["date_from"] = (
        (
            pd.to_datetime(daily_sales_df["date"])
            + pd.Timedelta(days_to_prediction, unit="D")
        )
        .dt.strftime("%Y-%m-%d")
        .astype(str)
    )
    studio_stats["date_from"] = (
        (
            pd.to_datetime(studio_stats["date"])
            + pd.Timedelta(days_to_prediction, unit="D")
        )
        .dt.strftime("%Y-%m-%d")
        .astype(str)
    )

    return daily_sales_df, studio_stats


def _create_events_table(
    fact_event_day_df: pd.DataFrame,
    filter_out_events=False,
    max_promo_days=30,
    min_daily_usd=5,
    lower_total_usd_cap=-1,
):
    """This function takes fact_event_day (that has many rows per discount event)
    and creates from it a table of discount events where each event is
    represented by only one row containing its key characteristics.
    (As of now the discount depth assigned to a given discount event is the largest one
    that is detected over the course of that event.)"""

    fact_event_day_df.sort_values(
        by=["unique_sku_id", "date"], ascending=True, inplace=True, ignore_index=True
    )

    event_agg = fact_event_day_df.groupby(by="event_id", sort=True).agg(
        {
            "unique_sku_id": ["first"],
            "portal_platform_region_id": ["first"],
            "studio_id": ["first"],
            "date_from": ["first"],
            "date_to": ["first"],
            "promo_length": ["first"],
            "discount_depth": ["max"],
            #            "days_since_previous_discount": ["first"],
            "gross_sales": ["first", "sum"],
        }
    )

    event_agg.columns = [
        (
            "discount_pct"
            if (c[0] == "discount_depth")
            else (
                c[0]
                if (c[0] != "gross_sales")
                else (
                    "total_sales"
                    if (c[0] == "gross_sales" and c[1] == "sum")
                    else (
                        "day_1_sales"
                        if (c[0] == "gross_sales" and c[1] == "first")
                        else c[1] + "_" + c[0]
                    )
                )
            )
        )
        for c in event_agg.columns
    ]

    # filtering out discount event with zero or negative total sales:
    if filter_out_events:
        plausible_event_filter = (
            (event_agg["total_sales"] > 0)
            & (event_agg["promo_length"] <= max_promo_days)
            & (event_agg["total_sales"] / event_agg["promo_length"] >= min_daily_usd)
        )
    else:
        plausible_event_filter = [True] * len(event_agg)
    # ---
    event_agg = event_agg[plausible_event_filter].sort_values(
        by=["unique_sku_id", "date_from"], ascending=True, ignore_index=True
    )

    if lower_total_usd_cap > 0:
        event_agg["total_sales"] = np.maximum(
            lower_total_usd_cap, event_agg["total_sales"]
        )
        event_agg["day_1_sales"] = np.maximum(
            lower_total_usd_cap, event_agg["day_1_sales"]
        )

    # Recalculation of 'days_since_previous_discount':
    event_agg["prev_date_to"] = event_agg.groupby("unique_sku_id")["date_to"].shift(1)
    event_agg["days_since_previous_discount"] = np.round(
        (
            pd.to_datetime(event_agg["date_from"])
            - pd.to_datetime(event_agg["prev_date_to"])
        ).dt.total_seconds()
        / 86400,
        0,
    )
    event_agg["days_since_previous_discount"] = (
        event_agg["days_since_previous_discount"].fillna(value=-1)
    ).astype(int)

    # Output is sorted by 'event_id' (concatenation of 'unique_sku_id' and 'date_from') in ascending order
    return event_agg.drop(columns=["prev_date_to"]).reset_index(drop=True)


def _create_dlc_01_flag(dim_sku_df: pd.DataFrame):
    """This function returns a "look-up" table for each base_sku_id (represented as a string
    that tells us whether the corresponding has SKU been marked is a DLC (best guess)"""

    # filling missing values (we don't want to convert names to lower case, since we use this table for Nintendo Switch advice):
    # dim_sku_df['human_name'] = dim_sku_df['human_name'].str.lower()
    dim_sku_df["product_name"] = dim_sku_df["product_name"].fillna(
        dim_sku_df["human_name"]
    )  # .str.lower()
    dim_sku_df["product_type"] = dim_sku_df["product_type"].fillna("nan").str.lower()

    # string with key words to search for in the human/product name and set of type associated with DLCs:
    key_words = (
        "dlc|music|advertis|series|movie|soundtrack|artbook|beta|preview|wallpaper"
    )
    dlc_types = {
        "beta/testing",
        "commercial license",
        "demo",
        "dlc",
        "extras",
        "soundtrack",
    }

    dim_sku_df["found_dlc"] = 1 * (
        dim_sku_df["human_name"].str.lower().str.contains(key_words)
        | dim_sku_df["product_name"].str.lower().str.contains(key_words)
        | dim_sku_df["product_type"].isin(dlc_types)
    )

    # Threat the wird case with the word "demon"
    filter_1 = dim_sku_df["human_name"].str.lower().str.contains("demo") & (
        ~(dim_sku_df["human_name"].str.lower().str.contains("demon"))
    )
    filter_2 = dim_sku_df["product_name"].str.lower().str.contains("demo") & (
        ~(dim_sku_df["product_name"].str.lower().str.contains("demon"))
    )
    demo_filter = (~dim_sku_df["found_dlc"]) & (filter_1 | filter_2)

    dim_sku_df["found_dlc"] = np.where(demo_filter, 1, dim_sku_df["found_dlc"])

    # grouping by base_sku_id in order to "fill" information from different studios (both "follow" the product, but one has
    # marked the product as a DLC, while the other one hasn't
    # however, this doesn't allow for "filling" information from different platforms
    # the latter is possible by grouping by human name, but this produces mistakes when two studios have different games with
    # the same name on different portals
    sku_dlc_table = (
        dim_sku_df.groupby("base_sku_id")["found_dlc"].sum().to_frame().reset_index()
    )

    sku_dlc_table["found_dlc"] = 1 * (sku_dlc_table["found_dlc"] > 0)

    return sku_dlc_table


def _cross_table_proliferation(
    daily_sales_df: pd.DataFrame,
    fact_event_day_df: pd.DataFrame,
    dim_sku_df: pd.DataFrame,
    base_sku_dlc: pd.DataFrame,
):
    """This function crossproliferates information between different tables
    (e.g., marking discount days in daily_sales_df or adding sales to fact_event_day_df) that is needed for
    feature creation."""

    # attaching a flag to the daily sales showing whether that day was a promo day
    daily_sales_df = pd.merge(
        daily_sales_df,
        fact_event_day_df[["unique_sku_id", "date"]],
        on=["unique_sku_id", "date"],
        how="left",
        indicator=True,
    ).rename(columns={"_merge": "promo_day"})
    daily_sales_df["promo_day"] = 1 * (daily_sales_df["promo_day"] == "both")

    # attaching the sales numbers to each discount day:
    fact_event_day_df = pd.merge(
        fact_event_day_df,
        daily_sales_df[["unique_sku_id", "date", "gross_sales"]],
        on=["unique_sku_id", "date"],
        how="left",
    )
    # fill with zeros if sales are missing:
    fact_event_day_df["gross_sales"] = fact_event_day_df["gross_sales"].fillna(value=0)

    # attaching the base_sku_id (needed later for the 0/1-flag indicating DLCs)
    daily_sales_df = pd.merge(
        daily_sales_df,
        dim_sku_df[["unique_sku_id", "base_sku_id"]],
        on="unique_sku_id",
        how="left",
    )

    # attaching the 0/1-flag indicating DLCs:
    daily_sales_df = pd.merge(
        daily_sales_df,
        base_sku_dlc[["base_sku_id", "found_dlc"]],
        on="base_sku_id",
        how="left",
    )
    fact_event_day_df = pd.merge(
        fact_event_day_df,
        base_sku_dlc[["base_sku_id", "found_dlc"]],
        on="base_sku_id",
        how="left",
    )

    return daily_sales_df, fact_event_day_df


def _filter_portals_and_studios(
    event_agg: pd.DataFrame, good_ppr_ids={171010}, bad_studios=set()
):
    """This function attaches the infiormation about the protal, platform and region (found in dim_portals)
    corresponding to each event and then filters for events on either Steam or Nintendo Switch
    """

    # filtering
    portal_filter = event_agg["portal_platform_region_id"].isin(good_ppr_ids)
    studio_filter = ~event_agg["studio_id"].isin(bad_studios)

    # order in event_agg should be preserved
    event_agg = event_agg[portal_filter & studio_filter].reset_index(drop=True)

    # output should be ordered by unique_sku_id and date_from in ascending order
    return event_agg


def _cap_discount_depths(event_agg: pd.DataFrame, max_discount_depth=90):
    """This function caps the discount depths at 90%"""

    event_agg["discount_pct"] = np.where(
        event_agg["portal_platform_region_id"] == 171010,
        np.minimum(event_agg["discount_pct"], 90),
        event_agg["discount_pct"],
    )

    return event_agg


def _detect_Steam_events(
    event_agg: pd.DataFrame,
    steam_events_calendar: pd.DataFrame,
    col_name_start="date_from",
    col_name_end="date_to",
    col_name_major="is_major_event",
    col_name_reg="is_steam_event",
):
    """For all discount events on Steam this functions adds the columns 'is_steam_event'
    and 'is_major_event' that denote whether the given discount event was an official Steam
    event and possibly even a major Steam event, respectively.
    (binary integer 0/1 values; 'is_major_event' = 1 => 'is_steam_event' = 1)
    *another way to do this would be to 'explode', deduplicate and left join it
    steam_events_calendar on fact_event_day"""

    # TODO I think the above description is a lie and it does much more than that

    all_steam_dates = set()
    all_major_dates = set()
    # dataframe steam_event_calendar has only < 200, so looping over them is fine
    for i in range(len(steam_events_calendar)):
        set_dates = set(
            pd.date_range(
                steam_events_calendar["start_date"][i],
                steam_events_calendar["end_date"][i],
                freq="D",
                inclusive="both",
            ).astype(str)
        )
        all_steam_dates = all_steam_dates.union(set_dates)
        if steam_events_calendar["major"][i] == 1:
            all_major_dates = all_major_dates.union(set_dates)

    # checking whether the discount event overlaps with any of the Steam events
    event_agg[col_name_reg] = (
        event_agg.apply(
            lambda row: len(
                set(
                    pd.date_range(
                        row[col_name_start],
                        row[col_name_end],
                        freq="D",
                        inclusive="both",
                    ).astype(str)
                ).intersection(all_steam_dates)
            )
            > 0,
            axis=1,
        )
    ).astype(int)
    # overwriting with zeros for all other portals (done here for simplicity)
    event_agg[col_name_reg] = np.where(
        event_agg["portal_platform_region_id"] != 171010, 0, event_agg[col_name_reg]
    )

    # checking whether the discount event overlaps with any of the major Steam events
    event_agg[col_name_major] = (
        event_agg.apply(
            lambda row: len(
                set(
                    pd.date_range(
                        row[col_name_start],
                        row[col_name_end],
                        freq="D",
                        inclusive="both",
                    ).astype(str)
                ).intersection(all_major_dates)
            )
            > 0,
            axis=1,
        )
    ).astype(int)
    # overwriting with zeros for all other portals (done here for simplicity)
    event_agg[col_name_major] = np.where(
        event_agg["portal_platform_region_id"] != 171010, 0, event_agg[col_name_major]
    )

    return event_agg


def _detect_US_holidays(
    event_agg: pd.DataFrame,
    col_name_start="date_from",
    col_name_end="date_to",
    col_name_n="n_us_holidays",
    col_name_01="has_holiday",
):
    """This function calculates the number of US holidays during the corresponding
    discount event."""

    cal = USFederalHolidayCalendar()

    # set of all US holidays until the end of 2050
    all_us_holidays = set(
        (cal.holidays(start="2000-01-01", end="2050-12-31")).astype(str)
    )

    # calculating the number of US holidays for each
    event_agg[col_name_n] = event_agg.apply(
        lambda row: len(
            set(
                pd.date_range(
                    row[col_name_start], row[col_name_end], freq="D", inclusive="both"
                ).astype(str)
            ).intersection(all_us_holidays)
        ),
        axis=1,
    )

    # detecting whether the event has at least one US holiday
    event_agg[col_name_01] = 1 * (event_agg[col_name_n] > 0)

    return event_agg


def _create_age_features(event_agg: pd.DataFrame, sku_rel_date: pd.DataFrame):
    """This function creates some of the needed features."""

    # attach the release date for each SKU (if not present):
    if "release_date" not in event_agg.columns:
        event_agg = pd.merge(
            event_agg,
            sku_rel_date[["unique_sku_id", "release_date"]],
            on="unique_sku_id",
            how="left",
        )

    # remove SKUs with a missing release date if any (ordering should persist):
    if event_agg["release_date"].isna().sum() > 0:
        event_agg = event_agg[~event_agg["release_date"].isna()].reset_index(drop=True)

    event_agg["age_in_days"] = np.round(
        (
            pd.to_datetime(event_agg["date_from"])
            - pd.to_datetime(event_agg["release_date"])
        ).dt.total_seconds()
        / 86400,
        0,
    ).astype(int)

    return event_agg


def _create_additional_features(event_agg: pd.DataFrame):
    """This function creates the additional features.
    (It relies on the dataframe being sorted by SKU and date_from; the latter in ascending order.)
    """

    bins_borders = [0] + list(np.linspace(10, 100, 19, endpoint=True) + 2.5)
    bins_labels = [
        "depth " + str(int(x)) for x in np.linspace(10, 100, 19, endpoint=True)
    ]
    event_agg["depth_group"] = pd.cut(
        event_agg["discount_pct"], bins=bins_borders, labels=bins_labels
    )

    event_agg["n_same_discounts_sofar"] = 1
    event_agg["n_same_discounts_sofar"] = (
        event_agg.groupby(["unique_sku_id", "depth_group"])[
            "n_same_discounts_sofar"
        ].cumsum()
        - 1
    )

    event_grouped = event_agg.groupby("unique_sku_id")

    shift_cols = [
        "discount_pct",
        "promo_length",
        "is_steam_event",
        "is_major_event",
        "has_holiday",
        "n_us_holidays",
        "total_sales",
    ]
    prev_names = ["prev_" + x for x in shift_cols]

    event_agg[prev_names] = event_grouped[shift_cols].shift(1)

    event_agg["max_discount_sofar"] = event_grouped["discount_pct"].cummax()
    event_agg["max_discount_sofar"] = event_grouped["max_discount_sofar"].shift(1)

    event_agg["curr_discount_to_max"] = (
        event_agg["discount_pct"] / event_agg["max_discount_sofar"]
    )

    event_agg["discount_ratio"] = (
        event_agg["discount_pct"] / event_agg["prev_discount_pct"]
    )
    event_agg["new_peak_discount"] = 1 * (
        event_agg["discount_pct"] > event_agg["max_discount_sofar"]
    )

    event_agg["length_ratio"] = (
        event_agg["promo_length"] / event_agg["prev_promo_length"]
    )

    event_agg["n_events_sofar"] = 1
    event_agg["n_events_sofar"] = event_grouped["n_events_sofar"].cumsum() - 1

    return event_agg


def _attach_retarded_features(
    event_agg: pd.DataFrame,
    studio_stats: pd.DataFrame,
    dim_sku_df: pd.DataFrame,
    daily_sales_df: pd.DataFrame,
    base_sku_dlc: pd.DataFrame,
):
    """This functions attaches the previously created retared features (e.g. 'small_game_01') as well as
    the DLC indicator."""

    # attaching studio-level features, e.g. number of products of that studio:
    event_agg = pd.merge(
        event_agg,
        studio_stats[["studio_id", "date_from", "n_products", "n_dlcs", "n_games"]],
        on=["studio_id", "date_from"],
        how="left",
    )
    # ---
    # attaching DLC indicator:
    event_agg = pd.merge(
        event_agg,
        dim_sku_df[["unique_sku_id", "base_sku_id"]],
        on="unique_sku_id",
        how="left",
    )
    event_agg = pd.merge(event_agg, base_sku_dlc, on="base_sku_id", how="left")
    # ---
    # attaching retarded features from daily sales
    event_agg = pd.merge(
        event_agg,
        daily_sales_df[
            [
                "unique_sku_id",
                "date_from",
                "small_game_01",
                "large_game_01",
                "pct_sales_from_discounts",
                "benchmark_regular_H1",
                "annual_gso",
            ]
        ],
        on=["unique_sku_id", "date_from"],
        how="left",
    )

    backfill_cols = [
        "n_products",
        "n_dlcs",
        "n_games",
        "small_game_01",
        "large_game_01",
        "pct_sales_from_discounts",
        "benchmark_regular_H1",
        "annual_gso",
    ]

    event_agg.sort_values(
        by=["unique_sku_id", "date_from"],
        ascending=True,
        ignore_index=True,
        inplace=True,
    )

    event_agg[backfill_cols] = event_agg.groupby("unique_sku_id")[backfill_cols].bfill()
    # all remaining NAs for features like 'small_game_01' are for 'age_in_months' = 0, i.e. all detected discount events
    # for those SKUs so far have happened in their first month

    return event_agg


def _add_target_columns(
    event_agg: pd.DataFrame,
    min_norm_sales=0.9,
    n_past_events=5,
    decay_factor=0.8,
    norm_shift=1,
):
    """This function creates target column(s) by properly normalizing the sales.
    If any of the sales are negative (previous or current event), then we insert
    an NA.
    norm_shift should equal 1 when calculating training data and zero for calculating the prediction table
    (event_agg must be sorted by SKU and date_from; the latter in ascending order.)"""

    event_agg.sort_values(
        by=["unique_sku_id", "date_from"],
        ascending=True,
        ignore_index=True,
        inplace=True,
    )

    events_group_sku = event_agg.groupby("unique_sku_id")

    event_agg["daily_sales"] = event_agg["total_sales"] / event_agg["promo_length"]
    event_agg["prev_daily_sales"] = events_group_sku["daily_sales"].shift(1)

    decay_vector = decay_factor ** np.array(range(n_past_events))[::-1]

    event_agg["daily_benchmark"] = (
        events_group_sku["daily_sales"]
        .rolling(window=n_past_events, min_periods=1)
        .agg(lambda x: scp.stats.gmean(x, weights=decay_vector[-len(x) :]))
        .reset_index(drop=True)
    )
    event_agg["daily_benchmark"] = events_group_sku["daily_benchmark"].shift(norm_shift)
    event_agg["daily_sales_norm"] = (
        event_agg["daily_sales"] / event_agg["daily_benchmark"]
    )

    event_agg["total_benchmark"] = (
        events_group_sku["total_sales"]
        .rolling(window=n_past_events, min_periods=1)
        .agg(lambda x: scp.stats.gmean(x, weights=decay_vector[-len(x) :]))
        .reset_index(drop=True)
    )
    event_agg["total_benchmark"] = events_group_sku["total_benchmark"].shift(norm_shift)
    event_agg["total_sales_norm"] = (
        event_agg["total_sales"] / event_agg["total_benchmark"]
    )

    event_agg["prev_daily_sales_norm"] = (
        event_agg["prev_daily_sales"] / event_agg["daily_benchmark"]
    )

    event_agg["prev_total_sales_norm"] = (
        event_agg["prev_total_sales"] / event_agg["total_benchmark"]
    )

    event_agg["daily_target_01"] = 1 * (event_agg["daily_sales_norm"] > min_norm_sales)

    event_agg["total_target_01"] = 1 * (event_agg["total_sales_norm"] > min_norm_sales)

    # normalization of 'benchmark_regular_H1' and 'annual_gso':
    event_agg["regular_H1_norm_daily"] = (
        event_agg["benchmark_regular_H1"] / event_agg["daily_benchmark"]
    )
    event_agg["regular_H1_norm_total"] = (
        event_agg["benchmark_regular_H1"] / event_agg["total_benchmark"]
    )
    event_agg["annual_gso_norm_daily"] = (
        event_agg["annual_gso"] / event_agg["daily_benchmark"]
    )
    event_agg["annual_gso_norm_total"] = (
        event_agg["annual_gso"] / event_agg["total_benchmark"]
    )

    return event_agg


def check_collision(start_dates: pd.Series, end_dates: pd.Series, event_table):
    event_table["start_date"] = event_table["start_date"].astype(str)
    event_table["end_date"] = event_table["end_date"].astype(str)

    event_table = event_table[event_table["end_date"] >= min(start_dates)]
    # need to sort event_table by 'major' in descending order and 'start_date' in ascending order
    # i.e., we go through the major events first in chronological order
    # as soon as the first collision is found, it is written down and we break the loop
    # if not collision is found, we continue by going through the mon-major events also in chronological order
    n = len(event_table)  # Important for speed!!!!
    steam_start_dates = event_table["start_date"].to_list()
    steam_end_dates = event_table["end_date"].to_list()
    steam_names = event_table["name"].to_list()
    steam_major = event_table["major"].to_list()

    major_start = []
    major_end = []
    major_name = []
    is_major = []
    for start_date, end_date in zip(start_dates, end_dates):
        found_event = False
        for j in range(n):
            if not (start_date > steam_end_dates[j] or end_date < steam_start_dates[j]):
                major_start.append(steam_start_dates[j])
                major_end.append(steam_end_dates[j])
                major_name.append(steam_names[j])
                is_major.append(steam_major[j])
                found_event = True
                break
        if not found_event:
            major_start.append(np.nan)
            major_end.append(np.nan)
            major_name.append(np.nan)
            is_major.append(np.nan)

    return major_start, major_end, major_name, is_major
