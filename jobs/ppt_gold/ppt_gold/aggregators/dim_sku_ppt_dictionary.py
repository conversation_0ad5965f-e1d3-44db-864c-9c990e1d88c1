import logging

import polars as pl
from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.tables import (
    LegacyDimSKUsTable,
    LegacyDimStudioTable,
    LegacyFactDetectedEventsTable,
    LegacyFactDiscountsTable,
    SilverSKUsTable,
    TableWithGoldPartitions,
)
from data_sdk.validators.schemas import NoCheckSchema

from ppt_gold.aggregators.dim_sku_ppt_gold import generate_dim_sku
from ppt_gold.aggregators.dim_studio_ppt_gold import generate_dim_studio
from ppt_gold.aggregators.tables import PPTGoldTable
from ppt_gold.config import ExternalDataStorageConfig

external_config = ExternalDataStorageConfig(base_dir="external_data/users")

log = logging.getLogger(__name__)


class DimSkuPptTable(TableWithGoldPartitions):
    table_name = PPTGoldTable.DIM_SKU_PPT
    model = NoCheckSchema


class DimSkuPptAggregator(BaseAggregator):
    table_cls = DimSkuPptTable

    def __init__(
        self,
        dim_studio: LegacyDimStudioTable,
        dim_sku: LegacyDimSKUsTable,
        fact_discounts: LegacyFactDiscountsTable,
        fact_detected_events: LegacyFactDetectedEventsTable,
        silver_sku: SilverSKUsTable,
    ) -> None:
        self._dim_studio = dim_studio
        self._dim_sku = dim_sku
        self._fact_discounts = fact_discounts
        self._fact_detected_events = fact_detected_events
        self._silver_sku = silver_sku

    def _aggregate(self) -> pl.DataFrame:
        columns = [
            "base_sku_id",
            "human_name",
            "store_id",
            "sku_studio",
            "studio_id",
            "portal_platform_region",
            "portal_platform_region_id",
            "product_id",
            "human_name_indicator",
            "sku_type",
            "product_name",
            "package_name",
            "custom_group",
            "product_type",
            "ratio",
            "gso",
            "is_baseline_precalculated",
            "has_detected_detected_events",
            "has_scraped_discounts",
            "is_partner",
            "company_name",
        ]

        dim_studio_gold = generate_dim_studio(dim_studio=self._dim_studio.df)
        dim_sku_gold = generate_dim_sku(
            dim_sku=self._dim_sku.df,
            fact_discounts=self._fact_discounts.df,
            fact_detected_events=self._fact_detected_events.df,
            dim_studio=self._dim_studio.df,
            silver_sku=self._silver_sku.df,
        )

        if dim_studio_gold.is_empty() or dim_sku_gold.is_empty():
            return pl.DataFrame({}, schema={col: pl.Utf8 for col in columns})
        # filter dim_studio_gold to only include where is_partner is True
        dim_studio_gold = dim_studio_gold.filter(pl.col("is_partner") == True)
        dim_sku = dim_sku_gold.join(
            dim_studio_gold.select(["studio_id", "is_partner", "company_name"]),
            on="studio_id",
            how="inner",
        )

        # sort_columns by gso for dim_sku highest to lowest
        dim_sku = dim_sku.sort("gso", descending=True)

        dim_sku = dim_sku.with_columns(
            pl.col("human_name")
            .str.to_lowercase()
            .str.replace(r"\W|_| ", "", literal=False)
            .alias("human_name_clean")
        )

        dim_sku = dim_sku.filter(
            (pl.col("gso") > 0)
            & (~pl.col("human_name_clean").str.contains("commerciallicense"))
            & (pl.col("sku_type") == "SALES")
        )

        # drop duplicated column base_sku_id keep first
        dim_sku = dim_sku.unique(
            subset=["base_sku_id", "portal_platform_region_id"], keep="first"
        )

        return dim_sku[columns]
