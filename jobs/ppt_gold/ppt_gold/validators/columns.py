from enum import Enum
from typing import Type

import pandas as pd
import pandera as pa

from ppt_gold.dictionaries.constants import StringLength


def ExactLengthString(length: int, **kwargs) -> pa.Column:  # noqa: N802
    return pa.Column(
        pa.String,
        coerce=True,
        checks=pa.Check.str_length(min_value=length, max_value=length),
        **kwargs,
    )


def HashString(**kwargs) -> pa.Column:  # noqa: N802
    return ExactLengthString(StringLength.TINY.value, **kwargs)


def TinyString(**kwargs) -> pa.Column:  # noqa: N802
    return pa.Column(
        pa.String,
        coerce=True,
        checks=pa.Check.str_length(max_value=StringLength.TINY.value),
        **kwargs,
    )


def SmallString(**kwargs) -> pa.Column:  # noqa: N802
    return pa.Column(
        pa.String,
        coerce=True,
        checks=pa.Check.str_length(max_value=StringLength.SMALL.value),
        **kwargs,
    )


def MediumString(**kwargs) -> pa.Column:  # noqa: N802
    return pa.Column(
        pa.String,
        coerce=True,
        checks=pa.Check.str_length(max_value=StringLength.MEDIUM.value),
        **kwargs,
    )


def HugeString(**kwargs) -> pa.Column:  # noqa: N802
    return pa.Column(
        pa.String,
        coerce=True,
        checks=pa.Check.str_length(max_value=StringLength.HUGE.value),
        **kwargs,
    )


def DatetimeUTC(**kwargs) -> pa.Column:  # noqa: N802
    return pa.Column(
        pd.DatetimeTZDtype(
            unit="ns",
            tz="UTC",
        ),
        coerce=True,
        **kwargs,
    )


def _has_two_decimals(x):
    # some values have only one decimal place, like 0.0, or 30.0
    return 1 <= str(x)[::-1].find(".") <= 2


_decimals_check = pa.Check(_has_two_decimals, element_wise=True)

_decimals_check_with_null = pa.Check(
    # if column is nullable, then None is OK
    lambda x: x is None or _has_two_decimals(x),
    element_wise=True,
)


def FloatWithTwoDecimalPlaces(**kwargs) -> pa.Column:  # noqa: N802
    return pa.Column(
        pa.Float,
        coerce=True,
        checks=_decimals_check,
        **kwargs,
    )


def Float(**kwargs) -> pa.Column:  # noqa: N802
    return pa.Column(
        pa.Float,
        coerce=True,
        **kwargs,
    )


def NullableFloatWithTwoDecimalPlaces(**kwargs) -> pa.Column:  # noqa: N802
    return pa.Column(
        pa.Float,
        coerce=True,
        nullable=True,
        checks=_decimals_check_with_null,
        **kwargs,
    )


def NonNegativeInt(**kwargs) -> pa.Column:  # noqa: N802
    return pa.Column(
        pa.Int,
        coerce=True,
        checks=pa.Check.greater_than_or_equal_to(0),
        **kwargs,
    )


def NonNegativeFloat(**kwargs) -> pa.Column:  # noqa: N802
    return pa.Column(
        pa.Float,
        coerce=True,
        checks=pa.Check.greater_than_or_equal_to(0),
        **kwargs,
    )


def Int(**kwargs) -> pa.Column:  # noqa: N802
    return pa.Column(
        pa.Int,
        **kwargs,
    )


def IntInRange(min_value: int, max_value: int, **kwargs) -> pa.Column:  # noqa: N802
    """
    Validator for integer columns, that checks if the value is
    in a specified range (inclusive on both ends).
    """
    return pa.Column(
        pa.Int,
        coerce=True,
        checks=pa.Check.in_range(min_value, max_value),
        **kwargs,
    )


def FloatInRange(min_value: float, max_value: float, **kwargs) -> pa.Column:  # noqa: N802
    """Validator for float columns, that checks if the value is
    in a specified range (inclusive on both ends).
    """
    return pa.Column(
        pa.Float,
        coerce=True,
        checks=pa.Check.in_range(min_value, max_value),
        **kwargs,
    )


def Bool(**kwargs) -> pa.Column:  # noqa: N802
    return pa.Column(
        pa.Bool,
        coerce=True,
        **kwargs,
    )


def EnumString(enum: Type[Enum], **kwargs) -> pa.Column:  # noqa: N802
    return pa.Column(
        pa.String,
        coerce=True,
        checks=pa.Check.isin([e.value for e in enum.__members__.values()]),
        **kwargs,
    )
