import polars as pl
from data_sdk.base_parititioner import (
    BasePartitioner,
    PartitionDefinition,
    SavingMethod,
    TableToSave,
)
from data_sdk.domain import JobMetadata, TableMetadata

from ppt_gold.utils.date_utils import datetime_to_string


class NoPartitioner(BasePartitioner):
    def partition(
        self, df: pl.DataFrame, table_metadata: TableMetadata, job_metadata: JobMetadata
    ) -> list[TableToSave]:
        chunks_folder_name = datetime_to_string(job_metadata.creation_datetime)
        output_folder = (
            f"data/studio_id={job_metadata.studio_id}/{table_metadata.table_name}"
        )
        return [
            TableToSave(
                data=df,
                base_path=f"{output_folder}",
                saving_method=SavingMethod.CUSTOM_PATH_PARTITIONING,
                partitioning_columns=None,
                custom_partitions=[
                    PartitionDefinition(
                        path=f"{chunks_folder_name}.parquet",
                        mask_expression=(True),
                    )
                ],
            )
        ]
