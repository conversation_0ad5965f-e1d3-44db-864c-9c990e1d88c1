# Run Job Locally
To run the job locally run
poetry run python scripts/run_locally.py --reader-env (env) --studio-id (id of your choice) --run-type (run_type of your choice) --writer-env (env)

Example:
poetry run python scripts/run_locally.py --input-env dev --studio-id 1 --run-type cp

The command reads from dev environment, from studio 1 and saves it locally
By default, poetry run python scripts/run_locally.py reads data from dev studio 1 and saves it locally, using run_type both

Run Types:
- delta - saves in delta format
- cp - saves in custom partitions format
- both - saves in both formats 

Available Reader Envs:
- dev - downloads data from silver dev
- prod - downloads data from silver gold

Available Writer Envs:
- dry - saves nothing
- local - saves data locally
- dev - saves data to dev


# Fetch necessary tables
To fetch all silver tables for local debugging (or anything you want) run this:
poetry run python scripts/fetch_data.py --reader_env (env) --studio_id (id of your choice)

Example:
poetry run python scripts/fetch_data.py --reader_env dev --studio_id 1
Fetches all the necessary tables to your local_output/fetch_data directory in parquet format
