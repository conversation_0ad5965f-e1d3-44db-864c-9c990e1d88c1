import logging
from enum import Enum
from pathlib import Path

import typer
from data_sdk.config import DLSConfig, LocalConfig
from data_sdk.domain import Portal
from data_sdk.logging import force_human_readable_handler_for_tty
from pipeline_sdk.monitoring.logs import configure_logger

from ppt_gold.config import Config
from ppt_gold.job import Params, run

configure_logger(
    custom_loggers_config={
        "ppt_gold": {"level": "INFO"},
        "data_sdk": {"level": "INFO"},
    }
)

force_human_readable_handler_for_tty()

log = logging.getLogger(__name__)


class InputEnv(str, Enum):
    DEV = "dev"
    PROD = "prod"


class OutputEnv(str, Enum):
    DEV = "dev"
    LOCAL = "local"


class RunType(str, Enum):
    DELTA = "delta"
    CUSTOM_PARTITIONS = "cp"
    BOTH = "both"


def main(
    input_env: InputEnv = InputEnv.DEV,
    output_env: OutputEnv = OutputEnv.LOCAL,
    studio_id: int = 1,
    local_output_dir: str = "playground",
    run_type: RunType = RunType.CUSTOM_PARTITIONS,
    portal: Portal | None = None,
):
    if input_env == InputEnv.DEV:
        reader_cfg = DLSConfig(
            account_name="dlsaggregateddevs7",
            container_name="core-silver",
            base_dir=Path("result"),
        )

    elif input_env == InputEnv.PROD:
        reader_cfg = DLSConfig(
            account_name="dlsaggregatedprodr9",
            container_name="core-silver",
            base_dir=Path("result"),
        )

    if output_env == OutputEnv.LOCAL:
        writer_cfg: LocalConfig | DLSConfig = LocalConfig(
            local_dir=Path(local_output_dir + "/ppt_gold/data"),
        )
    elif output_env == OutputEnv.DEV:
        writer_cfg = DLSConfig(
            account_name="dlsaggregateddevs7",
            container_name="ppt-gold-poc",
            base_dir=Path("data"),
        )

    config = Config(
        hostname="localhost",
        env=input_env,
        run_type=run_type,
        reader_cfg=reader_cfg,
        writer_cfg=writer_cfg,
    )

    run(params=Params(studio_id=studio_id), config=config, portal=portal)

    log.info("Finishing PPT Gold Job")


if __name__ == "__main__":
    typer.run(main)
