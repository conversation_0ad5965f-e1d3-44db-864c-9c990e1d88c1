from data_sdk.domain import Portal

from scripts.run_locally import InputEnv, main


def disable_test_aggregators_discount_no_scraped():
    main(input_env=InputEnv.DEV, studio_id=10548)


def disable_test_aggregators_discount_empty():
    main(input_env=InputEnv.DEV, studio_id=1434)


def disable_test_aggregators_missing_dim_sku():
    main(input_env=InputEnv.PROD, studio_id=461)


def disable_test_aggregators():
    main(input_env=InputEnv.PROD, studio_id=10913, portal=Portal.STEAM)


def disable_test_aggregators_dev():
    main(input_env=InputEnv.DEV, studio_id=12)


def disable_test_aggregators_empty_fact_sales():
    main(input_env=InputEnv.PROD, studio_id=10818)
