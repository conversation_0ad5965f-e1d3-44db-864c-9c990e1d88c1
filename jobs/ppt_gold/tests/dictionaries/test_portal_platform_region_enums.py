from data_sdk.domain.stores import Store

from ppt_gold.dictionaries.portal_platform_region_enums import store_to_snake_case


def test_store_to_snake_case_replaces_spaces_with_underscores():
    assert " " in Store.NINTENDO_SWITCH_AMERICAS.value
    assert (
        store_to_snake_case(Store.NINTENDO_SWITCH_AMERICAS)
        == "nintendo_switch_americas"
    )


def test_store_to_snake_case_replaces_slashes_with_underscores():
    assert "/" in Store.NINTENDO_SWITCH_TAIWAN_HONG_KONG.value
    assert (
        store_to_snake_case(Store.NINTENDO_SWITCH_TAIWAN_HONG_KONG)
        == "nintendo_switch_taiwan_hong_kong"
    )
