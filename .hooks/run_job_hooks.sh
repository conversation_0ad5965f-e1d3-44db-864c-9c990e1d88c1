#!/bin/bash

HOOK_TYPE=$1

if [ -z "$HOOK_TYPE" ]; then
    echo "Usage: $0 <hook-type>"
    exit 1
fi

REPO_DIR="$(git rev-parse --show-toplevel)"
SUCCESS=true

print_centered_text() {
    local text="$1"
    local line_length=79
    local plain_text=$(echo -e "$text" | sed 's/\x1b\[[0-9;]*m//g') # Remove color codes
    local text_length=${#plain_text}
    local padding=$(( (line_length - text_length) / 2 ))
    local left_padding=$(printf '%*s' $padding '' | tr ' ' '-')
    local right_padding=$(printf '%*s' $((line_length - text_length - padding)) '' | tr ' ' '-')
    echo -e "$left_padding$text$right_padding"
}

BLUE='\033[1;34m'
GREEN='\033[1;32m'
RED='\033[1;31m'
NC='\033[0m'

HOOKS_DIRS=($(find "$REPO_DIR" -type d -name ".hooks" ! -path "$REPO_DIR/.hooks"))

for hooks_dir in "${HOOKS_DIRS[@]}"; do
    TARGET_DIR="$(dirname "$hooks_dir")"
    RELATIVE_DIR="${TARGET_DIR#$REPO_DIR/}"
    if [ -f "$hooks_dir/$HOOK_TYPE" ]; then
        print_centered_text " Running $HOOK_TYPE hook in ${BLUE}$RELATIVE_DIR${NC} "
        "$hooks_dir/$HOOK_TYPE" || SUCCESS=false
    fi
done

print_centered_text ""

if [ "$SUCCESS" == false ]; then
    echo -e "${RED}Some $HOOK_TYPE hooks failed${NC}"
    exit 1
fi

echo -e "${GREEN}All $HOOK_TYPE hooks passed${NC}"
exit 0
