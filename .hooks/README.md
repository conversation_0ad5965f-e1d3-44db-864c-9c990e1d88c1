
# Hooks Management in Multi-Project Repository

## Problem Statement

This repository contains multiple smaller job projects, each of which has its own separate Python environment managed by [Poetry](https://python-poetry.org/). Managing Git hooks in such a setup is challenging due to the following reasons:

1. **Multiple Environments:** Each project has its own environment and dependencies, meaning the hooks need to be aware of and operate within the correct environment.
2. **Centralized Hook Management:** Git hooks typically reside in the `.git/hooks` directory. With multiple projects, maintaining and updating hooks for each environment can become cumbersome.

## Solution Overview

To address these challenges, we have implemented a structured approach to manage Git hooks across multiple projects with isolated environments. The solution involves the following components:

1. **Centralized Hook Installation Script:** To install and manage hooks for each project, follow these steps:

   - **Run the Installation Task:** Each sub-project has a specific task defined in its `pyproject.toml` file. To install the hooks for a particular project, activate the project's Poetry environment and run the task `hooks`. For example, for the `core_silver` project, you would run:
     ```bash
     poe hooks
     ```

   - **Task Definition:** The task `hooks` is defined in the `pyproject.toml` file of each sub-project. Here's an example definition for the `core_silver` project:
     ```toml
     [tool.poe.tasks]
     hooks = { "shell" = "../../.hooks/install_hooks.sh 'jobs/core_silver'" }
     ```

2. **Pre-commit Package and Configuration:**
   - Leverages the [pre-commit](https://pre-commit.com/) package to manage Git hooks.
   - Each sub-project contains a `.pre-commit-config.yaml` file that defines the hooks specific to that project.
   - This package helps define hooks that are aware of the Poetry environment in which they should be executed.

3. **Hook Execution Script:** A generic script (`run_job_hooks.sh <hook_type>`) that:
   - Accepts the type of hook (e.g., `pre-commit`, `pre-push`) as an argument.
   - Searches for and executes the corresponding hooks in the `.hooks` directories of the sub-projects.

4. **Git Hook Entry Points:** Simple scripts that act as entry points for Git hooks:
   - These scripts, originally stored in the `.hooks/` directory (e.g., `./.hooks/pre-commit`), are installed into the `.git/hooks` directory.
   - They call the generic hook execution script (`run_job_hooks.sh`) with the appropriate hook type.
   - They are triggered by Git during the relevant events (e.g., commit, push).

## Example Directory Structure

Below is an example directory structure illustrating how the hooks are organized within this repository:

```
data-jobs/
├── .git/
│   └── hooks/
│       ├── pre-commit               # will execute pre-commit A & D
│       ├── pre-push                 # will execute pre-push B & C
│       └── run_job_hooks.sh
├── .hooks/
│   ├── install_hooks.sh             # moves main hooks scripts from
│   │                                # this directory to .git/hooks/
│   ├── run_job_hooks.sh
│   ├── pre-commit
│   └── pre-push
├── jobs/
│   ├── core_silver/
│   │   ├── .pre-commit-config.yaml  # pre-commit based on this file,
│   │   │                            # will generate proper set of hooks,
│   │   │                            # like pre-commit #A & pre-push #B
│   │   └── .hooks/
│   │       ├── pre-commit   # A
│   │       └── pre-push     # B
│   └── ppt_gold/
│       ├── .pre-commit-config.yaml
│       └── .hooks/
│           └── pre-push     # C
└── libs/
    └── data-sdk/
        ├── .pre-commit-config.yaml
        └── .hooks/
            └── pre-commit   # D
```

## Usage

1. **Install Hooks:** To install the hooks for a specific sub-project, follow these steps:
   ```bash
   cd jobs/core_silver/
   poetry shell
   poe hooks
   ```

   Repeat these steps for each sub-project for which you want to have hooks installed.

2. **Commit or Push Changes:** The hooks will be automatically executed during the commit or push operations.

By following this approach, we can efficiently manage Git hooks across multiple sub-projects with isolated environments, ensuring consistency and avoiding conflicts.
