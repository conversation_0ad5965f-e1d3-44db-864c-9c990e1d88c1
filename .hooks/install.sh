#!/bin/bash

TARGET=$1
REPO_DIR="$(git rev-parse --show-toplevel)"
TARGET_DIR="$REPO_DIR/$TARGET"
HOOKS=("pre-commit") # list of supported hooks

if [ ! -d "$TARGET_DIR" ]; then
    echo "Directory $TARGET does not exist."
    exit 1
fi

cd "$TARGET_DIR" || exit

install_hooks() {
    for hook in "${HOOKS[@]}"; do
        echo "Running pre-commit install for $hook in $TARGET"
        pre-commit install -t $hook -f| awk '{print " - " $0}'

        if [ ! -f "$REPO_DIR/.git/hooks/$hook" ]; then
            echo " - pre-commit install did not generate a new $hook hook."
            exit 1
        fi

        mkdir -p "$TARGET_DIR/.hooks"

        echo " - moving new $hook hook to $TARGET/.hooks/"
        mv "$REPO_DIR/.git/hooks/$hook" "$TARGET_DIR/.hooks/$hook"
        echo ""
    done
}

copy_main_hooks() {
    REPO_HOOKS_DIR="$REPO_DIR/.hooks"
    if [ -d "$REPO_HOOKS_DIR" ]; then
        echo "Copying main hooks from $REPO_HOOKS_DIR to .git/hooks/"
        cp "$REPO_HOOKS_DIR"/* "$REPO_DIR/.git/hooks/"
    else
        echo "No main hooks directory found at $REPO_HOOKS_DIR."
        exit 1
    fi
}

install_hooks
copy_main_hooks

echo "Hooks installation complete."
