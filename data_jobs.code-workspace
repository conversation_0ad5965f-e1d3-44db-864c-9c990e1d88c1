{"folders": [{"name": "data-jobs", "path": "../data-jobs"}, {"name": "[LIBS] data_sdk", "path": "../data-jobs/libs/data-sdk"}, {"name": "[JOB] core_silver", "path": "../data-jobs/jobs/core_silver"}, {"name": "[JOB] find_shards", "path": "../data-jobs/jobs/find_shards"}, {"name": "[JOB] pbi_refresh", "path": "../data-jobs/jobs/pbi_refresh"}, {"name": "[JOB] update_shared", "path": "../data-jobs/jobs/update_shared"}, {"name": "[JOB] saas_gold", "path": "../data-jobs/jobs/saas_gold"}, {"name": "[JOB] ppt_gold", "path": "../data-jobs/jobs/ppt_gold"}, {"name": "[JOB] events_service_gold", "path": "../data-jobs/jobs/events_service_gold"}], "settings": {"files.exclude": {"**/__pycache__": true}, "[python]": {"editor.defaultFormatter": "charliermarsh.ruff", "editor.formatOnSave": true}}, "extensions": {"recommendations": ["ms-python.python", "ms-vscode-remote.remote-wsl", "ms-toolsai.jupyter", "nickmillerdev.pytest-fixtures", "charliermarsh.ruff"]}}